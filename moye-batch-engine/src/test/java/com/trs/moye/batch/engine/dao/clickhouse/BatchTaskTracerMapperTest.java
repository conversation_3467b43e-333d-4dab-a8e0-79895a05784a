package com.trs.moye.batch.engine.dao.clickhouse;

import com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration;
import com.baomidou.mybatisplus.test.autoconfigure.MybatisPlusTest;
import com.trs.moye.batch.engine.entity.BatchTaskTracer;
import com.trs.moye.batch.engine.enums.TaskNodeEnum;
import java.time.LocalDateTime;
import javax.annotation.Resource;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.boot.autoconfigure.ImportAutoConfiguration;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.boot.test.autoconfigure.jdbc.AutoConfigureTestDatabase;

@MybatisPlusTest
@AutoConfigureTestDatabase(replace = AutoConfigureTestDatabase.Replace.NONE) // 使用真实的数据源
@ImportAutoConfiguration(value = {
    DynamicDataSourceAutoConfiguration.class}, exclude = DataSourceAutoConfiguration.class) // 导入数据源自动配置并使用动态数据源
class BatchTaskTracerMapperTest {

    @Resource
    private BatchTaskTracerMapper mapper;

    @Test
    void insertBatchTaskTracer() {

        // delete all records id = 1
        mapper.deleteById(1L);

        BatchTaskTracer tracer = BatchTaskTracer.builder()
            .id(1L)
            .executeId("test-execute-id")
            .startTime(LocalDateTime.now())
            .node(TaskNodeEnum.FIRE.getName())
            .build();

        mapper.insert(tracer);
        BatchTaskTracer insertedTracer = mapper.getById(tracer.getId());

        Assertions.assertNotNull(insertedTracer, "Inserted tracer should not be null");
        Assertions.assertEquals(tracer.getExecuteId(), insertedTracer.getExecuteId(), "Execute ID should match");
        Assertions.assertEquals(tracer.getStartTime().withNano(0), insertedTracer.getStartTime().withNano(0),
            "Start time should match");
        Assertions.assertEquals(tracer.getNode(), insertedTracer.getNode(), "Node should match");

        mapper.deleteById(1L);
    }
}