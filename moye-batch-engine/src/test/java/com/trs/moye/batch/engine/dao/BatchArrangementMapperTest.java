package com.trs.moye.batch.engine.dao;

import static org.junit.jupiter.api.Assertions.assertEquals;

import com.baomidou.mybatisplus.test.autoconfigure.MybatisPlusTest;
import com.trs.moye.ability.entity.Ability;
import com.trs.moye.ability.entity.Schema.BaseTypeSchema;
import com.trs.moye.ability.entity.operator.BatchOperator;
import com.trs.moye.ability.entity.operator.OperatorRowType;
import com.trs.moye.ability.enums.AbilityTestStatus;
import com.trs.moye.ability.enums.AbilityType;
import com.trs.moye.ability.enums.AbilityUpdateStatus;
import com.trs.moye.batch.engine.entity.BatchArrangement;
import com.trs.moye.batch.engine.entity.BatchCodeSubtask;
import com.trs.moye.batch.engine.enums.ArrangeDisplayType;
import com.trs.moye.batch.engine.enums.CodeTypeEnum;
import java.util.List;
import javax.annotation.Resource;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.autoconfigure.jdbc.AutoConfigureTestDatabase;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.test.context.ActiveProfiles;

/**
 * BatchArrangementMapperTest
 *
 * <AUTHOR>
 * @since 2025/3/27 16:03
 */
@MybatisPlusTest
@ActiveProfiles("test")
@AutoConfigureTestDatabase(replace = AutoConfigureTestDatabase.Replace.NONE)
class BatchArrangementMapperTest {

    @Resource
    private BatchArrangementMapper batchArrangementMapper;
    @Resource
    private BatchOperatorMapper batchOperatorMapper;
    @Resource
    private AbilityMapper abilityMapper;
    @Resource
    private JdbcTemplate jdbcTemplate;


    @BeforeEach
    void setUp() {
        jdbcTemplate.execute("TRUNCATE TABLE batch_arrangement");
        jdbcTemplate.execute("TRUNCATE TABLE batch_operator");
        jdbcTemplate.execute("TRUNCATE TABLE ability");
    }

    @Test
    void selectByDataModelId() {
        Integer abilityId = 1;
        Integer arrangementId = 1;
        Integer dataModelId = 1;

        // 造ability数据
        Ability ability = new Ability();
        ability.setId(abilityId);
        ability.setEnName("Ability 1");
        ability.setZhName("能力1");
        ability.setDescription("Description of Ability 1");
        ability.setType(AbilityType.BATCH);
        ability.setPath("/path/to/ability1");
        ability.setOperatorCategoryId(1);
        ability.setIconName("icon1");
        ability.setUpdateStatus(AbilityUpdateStatus.UPDATED);
        ability.setTestStatus(AbilityTestStatus.UNTESTED);
        ability.setInputSchema(new BaseTypeSchema());
        ability.setOutputSchema(new BaseTypeSchema());
        ability.setInputSize(1);
        abilityMapper.insert(ability);

        // 造arrangement数据
        BatchArrangement batchArrangement = new BatchArrangement();
        batchArrangement.setId(arrangementId);
        batchArrangement.setDataModelId(dataModelId);
        batchArrangement.setDisplayType(ArrangeDisplayType.CANVAS);
        batchArrangementMapper.insert(batchArrangement);

        // 造operator数据
        OperatorRowType inputFields = new OperatorRowType();
        BaseTypeSchema schema = new BaseTypeSchema();
        schema.setEnName("age");
        inputFields.put("age", schema);
        List<BatchOperator> operators = List.of(
            BatchOperator.builder().arrangementId(arrangementId).dataModelId(dataModelId).displayId(1L).name("Operator 1").abilityId(ability.getId()).enabled(true).inputFields(inputFields).build(),
            BatchOperator.builder().arrangementId(arrangementId).dataModelId(dataModelId).displayId(2L).name("Operator 2").abilityId(ability.getId()).enabled(true).build(),
            BatchOperator.builder().arrangementId(2).dataModelId(2).displayId(3L).name("Operator 3").abilityId(ability.getId()).enabled(true).build()
        );
        batchOperatorMapper.insert(operators);

        BatchArrangement actualBatchArrangement = batchArrangementMapper.selectByDataModelId(dataModelId);
        assertEquals(dataModelId, actualBatchArrangement.getDataModelId());
        assertEquals(arrangementId, actualBatchArrangement.getId());
        assertEquals(2, actualBatchArrangement.getOperators().size());
        assertEquals(1, actualBatchArrangement.getOperators().get(0).getAbility().getInputSize());
    }

    @Test
    void selectByDataModelId_code() {
        BatchArrangement batchArrangement = new BatchArrangement();
        batchArrangement.setId(2);
        batchArrangement.setDataModelId(2);
        batchArrangement.setDisplayType(ArrangeDisplayType.CODE);
        batchArrangement.setCodeSubTasks(List.of(new BatchCodeSubtask("a", "code", CodeTypeEnum.PYTHON)));
        batchArrangementMapper.insert(batchArrangement);

        BatchArrangement actualBatchArrangement = batchArrangementMapper.selectByDataModelId(2);
        assertEquals("code", actualBatchArrangement.getCodeSubTasks().get(0).getCode());
    }
}