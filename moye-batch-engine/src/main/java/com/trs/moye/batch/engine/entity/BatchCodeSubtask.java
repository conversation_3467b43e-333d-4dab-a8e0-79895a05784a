package com.trs.moye.batch.engine.entity;

import com.trs.moye.batch.engine.enums.CodeTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 要素库-代码模式-子任务(单个代码脚本)
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class BatchCodeSubtask {

    /**
     * 子任务名称
     */
    private String name;

    /**
     * 子任务代码
     */
    private String code;

    /**
     * 代码脚本类型
     */
    private CodeTypeEnum codeType;

}
