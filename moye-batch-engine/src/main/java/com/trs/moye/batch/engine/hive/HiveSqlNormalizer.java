package com.trs.moye.batch.engine.hive;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * Hive SQL 标准化工具类
 */
public class HiveSqlNormalizer {

    private HiveSqlNormalizer() {
    }

    /**
     * 标准化SQL
     *
     * @param sqlText SQL文本
     * @return 标准化后的SQL
     */
    public static List<String> normalizeSql(String sqlText) {
        // 1. 去除注释
        String formattedSql = removeComments(sqlText);

        // 2. 去除换行和空格
        formattedSql = formattedSql.replaceAll("[\n\r\t ]+", " ");

        // 3. 将关键字大写
        formattedSql = capitalizeKeywords(formattedSql);

        // 4. 分割多行SQL
        List<String> sqlList = splitSqlByDelimiter(formattedSql);

        // 拼接格式化后的SQL
        List<String> resultSql = new ArrayList<>();
//        StringBuilder sb = new StringBuilder();
        for (String sql : sqlList) {
            String trim = sql.trim();
            if (trim.isEmpty()) {
                continue;
            }
            // 5. 检查是否是SELECT语句并添加LIMIT 10000
            if (trim.toUpperCase().startsWith("SELECT") && !trim.toUpperCase().contains(" LIMIT ")) {
                trim += " LIMIT 10000";
            }
            resultSql.add(trim + ";");
        }

        return resultSql;
    }

    private static String removeComments(String sql) {
        // 去除/**/注释
        Pattern pattern = Pattern.compile("/\\*.*?\\*/", Pattern.DOTALL);
        Matcher matcher = pattern.matcher(sql);
        sql = matcher.replaceAll("");

        // 去除--注释
        pattern = Pattern.compile("--.*?($|\n)");
        matcher = pattern.matcher(sql);
        sql = matcher.replaceAll("");
        return sql;
    }

    private static String capitalizeKeywords(String sql) {
        // Hive SQL关键字列表
        String[] keywords = {"SELECT", "FROM", "WHERE", "GROUP", "BY", "HAVING", "ORDER", "LIMIT", "OFFSET",
            "JOIN", "LEFT", "RIGHT", "INNER", "OUTER", "ON", "AND", "OR", "NOT", "IN", "LIKE", "BETWEEN",
            "IS", "NULL", "AS", "CREATE", "TABLE", "DATABASE", "DROP", "ALTER", "INSERT", "INTO", "VALUES",
            "UPDATE", "DELETE", "PARTITION", "CLUSTER", "SORT", "EXCHANGE", "LOAD", "DATA", "OVERWRITE",
            "DIRECTORY", "LOCAL", "INPATH", "VIEW", "TEMPORARY", "FUNCTION", "EXPLAIN", "CASE", "WHEN", "THEN",
            "ELSE", "END"};

        for (String keyword : keywords) {
            sql = sql.replaceAll("(?i)\\b" + keyword + "\\b", keyword);
        }

        return sql;
    }

    private static List<String> splitSqlByDelimiter(String sql) {
        List<String> sqlList = new ArrayList<>();
        String[] sqlArray = sql.split(";");
        Collections.addAll(sqlList, sqlArray);
        return sqlList;
    }
}
