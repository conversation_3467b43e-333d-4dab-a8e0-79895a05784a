package com.trs.moye.batch.engine.service;

import com.trs.moye.batch.engine.dao.clickhouse.BatchTaskRecordMapper;
import com.trs.moye.batch.engine.dao.clickhouse.BatchTaskTracerMapper;
import com.trs.moye.batch.engine.entity.BatchTaskRecord;
import com.trs.moye.batch.engine.entity.BatchTaskTracer;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 监控日志
 */
@Slf4j
@Service
public class BatchTaskMonitor {

    private static BatchTaskRecordMapper recordMapper;
    private static BatchTaskTracerMapper tracerMapper;

    /**
     * 注入 mapper
     *
     * @param recordMapper recordMapper
     * @param tracerMapper tracerMapper
     */
    @Autowired
    public BatchTaskMonitor(BatchTaskRecordMapper recordMapper, BatchTaskTracerMapper tracerMapper) {
        BatchTaskMonitor.recordMapper = recordMapper;
        BatchTaskMonitor.tracerMapper = tracerMapper;
    }

    /**
     * 插入单条record数据，任何运行时异常不能抛出影响任务流程
     *
     * @param record 批处理监控记录
     */
    public static synchronized void insertOrUpdateRecord(BatchTaskRecord record) {
        try {
            BatchTaskRecord oldRecord = recordMapper.getByExecuteId(record.getExecuteId());
            if (Objects.isNull(oldRecord)) {
                record.setErrorMsgReadFlag(0);
                log.info("BatchTaskRecord 新增数据: {} ", record);
                recordMapper.insert(record);
            } else {
                log.info("BatchTaskRecord 更新数据: {} ", record);
                recordMapper.updateSelective(record);
            }
        } catch (RuntimeException e) {
            log.error("监控数据 BatchTaskRecord 存储到库过程发生异常", e);
        }
    }

    /**
     * 更新applicationId
     *
     * @param executeId     execute id
     * @param applicationId spark application id
     */
    public static void updateApplicationId(String executeId, String applicationId) {
        try {
            if (Objects.nonNull(executeId) && Objects.nonNull(applicationId)) {
                recordMapper.updateApplicationId(executeId, applicationId);
            } else {
                log.warn(
                    "试图更新监控数据BatchTaskRecord的applicationId，参数为空因此忽略更新操作，executeId:{}, applicationId:{}",
                    executeId, applicationId);
            }
        } catch (RuntimeException e) {
            log.error("监控数据 BatchTaskRecord 更新applicationId 发生异常", e);
        }
    }

    /**
     * 插入单条tracer数据，任何运行时异常都不应该抛出，记录日志不能影响任务流程
     *
     * @param tracer 批处理监控数据
     */
    public static void insertTracer(BatchTaskTracer tracer) {
        try {
            tracerMapper.insert(tracer);
        } catch (RuntimeException e) {
            log.error("监控数据 BatchTaskTracer 存储到库过程发生异常", e);
        }
    }

    /**
     * 批量插入tracer数据
     *
     * @param tracerList 批处理监控数据
     */
    public static void insertTracerList(List<BatchTaskTracer> tracerList) {
        try {
            tracerMapper.insertBatch(tracerList);
        } catch (RuntimeException e) {
            log.error("监控数据 BatchTaskTracer 存储到库过程发生异常", e);
        }
    }

    /**
     * 服务shutdown前对未结束任务进行标记
     *
     * @param executeIdList 未结束的任务执行id
     */
    public static void whenShutdown(List<String> executeIdList) {
        log.info("批量标记任务为丢失状态");
        LocalDateTime endTime = LocalDateTime.now();
        try {
            tracerMapper.insertBatch(BatchTaskTracer.lostTask(executeIdList));
            recordMapper.updateWhenLostTask(executeIdList, endTime);
        } catch (RuntimeException e) {
            log.error("批量标记任务为丢失状态时发生异常", e);
        }
    }

}
