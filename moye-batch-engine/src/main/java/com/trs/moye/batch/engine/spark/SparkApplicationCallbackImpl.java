package com.trs.moye.batch.engine.spark;

import com.trs.moye.ability.utils.BeanUtil;
import com.trs.moye.batch.engine.entity.vo.BatchLogVO;
import com.trs.moye.batch.engine.service.BatchTaskMonitor;
import com.trs.moye.batch.engine.spark.base.SparkApplicationCallback;
import com.trs.moye.batch.engine.spark.config.SparkApplicationConfig;
import com.trs.moye.batch.engine.utils.yarnlog.YarnLogUtil;
import com.trs.moye.batch.engine.websocket.SessionManager;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.util.Objects;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.spark.launcher.SparkAppHandle;

/**
 * spark application 回调类
 */
@Data
@Slf4j
public class SparkApplicationCallbackImpl implements SparkApplicationCallback {

    /**
     * 是否需要实时日志
     */
    private boolean needRealTimeLog;
    /**
     * 任务 id
     */
    private String taskId;
    /**
     * 执行id
     */
    private String executeId;
    /**
     * 用户 id
     */
    private Integer userId;
    /**
     * kerberos用户
     */
    private String principal;
    /**
     * kerberos凭据
     */
    private String keytabPath;

    /**
     * 是否已经记录过appId，避免反复更新ck造成无端负载
     */
    private boolean hasRecordAppId;

    /**
     * {@link SparkTaskManager} 的 Bean
     */
    private SparkTaskManager sparkTaskManager;
    /**
     * {@link SparkApplicationConfig} Bean
     */
    private SparkApplicationConfig sparkApplicationConfig;

    /**
     * 构造方法
     *
     * @param needRealTimeLog 是否需要实时日志
     * @param taskId          任务id
     * @param executeId       执行id
     * @param userId          用户id
     * @param principal       kerberos认证principal
     * @param keytabPath      kerberos认证keytab文件
     */
    public SparkApplicationCallbackImpl(boolean needRealTimeLog, String taskId, String executeId, Integer userId,
        String principal,
        String keytabPath) {
        this.needRealTimeLog = needRealTimeLog;
        this.taskId = taskId;
        this.executeId = executeId;
        this.userId = userId;
        this.principal = principal;
        this.keytabPath = keytabPath;
        this.hasRecordAppId = false;
        this.sparkTaskManager = BeanUtil.getBean(SparkTaskManager.class);
        this.sparkApplicationConfig = BeanUtil.getBean(SparkApplicationConfig.class);
    }

    /**
     * 在 connected 状态触发 <br/> 任务 handle 存到 map 中
     *
     * @param handle spark application id
     */
    @Override
    public void onConnected(SparkAppHandle handle) {
    }

    /**
     * 在 spark 任务结束时触发 <br/> 从 map 移除 任务handle
     *
     * @param handle spark application id
     */
    @Override
    public void whenFinal(SparkAppHandle handle) {
    }

    /**
     * 在 info changed 时触发，根据 handle 内信息 执行方法
     * <br/>
     * 1. 存储 appId 到 clickhouse
     *
     * @param handle spark 应用句柄
     */
    @Override
    public void whenInfoChanged(SparkAppHandle handle) {
        if (!hasRecordAppId && Objects.nonNull(handle.getAppId())) {
            log.info("任务 executeId: {} 监听到 appId {}, 存储到 clickhouse", executeId, handle.getAppId());
            BatchTaskMonitor.updateApplicationId(executeId, handle.getAppId());
            realTimeLog(handle);
            hasRecordAppId = true;
        }
    }

    /**
     * 日志输出到websocket
     *
     * @param handle spark 应用句柄
     */
    private void realTimeLog(SparkAppHandle handle) {
        // 定时任务无需实时日志
        if (!needRealTimeLog) {
            log.info("[任务 taskId: {} executeId: {}] 不触发实时日志", taskId, executeId);
            return;
        }

        // 获取日志输出
        InputStream logStream = null;
        if (sparkApplicationConfig.getMode().isYarnMode()) {
            logStream = realTimeLogFromYarn(handle.getAppId());
        }

        // 日志流为空，返回
        if (Objects.isNull(logStream)) {
            return;
        }

        // 输出到ws
        try (BufferedReader reader = new BufferedReader(new InputStreamReader(logStream))) {
            log.info("[任务 taskId:{} executeId:{} applicationId:{}] 开始实时拉取日志", taskId, executeId,
                handle.getAppId());
            String line;
            while ((line = reader.readLine()) != null) {
                SessionManager.getInstance().sendMessage(taskId, userId, BatchLogVO.logContent(line));
            }
        } catch (IOException | RuntimeException e) {
            log.error("[任务 taskId:{} executeId:{} applicationId:{}] 实时日志出现异常", taskId, executeId,
                handle.getAppId(), e);
        } finally {
            try {
                logStream.close();
            } catch (IOException e) {
                log.error("[任务 taskId:{} executeId:{}] 关闭日志流logStream出现异常", taskId, executeId, e);
            }
        }
    }

    /**
     * yarn 日志流
     *
     * @param appId spark application id
     * @return 日志流
     */
    private InputStream realTimeLogFromYarn(String appId) {
        try {
            return YarnLogUtil.streamLogs(appId, principal, keytabPath);
        } catch (IOException | RuntimeException e) {
            log.error("[任务 taskId:{} executeId:{} applicationId:{}] 获取yarn日志流出现异常", taskId, executeId,
                appId, e);
        }
        return null;
    }

}
