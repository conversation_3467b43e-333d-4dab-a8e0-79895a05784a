package com.trs.moye.batch.engine.spark.base;

import java.util.Objects;

/**
 * Mode <br/> spark 任务模式
 *
 * <AUTHOR>
 * @since 2024/12/26 16:54
 */
public enum Mode {

    LOCAL, STANDALONE, YARN, K8S;

    /**
     * 根据master判断模式
     * <table>
     *     <tr>
     *         <th>master</th>
     *         <th>Mode</th>
     *     </tr>
     *     <tr>
     *         <td>local</td>
     *         <td>{@linkplain #LOCAL}</td>
     *     </tr>
     *     <tr>
     *         <td>spark://master-ip:7077</td>
     *         <td>{@linkplain #STANDALONE}</td>
     *     </tr>
     *     <tr>
     *         <td>yarn</td>
     *         <td>{@linkplain #YARN}</td>
     *     </tr>
     * </table>
     *
     * @param master master
     * @return 模式
     */
    public static Mode getMode(String master) {
        if (Objects.isNull(master)) {
            return LOCAL;
        } else if ("yarn".equalsIgnoreCase(master)) {
            return YARN;
        } else if (master.startsWith("spark")) {
            return STANDALONE;
        }
        return LOCAL;
    }

    /**
     * 是否是 yarn 模式
     *
     * @return 是否是 yarn 模式
     */
    public boolean isYarnMode() {
        return this == YARN;
    }

    /**
     * 是否是spark集群模式
     *
     * @return 是否是spark集群模式
     */
    public boolean isStandaloneMode() {
        return this == STANDALONE;
    }
}
