package com.trs.moye.batch.engine.utils.code;

import java.util.Arrays;
import java.util.Map;
import org.apache.commons.lang3.StringUtils;

/**
 * 为用户提交的 python/sql 代码 做参数替换
 */
public class CodeHelper {

    /**
     * 占位符 #{}
     */
    private static final String PLACEHOLDER_PREFIX = "#{";
    private static final String PLACEHOLDER_SUFFIX = "}";

    /**
     * 匹配 占位符 替换参数
     * <br>
     * 占位符内参数不正确则抛出异常，即 既不是中台规定的参数也不是自定义参数
     *
     * @param codeTemplate 未经过参数替换的 python 代码
     * @param parameters   可替换参数，和它的值
     * @return 替换后的 python 代码
     */
    public static String replaceParameters(String codeTemplate, Map<String, String> parameters) {

        if (StringUtils.isBlank(codeTemplate)) {
            return codeTemplate;
        }

        if (parameters == null) {
            parameters = Map.of();
        }

        // 记录完成参数替换的python代码
        StringBuilder sb = new StringBuilder();

        int cur = 0;
        int placeHolderStart;

        // 匹配占位符
        while ((placeHolderStart = codeTemplate.indexOf(PLACEHOLDER_PREFIX, cur)) != -1) {
            // 不含占位符的代码
            sb.append(codeTemplate, cur, placeHolderStart);

            // 占位符结束
            int endIndex = codeTemplate.indexOf(PLACEHOLDER_SUFFIX, placeHolderStart + PLACEHOLDER_PREFIX.length());
            if (endIndex == -1) {
                String errMsg = String.format("参数替换异常，未找到占位终止符 '}'，对应的占位起始符：%s ",
                    codeTemplate.substring(placeHolderStart));
                throw new IllegalArgumentException(errMsg);
            }

            // 参数替换
            String placeholder = codeTemplate.substring(placeHolderStart + PLACEHOLDER_PREFIX.length(), endIndex);
            String value = getParameterValue(placeholder, parameters);
            sb.append(value);

            cur = endIndex + PLACEHOLDER_SUFFIX.length();
        }
        // 剩余不含占位符的代码
        sb.append(codeTemplate, cur, codeTemplate.length());
        return sb.toString();
    }

    private static String getParameterValue(String placeholder, Map<String, String> parameters) {
        String value = parameters.get(placeholder);
        if (value == null) {
            String errMsg = String.format("参数替换异常，未找到参数 %s，可用的参数: %s", placeholder,
                Arrays.toString(parameters.keySet().toArray()));
            throw new IllegalArgumentException(errMsg);
        }
        return value;
    }

}
