package com.trs.moye.batch.engine.hive;

import com.trs.moye.base.common.exception.BizException;
import com.trs.moye.batch.engine.entity.vo.BatchLogVO;
import com.trs.moye.batch.engine.enums.TaskStatusEnum;
import com.trs.moye.batch.engine.task.LogFileWriter;
import com.trs.moye.batch.engine.utils.LogParseUtil;
import com.trs.moye.batch.engine.websocket.SessionManager;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import java.util.function.Consumer;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

/**
 * hive 命令行客户端
 */
@Component
@Slf4j
public class HiveCommandLineClient {

    @Resource
    private HiveProperties hiveProperties;

    @Resource
    private LogFileWriter logFileWriter;

    /**
     * 执行 hive sql 并发送日志
     *
     * @param sql  sql
     * @param task 任务信息
     */
    public void executeSqlAndSendLog(String sql, SqlTaskContext task) {
        String taskId = task.getTaskId();
        if (StringUtils.isBlank(taskId)) {
            throw new IllegalArgumentException("taskId 不能为空");
        }
        String executionId = task.getExecutionId();
        if (StringUtils.isBlank(executionId)) {
            throw new IllegalArgumentException("executionId 不能为空");
        }
        Integer userId = task.getUserId();
        String taskName = task.getTaskName();
        if (userId == null) {
            throw new IllegalArgumentException("userId 不能为空");
        }

        log.info("开始执行 hive sql: 任务Id: {}，sql: {}", taskId, sql);
        try {
            if (StringUtils.isBlank(sql)) {
                log.warn("sql 为空！");
                SessionManager.getInstance()
                    .sendMessage(taskId, userId,
                        BatchLogVO.getTaskStatusMsg(task.getTaskName(), TaskStatusEnum.SUCCESS));
                return;
            }
            Consumer<String> recordLog = line -> {
                logFileWriter.write(task, line);
                log.info(line);
                if (LogParseUtil.isSqlResultLine(line)) {
                    return;
                }
                SessionManager.getInstance().sendMessage(taskId, userId, BatchLogVO.logContent(line));
            };
            Consumer<String> recordError = line -> {
                logFileWriter.write(task, line);
                log.error(line);
                if (LogParseUtil.isSqlResultLine(line)) {
                    return;
                }
                SessionManager.getInstance().sendMessage(taskId, userId, BatchLogVO.logContent(line));
            };
            long timeSpend = executeHiveSql(sql, recordLog, recordError);
            log.info("hive sql 执行完毕，耗时 {} ms , sql: {}", timeSpend, sql);
        } catch (Exception e) {
            logFileWriter.flush(task);
            SessionManager.getInstance()
                .sendMessage(taskId, userId, BatchLogVO.getTaskStatusMsg(taskName, TaskStatusEnum.FAIL));
            throw e;
        }
    }

    /**
     * 执行 hive sql 不发送日志
     *
     * @param task 任务信息
     * @param sql  sql
     */
    public void executeSql(String sql, SqlTaskContext task) {
        if (StringUtils.isBlank(sql)) {
            throw new IllegalArgumentException("sql 不能为空");
        }
        log.info("开始执行 hive sql: {}", sql);
        try {
            Consumer<String> recordInfo = line -> {
                logFileWriter.write(task, line);
                log.info(line);
            };
            Consumer<String> recordError = line -> {
                logFileWriter.write(task, line);
                log.error(line);
            };
            long timeSpend = executeHiveSql(sql, recordInfo, recordError);
            log.info("hive sql 执行完毕，耗时 {} ms", timeSpend);
        } catch (BizException e) {
            // 记录异常并重新抛出
            log.error("执行 hive sql 异常", e);
            throw e;
        } finally {
            logFileWriter.flush(task);
        }
    }

    private long executeHiveSql(String sql, Consumer<String> outputHandler, Consumer<String> errorHandler)
        throws BizException {
        final long beginTime = System.currentTimeMillis();
        log.info("准备执行sql：{}", sql);
        // 写入临时文件
        String tmpFile = UUID.randomUUID() + ".sql";
        Path tempFilePath = Path.of(tmpFile);
        try {
            Files.write(tempFilePath, sql.getBytes());
            log.info("创建临时文件: {}", tmpFile);
            List<String> commands = new ArrayList<>();
            commands.add("/bin/bash");
            commands.add("-c");
            commands.add(hiveProperties.getCommand(tmpFile));
            log.info("命令行 : {}", String.join(" ", commands));

            ProcessBuilder processBuilder = new ProcessBuilder(commands);
            Process process = processBuilder.start();

            final Thread outputReaderThread = new Thread(() -> {
                try (BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()))) {
                    // 获取命令输出
                    String line;
                    while ((line = reader.readLine()) != null) {
                        outputHandler.accept(line);
                    }
                } catch (IOException e) {
                    log.error("读取命令输出异常", e);
                }
            });

            final Thread errorReaderThread = new Thread(() -> {
                try (BufferedReader errorReader = new BufferedReader(new InputStreamReader(process.getErrorStream()))) {
                    // 读取标准错误流
                    String errorLine;
                    while ((errorLine = errorReader.readLine()) != null) {
                        errorHandler.accept(errorLine);
                    }
                } catch (IOException e) {
                    log.error("读取标准错误流异常", e);
                }
            });
            outputReaderThread.start();
            errorReaderThread.start();

            // 获取命令退出状态码
            int exitCode = process.waitFor();
            outputReaderThread.join();
            errorReaderThread.join();
            if (exitCode != 0) {
                log.error("hive sql 执行失败，退出状态码：{}, sql: {}", exitCode, sql);
                throw new BizException(
                    String.format("执行失败，不再执行后续任务！退出状态码: %d, sql: %s,  ", exitCode, sql));
            }
            return System.currentTimeMillis() - beginTime;
        } catch (IOException | InterruptedException e) {
            log.error("执行 hive sql 异常, sql: {}", sql, e);
            if (e instanceof InterruptedException) {
                Thread.currentThread().interrupt();
            }
            throw new BizException(
                String.format("执行异常，不再执行后续任务！sql: %s, 异常信息: %s", sql, e.getLocalizedMessage()));
        } finally {
            try {
                // 删除临时sql文件
                Files.delete(tempFilePath);
            } catch (IOException e) {
                log.error("删除临时文件失败", e);
            }
        }
    }

    /**
     * 作为几句sql结束的标识
     *
     * @param task 任务
     */
    public void writeFinishFlag(SqlTaskContext task) {
        logFileWriter.write(task, "Finish executing HIVE SQL");
    }

    /**
     * 完成sql之后发消息写日志等操作
     *
     * @param task 任务
     * @param sendLog 是否发/写日志
     */
    public void finishSql(SqlTaskContext task, boolean sendLog) {
        if(!sendLog){
            return;
        }
        String taskId = task.getTaskId();
        Integer userId = task.getUserId();
        SessionManager.getInstance()
            .sendMessage(taskId, userId, BatchLogVO.logContent("执行 hive sql 完成"));
        logFileWriter.flush(task);
        SessionManager.getInstance()
            .sendMessage(taskId, userId,
                BatchLogVO.getTaskStatusMsg(task.getTaskName(), TaskStatusEnum.SUCCESS));
    }
}
