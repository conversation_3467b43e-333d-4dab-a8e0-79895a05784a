package com.trs.moye.batch.engine.service;

import com.trs.moye.ability.entity.Ability;
import com.trs.moye.ability.entity.operator.BatchOperatorRuntimeEntity;
import com.trs.moye.ability.entity.operator.BatchOperatorRuntimeEntity.TableInfo;
import com.trs.moye.ability.entity.operator.BatchOperatorRuntimeEntity.Type;
import com.trs.moye.ability.enums.AbilityType;
import com.trs.moye.base.common.enums.FieldType;
import com.trs.moye.base.common.enums.ModelLayer;
import com.trs.moye.base.common.exception.BizException;
import com.trs.moye.base.common.request.BasicConditionSearchParams;
import com.trs.moye.base.common.request.PageParams;
import com.trs.moye.base.common.request.SortField;
import com.trs.moye.base.common.request.SortField.Order;
import com.trs.moye.base.common.response.PageResponse;
import com.trs.moye.base.common.utils.DateTimeUtils;
import com.trs.moye.base.common.utils.JsonUtils;
import com.trs.moye.base.data.connection.enums.ConnectionType;
import com.trs.moye.base.data.indicator.dao.IndicatorConfigMapper;
import com.trs.moye.base.data.indicator.dao.IndicatorPeriodConfigMapper;
import com.trs.moye.base.data.indicator.entity.IndicatorConfig;
import com.trs.moye.base.data.indicator.entity.IndicatorPeriodConfig;
import com.trs.moye.base.data.indicator.entity.IndicatorPeriodConfigEntity;
import com.trs.moye.base.data.indicator.entity.StatisticPeriod;
import com.trs.moye.base.data.indicator.enums.IndicatorConstants;
import com.trs.moye.base.data.indicator.enums.IndicatorTimeRange;
import com.trs.moye.base.data.indicator.utils.IndicatorPeriodConverter;
import com.trs.moye.base.data.indicator.utils.IndicatorSqlConvertor;
import com.trs.moye.base.data.model.dao.DataModelFieldMapper;
import com.trs.moye.base.data.model.dao.DataModelMapper;
import com.trs.moye.base.data.model.entity.DataModel;
import com.trs.moye.base.data.model.entity.DataSourceConfig;
import com.trs.moye.base.data.service.entity.Condition;
import com.trs.moye.base.data.service.entity.DataServiceField;
import com.trs.moye.base.data.service.entity.ValueObject;
import com.trs.moye.base.data.service.enums.DataServiceConditionType;
import com.trs.moye.base.data.service.enums.EvaluateOperator;
import com.trs.moye.base.data.storage.DataStorage;
import com.trs.moye.base.data.storage.dao.DataStorageMapper;
import com.trs.moye.batch.engine.dao.BatchArrangementMapper;
import com.trs.moye.batch.engine.entity.BatchArrangement;
import com.trs.moye.batch.engine.feign.SearchFeign;
import com.trs.moye.batch.engine.properties.AbilityCenterGatewayProperties;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

/**
 * 根据批处理任务创建BatchOperatorRuntimeEntity列表，用于更新到redis
 *
 * <AUTHOR>
 * @since 2025/5/20 14:21
 */
@Service
@Slf4j
public class RuntimeOperatorService {

    @Resource
    private DataModelMapper dataModelMapper;

    @Resource
    private BatchArrangementMapper batchArrangementMapper;

    @Resource
    private DataStorageMapper dataStorageMapper;

    @Resource
    private DataModelFieldMapper dataModelFieldMapper;

    @Resource
    private IndicatorConfigMapper indicatorConfigMapper;

    @Resource
    private IndicatorPeriodConfigMapper indicatorPeriodConfigMapper;

    @Resource
    private AbilityCenterGatewayProperties abilityCenterGatewayProperties;

    @Resource
    private SearchFeign searchFeign;

    private static final String SAVE = "数据存储：";

    @Value("${indicator.task.default.periods.count:3}")
    private Integer periodCount;

//    @PostConstruct
//    public void test() {
//        List<BatchOperatorRuntimeEntity> entities = createRuntimeOperators(10357,
//                DateTimeUtils.parse("2025-07-01 11:26:00"), DateTimeUtils.parse("2025-07-02 11:26:00"));
//    entities.size();
//    }

    /**
     * createRuntimeOperators
     *
     * @param dataModelId 建模id
     * @param beginTime   开始时间
     * @param endTime     结束时间
     * @return BatchOperatorRuntimeEntity
     */
    public List<BatchOperatorRuntimeEntity> createRuntimeOperators(Integer dataModelId,
        LocalDateTime beginTime, LocalDateTime endTime) {
        DataModel dataModel = dataModelMapper.selectById(dataModelId);
        List<BatchOperatorRuntimeEntity> entities;
        //指标库任务，走额外逻辑
        if (dataModel.getLayer().equals(ModelLayer.INDICATOR)) {
            entities = createIndicatorOperators(dataModel, beginTime, endTime);
        } else {
            BatchArrangement batchArrangement = batchArrangementMapper.selectByDataModelId(dataModelId);
            entities = BatchOperatorRuntimeEntity.buildRuntimeEntities(
                batchArrangement.getOperators(), dataModelId, dataModelFieldMapper, dataStorageMapper, dataModelMapper);
            entities.forEach(entity -> {
                if (entity.getAbility() != null) {
                    entity.getAbility().setAbilityCenterProperties(abilityCenterGatewayProperties.to());
                }
            });
        }
        log.info("更新批处理任务：{} 的redis算子配置：\n{}", dataModelId, JsonUtils.toJsonString(entities));
        return entities;
    }

    private List<BatchOperatorRuntimeEntity> createIndicatorOperators(DataModel dataModel,
        LocalDateTime beginTime, LocalDateTime endTime) {
        DataSourceConfig dataSourceConfig = dataModel.getDataSource().get(0);
        DataModel sourceDataModel = dataModelMapper.selectById(dataSourceConfig.getSourceModelId());
        if (sourceDataModel.getDataStorages().isEmpty()) {
            throw new BizException("数据建模：%d的存储点为空！", sourceDataModel.getId());
        }
        DataStorage dataStorage = dataStorageMapper.selectByDataModelIdAndConnectionIdWithConnection(
            dataModel.getId(), IndicatorConstants.INDICATOR_CONNECTION_ID);

        //接入算子
        IndicatorConfig config = indicatorConfigMapper.selectByDataModelId(dataModel.getId());
        IndicatorPeriodConfigEntity periodConfig = indicatorPeriodConfigMapper.selectByPeriodType(
            config.getStatisticStrategyInfo().getPeriod().name());
        DataStorage sourceDataStorage = sourceDataModel.getDataStorages().get(0);
        ConnectionType connectionType = sourceDataStorage.getConnection().getConnectionType();
        List<BatchOperatorRuntimeEntity> entities = new ArrayList<>();
        Map<Long, String> tableNames = new HashMap<>();

        // 获取需要进行统计的时间范围列表
        List<StatisticPeriod> periods = getStatisticPeriods(periodConfig.getConfig(), dataModel.getEnName(), beginTime,
            endTime);
        periods.sort(Comparator.comparing(StatisticPeriod::getStartTime));

        for (long i = 0L; i < periods.size(); i++) {
            BatchOperatorRuntimeEntity source = createInputOperator(sourceDataStorage, sourceDataModel.getEnName(),
                sourceDataModel.getZhName(), connectionType, config, periods.get((int) i));
            tableNames.put(i, source.getOutputTableName());
            entities.add(source);
        }

        // 如果统计算子不为空，则增加union和save算子
        if (!entities.isEmpty()) {
            //union
            BatchOperatorRuntimeEntity union = new BatchOperatorRuntimeEntity();
            union.setName("对输入数据进行union all");
            union.setType(Type.ABILITY);
            union.setEnabled(true);
            union.setInputTables(tableNames);
            Ability unionAbility = new Ability();
            unionAbility.setType(AbilityType.BATCH);
            unionAbility.setEnName("union");
            unionAbility.setZhName("联合数据");
            union.setAbility(unionAbility);
            union.setOutputTableName("union");
            entities.add(union);
            //存储算子
            BatchOperatorRuntimeEntity storage = new BatchOperatorRuntimeEntity();
            storage.setName(SAVE + dataModel.getZhName());
            storage.setOutputTableName(dataModel.getEnName());
            storage.setInputTables(Map.of(0L, "union"));
            storage.setEnabled(true);
            storage.setType(Type.STORAGE);
            // 如果有开始时间和结束时间，则设置为覆盖
            List<String> deletePartitions = getNeedDeletePartitions((beginTime != null && endTime != null), periods);
            storage.setStorageTableInfos(List.of(TableInfo.fromDataStorage(dataStorage, deletePartitions)));
            entities.add(storage);
        }
        return entities;
    }


    protected List<String> getNeedDeletePartitions(boolean needDeletePartitions, List<StatisticPeriod> periods) {
        if (!needDeletePartitions || periods.isEmpty()) {
            return new ArrayList<>();
        }
        // 取第一个和最后一个周期的开始时间
        LocalDateTime beginTime = periods.get(0).getStartTime();
        LocalDateTime endTime = periods.get(periods.size() - 1).getStartTime();
        return getNeedDeletePartitions(beginTime, endTime);
    }

    /**
     * 获取统计范围内的分区
     *
     * @param beginTime 开始时间
     * @param endTime   结束时间
     * @return 分区列表
     */
    protected List<String> getNeedDeletePartitions(LocalDateTime beginTime, LocalDateTime endTime) {
        if (beginTime == null || endTime == null) {
            return new ArrayList<>();
        }
        // 建表分区加了8小时，这里也要统一加8小时
        LocalDate begin = beginTime.plusHours(8).toLocalDate();
        LocalDate end = endTime.plusHours(8).toLocalDate();
        List<String> dateRange = new ArrayList<>();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");

        LocalDate current = begin;
        while (!current.isAfter(end)) {
            String formattedDate = current.format(formatter);
            if (!dateRange.contains(formattedDate)) {
                dateRange.add(formattedDate);
            }
            current = current.plusDays(1);
        }

        return dateRange;
    }

    /**
     * 获取重跑时间周期
     *
     * @param config      配置
     * @param periods     周期列表
     * @param currentTime 当前时间
     * @param endTime     统计结束时间
     */
    public static void getRerunStatisticPeriods(IndicatorPeriodConfig<?> config, List<StatisticPeriod> periods,
        LocalDateTime currentTime, LocalDateTime endTime) {
        StatisticPeriod period = IndicatorPeriodConverter.calculateCurrentPeriod(currentTime, config);
        // 只要周期的开始时间在统计时间范围的结束时间之前，就算有效周期
        if (period.getStartTime().isBefore(endTime)) {
            periods.add(period);
            // 用当前周期的结束时间加1分钟，继续获取下一个周期
            getRerunStatisticPeriods(config, periods, period.getEndTime().plusMinutes(1), endTime);
        }
    }

    private List<StatisticPeriod> getStatisticPeriods(IndicatorPeriodConfig<?> config, String tableName,
        LocalDateTime beginTime, LocalDateTime endTime) {
        List<StatisticPeriod> periods = new ArrayList<>();
        // 有开始时间和结束时间时，直接返回这段时间中的统计周期
        if (beginTime != null && endTime != null) {
            // 时间抹去时分秒
            beginTime = beginTime.toLocalDate().atStartOfDay();
            endTime = endTime.toLocalDate().atStartOfDay();
            getRerunStatisticPeriods(config, periods, beginTime, endTime);
            return periods;
        }

        LocalDateTime startTime = getLastStatisticTime(tableName);
        if (startTime == null) {
            //如果没有上次统计时间，则默认多往前统计periodCount个周期
            StatisticPeriod current = IndicatorTimeRange.CURRENT_PERIOD.getPeriodTime(config);
            periods.add(current);
            StatisticPeriod last = IndicatorTimeRange.LAST_PERIOD.getPeriodTime(config);
            periods.add(last);
            for (int i = 1; i < periodCount; i++) {
                StatisticPeriod last2 = IndicatorPeriodConverter.calculateStatPeriod(last.getStartTime(), config);
                periods.add(last2);
                last = last2;
            }
        } else {
            // 如果有上次统计时间，则从上次统计时间开始直到现在，获取统计周期
            getStatisticPeriodIteration(config, periods, startTime, LocalDateTime.now());
        }

        StatisticPeriod lastYear = IndicatorTimeRange.LAST_YEAR.getPeriodTime(config);
        if (isLastYearNotExist(config, tableName)) {
            //如果上年统计时间不存在，则需要补充上年的统计周期
            periods.add(lastYear);
        }
        return periods;
    }

    private boolean isLastYearNotExist(IndicatorPeriodConfig<?> config, String tableName) {
        try {
            StatisticPeriod lastYear = IndicatorTimeRange.LAST_YEAR.getPeriodTime(config);
            List<Condition> conditions = new ArrayList<>();
            conditions.add(
                createTimeCondition(lastYear.getStartTime(), IndicatorConstants.START_TIME, EvaluateOperator.GTE));
            conditions.add(Condition.AND);
            conditions.add(
                createTimeCondition(lastYear.getEndTime(), IndicatorConstants.END_TIME, EvaluateOperator.LTE));
            BasicConditionSearchParams params = new BasicConditionSearchParams();
            params.setConditions(conditions);
            PageResponse<Map<String, Object>> response = searchFeign.conditionQuery(
                IndicatorConstants.INDICATOR_CONNECTION_ID, tableName, params);
            return response.getItems().isEmpty();
        } catch (Exception e) {
            log.error("查询指标库{}的上年统计时间失败", tableName, e);
            return true; // 如果查询失败，认为上年统计时间不存在
        }
    }

    private Condition createTimeCondition(LocalDateTime time, String timeField, EvaluateOperator operator) {
        Condition condition = new Condition();
        DataServiceField field = new DataServiceField();
        field.setEnName(timeField);
        field.setType(FieldType.DATETIME);
        condition.setKey(field);
        condition.setOperator(operator);
        condition.setType(DataServiceConditionType.EXPRESSION);
        condition.setValues(List.of(new ValueObject(DateTimeUtils.formatStr(time))));
        return condition;
    }

    private LocalDateTime getLastStatisticTime(String tableName) {
        try {
            //获取指标库里的最新一条数据；在此之前的统计周期不必再统计入库
            BasicConditionSearchParams params = new BasicConditionSearchParams();
            params.setPageParams(new PageParams(1, 1));
            params.setSortFields(List.of(
                new SortField(IndicatorConstants.START_TIME, Order.DESC)
            )); // 按照统计时间降序排列
            PageResponse<Map<String, Object>> response = searchFeign.conditionQuery(
                IndicatorConstants.INDICATOR_CONNECTION_ID, tableName, params);

            if (response.getItems().size() == 1) {
                Map<String, Object> item = response.getItems().get(0);
                if (item.containsKey(IndicatorConstants.START_TIME)) {
                    // 如果有开始时间字段，解析为 LocalDateTime
                    Object o = item.get(IndicatorConstants.START_TIME);
                    if (o instanceof LocalDateTime) {
                        return (LocalDateTime) o;
                    } else if (o instanceof String) {
                        // 如果是字符串，尝试解析为 LocalDateTime
                        return DateTimeUtils.parse((String) o);
                    }
                }
            }
            // 没有找到数据，返回null
            return null;
        } catch (Exception e) {
            // 如果查询失败，返回null
            log.error("获取上次统计时间失败", e);
            return null;
        }
    }

    /**
     * 获取统计周期的迭代方法
     *
     * @param config         指标周期配置
     * @param periods        统计周期列表
     * @param startTime      最后一个已统计周期的开始时间
     * @param statisticsTime 统计结束时间
     */
    public static void getStatisticPeriodIteration(IndicatorPeriodConfig<?> config, List<StatisticPeriod> periods,
        LocalDateTime startTime, LocalDateTime statisticsTime) {
        StatisticPeriod current = IndicatorPeriodConverter.calculateStatPeriod(statisticsTime, config);
        if (!current.getStartTime().isBefore(startTime)) {
            periods.add(current);
            getStatisticPeriodIteration(config, periods, startTime, current.getStartTime());
        }
    }

    private BatchOperatorRuntimeEntity createSourceOperator(DataStorage sourceDataStorage) {
        TableInfo tableInfo = TableInfo.fromDataStorage(sourceDataStorage);
        BatchOperatorRuntimeEntity source = new BatchOperatorRuntimeEntity();
        source.setSourceTableInfo(tableInfo);
        source.setEnabled(true);
        source.setType(Type.SOURCE);
        return source;
    }

    private String getIndicatorSql(ConnectionType connectionType, IndicatorConfig config, StatisticPeriod period) {
        return IndicatorSqlConvertor.createExecuteSql(config.getStatisticSql(),
            Arrays.asList(config.getConditions()), connectionType, period);
    }

    private String createStepName(String tableName, StatisticPeriod period) {
        return String.format("统计%s%s-%s数据", tableName,
            DateTimeUtils.formatDate(period.getStartTime()), DateTimeUtils.formatDate(period.getEndTime()));
    }

    private BatchOperatorRuntimeEntity createInputOperator(DataStorage sourceDataStorage, String enName, String zhName,
        ConnectionType connectionType, IndicatorConfig config, StatisticPeriod period) {
        BatchOperatorRuntimeEntity lastYearSource = createSourceOperator(sourceDataStorage);
        lastYearSource.setName(createStepName(zhName, period));
        lastYearSource.setOutputTableName(enName + "_" + DateTimeUtils.formatDate(period.getStartTime()) + "_" +
            DateTimeUtils.formatDate(period.getEndTime()));
        lastYearSource.setConditionSql(getIndicatorSql(connectionType, config, period).replace(";", ""));
        return lastYearSource;
    }
}
