package com.trs.moye.batch.engine.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.trs.moye.base.common.enums.ModelLayer;
import com.trs.moye.base.common.typehandler.CustomJacksonTypeHandler;
import com.trs.moye.batch.engine.entity.vo.BatchTaskVO;
import com.trs.moye.batch.engine.enums.TriggerModeEnum;
import java.time.LocalDateTime;
import java.util.Objects;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 批处理记录
 *
 * <AUTHOR>
 * @since 2024/06/19 10:10
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BatchTaskRecord {

    /**
     * 执行id，主键，定时任务触发时产生一条记录
     */
    private String executeId;

    /**
     * spark任务id
     */
    private String applicationId;

    /**
     * 触发模式：fixed_time（定时），immediate（立即执行）
     */
    private TriggerModeEnum triggerMode;

    /**
     * 任务id（对应要素库id）
     */
    private String taskId;

    /**
     * 任务名称（yarn上的任务名称）
     */
    private String taskName;

    /**
     * 开始时间
     */
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    private LocalDateTime endTime;

    /**
     * 是否异常：
     */
    private Integer isError;
    public static final Integer IS_ERROR = 1;
    public static final Integer NON_ERROR = 0;

    /**
     * 分层
     */
    private ModelLayer layer;

    /**
     * 存储到表的数据量
     */
    private Long storageSuccessCount;

    /**
     * 异常信息读取标记
     */
    private Integer errorMsgReadFlag = 0;


    /**
     * 写入数量信息
     */
    @TableField(typeHandler = CustomJacksonTypeHandler.class)
    private TaskWriteCount[] writeCountInfo = new TaskWriteCount[0];

    /**
     * 任务写入数量信息
     */
    @Data
    public static class TaskWriteCount {

        // 成功量
        private Long successCount;

        // 失败量
        private Long failCount;

        // 存储id
        private Integer storageId;
    }

    /**
     * 从 BatchTaskVO 创建 监控记录
     *
     * @param vo          BatchTaskVO
     * @param triggerMode TriggerModeEnum
     * @param startTime   开始时间
     * @return BatchTaskRecord
     */
    public static BatchTaskRecord beginTask(BatchTaskVO vo, TriggerModeEnum triggerMode,
        LocalDateTime startTime) {
        String taskName = String.format("%s_%s%s", vo.getTaskName(),
            Objects.isNull(vo.getSubTaskName()) ? "" : vo.getSubTaskName() + "_", vo.getExecuteId());
        return BatchTaskRecord.builder()
            .executeId(vo.getExecuteId())
            .taskId(vo.getTaskId())
            .taskName(taskName)
            .startTime(startTime)
            .triggerMode(triggerMode)
            .isError(NON_ERROR)
            .layer(vo.getLayer())
            .writeCountInfo(new TaskWriteCount[0])
            .build();
    }

    /**
     * 结束日志
     *
     * @param executeId      执行id
     * @param e              异常
     * @param endTime        结束时间
     * @param storageCount   存储到表的数据量
     * @param taskWriteCount 任务写入数量信息
     * @return BatchTaskRecord 批处理任务记录
     */
    public static BatchTaskRecord endTask(String executeId, Throwable e, LocalDateTime endTime, Long storageCount,
        TaskWriteCount[] taskWriteCount) {
        return BatchTaskRecord.builder()
            .executeId(executeId)
            .endTime(endTime)
            .isError(Objects.isNull(e) ? NON_ERROR : IS_ERROR)
            .storageSuccessCount(storageCount)
            .writeCountInfo(taskWriteCount)
            .build();
    }

    /**
     * 结束日志
     *
     * @param executeId      执行id
     * @param endTime        结束时间
     * @return BatchTaskRecord 批处理任务记录
     */
    public static BatchTaskRecord killTask(String executeId, LocalDateTime endTime) {
        return BatchTaskRecord.builder()
            .executeId(executeId)
            .endTime(endTime)
            .isError(IS_ERROR)
            .storageSuccessCount(0L)
            .build();
    }

}
