package com.trs.moye.batch.engine.dao.clickhouse;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.trs.moye.batch.engine.entity.BatchTaskTracer;
import java.util.Collection;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * BatchTaskTracer数据库操作类
 */
@DS("clickhouse")
@Mapper
public interface BatchTaskTracerMapper {

    /**
     * 单条插入
     *
     * @param data 单条插入
     * @return int
     */
    int insert(@Param("data") BatchTaskTracer data);

    /**
     * 批量插入；数据集合为空会报错，使用时请判断是否空
     *
     * @param dataCollection 数据集合
     * @return int
     */
    int insertBatch(@Param("dataCollection") Collection<BatchTaskTracer> dataCollection);

    /**
     * 动态修改
     *
     * @param data 数据
     * @return int
     */
    int updateSelective(@Param("data") BatchTaskTracer data);

    /**
     * 条件删除：通过id删除
     *
     * @param id 主键
     * @return int
     */
    int deleteById(@Param("id") Long id);

    /**
     * 条件删除：通过executeId删除
     *
     * @param executeId 执行id
     * @return int
     */
    int deleteByExecuteId(@Param("executeId") String executeId);

    /**
     * 集合删除：通过idCollection删除
     *
     * @param idCollection 主键集合
     * @return int
     */
    int deleteByIdCollection(@Param("idCollection") Collection<Long> idCollection);

    /**
     * 集合删除：通过executeIdCollection删除
     *
     * @param executeIdCollection 执行id集合
     * @return int
     */
    int deleteByExecuteIdCollection(@Param("executeIdCollection") Collection<String> executeIdCollection);

    /**
     * 单条查询：通过id查询
     *
     * @param id 主键
     * @return 数据
     */
    BatchTaskTracer getById(@Param("id") Long id);

    /**
     * 多条查询：通过executeId查询
     *
     * @param executeId 执行id
     * @return 数据集合
     */
    List<BatchTaskTracer> listByExecuteId(@Param("executeId") String executeId);

    /**
     * 集合查询：通过idCollection查询
     *
     * @param idCollection 主键集合
     * @return 数据集合
     */
    List<BatchTaskTracer> listByIdCollection(@Param("idCollection") Collection<Long> idCollection);

    /**
     * 集合查询：通过executeIdCollection查询
     *
     * @param executeIdCollection 执行id集合
     * @return 数据集合
     */
    List<BatchTaskTracer> listByExecuteIdCollection(
        @Param("executeIdCollection") Collection<String> executeIdCollection);

}