package com.trs.moye.batch.engine.utils;

import com.trs.moye.batch.engine.exception.ReflectException;
import java.beans.BeanInfo;
import java.beans.IntrospectionException;
import java.beans.Introspector;
import java.beans.PropertyDescriptor;
import java.util.HashMap;
import java.util.Map;
import java.util.function.Consumer;

/**
 * 反射攻击力
 *
 * <AUTHOR>
 * @since 2024-09-23 22:02
 */
public class ReflectUtils {

    private ReflectUtils() {
    }

    /**
     * 获取Bean属性描述符
     *
     * @param beanClass        ben类型
     * @param ignoreProperties 忽略的属性
     * @return 属性描述符Map，key：属性名称
     */
    public static Map<String, PropertyDescriptor> getDescriptorMap(Class<?> beanClass, String... ignoreProperties) {
        Map<String, PropertyDescriptor> descriptorMap = new HashMap<>();
        consumeBeanDescriptor(beanClass, descriptor -> {
            if (descriptor.getWriteMethod() != null && descriptor.getReadMethod() != null) {
                descriptorMap.put(descriptor.getName(), descriptor);
            }
        });
        for (String ignoreProperty : ignoreProperties) {
            descriptorMap.remove(ignoreProperty);
        }
        return descriptorMap;
    }

    /**
     * 消费Bean属性描述符
     *
     * @param beanClass Bean类型
     * @param consumer  消费函数
     */
    public static void consumeBeanDescriptor(Class<?> beanClass, Consumer<PropertyDescriptor> consumer) {
        BeanInfo beanInfo;
        try {
            beanInfo = Introspector.getBeanInfo(beanClass);
        } catch (IntrospectionException e) {
            throw new ReflectException(
                String.format("获取【%s】setter失败，原因是获取【%s】BeanInfo发生异常，异常信息：%s", beanClass, beanClass,
                    e.getMessage()));
        }
        PropertyDescriptor[] propertyDescriptors = beanInfo.getPropertyDescriptors();
        if (propertyDescriptors != null) {
            for (PropertyDescriptor descriptor : propertyDescriptors) {
                consumer.accept(descriptor);
            }
        }
    }

    /**
     * 数据转Map
     *
     * @param data             数据
     * @param ignoreProperties 忽略的属性
     * @return Map
     */
    public static Map<String, Object> toMap(Object data, String... ignoreProperties) {
        Class<?> dataClass = data.getClass();
        Map<String, PropertyDescriptor> descriptorMap = getDescriptorMap(data.getClass(), ignoreProperties);
        Map<String, Object> map = new HashMap<>();
        for (Map.Entry<String, PropertyDescriptor> entry : descriptorMap.entrySet()) {
            try {
                Object value = entry.getValue().getReadMethod().invoke(data);
                if (value == null) {
                    continue;
                }
                map.put(entry.getKey(), value);
            } catch (Exception e) {
                throw new ReflectException("获取【%s】类型【%s】属性发生异常", dataClass.getName(), entry.getKey());
            }
        }
        return map;
    }
}
