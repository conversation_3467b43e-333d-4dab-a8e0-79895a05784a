package com.trs.moye.batch.engine.feign;

import com.trs.moye.base.common.request.BasicConditionSearchParams;
import com.trs.moye.base.common.response.PageResponse;
import java.util.Map;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * 存储引擎搜索feign
 */
@FeignClient(name = "${data.service.application.name:moye-storage-engine}", path = "/storage/search", contextId = "search", configuration = OpenFeignConfig.class)
public interface SearchFeign {

    /**
     * 查询数据量
     *
     * @param connectionId 数据库连接
     * @param tableName    表名
     * @return 计数
     */
    @PostMapping(value = "/count")
    Long count(
        @RequestParam("connectionId") @NotNull(message = "连接id不能为空") Integer connectionId,
        @RequestParam("tableName") @NotBlank(message = "表名不能为空") String tableName);

    /**
     * 条件查询
     *
     * @param connectionId 数据库连接
     * @param tableName    表名
     * @param request      查询参数
     * @return 搜索结果
     */
    @PostMapping(value = "/condition")
    PageResponse<Map<String, Object>> conditionQuery(
        @RequestParam("connectionId") @NotNull(message = "连接id不能为空") Integer connectionId,
        @RequestParam("tableName") @NotBlank(message = "表名不能为空") String tableName,
        @RequestBody BasicConditionSearchParams request);

}
