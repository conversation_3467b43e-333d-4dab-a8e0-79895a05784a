package com.trs.moye.batch.engine.spark.python;

import com.trs.moye.batch.engine.spark.SparkApplication;
import com.trs.moye.batch.engine.spark.SparkApplicationContext;
import com.trs.moye.batch.engine.spark.config.SparkApplicationConfig;
import java.io.File;
import java.util.function.Consumer;
import org.apache.spark.launcher.SparkLauncher;


/**
 * python 类型 的 spark application 管理
 *
 * <AUTHOR>
 * @since 2024/06/13
 */
public class PythonSparkApplication extends SparkApplication {

    public PythonSparkApplication(SparkApplicationConfig config, SparkApplicationContext context,
        Consumer<String> outputHandler) {
        super(config, context, outputHandler);
    }

    @Override
    protected void configureSparkLauncher(SparkLauncher sparkLauncher) {
        // 如果是 standalone 模式, 设置 deploy mode 为 client. standalone 模式 python 脚本 不支持 cluster 模式
        if (isStandaloneMode()) {
            sparkLauncher.setDeployMode("client");
            configureClientDriverInfo(sparkLauncher);
        }

        // 添加额外python包
        File fileDirectory = new File(config.getPyFileDirectory());
        File[] files = fileDirectory.listFiles();
        if (files != null) {
            for (File extraFile : files) {
                sparkLauncher.addPyFile(extraFile.getAbsolutePath());
            }
        }

        // python主文件
        sparkLauncher.setAppResource(context.getAppResource());
    }
}
