package com.trs.moye.batch.engine.hive;

import com.trs.moye.base.common.exception.BizException;
import com.trs.moye.batch.engine.task.TaskExecutor;
import java.util.List;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * sql 任务执行器
 */
@Service
@Slf4j
public class SqlTaskExecutor implements TaskExecutor<SqlTaskContext> {

    @Resource
    HiveCommandLineClient hiveCommandLineClient;


    /**
     * 执行 SQL 任务
     *
     * @param task    任务上下文
     * @param sendLog 是否推送日志
     */
    public void executeSql(SqlTaskContext task, boolean sendLog) {
        List<String> sqlList = HiveSqlNormalizer.normalizeSql(task.getCode());
        try {
            String sql = String.join("\n", sqlList);
            if (sendLog) {
                //执行sql
                hiveCommandLineClient.executeSqlAndSendLog(sql, task);
            } else {
                hiveCommandLineClient.executeSql(sql, task);
            }
            //写结尾日志
            hiveCommandLineClient.writeFinishFlag(task);
            //存日志 发消息
            hiveCommandLineClient.finishSql(task, sendLog);
        } catch (Exception e) {
            String errorMsg = String.format("任务[taskId=%s]执行失败，不再执行后续任务！原因：%s。sql: %s",
                task.getTaskId(),
                e.getMessage(), sqlList);
            throw new BizException(errorMsg, e);
        }
    }
}
