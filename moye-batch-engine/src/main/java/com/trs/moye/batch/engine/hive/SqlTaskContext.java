package com.trs.moye.batch.engine.hive;

import com.trs.moye.batch.engine.task.Task;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * sql 任务
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class SqlTaskContext extends Task {

    /**
     * 代码
     */
    private String code;

    /**
     * 获取日志路径 {taskId}/{executionId}.log
     *
     * @return 日志路径
     */
    public String getLogPath() {
        return taskId + "/" + executionId + ".log";
    }


}
