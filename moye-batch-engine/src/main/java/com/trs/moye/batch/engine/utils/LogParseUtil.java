package com.trs.moye.batch.engine.utils;

import com.trs.moye.batch.engine.entity.vo.ExecuteResultMap;
import com.trs.moye.batch.engine.entity.vo.ExecuteResultRequest;
import java.io.BufferedReader;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import lombok.extern.slf4j.Slf4j;

/**
 * 执行sql的日志解析器
 *
 * <AUTHOR>
 * @since 2024/11/26
 */
@Slf4j
public class LogParseUtil {

    // 私有构造函数，防止实例化
    private LogParseUtil() {
        throw new UnsupportedOperationException("This is a utility class and cannot be instantiated");
    }

    /**
     * 解析HIVE日志
     *
     * @param inputStream          日志文件流
     * @param executeResultRequest 参数
     * @return 解析数据
     */
    public static List<ExecuteResultMap> parseHiveLog(InputStream inputStream,
        ExecuteResultRequest executeResultRequest) {
        List<ExecuteResultMap> resultMaps = new ArrayList<>();
        String line;
        boolean isResultSection = false;
        List<Map<String, Object>> currentResult = new ArrayList<>();
        long totalRows = 0L;
        int currentRowCount = 0;
        int sqlCount = 0;
        List<String> headers = new ArrayList<>();
        try (BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream))) {
            while ((line = reader.readLine()) != null) {
                line = line.trim();
                // 处理 SQL 语句
                if (line.contains("SELECT") && line.contains("Executing")) {
                    log.info("解析SELECT开始");
                    handleNewQuery(resultMaps, executeResultRequest, currentResult, totalRows, sqlCount);
                    isResultSection = true; // 开始新的 SQL 结果部分
                    sqlCount++;
                    headers.clear();
                    currentRowCount = 0;
                } else if (isResultSection) {
                    currentRowCount = handleResultLine(line, currentResult, headers, executeResultRequest,
                        currentRowCount);
                    if (line.contains("rows selected") || line.contains("row selected")) {
                        log.info("解析总数开始");
                        totalRows = extractTotalRows(line);
                    } else if (line.startsWith("Finish executing HIVE SQL")) {
                        saveResultIfNeeded(resultMaps, executeResultRequest, currentResult, totalRows, sqlCount);
                        isResultSection = false; // 结束当前 SQL 的结果解析
                        totalRows = 0; // 重置总数
                    }
                }
            }
        } catch (Exception e) {
            log.error(e.getMessage());
            throw new RuntimeException(e);
        }
        return resultMaps; // 返回结果
    }

    private static void handleNewQuery(List<ExecuteResultMap> resultMaps, ExecuteResultRequest executeResultRequest,
        List<Map<String, Object>> currentResult, long totalRows, int sqlCount) {
        if (!currentResult.isEmpty()) {
            saveResultIfNeeded(resultMaps, executeResultRequest, currentResult, totalRows, sqlCount);
        }
        currentResult.clear(); // 清空当前结果
    }

    private static int handleResultLine(String line, List<Map<String, Object>> currentResult,
        List<String> headers, ExecuteResultRequest executeResultRequest, int currentRowCount) {
        if (isStartWithLine(line)) {
            if (headers.isEmpty()) {
                log.info("解析表头开始");
                headers.addAll(extractHeaders(line)); // 提取表头
            } else {
                currentRowCount++;
                if (shouldAddRow(executeResultRequest, currentRowCount)) {
                    log.info("添加当前行!");
                    currentResult.add(parseRow(line, headers));
                }
            }
        }
        return currentRowCount;
    }

    private static boolean isStartWithLine(String line) {
        return line.startsWith("|");
    }

    /**
     * 开头是否是| 或者+
     *
     * @param line 行
     * @return 是否是以| 或者 + 开头
     */
    public static boolean isSqlResultLine(String line) {
        return isStartWithLine(line) || line.startsWith("+");
    }

    private static long extractTotalRows(String line) {
        if (line.contains("No rows selected")) {
            return 0L;
        }
        return Long.parseLong(line.split(" ")[0]);
    }

    private static void saveResultIfNeeded(List<ExecuteResultMap> resultMaps, ExecuteResultRequest executeResultRequest,
        List<Map<String, Object>> currentResult, long totalRows, int sqlCount) {
        if (executeResultRequest.getPart() == null || executeResultRequest.getPart() == sqlCount) {
            saveResult(resultMaps, currentResult, totalRows, sqlCount);
        }
    }

    private static List<String> extractHeaders(String line) {
        // 提取表头，去掉开头的 "| " 和结尾的 " |"
        String[] headersArray = line.substring(1, line.length() - 1).trim().split("\\|");
        List<String> headers = new ArrayList<>();
        for (String header : headersArray) {
            String[] split = header.trim().split("\\.");
            headers.add(split.length > 1 ? split[1] : split[0]); // 去掉空格并添加到列表
        }
        log.info("表头: {}", headers);
        return headers;
    }

    private static Map<String, Object> parseRow(String line, List<String> headers) {
        // 解析行数据
        // 使用正则表达式匹配竖线后面的值
        Pattern pattern = Pattern.compile("\\|\\s+([^|]+)");
        Matcher matcher = pattern.matcher(line);

        List<String> columnsList = new ArrayList<>();

        while (matcher.find()) {
            // 添加匹配到的值到列表中
            columnsList.add(matcher.group(1).trim()); // 去掉首尾空格
        }

        // 转换为数组
        String[] columns = columnsList.toArray(new String[0]);
        Map<String, Object> row = new HashMap<>();

        // 从第二个元素开始，解析每一列
        for (int i = 1; i <= columns.length - 1; i++) {
            String columnData = columns[i].trim();
            if (!columnData.isEmpty()) {
                row.put(headers.get(i - 1), columnData); // 使用表头作为键
            }
        }
        log.info("当前行:{}", row);
        return row;
    }

    private static void saveResult(List<ExecuteResultMap> resultMaps, List<Map<String, Object>> currentResult,
        long totalRows, int sqlCount) {
        ExecuteResultMap resultMap = new ExecuteResultMap();
        resultMap.setResult(new ArrayList<>(currentResult)); // 使用新列表
        resultMap.setTotal(totalRows);
        resultMap.setPart(sqlCount);
        log.info("保存结果:{}", currentResult);
        resultMaps.add(resultMap);
    }

    private static boolean shouldAddRow(ExecuteResultRequest executeResultRequest, int currentRowCount) {
        int startRow = (executeResultRequest.getPageNo() - 1) * executeResultRequest.getPageSize();
        int endRow = startRow + executeResultRequest.getPageSize();
        return currentRowCount > startRow && currentRowCount <= endRow;
    }
}

