package com.trs.moye.batch.engine.hive;

import java.util.Objects;
import lombok.Data;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * hive配置类
 * <p>
 * author lai.yi
 */
@Component
@ConfigurationProperties(prefix = "com.trs.hive")
@Data
public class HiveProperties {

    private String host;

    private Integer port;

    private String defaultDatabase = "default";

    private Kerberos kerberos;

    @Value("${com.trs.spark.home}")
    private String sparkHome = "/opt/spark-3.4.1-bin-hadoop3";

    /**
     * 根据hive配置获取jdbc url
     *
     * @return jdbc url
     */
    public String jdbcUrl() {
        if (kerberos.getEnable()) {
            return this.getKerberosUrl();
        }
        return this.getNormalUrl();
    }

    /**
     * 获取普通hive连接的url
     *
     * @return 普通连接url
     */
    @NotNull
    private String getNormalUrl() {
        return "jdbc:hive2://" + getHost()
            + ":"
            + getPort()
            + "/"
            + getDefaultDatabase();
    }

    /**
     * 获取kerberos连接url
     *
     * @return kerberos连接url
     */
    private String getKerberosUrl() {
        return "jdbc:hive2://" + getHost()
            + ":"
            + getPort()
            + "/"
            + getDefaultDatabase()
            + kerberos.toUrlPrefix();
    }

    /**
     * 获取执行通过beeline客户端执行hive sql的命令行命令
     *
     * @param tempFile 临时文件
     * @return 命令行命令
     */
    public String getCommand(String tempFile) {
        if (Objects.nonNull(kerberos) && kerberos.getEnable()) {
            return "source /home/<USER>/bigdata_env; kinit -k -t "
                + kerberos.getUserKeytab() + " " + kerberos.getUserPrincipal()
                + "; beeline -f " + tempFile;
        } else {
            return sparkHome + "/bin/beeline -n root -u " + getNormalUrl() + " -f " + tempFile;
        }
    }

    /**
     * kerberos配置类
     * <p>
     * author lai.yi
     */
    @Component
    @ConfigurationProperties(prefix = "com.trs.hive.kerberos")
    @Data
    public static class Kerberos {

        private Boolean enable = false;

        private String principal = "hive/<EMAIL>";

        private String userPrincipal = "tuoersi";

        private String userKeytab = "/TRS/data/moye/proof/usertrs.keytab";

        private String zooKeeperNamespace = "hiveserver2";

        private String zookeeperPrincipal = "zookeeper/hadoop";

        private String krb5ConfPath = "/etc/krb5.conf";

        private String userName = "<EMAIL>";

        private String serviceDiscoveryMode = "zooKeeper";

        private String saslQop = "auth-conf";

        /**
         * 生成hive jdbc url所需要的配置map
         *
         * @return url配置map
         */
        public String toUrlPrefix() {
            return ";" + "principal=" + principal
                + ";" + "sasl.qop=" + saslQop
                + ";" + "serviceDiscoveryMode=" + serviceDiscoveryMode
                + ";" + "auth=KERBEROS"
                + ";" + "zooKeeperNamespace=" + zooKeeperNamespace
                + ";" + "user.principal=" + userPrincipal
                + ";" + "user.keytab=" + userKeytab;
        }
    }
}
