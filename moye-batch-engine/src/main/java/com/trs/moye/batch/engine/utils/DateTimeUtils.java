package com.trs.moye.batch.engine.utils;

import static java.time.temporal.ChronoField.DAY_OF_MONTH;
import static java.time.temporal.ChronoField.MONTH_OF_YEAR;
import static java.time.temporal.ChronoField.YEAR;

import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeFormatterBuilder;
import java.time.format.SignStyle;
import java.time.temporal.TemporalAccessor;
import java.time.temporal.TemporalQueries;
import java.util.Calendar;
import java.util.Date;
import java.util.EnumMap;
import java.util.LinkedHashMap;
import java.util.LinkedHashSet;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Objects;
import java.util.Set;
import java.util.regex.Pattern;
import lombok.Getter;

/**
 * 日期时间工具类
 *
 * <AUTHOR>
 */
public class DateTimeUtils {

    private static final Map<Formatter, DateTimeFormatter> FORMATTER_MAP = new EnumMap<>(Formatter.class);

    static {
        FORMATTER_MAP.put(Formatter.YYYY_MM_DD_HH_MM_SS,
            DateTimeFormatter.ofPattern(Formatter.YYYY_MM_DD_HH_MM_SS.value));
        FORMATTER_MAP.put(Formatter.YYYY_MM_DD_HH_MM_SS_SSSSSS,
            DateTimeFormatter.ofPattern(Formatter.YYYY_MM_DD_HH_MM_SS_SSSSSS.value));
        FORMATTER_MAP.put(Formatter.YYYY_MM_DD_HH_MM_SS_SPOT,
            DateTimeFormatter.ofPattern(Formatter.YYYY_MM_DD_HH_MM_SS_SPOT.value));
        FORMATTER_MAP.put(Formatter.YYYY_MM_DD_HH_MM_SS_SLASH,
            DateTimeFormatter.ofPattern(Formatter.YYYY_MM_DD_HH_MM_SS_SLASH.value));
        FORMATTER_MAP.put(Formatter.YYYY_MM_DD_HH_MM_SS_NO_SPLIT,
            DateTimeFormatter.ofPattern(Formatter.YYYY_MM_DD_HH_MM_SS_NO_SPLIT.value));
        FORMATTER_MAP.put(Formatter.YYYY_MM_DD_HH_MM_SS_ISO8601,
            DateTimeFormatter.ofPattern(Formatter.YYYY_MM_DD_HH_MM_SS_ISO8601.value));
        FORMATTER_MAP.put(Formatter.YYYY_MM_DD_HH_MM_SS_SSS_ISO8601,
            DateTimeFormatter.ofPattern(Formatter.YYYY_MM_DD_HH_MM_SS_SSS_ISO8601.value));
        FORMATTER_MAP.put(Formatter.YYYY_MM_DD_HH_MM_SS_SSSSSS_ISO8601,
            DateTimeFormatter.ofPattern(Formatter.YYYY_MM_DD_HH_MM_SS_SSSSSS_ISO8601.value));
        FORMATTER_MAP.put(Formatter.YYYY_MM_DD_HH_MM_SS_SSSSSSSSS_ISO8601,
            DateTimeFormatter.ofPattern(Formatter.YYYY_MM_DD_HH_MM_SS_SSSSSSSSS_ISO8601.value));
    }

    // 如果日期时间字符串长度为19，则从此映射中找到DateTimeFormatter
    private static final Map<Pattern, DateTimeFormatter> YYYY_MM_DD_HH_MM_SS_19_FORMATTER_MAP = new LinkedHashMap<>();
    private static final Set<Entry<Pattern, DateTimeFormatter>> YYYY_MM_DD_HH_MM_SS_19_FORMATTER_MAP_ENTRY_SET = new LinkedHashSet<>();

    // 如果日期时间字符串长度大于19，则从此映射中查找DateTimeFormatter
    private static final Map<Pattern, DateTimeFormatter> YYYY_MM_DD_HH_MM_SS_M19_FORMATTER_MAP = new LinkedHashMap<>();
    private static final Set<Entry<Pattern, DateTimeFormatter>> YYYY_MM_DD_HH_MM_SS_M19_FORMATTER_MAP_ENTRY_SET = new LinkedHashSet<>();

    // 如果日期时间字符串长度为 14，使用此 formatter
    private static final DateTimeFormatter YYYY_MM_DD_HH_MM_SS_14_FORMATTER = DateTimeFormatter.ofPattern(
        Formatter.YYYY_MM_DD_HH_MM_SS_NO_SPLIT.value);

    private static final DateTimeFormatter YYYY_MM_DD_FORMATTER = DateTimeFormatter.ofPattern(
        Formatter.YYYY_MM_DD.value);

    static {
        YYYY_MM_DD_HH_MM_SS_19_FORMATTER_MAP.put(Pattern.compile("\\d{4}-\\d{2}-\\d{2}\\s\\d{2}:\\d{2}:\\d{2}"),
            DateTimeFormatter.ofPattern(Formatter.YYYY_MM_DD_HH_MM_SS.value));

        YYYY_MM_DD_HH_MM_SS_M19_FORMATTER_MAP.put(Pattern.compile("\\d{4}-\\d{2}-\\d{2}\\s\\d{2}:\\d{2}.*"),
            new DateTimeFormatterBuilder().parseCaseInsensitive().append(DateTimeFormatter.ISO_LOCAL_DATE)
                .appendLiteral(' ').append(DateTimeFormatter.ISO_LOCAL_TIME).toFormatter());

        YYYY_MM_DD_HH_MM_SS_19_FORMATTER_MAP.put(Pattern.compile("\\d{4}-\\d{2}-\\d{2}T\\d{2}:\\d{2}:\\d{2}"),
            DateTimeFormatter.ofPattern(Formatter.YYYY_MM_DD_HH_MM_SS_ISO8601.value));

        YYYY_MM_DD_HH_MM_SS_M19_FORMATTER_MAP.put(Pattern.compile("\\d{4}-\\d{2}-\\d{2}T\\d{2}:\\d{2}.*"),
            DateTimeFormatter.ISO_LOCAL_DATE_TIME);

        YYYY_MM_DD_HH_MM_SS_19_FORMATTER_MAP.put(Pattern.compile("\\d{4}/\\d{2}/\\d{2}\\s\\d{2}:\\d{2}:\\d{2}"),
            DateTimeFormatter.ofPattern(Formatter.YYYY_MM_DD_HH_MM_SS_SLASH.value));

        YYYY_MM_DD_HH_MM_SS_M19_FORMATTER_MAP.put(Pattern.compile("\\d{4}/\\d{2}/\\d{2}\\s\\d{2}:\\d{2}.*"),
            new DateTimeFormatterBuilder().parseCaseInsensitive().append(
                    new DateTimeFormatterBuilder().appendValue(YEAR, 4, 10, SignStyle.EXCEEDS_PAD).appendLiteral('/')
                        .appendValue(MONTH_OF_YEAR, 2).appendLiteral('/').appendValue(DAY_OF_MONTH, 2).toFormatter())
                .appendLiteral(' ').append(DateTimeFormatter.ISO_LOCAL_TIME).toFormatter());

        YYYY_MM_DD_HH_MM_SS_19_FORMATTER_MAP.put(Pattern.compile("\\d{4}\\.\\d{2}\\.\\d{2}\\s\\d{2}:\\d{2}:\\d{2}"),
            DateTimeFormatter.ofPattern(Formatter.YYYY_MM_DD_HH_MM_SS_SPOT.value));

        YYYY_MM_DD_HH_MM_SS_M19_FORMATTER_MAP.put(Pattern.compile("\\d{4}\\.\\d{2}\\.\\d{2}\\s\\d{2}:\\d{2}.*"),
            new DateTimeFormatterBuilder().parseCaseInsensitive().append(
                    new DateTimeFormatterBuilder().appendValue(YEAR, 4, 10, SignStyle.EXCEEDS_PAD).appendLiteral('.')
                        .appendValue(MONTH_OF_YEAR, 2).appendLiteral('.').appendValue(DAY_OF_MONTH, 2).toFormatter())
                .appendLiteral(' ').append(DateTimeFormatter.ISO_LOCAL_TIME).toFormatter());

        YYYY_MM_DD_HH_MM_SS_M19_FORMATTER_MAP.put(
            Pattern.compile("\\d{4}年\\d{2}月\\d{2}日\\s\\d{2}时\\d{2}分\\d{2}秒"),
            DateTimeFormatter.ofPattern("yyyy年MM月dd日 HH时mm分ss秒"));

        YYYY_MM_DD_HH_MM_SS_19_FORMATTER_MAP_ENTRY_SET.addAll(YYYY_MM_DD_HH_MM_SS_19_FORMATTER_MAP.entrySet());
        YYYY_MM_DD_HH_MM_SS_M19_FORMATTER_MAP_ENTRY_SET.addAll(YYYY_MM_DD_HH_MM_SS_M19_FORMATTER_MAP.entrySet());
    }

    /**
     * 从时间字符串匹配对应格式的 {@link DateTimeFormatter}
     *
     * @param dateTime eg: 2020-02-03 12:12:10.101
     * @return 匹配的 DateTimeFormatter ，当不匹配任何模式时将返回 null
     */
    public static DateTimeFormatter matchDateTimeFormatter(String dateTime) throws NoMatchingFormatterException {
        if (dateTime.length() == 19) {
            for (Entry<Pattern, DateTimeFormatter> entry : YYYY_MM_DD_HH_MM_SS_19_FORMATTER_MAP_ENTRY_SET) {
                if (entry.getKey().matcher(dateTime).matches()) {
                    return entry.getValue();
                }
            }
        } else if (dateTime.length() > 19) {
            for (Entry<Pattern, DateTimeFormatter> entry : YYYY_MM_DD_HH_MM_SS_M19_FORMATTER_MAP_ENTRY_SET) {
                if (entry.getKey().matcher(dateTime).matches()) {
                    return entry.getValue();
                }
            }
        } else if (dateTime.length() == 14) {
            return YYYY_MM_DD_HH_MM_SS_14_FORMATTER;
        }
        throw new NoMatchingFormatterException("No matching formatter for [" + dateTime + "]");
    }

    /**
     * 时间字符串转换成LocalDateTime
     *
     * @param dateTime  时间字符串
     * @param formatter 格式
     * @return 转换后的 {@link LocalDateTime}
     */
    public static LocalDateTime parse(String dateTime, Formatter formatter) {
        return LocalDateTime.parse(dateTime, FORMATTER_MAP.get(formatter));
    }

    /**
     * 时间戳转换成LocalDateTime
     *
     * @param timestamp 时间戳
     * @return 转换后的 {@link LocalDateTime}
     */
    public static LocalDateTime parse(long timestamp) {
        return parse(timestamp, ZoneId.systemDefault());
    }

    /**
     * 时间戳转换成LocalDateTime
     *
     * @param timestamp 时间戳
     * @param zoneId    时区
     * @return 转换后的 {@link LocalDateTime}
     */
    public static LocalDateTime parse(long timestamp, ZoneId zoneId) {
        Instant instant = Instant.ofEpochMilli(timestamp);
        return LocalDateTime.ofInstant(instant, zoneId);
    }

    /**
     * 时间字符串转换成LocalDateTime
     *
     * @param dateTime          时间字符串
     * @param dateTimeFormatter 格式
     * @return 转换后的 {@link LocalDateTime}
     */
    public static LocalDateTime parse(String dateTime, DateTimeFormatter dateTimeFormatter) {
        TemporalAccessor parsedTimestamp = dateTimeFormatter.parse(dateTime);
        LocalTime localTime = parsedTimestamp.query(TemporalQueries.localTime());
        LocalDate localDate = parsedTimestamp.query(TemporalQueries.localDate());
        return LocalDateTime.of(localDate, localTime);
    }

    /**
     * 时间字符串转换成LocalDateTime
     *
     * @param dateTime 时间字符串
     * @return LocalDateTime
     */
    public static LocalDateTime parse(String dateTime) {
        DateTimeFormatter dateTimeFormatter = matchDateTimeFormatter(dateTime);
        return LocalDateTime.parse(dateTime, dateTimeFormatter);
    }


    /**
     * 时间字符串转换成LocalDate
     *
     * @param dateTime 时间字符串
     * @return LocalDateTime
     */
    public static LocalDate parseLocalDate(String dateTime) {
        return LocalDate.parse(dateTime, YYYY_MM_DD_FORMATTER);
    }


    /**
     * 时间转时间戳
     *
     * @param dateTime 时间
     * @return 时间戳
     */
    public static long toTimestamp(LocalDateTime dateTime) {
        return dateTime.atZone(ZoneId.systemDefault()).toInstant().toEpochMilli();
    }

    /**
     * 日期转时间戳
     *
     * @param date 日期
     * @return 时间戳
     */
    public static long toTimestamp(LocalDate date) {
        return date.atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli();
    }

    /**
     * 将时间转成YYYY_MM_DD_HH_MM_SS字符串
     *
     * @param dateTime 时间
     * @return {@link String}
     * <AUTHOR>
     * @since 2024/10/18 18:44
     */
    public static String formatStr(LocalDateTime dateTime) {
        return dateTime.format(Formatter.YYYY_MM_DD_HH_MM_SS.dateTimeFormatter);
    }

    /**
     * 格式化时间对象
     *
     * @param dateTime  时间
     * @param formatter 时间格式
     * @return 时间字符串
     */
    public static String format(LocalDateTime dateTime, Formatter formatter) {
        return dateTime.format(formatter.dateTimeFormatter);
    }


    /**
     * 格式化时间对象
     *
     * @param date      日期
     * @param formatter 时间格式
     * @return 时间字符串
     */
    public static String format(LocalDate date, Formatter formatter) {
        return date.format(formatter.dateTimeFormatter);
    }


    /**
     * 将时间转换为时间戳
     *
     * @param localDateTime 时间
     * @return {@link Long}
     * <AUTHOR>
     * @since 2024/10/25 17:10
     */
    public static Long getTimestampOfDateTime(LocalDateTime localDateTime) {
        if (Objects.isNull(localDateTime)) {
            return null;
        }
        ZoneId zone = ZoneId.systemDefault();
        Instant instant = localDateTime.atZone(zone).toInstant();
        return instant.toEpochMilli();
    }

    /**
     * 格式枚举
     */
    @Getter
    public enum Formatter {
        /**
         * formatter
         */
        YYYY_MM_DD("yyyy-MM-dd"), YYYY_MM_DD_HH_MM_SS("yyyy-MM-dd HH:mm:ss"), YYYY_MM_DD_HH_MM_SS_SSSSSS(
            "yyyy-MM-dd HH:mm:ss.SSSSSS"), YYYY_MM_DD_HH_MM_SS_SPOT("yyyy.MM.dd HH:mm:ss"), YYYY_MM_DD_HH_MM_SS_SLASH(
            "yyyy/MM/dd HH:mm:ss"), YYYY_MM_DD_HH_MM_SS_NO_SPLIT("yyyyMMddHHmmss"), YYYY_MM_DD_HH_MM_SS_ISO8601(
            "yyyy-MM-dd'T'HH:mm:ss"), YYYY_MM_DD_HH_MM_SS_SSS_ISO8601(
            "yyyy-MM-dd'T'HH:mm:ss.SSS"), YYYY_MM_DD_HH_MM_SS_SSSSSS_ISO8601(
            "yyyy-MM-dd'T'HH:mm:ss.SSSSSS"), YYYY_MM_DD_HH_MM_SS_SSSSSSSSS_ISO8601(
            "yyyy-MM-dd'T'HH:mm:ss.SSSSSSSSS"), YYYYMMDD("yyyyMMdd");

        private final String value;
        private final DateTimeFormatter dateTimeFormatter;

        Formatter(String value) {
            this.value = value;
            this.dateTimeFormatter = DateTimeFormatter.ofPattern(value);
        }

        /**
         * 根据格式字符串获取枚举
         *
         * @param format 格式字符串
         * @return 格式枚举
         */
        public static Formatter parse(String format) {
            Formatter[] formatters = Formatter.values();
            for (Formatter formatter : formatters) {
                if (formatter.getValue().equals(format)) {
                    return formatter;
                }
            }
            String errorMsg = String.format("Illegal format [%s]", format);
            throw new IllegalArgumentException(errorMsg);
        }
    }

    /**
     * 未找到匹配的格式化器异常
     *
     * <AUTHOR>
     */
    public static class NoMatchingFormatterException extends RuntimeException {

        public NoMatchingFormatterException(String message) {
            super(message);
        }
    }

    /**
     * Instant to LocalDateTime
     *
     * @param instant {@link Instant}
     * @return {@link LocalDateTime}
     */
    public static LocalDateTime toLocalDateTime(Instant instant) {
        return LocalDateTime.ofInstant(instant, ZoneId.systemDefault());
    }

    /**
     * LocalDateTime to Instant
     *
     * @param localDateTime {@link LocalDateTime}
     * @return {@link Instant}
     */
    public static Instant toInstant(LocalDateTime localDateTime) {
        return localDateTime.atZone(ZoneId.systemDefault()).toInstant();
    }


    /**
     * 获取多少天以前的时间
     *
     * @param days 天数
     * @return {@link Integer}
     * <AUTHOR>
     * @since 2024/12/5 14:58
     */
    public static Integer getDateNDaysAgo(int days) {
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.DAY_OF_YEAR, -days);
        Date date = calendar.getTime();
        SimpleDateFormat sdf = new SimpleDateFormat(Formatter.YYYYMMDD.value);
        return Integer.valueOf(sdf.format(date));
    }


}
