package com.trs.moye.batch.engine.dao.clickhouse;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.trs.moye.batch.engine.entity.BatchTaskRecord;
import java.time.LocalDateTime;
import java.util.Collection;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * BatchTaskRecord数据库操作类
 */
@DS("clickhouse")
@Mapper
public interface BatchTaskRecordMapper {

    /**
     * 单条插入
     *
     * @param data 数据
     * @return int
     */
    int insert(@Param("data") BatchTaskRecord data);

    /**
     * 批量插入；数据集合为空会报错，使用时请判断是否空
     *
     * @param dataCollection 数据集合
     * @return int
     */
    int insertBatch(@Param("dataCollection") Collection<BatchTaskRecord> dataCollection);

    /**
     * 动态修改
     *
     * @param data 数据
     * @return int
     */
    int updateSelective(@Param("data") BatchTaskRecord data);


    /**
     * 条件删除：通过executeId删除
     *
     * @param executeId 执行id
     * @return int
     */
    int deleteByExecuteId(@Param("executeId") String executeId);

    /**
     * 条件删除：通过applicationId删除
     *
     * @param applicationId spark任务id
     * @return int
     */
    int deleteByApplicationId(@Param("applicationId") String applicationId);

    /**
     * 条件删除：通过taskId删除
     *
     * @param taskId 任务id
     * @return int
     */
    int deleteByTaskId(@Param("taskId") Integer taskId);

    /**
     * 集合删除：通过executeIdCollection删除
     *
     * @param executeIdCollection 执行id集合
     * @return int
     */
    int deleteByExecuteIdCollection(@Param("executeIdCollection") Collection<String> executeIdCollection);

    /**
     * 集合删除：通过applicationIdCollection删除
     *
     * @param applicationIdCollection spark任务id集合
     * @return int
     */
    int deleteByApplicationIdCollection(@Param("applicationIdCollection") Collection<String> applicationIdCollection);

    /**
     * 集合删除：通过taskIdCollection删除
     *
     * @param taskIdCollection 任务id集合
     * @return int
     */
    int deleteByTaskIdCollection(@Param("taskIdCollection") Collection<Integer> taskIdCollection);

    /**
     * 单条查询：通过executeId查询
     *
     * @param executeId 执行id
     * @return 数据
     */
    BatchTaskRecord getByExecuteId(@Param("executeId") String executeId);

    /**
     * 单条查询：通过taskId查询上一次执行记录
     *
     * @param taskId 任务id
     * @return BatchTaskRecord
     */
    BatchTaskRecord getLastBatchTaskRecord(@Param("taskId") String taskId);

    /**
     * 单条查询：通过taskId查询上一次成功执行记录
     *
     * @param taskId 任务id
     * @return 成功执行记录
     */
    BatchTaskRecord getLastSuccessBatchTaskRecord(@Param("taskId") String taskId);

    /**
     * 单条查询：通过applicationId查询
     *
     * @param applicationId 应用id集合
     * @return 数据
     */
    BatchTaskRecord getByApplicationId(@Param("applicationId") String applicationId);

    /**
     * 多条查询：通过taskId查询
     *
     * @param taskId 任务id
     * @return 数据集合
     */
    List<BatchTaskRecord> listByTaskId(@Param("taskId") Integer taskId);

    /**
     * 集合查询：通过executeIdCollection查询
     *
     * @param executeIdCollection 执行id集合
     * @return 数据集合
     */
    List<BatchTaskRecord> listByExecuteIdCollection(
        @Param("executeIdCollection") Collection<String> executeIdCollection);

    /**
     * 集合查询：通过applicationIdCollection查询
     *
     * @param applicationIdCollection spark任务id集合
     * @return 数据集合
     */
    List<BatchTaskRecord> listByApplicationIdCollection(
        @Param("applicationIdCollection") Collection<String> applicationIdCollection);

    /**
     * 集合查询：通过taskIdCollection查询
     *
     * @param taskIdCollection 任务id集合
     * @return 数据集合
     */
    List<BatchTaskRecord> listByTaskIdCollection(@Param("taskIdCollection") Collection<Integer> taskIdCollection);


    /**
     * 更新applicationId
     *
     * @param executeId     执行id
     * @param applicationId 应用id
     */
    void updateApplicationId(@Param("executeId") String executeId, @Param("applicationId") String applicationId);

    /**
     * 标记任务丢失
     *
     * @param executeIdList 执行id
     * @param endTime       结束时间
     */
    void updateWhenLostTask(@Param("executeIdList") List<String> executeIdList,
        @Param("endTime") LocalDateTime endTime);
}