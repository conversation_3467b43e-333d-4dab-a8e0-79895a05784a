package com.trs.moye.batch.engine.websocket;

import com.trs.moye.batch.engine.entity.vo.BatchLogVO;
import com.trs.moye.batch.engine.enums.WsMsgTypeEnum;
import java.io.IOException;
import java.io.OutputStream;
import java.io.PrintStream;
import java.time.LocalDateTime;
import java.util.concurrent.Future;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 */
@Slf4j
public class RealTimeLogTask {

    private final PrintStream wsPrintStream;

    private final Integer userId;

    private final String taskId;

    private Future<?> future;

    public RealTimeLogTask(String taskId, Integer userId) {
        this.userId = userId;
        this.taskId = taskId;
        OutputStream outputStream = new OutputStream() {
            @Override
            public void write(int b) {}

            @Override
            public void write(byte b[], int off, int len) throws IOException {
                SessionManager sessionManager = SessionManager.getInstance();
                sessionManager.sendMessage(taskId, userId, new BatchLogVO(WsMsgTypeEnum.LOG, "subtaskName", LocalDateTime.now(), new String(b).trim()));
            }
        };
        this.wsPrintStream = new PrintStream(outputStream, true);
    }

    /**
     * 停止日志推送
     */
    public void stop() {
        if (future != null) {
            future.cancel(true);
            wsPrintStream.close();
        }
    }

    /**
     * 获取状态
     *
     * @return 状态
     */
    public String status() {
        if (future != null) {
            if (!future.isDone()) {
                return "running";
            } else {
                if (future.isCancelled()) {
                    return "cancelled";
                } else {
                    return "done";
                }
            }
        } else {
            return "not exist";
        }
    }

}
