package com.trs.moye.batch.engine.entity.vo;

import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 
 * 执行结果
 *
 * <AUTHOR>
 * @since  2024/11/26
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ExecuteResultRequest {

     /**
      * 数据建模id
      */
     private Integer dataModelId;

     /**
      * 执行id
      */
     private List<String> executeIds;

     /**
      * 第几个sql片段(结果1,结果2)
      */
     private Integer part;

     /**
      * 分页页码
      */
     private int pageNo;

     /**
      * 分页参数
      */
     private int pageSize;
}
