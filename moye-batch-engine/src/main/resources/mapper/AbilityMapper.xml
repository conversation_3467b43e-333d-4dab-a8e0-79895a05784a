<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.trs.moye.batch.engine.dao.AbilityMapper">

    <resultMap id="BaseResultMap" type="com.trs.moye.ability.entity.Ability">
        <id column="id" property="id"/>
        <result column="en_name" property="enName"/>
        <result column="zh_name" property="zhName"/>
        <result column="description" property="description"/>
        <result column="type" property="type"/>
        <result column="path" property="path"/>
        <result column="operator_category_id" property="operatorCategoryId"/>
        <result column="icon_name" property="iconName"/>
        <result column="update_status" property="updateStatus"/>
        <result column="test_status" property="testStatus"/>
        <result column="http_request_config" property="httpRequestConfig"
            typeHandler="com.trs.moye.ability.typehandler.HttpRequestConfigTypeHandler"/>
        <result column="input_schema" property="inputSchema"
            typeHandler="com.trs.moye.ability.typehandler.SchemaTypeHandler"/>
        <result column="output_schema" property="outputSchema"
            typeHandler="com.trs.moye.ability.typehandler.SchemaTypeHandler"/>
        <result column="create_by" property="createBy"/>
        <result column="update_by" property="updateBy"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="input_size" property="inputSize"/>
        <result column="output_size" property="outputSize"/>
    </resultMap>

    <select id="selectById" resultMap="BaseResultMap">
        SELECT * FROM ability WHERE id = #{id}
    </select>

</mapper>

