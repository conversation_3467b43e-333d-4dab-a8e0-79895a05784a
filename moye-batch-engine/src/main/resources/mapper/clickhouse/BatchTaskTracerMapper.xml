<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.trs.moye.batch.engine.dao.clickhouse.BatchTaskTracerMapper">
  <!-- 结果集映射 -->
  <resultMap id="BaseResultMap" type="com.trs.moye.batch.engine.entity.BatchTaskTracer">
    <result column="id" jdbcType="BIGINT" property="id" />
    <result column="node" property="node" />
    <result column="execute_id" jdbcType="VARCHAR" property="executeId" />
    <result column="start_time" jdbcType="TIMESTAMP" property="startTime" />
    <result column="end_time" jdbcType="TIMESTAMP" property="endTime" />
    <result column="process_time" jdbcType="BIGINT" property="processTime" />
    <result column="is_error" jdbcType="TINYINT" property="isError" />
    <result column="error_msg" jdbcType="VARCHAR" property="errorMsg" />
    <result column="data_count" jdbcType="BIGINT" property="dataCount"/>
    <result column="output_table_name" jdbcType="VARCHAR" property="outputTableName"/>
  </resultMap>
  <!-- 属性列表 -->
  <sql id="Base_Column_List">
    <trim suffixOverrides=",">
      id,
      node,
      execute_id,
      start_time,
      end_time,
      process_time,
      is_error,
      error_msg,
      data_count,
      output_table_name,
      arranged_name,
      input_table_name,
      conditions,
      sample_data,
      `fields`,
      pre_process_data_count,
    </trim>
  </sql>
  <!-- 普通插入属性列表，注意与Base_Column_List属性列表一一对应 -->
  <sql id="Insert_Property_List">
    <trim suffixOverrides=",">
      #{data.id,jdbcType=BIGINT},
      #{data.node,jdbcType=VARCHAR},
      #{data.executeId,jdbcType=VARCHAR},
      #{data.startTime,jdbcType=TIMESTAMP},
      #{data.endTime,jdbcType=TIMESTAMP},
      #{data.processTime,jdbcType=BIGINT},
      #{data.isError,jdbcType=TINYINT},
      #{data.errorMsg,jdbcType=VARCHAR},
      #{data.dataCount,jdbcType=BIGINT},
      #{data.outputTableName,jdbcType=VARCHAR},
      #{data.arrangedName,jdbcType=VARCHAR},
      #{data.inputTableName,jdbcType=VARCHAR},
      #{data.conditions,jdbcType=VARCHAR},
      #{data.sampleData,jdbcType=VARCHAR,typeHandler=com.trs.moye.base.common.typehandler.CustomJacksonTypeHandler},
      #{data.fields,jdbcType=VARCHAR,typeHandler=com.trs.moye.base.common.typehandler.CustomJacksonTypeHandler},
      #{data.preProcessDataCount,jdbcType=BIGINT},
    </trim>
  </sql>
  
  <!-- 普通修改属性列表（非主键属性） -->
  <sql id="Update_Property_List">
    <trim suffixOverrides=",">
      node = #{data.node,jdbcType=VARCHAR},
      execute_id = #{data.executeId,jdbcType=VARCHAR},
      start_time = #{data.startTime,jdbcType=TIMESTAMP},
      end_time = #{data.endTime,jdbcType=TIMESTAMP},
      process_time = #{data.processTime,jdbcType=BIGINT},
      is_error = #{data.isError,jdbcType=TINYINT},
      error_msg = #{data.errorMsg,jdbcType=VARCHAR},
    </trim>
  </sql>
  <!-- 动态修改属性列表（非主键属性） -->
  <sql id="Update_Selective_Property_List">
    <trim suffixOverrides=",">
      <if test="data.node != null">
        node = #{data.node,jdbcType=VARCHAR},
      </if>
      <if test="data.executeId != null">
        execute_id = #{data.executeId,jdbcType=VARCHAR},
      </if>
      <if test="data.startTime != null">
        start_time = #{data.startTime,jdbcType=TIMESTAMP},
      </if>
      <if test="data.endTime != null">
        end_time = #{data.endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="data.processTime != null">
        process_time = #{data.processTime,jdbcType=BIGINT},
      </if>
      <if test="data.isError != null">
        is_error = #{data.isError,jdbcType=TINYINT},
      </if>
      <if test="data.errorMsg != null">
        error_msg = #{data.errorMsg,jdbcType=VARCHAR},
      </if>
    </trim>
  </sql>
  
  <insert id="insert">
    insert into batch_task_tracer
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <include refid="Base_Column_List" />
    </trim>
    values
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <include refid="Insert_Property_List" />
    </trim>
  </insert>
  <insert id="insertBatch">
    insert into batch_task_tracer
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <include refid="Base_Column_List" />
    </trim>
    values
    <foreach collection="dataCollection" item="data" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        <include refid="Insert_Property_List" />
      </trim>
    </foreach>
  </insert>
  <update id="updateSelective">
    update batch_task_tracer
    <set>
      <include refid="Update_Selective_Property_List" />
    </set>
    <where>
      id = #{data.id,jdbcType=BIGINT}
    </where>
  </update>
  <delete id="deleteById">
    delete from batch_task_tracer
    <where>
      and id = #{id,jdbcType=BIGINT}
    </where>
  </delete>
  <delete id="deleteByExecuteId">
    delete from batch_task_tracer
    <where>
      and execute_id = #{executeId,jdbcType=VARCHAR}
    </where>
  </delete>
  <delete id="deleteByIdCollection">
    delete from batch_task_tracer
    <where>
      <choose>
        <when test="idCollection == null or idCollection.size == 0">
          and false
        </when>
        <otherwise>
          and id in 
          <foreach close=")" collection="idCollection" item="id" open="(" separator=",">
            #{id,jdbcType=BIGINT}
          </foreach>
        </otherwise>
      </choose>
    </where>
  </delete>
  <delete id="deleteByExecuteIdCollection">
    delete from batch_task_tracer
    <where>
      <choose>
        <when test="executeIdCollection == null or executeIdCollection.size == 0">
          and false
        </when>
        <otherwise>
          and execute_id in 
          <foreach close=")" collection="executeIdCollection" item="executeId" open="(" separator=",">
            #{executeId,jdbcType=VARCHAR}
          </foreach>
        </otherwise>
      </choose>
    </where>
  </delete>
  <select id="getById" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from batch_task_tracer
    <where>
      and id = #{id,jdbcType=BIGINT}
    </where>
  </select>
  <select id="listByExecuteId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from batch_task_tracer
    <where>
      and execute_id = #{executeId,jdbcType=VARCHAR}
    </where>
  </select>
  <select id="listByIdCollection" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from batch_task_tracer
    <where>
      <choose>
        <when test="idCollection == null or idCollection.size == 0">
          and false
        </when>
        <otherwise>
          and id in 
          <foreach close=")" collection="idCollection" item="id" open="(" separator=",">
            #{id,jdbcType=BIGINT}
          </foreach>
        </otherwise>
      </choose>
    </where>
  </select>
  <select id="listByExecuteIdCollection" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from batch_task_tracer
    <where>
      <choose>
        <when test="executeIdCollection == null or executeIdCollection.size == 0">
          and false
        </when>
        <otherwise>
          and execute_id in 
          <foreach close=")" collection="executeIdCollection" item="executeId" open="(" separator=",">
            #{executeId,jdbcType=VARCHAR}
          </foreach>
        </otherwise>
      </choose>
    </where>
  </select>
  
  <!-- 以上代码由MbgCode自动生成2024/7/25 下午6:03 -->



  <!-- 以下为您的代码 -->
  
</mapper>