server.port=9024
server.servlet.context-path=/batch-engine

#logging configuration
logging.file.path=./logs

# spark 任务配置
spark.spark-home=/home/<USER>
# master 是一个 Spark，Mesos 或 YARN 的 cluster URL，或者指定为在 local mode（本地模式）中运行的 “local” 字符串
# 本地spark运行模式(启动本地spark环境，使用spark 命令将任务提交到服务器)
# spark.master=yarn
spark.master=spark://spark-manager:7077
# 模式cluster, client
spark.deploy-mode=client
# 提交spark任务时附上的额外jar包所在目录
spark.jars-directory=/home/<USER>/jars
# 提交spark任务时附上的额外文件所在目录
spark.file-directory=/home/<USER>/files
# java包主类名称
spark.java-main-class=spark.Main
# java包路径
spark.java-jar-path=/opt/moye-spark-application-4.3.0.jar
spark.py-file-directory=/home/<USER>/py-files
# hive路径
spark.hive-metastore-uris=thrift://**************:9083
# yarn
com.trs.hadoop.hadoop-conf-dir=/home/<USER>
# standalone python
#spark.driver-host=***************
spark.driver-host=trs-moye-batch-engine-svc
spark.driver-port=40000
spark.block-manager-port=40200
spark.port-max-retries=200
# spark集群web ui
spark.web-ui-url=http://spark-manager:8080/

# hive
com.trs.hive.host=**************
com.trs.hive.port=10000
com.trs.hive.defaultDatabase=default
com.trs.hive.kerberos.enable=false
com.trs.hive.kerberos.userPrincipal=<EMAIL>
com.trs.hive.kerberos.userKeytab=/TRS/data/moye/proof/usertrs.keytab
com.trs.hive.kerberos.zookeeperPrincipal=zookeeper/hadoop
com.trs.hive.kerberos.krb5ConfPath=/TRS/data/moye/proof/krb5.conf
com.trs.hive.kerberos.principal=hive/<EMAIL>
com.trs.hive.kerberos.serviceDiscoveryMode=zooKeeper
com.trs.hive.kerberos.saslQop=auth-conf
com.trs.hive.kerberos.zooKeeperNamespace=hiveserver2

com.trs.spark.home=/home/<USER>

