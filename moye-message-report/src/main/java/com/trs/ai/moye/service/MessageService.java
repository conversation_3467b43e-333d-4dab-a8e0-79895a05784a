package com.trs.ai.moye.service;

import com.trs.ai.moye.entity.SendDingTalkResult;
import com.trs.ai.moye.entity.vo.DingTalkMessageVO;
import com.trs.ai.moye.entity.vo.PhoneMessageVO;
import java.util.List;

/**
 * 消息服务
 *
 * <AUTHOR>
 * @date 2024/9/5 15:40
 */
public interface MessageService {

    /**
     * 发送短信消息
     *
     * @param messageVO 短信消息
     * @return {@link String }
     */
    String sendPhoneMessage(PhoneMessageVO messageVO);

    /**
     * 发送钉钉消息
     *
     * @param messageVO 钉钉消息
     * @return {@link String }
     */
    List<SendDingTalkResult> sendDingTalkMessage(DingTalkMessageVO messageVO);
}
