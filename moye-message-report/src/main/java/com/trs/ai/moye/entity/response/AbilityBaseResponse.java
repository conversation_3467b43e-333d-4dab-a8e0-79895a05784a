package com.trs.ai.moye.entity.response;

import java.util.ArrayList;
import java.util.List;
import lombok.Data;


/**
 * 能力基础响应
 *
 * @param <T> 数据对象载体
 * <AUTHOR>
 * @date 2024/07/29 15:13:10
 */
@Data
public class AbilityBaseResponse<T> {
    /**
     * 状态码
     */
    Integer code;

    /**
     * 数据对象载体
     */
    List<T> data = new ArrayList<>();

    /**
     * 分页信息
     */
    AbilityPageInfo pageInfo;
}
