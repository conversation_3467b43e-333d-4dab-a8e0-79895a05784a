package com.trs.ai.moye.constant;

import java.io.File;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.sql.Timestamp;
import java.util.Collection;
import java.util.HashMap;
import java.util.Map;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/6/25
 */

@Getter
@AllArgsConstructor
public enum AbilityDataTypeEnum {
    STRING(1, "String"),
    INTEGER(2, "Integer"),
    LONG(3, "Long"),
    FLOAT(4, "Float"),
    DOUBLE(5, "Double"),
    BOOLEAN(6, "Boolean"),
    OBJECT(7, "Object"),
    ARRAY(8, "Array"),
    TIMESTAMP(9,"Timestamp"),
    BIGINT(10,"Bigint"),
    TEXT(11,"Text"),
    NUMBER(12,"Number"),
    FILE(13,"File"),
    ;

    private final int code;
    private final String name;

    // 映射Java类型到AbilityDataTypeEnum
    private static final Map<Class<?>, AbilityDataTypeEnum> JAVA_TYPE_MAP = new HashMap<>();

    static {
        JAVA_TYPE_MAP.put(String.class, STRING);
        JAVA_TYPE_MAP.put(Integer.class, INTEGER);
        JAVA_TYPE_MAP.put(int.class, INTEGER);
        JAVA_TYPE_MAP.put(Long.class, LONG);
        JAVA_TYPE_MAP.put(long.class, LONG);
        JAVA_TYPE_MAP.put(Float.class, FLOAT);
        JAVA_TYPE_MAP.put(float.class, FLOAT);
        JAVA_TYPE_MAP.put(Double.class, DOUBLE);
        JAVA_TYPE_MAP.put(double.class, DOUBLE);
        JAVA_TYPE_MAP.put(Boolean.class, BOOLEAN);
        JAVA_TYPE_MAP.put(boolean.class, BOOLEAN);
        JAVA_TYPE_MAP.put(Object.class, OBJECT);
        JAVA_TYPE_MAP.put(Timestamp.class, TIMESTAMP);
        JAVA_TYPE_MAP.put(BigInteger.class, BIGINT);
        JAVA_TYPE_MAP.put(BigDecimal.class, NUMBER);
        JAVA_TYPE_MAP.put(File.class, FILE);
    }

    public static AbilityDataTypeEnum fromJavaType(Class<?> javaType) {
        // 检查是否是数组或者集合类型
        if (javaType.isArray() || Collection.class.isAssignableFrom(javaType)) {
            return ARRAY;
        }
        // 映射查找
        return JAVA_TYPE_MAP.getOrDefault(javaType, OBJECT);
    }
}
