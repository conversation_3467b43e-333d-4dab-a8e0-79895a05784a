package com.trs.ai.moye.entity.request.ability;

import lombok.Data;

/**
 * <AUTHOR>
 * @version v1.0
 * @date 2023/6/14
 */

@Data
public class PowerParameter {

    /**
     * 参数位置(1:Query 2:Headers 3:Form 4:Json 5:Xml 6:Path)
     */
    private Integer parameterLocation;

    /**
     * 参数名字，英文名字
     */
    private String parameterName;

    /**
     * 请求Body结构
     */
    private String reqBody = "{\"root\":\"root\",\"type\":7,\"properties\":{},\"dataType\":7}";

    /**
     * 参数说明
     */
    private String description;

    /**
     * 当parameterLocation为3:Form时,dataType 1代表text 2代表file(file暂时屏蔽,网关不路由文件) 其他情况都为null
     * <p>
     * 参数类型：1-String 2-Number 3-Integer 4-File 5-Array
     */
    private Integer dataType;

    /**
     * 是否必填 1-必填 2-非必填
     */
    private Integer required;

    /**
     * 是否加密 1-加密 2-不加密
     */
    private Integer encrypted;

    /**
     * 请求参数示例
     */
    private String mock;

}
