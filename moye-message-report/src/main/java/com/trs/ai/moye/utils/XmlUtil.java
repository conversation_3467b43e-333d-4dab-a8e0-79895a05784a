package com.trs.ai.moye.utils;

import com.fasterxml.jackson.dataformat.xml.XmlMapper;
import java.io.IOException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import javax.xml.bind.annotation.adapters.XmlAdapter;
import lombok.extern.slf4j.Slf4j;

/**
 * xml工具类
 *
 * <AUTHOR>
 */
@Slf4j
public class XmlUtil {

    private XmlUtil() {
    }

    private static final XmlMapper xmlMapper = new XmlMapper();

    /**
     * XML转pojo
     *
     * @param clazz pojo类型
     * @param xml   xml字符串
     * @param <T>   范型参数
     * @return 转换好的pojo对象
     */
    public static <T> T convertXmlToObject(Class<T> clazz, String xml) throws IOException {
        try {
            return xmlMapper.readValue(xml, clazz);
        } catch (IOException e) {
            log.error("Exception while converting xml to object", e);
            throw new IOException(String.format("Exception while Unmarshaller: %s", e.getMessage()));
        }
    }

    /**
     * xml字符串和日期时间转换适配器
     *
     * <AUTHOR>
     */
    public static class LocalDateTimeAdapter extends XmlAdapter<String, LocalDateTime> {

        private final DateTimeFormatter dateFormat = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

        @Override
        public String marshal(LocalDateTime dateTime) {
            return dateTime.format(dateFormat);
        }

        @Override
        public LocalDateTime unmarshal(String dateTime) {
            return LocalDateTime.parse(dateTime, dateFormat);
        }
    }
}
