package com.trs.ai.moye.properties;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 能力中心属性
 *
 * <AUTHOR>
 * @date 2024/9/9 14:47
 */
@Component
@ConfigurationProperties(prefix = "com.trs.ability")
@Data
public class AbilityCenterProperties {

    /**
     * 授权
     */
    private String authorization;
    /**
     * 能力中心 URL
     */
    private String baseUrl;
    /**
     * 客户端名称
     */
    private String messageReportClientName;
    /**
     * 获取appid接口路径
     */
    private String appPath;
    /**
     * 发布接口路径
     */
    private String publishPath;
    /**
     * 能力检查是否存在接口路径
     */
    private String abilityCheckExist;
    /**
     * 重新发布接口路径
     */
    private String republishPath;

    /**
     * 检查是否可以撤销发布的接口路径
     */
    private String checkDeletePath;

    /**
     * 跳转到能力中心指定能力的基础地址
     */
    private String abilityBaseAddress;

    /**
     * 撤销发布的接口路径
     */
    private String unpublishPath;
}
