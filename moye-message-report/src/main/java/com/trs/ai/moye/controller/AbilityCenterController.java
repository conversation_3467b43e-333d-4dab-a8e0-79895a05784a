package com.trs.ai.moye.controller;

import com.trs.ai.moye.annotation.AbilityRegisterMethod;
import com.trs.ai.moye.constant.MessageMethodEnum;
import com.trs.ai.moye.entity.PublishToAbilityParam;
import com.trs.ai.moye.entity.request.RepublishRequest;
import com.trs.ai.moye.entity.request.ability.BodyProperty;
import com.trs.ai.moye.exception.AppException;
import com.trs.ai.moye.service.AbilityCenterService;
import com.trs.ai.moye.utils.PropertyMapBuilder;
import java.lang.reflect.Method;
import java.lang.reflect.Parameter;
import java.util.Map;
import java.util.Objects;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 能力中心控制器
 *
 * <AUTHOR>
 * @date 2024/9/10 14:30
 */
@Slf4j
@RestController
@RequestMapping("/ability-center")
public class AbilityCenterController {

    @Resource
    private AbilityCenterService abilityCenterService;

    @PostMapping("/republish")
    public void republish(@RequestBody @Validated RepublishRequest request) {
        try {
            // 提取类名和方法名
            int lastDotIndex = request.getFullMethodPath().lastIndexOf('.');
            String className = request.getFullMethodPath().substring(0, lastDotIndex);
            String methodName = request.getFullMethodPath().substring(lastDotIndex + 1);
            // 加载类
            Class<?> clazz = Class.forName(className);
            // 遍历类的方法
            for (Method method : clazz.getDeclaredMethods()) {
                if (method.getName().equals(methodName)) {
                    if (!method.isAnnotationPresent(AbilityRegisterMethod.class)) {
                        throw new AppException(String.format("该方法未使用 %s 注解,将其标记为需注册至能力中心的方法",
                            AbilityRegisterMethod.class.getSimpleName()));
                    }
                    String finalMethodPath = getPostMethodPath(method, clazz);
                    // 方法参数
                    Parameter[] parameters = method.getParameters();
                    republishMethod(request.getAbilityId(), request.getFullMethodPath(), method, parameters,
                        finalMethodPath);
                    break;
                }
            }
        } catch (ClassNotFoundException e) {
            throw new AppException("未找到对应的类", e);
        }
    }

    private void republishMethod(Long abilityId, String fullMethodPath, Method method, Parameter[] parameters,
        String finalMethodPath) {
        if (parameters.length == 1) {
            Class<?> parameterType = parameters[0].getType();
            MessageMethodEnum methodEnum = MessageMethodEnum.getByMethodPath(fullMethodPath).orElse(null);
            String zhName = Objects.isNull(methodEnum) ? method.getName() : methodEnum.getMethodName();
            String desc = Objects.isNull(methodEnum) ? method.getName() : methodEnum.getMethodDesc();
            PropertyMapBuilder builder = new PropertyMapBuilder(parameterType);
            Map<String, BodyProperty> propertyMap = builder.getPropertyMap();
            PublishToAbilityParam param = new PublishToAbilityParam(zhName, desc,
                finalMethodPath, propertyMap);
            try {
                // 尝试检查是否需要取消发布
                if (Boolean.TRUE.equals(abilityCenterService.checkUnpublish(abilityId, zhName))) {
                    // 尝试取消发布
                    abilityCenterService.unpublish(abilityId);
                }
            } catch (Exception e) {
                log.error("取消发布失败", e);
            }
            // 尝试重新发布
            abilityCenterService.republish(param, abilityId);
        }
    }

    private static @NotNull String getPostMethodPath(Method method, Class<?> clazz) {
        // 获取类级别的路径
        String basePath;
        // 获取方法级别的路径和请求类型
        String methodPath;
        if (clazz.isAnnotationPresent(RequestMapping.class)) {
            RequestMapping classRequestMapping = clazz.getAnnotation(RequestMapping.class);
            basePath = String.join(",", classRequestMapping.value());
        } else {
            throw new AppException("该类方法无法注册至能力中心,请检查是否使用了RequestMapping注解");
        }

        if (method.isAnnotationPresent(PostMapping.class)) {
            PostMapping mapping = method.getAnnotation(PostMapping.class);
            methodPath = String.join(",", mapping.value());
        } else {
            throw new AppException("只支持POST请求注册至能力中心,请检查是否使用了PostMapping注解");
        }
        // 输出接口路径信息
        log.info("Request Path: {}{}", basePath, methodPath);
        return basePath + methodPath;
    }
}
