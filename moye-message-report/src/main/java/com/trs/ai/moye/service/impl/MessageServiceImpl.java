package com.trs.ai.moye.service.impl;

import com.alibaba.fastjson.JSON;
import com.dingtalk.api.DefaultDingTalkClient;
import com.dingtalk.api.DingTalkClient;
import com.dingtalk.api.request.OapiRobotSendRequest;
import com.dingtalk.api.request.OapiRobotSendRequest.At;
import com.dingtalk.api.request.OapiRobotSendRequest.Markdown;
import com.dingtalk.api.request.OapiRobotSendRequest.Text;
import com.dingtalk.api.response.OapiRobotSendResponse;
import com.taobao.api.ApiException;
import com.trs.ai.moye.constant.DingTalkMsgTypeEnum;
import com.trs.ai.moye.entity.DingTalkWebhookParams;
import com.trs.ai.moye.entity.SendDingTalkResult;
import com.trs.ai.moye.entity.SendResult;
import com.trs.ai.moye.entity.SendResult.SminfoReturn;
import com.trs.ai.moye.entity.vo.DingTalkMessageVO;
import com.trs.ai.moye.entity.vo.MessageVO;
import com.trs.ai.moye.entity.vo.PhoneMessageVO;
import com.trs.ai.moye.properties.ScgaMsgGatewayProperties;
import com.trs.ai.moye.service.MessageService;
import com.trs.ai.moye.utils.DingTalkUtil;
import com.trs.ai.moye.utils.StringUtil;
import com.trs.ai.moye.utils.XmlUtil;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.cxf.endpoint.Client;
import org.apache.cxf.jaxws.endpoint.dynamic.JaxWsDynamicClientFactory;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class MessageServiceImpl implements MessageService {

    @Resource
    private ScgaMsgGatewayProperties msgGatewayProperties;

    private static final String METHOD_NAME_SEND_MESSAGE = "sendMessage";

    /**
     * 发送短信
     *
     * @param messageVO 短信内容
     * @return 短信扩展码
     */
    @Override
    public String sendPhoneMessage(PhoneMessageVO messageVO) {
        log.info("发送短信：{}", messageVO);

        if (!msgGatewayProperties.getEnable()) {
            log.info("未启用短信网关！取消短信发送");
            return null;
        }
        // 扩展码
        String extensionCode = String.format("%s%s", msgGatewayProperties.getExtensionCodePrefix(),
            System.currentTimeMillis());
        // 系统id
        String systemId = msgGatewayProperties.getSystemId();
        // 发送者名称
        String sendFrom = "数据中台";
        // 过滤出合法的手机号
        String[] legalPhone = Arrays.stream(messageVO.getPhoneNumbers())
            .filter(StringUtil::isLegalMobilePhone)
            .toArray(String[]::new);
        if (legalPhone.length == 0) {
            log.error("没有合法的手机号, 发送失败！：{}", Arrays.toString(legalPhone));
            return null;
        }
        MessageVO formatContentVo = null;
        try {
            formatContentVo = JSON.parseObject(messageVO.getContent(), MessageVO.class);
        } catch (Exception e) {
            // 无需处理 messageVO 为 null 的情况，继续执行
        }
        String sendContent = messageVO.getContent();
        if (Objects.nonNull(formatContentVo)) {
            sendContent = formatContentVo.format();
        }
        String response = doRequest(METHOD_NAME_SEND_MESSAGE, systemId, extensionCode, String.join(",", legalPhone),
            sendContent, sendFrom);
        try {
            SendResult result = XmlUtil.convertXmlToObject(SendResult.class, response);
            SminfoReturn data = result.getData();
            log.info("短信发送结果：{}", data);
            return extensionCode;
        } catch (Exception exception) {
            log.error("短信发送失败! ", exception);
            return null;
        }
    }

    @Override
    public List<SendDingTalkResult> sendDingTalkMessage(DingTalkMessageVO messageVO) {
        List<SendDingTalkResult> results = new ArrayList<>();
        for (DingTalkWebhookParams dingTalkWebhookParams : messageVO.getDingTalkWebhookParams()) {
            try {
                SendDingTalkResult result = sendSingleDingTalkMessage(dingTalkWebhookParams, messageVO.getContent());
                if (!result.isSuccess()) {
                    results.add(result);
                }
            } catch (Exception e) {
                log.error("发送钉钉消息失败", e);
                results.add(new SendDingTalkResult(-2L, e.getLocalizedMessage()));
            }

        }
        return results;
    }

    private SendDingTalkResult sendSingleDingTalkMessage(DingTalkWebhookParams dingTalkWebhookParams, String content) {
        String webhookUrl = dingTalkWebhookParams.getWebhookUrl();
        //sign字段和timestamp字段必须拼接到请求URL上，否则会出现 310000 的错误信息
        String url = DingTalkUtil.buildDingTalkUrl(webhookUrl, dingTalkWebhookParams.getSecret());

        MessageVO messageVO = null;
        try {
            messageVO = JSON.parseObject(content, MessageVO.class);
        } catch (Exception e) {
            // 无需处理 messageVO 为 null 的情况，继续执行
        }

        OapiRobotSendResponse rsp;
        try {
            if (Objects.nonNull(messageVO)) {
                rsp = doSendMarkDownDingTalkMessage(dingTalkWebhookParams, messageVO, url);
            } else {
                rsp = doSendTextDingTalkMessage(dingTalkWebhookParams, content, url);
            }
            return new SendDingTalkResult(rsp);
        } catch (ApiException e) {
            log.error("发送钉钉消息失败", e);
            return new SendDingTalkResult(-2L, e.getLocalizedMessage());
        }
    }

    private OapiRobotSendResponse doSendTextDingTalkMessage(DingTalkWebhookParams dingTalkWebhookParams, String content,
        String url) throws ApiException {
        DingTalkClient client = new DefaultDingTalkClient(url);
        OapiRobotSendRequest req = new OapiRobotSendRequest();
        //定义文本内容
        Text text = new Text();
        text.setContent(content);
        //定义 @ 对象
        At at = new At();
        at.setAtUserIds(dingTalkWebhookParams.getAtUserIds());
        at.setAtMobiles(dingTalkWebhookParams.getAtMobiles());
        at.setIsAtAll(dingTalkWebhookParams.getAtAll());
        //设置消息类型
        req.setMsgtype(DingTalkMsgTypeEnum.TEXT.getEnName());
        req.setText(text);
        req.setAt(at);
        OapiRobotSendResponse rsp = client.execute(req);
        log.info(rsp.getBody());
        return rsp;
    }

    private OapiRobotSendResponse doSendMarkDownDingTalkMessage(DingTalkWebhookParams dingTalkWebhookParams,
        MessageVO messageVO,
        String url) throws ApiException {
        DingTalkClient client = new DefaultDingTalkClient(url);
        OapiRobotSendRequest req = new OapiRobotSendRequest();
        //定义文本内容
        Markdown markdown = new Markdown();
        markdown.setTitle(messageVO.getTitle());
        markdown.setText(messageVO.toMarkdown());
        //定义 @ 对象
        At at = new At();
        at.setAtUserIds(dingTalkWebhookParams.getAtUserIds());
        at.setAtMobiles(dingTalkWebhookParams.getAtMobiles());
        at.setIsAtAll(dingTalkWebhookParams.getAtAll());
        //设置消息类型
        req.setMsgtype(DingTalkMsgTypeEnum.MARKDOWN.getEnName());
        req.setMarkdown(markdown);
        req.setAt(at);
        OapiRobotSendResponse rsp = client.execute(req);
        log.info(rsp.getBody());
        return rsp;
    }

    private String doRequest(String methodName, Object... params) {
        JaxWsDynamicClientFactory dcf = JaxWsDynamicClientFactory.newInstance();
        try (Client client = dcf.createClient(msgGatewayProperties.getWsdl())) {
            log.info("webservice 请求接口: {}", methodName);
            Arrays.stream(params).forEachOrdered(p -> log.info("参数: {}", p));
            Object[] objects = client.invoke(methodName, params);
            log.info("webservice 响应内容：{}", objects[0]);
            return (String) objects[0];
        } catch (Exception e) {
            log.error("webservice 接口调用失败", e);
            return null;
        }
    }
}
