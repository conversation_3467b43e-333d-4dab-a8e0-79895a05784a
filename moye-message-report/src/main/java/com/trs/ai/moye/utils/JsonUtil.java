package com.trs.ai.moye.utils;

import com.fasterxml.jackson.databind.ObjectMapper;

/**
 * JSON Util
 *
 * <AUTHOR>
 * @date 2024/9/9 10:40
 */
public class JsonUtil {
    public static final ObjectMapper OBJECT_MAPPER = new ObjectMapper();
    /**
     * 实体类转json字符串
     *
     * @param object 实体类对象
     * @return 转换出来的json字符串，转换失败返回emptyString
     */
    public static String toJsonString(Object object) {
        try {
            return OBJECT_MAPPER.writer().writeValueAsString(object);
        } catch (Exception e) {
            return "";
        }
    }
}
