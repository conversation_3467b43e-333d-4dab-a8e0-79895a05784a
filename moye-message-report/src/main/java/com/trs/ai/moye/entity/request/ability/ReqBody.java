package com.trs.ai.moye.entity.request.ability;

import com.trs.ai.moye.constant.AbilityDataTypeEnum;
import java.util.Map;
import lombok.Data;

/**
 * req body
 *
 * <AUTHOR>
 * @date 2024/09/09 13:49:11
 */
@Data
public class ReqBody {

    private static final String ROOT = "root";

    private Integer type = AbilityDataTypeEnum.OBJECT.getCode();

    private Integer dataType = AbilityDataTypeEnum.OBJECT.getCode();

    private Map<String, BodyProperty> properties;
}