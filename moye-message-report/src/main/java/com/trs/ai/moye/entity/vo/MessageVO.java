package com.trs.ai.moye.entity.vo;

import lombok.Data;

/**
 * moye 监控消息
 *
 * <AUTHOR>
 * @date 2024/9/19 10:46
 */
@Data
public class MessageVO {

    /**
     * 内容
     */
    private String content;
    /**
     * 标题
     */
    private String title;
    /**
     * 通知类型
     */
    private String noticeType;
    /**
     * 监控时间
     */
    private String publishTime;

    public String toMarkdown() {
        return String.format(
            "# **%s**%n%n- **消息类型：** %s%n- **监控时间：** %s%n- **内容：** %s",
            title,
            noticeType,
            publishTime,
            content
        );
    }

    public String format() {
        return String.format(
            "【数据中台消息】：%s%n消息类型：%s%n监控时间：%s%n内容：%s%n",
            title,
            noticeType,
            publishTime,
            content
        );
    }

}
