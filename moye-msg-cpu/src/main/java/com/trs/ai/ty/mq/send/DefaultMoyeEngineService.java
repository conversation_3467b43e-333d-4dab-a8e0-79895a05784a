package com.trs.ai.ty.mq.send;

import com.alibaba.fastjson.JSONArray;
import com.trs.ai.ty.base.utils.AppUtil;
import com.trs.ai.ty.mq.constant.SendConstants;
import com.trs.ai.ty.mq.entity.DataSource;
import com.trs.ai.ty.mq.send.handle.AfterHandle;
import com.trs.ai.ty.mq.send.handle.BeforeHandle;
import com.trs.ai.ty.mq.service.ActiveDataSourceService;
import com.trs.ai.ty.mq.service.L1CacheService;
import com.trs.ai.ty.mq.service.MQService;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @date 2024-05-26 11:39
 */
@Slf4j
@Component
public class DefaultMoyeEngineService implements MoyeEngineService {

    @Autowired
    private List<BeforeHandle> beforeHandleList;

    @Resource
    private List<AfterHandle> afterHandleList;

    @Resource
    private MQService mqService;

    @Resource
    private ActiveDataSourceService activeDataSourceService;

    @Resource
    private L1CacheService l1CacheService;

    @PostConstruct
    public void init(){
        if (beforeHandleList == null){
            beforeHandleList = new ArrayList<>();
        }
        if (afterHandleList == null){
            afterHandleList = new ArrayList<>();
        }
        beforeHandleList.sort(Comparator.comparing(BeforeHandle::beforeOrder));
        afterHandleList.sort(Comparator.comparing(AfterHandle::afterOrder));
    }

    @Override
    public SendInfo buildSendInfo(Integer dataSourceId){
        DataSource dataSource = activeDataSourceService.getDataSource(dataSourceId);
        SendInfo sendInfo = new SendInfo();
        sendInfo.setDataSourceId(dataSourceId);
        sendInfo.setDataSource(dataSource);
        sendInfo.setTempStore(new HashMap<>());
        String batchNo = dataSource.getReceiveClassification() + "-" + dataSourceId + "-" +
                LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmssSSS"));
        sendInfo.putStore(SendConstants.DATA_SOURCE_PROPERTIES_KEY, l1CacheService.getDataSourceSystemProperties(dataSourceId));
        sendInfo.putStore(SendConstants.TASK_MONITOR_BATCH_NO_KEY, batchNo);
        return sendInfo;
    }

    @Override
    public void sends(SendInfo sendInfo, JSONArray dataList) {
        if (AppUtil.isEmpty(dataList)){
            log.warn("数据列表为空");
            return;
        }
        for (int i = 0; i < dataList.size(); i++){
            try {
                sendInfo.setData(dataList.getJSONObject(i));
            }catch (Throwable e){
                continue;
            }
            send(sendInfo);
        }
    }

    @Override
    public void sends(SendInfo sendInfo, List<Map<String, Object>> dataList) {
        if (AppUtil.isEmpty(dataList)){
            log.warn("数据列表为空");
            return;
        }
        for (Map<String, Object> data : dataList){
            sendInfo.setData(data);
            send(sendInfo);
        }
    }

    @Override
    public void send(SendInfo sendInfo) {
        try {
            if (!invokeBeforeHandle(sendInfo)){
                return;
            }
            sendInfo.setSendResult(mqService.sendToProcess(sendInfo.getDataSourceId().toString(), sendInfo.getData()));
        }
        catch (Throwable e){
            sendInfo.setThrowable(e);
        }
        invokeAfterHandle(sendInfo);
    }

    private boolean invokeBeforeHandle(SendInfo sendInfo){
        for (BeforeHandle beforeHandle : beforeHandleList){
            if (!beforeHandle.beforeHandle(sendInfo)){
                return false;
            }
        }
        return true;
    }

    private void invokeAfterHandle(SendInfo sendInfo){
        for (AfterHandle afterHandle : afterHandleList){
            try {
                afterHandle.afterHandle(sendInfo);
            }catch (Throwable e){
                log.error(String.format("后置处理器【%s】执行发生异常，数据源[%s]"
                        , afterHandle.getClass().getSimpleName()
                        , sendInfo.getDataSourceId()), e);
            }
        }
    }

}
