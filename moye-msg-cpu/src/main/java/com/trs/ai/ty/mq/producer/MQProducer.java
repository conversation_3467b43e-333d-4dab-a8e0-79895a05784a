package com.trs.ai.ty.mq.producer;

import com.trs.ai.ty.mq.entity.NullValue;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;
import lombok.extern.slf4j.Slf4j;
import trs.cloud.mq.MQSendFactory;
import trs.cloud.mq.exception.MQConnectionException;
import trs.cloud.mq.exception.MQSessionException;
import trs.cloud.mq.format.MQFieldType;

@Slf4j
public class MQProducer {

    private static final Map<String, MQSendFactory> sessions = new ConcurrentHashMap<>();

    /**
     * 发送消息到网信办，添加同步方法锁，避免多线程中出现数据交叉add
     *
     * @param server
     * @param topic
     * @param msgs
     * @return
     * @throws
     * @creator li.hao
     * @since 2020/9/28 10:16
     */
    public static synchronized boolean send(String server, String topic, Map<String, Object> msgs) throws MQConnectionException {
        StringBuilder sessionKey = new StringBuilder();
        sessionKey.append(server).append("#").append(topic);
        MQSendFactory session = sessions.get(sessionKey.toString());
        if(Objects.isNull(session)) {
            session = new MQSendFactory(server, topic);

            session.start();
            sessions.put(sessionKey.toString(), session);
        }
        //
        List<Object> doc = new ArrayList<>();
        List<String> colName = new ArrayList<>();
        List<MQFieldType> types = new ArrayList<>();
        convert2MQData(msgs, session, doc, colName, types);
        //
        send(session, doc.toArray(), colName.toArray(new String[0]), types.toArray(new MQFieldType[0]));
        //
        return true;
    }

    /**
     * 把收到的消息转换为MQ发送需要的数据
     *
     * @param msgs
     * @param session
     * @param doc
     * @param colName
     * @param types
     * @creator linwei
     * @since 2020/9/29 6:03 下午
     */
    protected static void convert2MQData(Map<String, Object> msgs, MQSendFactory session, List<Object> doc, List<String> colName, List<MQFieldType> types) {
        //
        for (String fieldName : msgs.keySet()) {
            //map中可能有value为null的情况，统一作为字符串处理
            Object o = Objects.nonNull(msgs.get(fieldName)) ? msgs.get(fieldName) : "";
            //
            MQFieldType fieldType;
            if (o instanceof NullValue) {
                fieldType = ((NullValue) o).getType();
                o = null;
            } else {
                fieldType = getFieldType(o.getClass());
            }
            //设置数据结构，参数为每一列的列名、列类型、是否可以为空。 注意： 如果设置为不能为空，那么如果添加数据时没有添加该列数据，会报错。
            session.addColumn(fieldName, fieldType, true);
            //
            doc.add(o);
            //
            colName.add(fieldName);
            //
            types.add(fieldType);
        }
    }

    public static MQFieldType getFieldType(Class<?> type) {
        if (type.equals(Boolean.class) || type.equals(boolean.class)) {
            return MQFieldType.Boolean;
        } else if (type.equals(Float.class) || type.equals(float.class)) {
            return MQFieldType.Float;
        } else if (type.equals(Double.class) || type.equals(double.class)) {
            return MQFieldType.Double;
        } else if (type.equals(Integer.class) || type.equals(int.class)) {
            return MQFieldType.Int;
        } else if (type.equals(Long.class) || type.equals(long.class)) {
            return MQFieldType.Long;
        } else if (type.equals(Byte.class) || type.equals(byte.class)) {
            return MQFieldType.Binary;
        } else if (type.equals(Integer[].class) || type.equals(int[].class)) {
            return MQFieldType.Ints;
        } else if (type.equals(Long[].class) || type.equals(long[].class)) {
            return MQFieldType.Longs;
        } else if (type.equals(Float[].class) || type.equals(float[].class)) {
            return MQFieldType.Floats;
        } else if (type.equals(Byte[].class) || type.equals(byte[].class)) {
            return MQFieldType.Binaries;
        } else if (type.equals(Boolean[].class) || type.equals(boolean[].class)) {
            return MQFieldType.Booleans;
        } else if (type.equals(Double[].class) || type.equals(double[].class)) {
            return MQFieldType.Doubles;
        } else if (type.equals(String[].class)) {
            return MQFieldType.Strings;
        } else {
            return MQFieldType.String;
        }
    }


    public static void send(MQSendFactory session, Object[] doc, String[] colname, MQFieldType[] coltype) {
        try {
            // 开始发送
            int colLen = colname.length;

            for (int i = 0; i < colLen; ++i) {
                switch (coltype[i]) {
                    case Strings:
                        session.setStrings(colname[i], (String[]) doc[i]);
                        break;
                    case Int:
                        session.setInt(colname[i], doc[i] == null ? 0 : (int) doc[i]);
                        break;
                    case String:
                        session.setString(colname[i], doc[i].toString());
                        break;
                    case Long:
                        session.setLong(colname[i], doc[i] == null ? 0L : (long) doc[i]);
                        break;
                    case Ints:
                        session.setInts(colname[i], (int[]) doc[i]);
                        break;
                    case Float:
                        session.setFloat(colname[i], (float) doc[i]);
                        break;
                    case Floats:
                        session.setFloats(colname[i], (float[]) doc[i]);
                        break;
                    default:
                        break;
                }
            }

            long startTime = System.currentTimeMillis();
            session.add();
            log.debug("session add use time : {}ms", System.currentTimeMillis() - startTime);

            // 获得各种统计量
            // 当前缓存的数据总量
//            log.info("current data size当前缓存的数据总量: " + session.getCurrentDataSize());
            // 当缓存数据较多时，数据会被分割为小包，查看当前缓存的数据包数
            log.debug("current pac num 当前缓存的数据包数: " + session.getCurrentPacNum());
            // 查看当前数据包中数据的条数
            log.debug("record num int current pac 数据包中数据的条数: " + session.getRecordNumInCurrentPac());
            // 添加完所有数据，flush即可发送。
        } catch (MQSessionException e) {
            log.error("消息发送异常", e);
        } finally {
            if (session != null) {
                try {
                    long startTime = System.currentTimeMillis();
                    session.flush();
                    log.debug("session flush use time : {}ms", System.currentTimeMillis() - startTime);
                } catch (MQSessionException | MQConnectionException e) {
                    log.error("send-err", e);
                }
            }
        }
    }
}
