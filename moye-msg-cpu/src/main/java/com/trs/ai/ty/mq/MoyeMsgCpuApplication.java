package com.trs.ai.ty.mq;

import com.trs.ai.ty.base.service.impl.RootLoggerServiceImpl;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.scheduling.annotation.EnableAsync;

@MapperScan(basePackages = {"com.trs.**.dao"})
@SpringBootApplication(scanBasePackages = {"com.trs.ai.ty.mq", "com.trs.ai.ty.grpc", "com.trs.ai.ty.base"})
@EnableAsync
public class MoyeMsgCpuApplication {

    public static void main(String[] args) {
        SpringApplication app = new SpringApplication(MoyeMsgCpuApplication.class);
        app.addInitializers(new RootLoggerServiceImpl());
        app.run(args);
        System.out.println(
        ""
            + "                      █████                ██████         \n"
            + " ██████████   ██████ ██░░░██        █████ ░██░░░██ ██   ██\n"
            + "░░██░░██░░██ ██░░░░ ░██  ░██ █████ ██░░░██░██  ░██░██  ░██\n"
            + " ░██ ░██ ░██░░█████ ░░██████░░░░░ ░██  ░░ ░██████ ░██  ░██\n"
            + " ░██ ░██ ░██ ░░░░░██ ░░░░░██      ░██   ██░██░░░  ░██  ░██\n"
            + " ███ ░██ ░██ ██████   █████       ░░█████ ░██     ░░██████\n"
            + "░░░  ░░  ░░ ░░░░░░   ░░░░░         ░░░░░  ░░       ░░░░░░ ");
    }

}
