package com.trs.ai.ty.mq.receiver;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.trs.ai.ty.base.utils.StringUtil;
import com.trs.ai.ty.mq.send.MoyeEngineService;
import com.trs.ai.ty.mq.send.SendInfo;
import com.trs.ai.ty.mq.service.PulsarConsumerMapService;
import java.util.Objects;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.pulsar.client.api.Consumer;
import org.apache.pulsar.client.api.Message;
import org.apache.pulsar.client.api.MessageListener;
import org.apache.pulsar.client.api.PulsarClient;
import org.apache.pulsar.client.api.PulsarClientException;
import org.apache.pulsar.client.api.SubscriptionType;
import org.springframework.stereotype.Component;

/**
 * pulsar消费
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022/7/28 17:24
 **/
@Component
@Slf4j
public class PulsarConsumer {

    private static final String URL_PREFIX = "pulsar://";

    @Resource
    private PulsarConsumerMapService pulsarMapService;

    @Resource
    private MoyeEngineService moyeEngineService;

    public void startConsumer(Integer dataSourceId, String url, String topic, String subscriptionName)
        throws PulsarClientException {
        PulsarClient client = pulsarMapService.getClient(url);
        if (Objects.isNull(client)) {
            client = PulsarClient.builder()
                .serviceUrl(URL_PREFIX + url)
                .build();
            pulsarMapService.putClient(url, client);
        }
        MessageListener pulsarMessageListener = (consumer, msg) -> {
            try {

                //转换并发送消息
                convertAndSendMsg(dataSourceId, msg);

                consumer.acknowledge(msg);
            } catch (Exception e) {
                log.error("Failed to process message! dataSourceId={}, topic={}, serviceUrl={}, subscriptionName={}",
                    dataSourceId,
                    url,
                    topic,
                    subscriptionName, e);
                consumer.negativeAcknowledge(msg);
            }
        };

        Consumer consumer = client.newConsumer()
            .topic(topic)
            .subscriptionName(subscriptionName)
            .subscriptionType(SubscriptionType.Shared)
            .messageListener(pulsarMessageListener)
            .subscribe();

        //TODO pulsar配置从指定消息消费
        //consumer.seek(MessageId.earliest);
        pulsarMapService.putConsumer(dataSourceId, consumer);
    }

    public void stopConsumer(Integer dataSourceId) throws PulsarClientException {
        Consumer consumer = pulsarMapService.getConsumer(dataSourceId);
        consumer.unsubscribe();
    }

    private void convertAndSendMsg(Integer dataSourceId, Message msg) {
        String message = new String(msg.getData());
        if (!isNotNullArray(message)) {
            log.error("Illegal message : {}", message);
            return;
        }
        SendInfo sendInfo = moyeEngineService.buildSendInfo(dataSourceId);
        try {
            JSONArray jsonArray = JSON.parseArray(message);
            moyeEngineService.sends(sendInfo, jsonArray);
        } catch (Throwable e) {
            log.error("", e);
        }
    }

    public boolean isNotNullArray(String message) {
        return StringUtil.isNotEmpty(message) && message.startsWith("[") && message.endsWith("]");
    }

}
