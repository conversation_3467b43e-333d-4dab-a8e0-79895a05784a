package com.trs.ai.ty.mq.send.handle;

import com.alibaba.fastjson.JSON;
import com.trs.ai.ty.mq.constant.SendConstants;
import com.trs.ai.ty.mq.entity.DataSource;
import com.trs.ai.ty.mq.entity.DataSourceSystemProperty;
import com.trs.ai.ty.mq.entity.TaskMonitoringMsgData;
import com.trs.ai.ty.mq.enums.DataAccessTraceTypeEnum;
import com.trs.ai.ty.mq.send.HandleOrder;
import com.trs.ai.ty.mq.send.SendInfo;
import com.trs.ai.ty.mq.service.DataTracerService;
import java.time.LocalDateTime;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @date 2024-05-26 16:48
 */
@Slf4j
@Component
public class MonitorVerifyHandle implements BeforeHandle{

    @Autowired
    private DataTracerService dataTracerService;

    @Override
    public boolean beforeHandle(SendInfo sendInfo) {
        List<TaskMonitoringMsgData> taskMonitoringMsgDataList = (List<TaskMonitoringMsgData>) sendInfo.getStore(SendConstants.TASK_MONITOR_DATA_LIST_KEY);
        LocalDateTime beginTime = sendInfo.getStore(SendConstants.BEGIN_TIME_KEY, LocalDateTime.class);
        taskMonitoringMsgDataList.add(TaskMonitoringMsgData.of(DataAccessTraceTypeEnum.GET.getName()
                , beginTime
                , sendInfo.getStore(SendConstants.TASK_MONITOR_DATA_SOURCE_KEY, DataSource.class)
                , false
                ,null
                , JSON.toJSONString(sendInfo.getData())
                , sendInfo.getStore(SendConstants.RECORD_ID_KEY, Long.class)
                , sendInfo.getStore(SendConstants.TASK_MONITOR_BATCH_NO_KEY, String.class)
                ,null
                , LocalDateTime.now()));
        return !dataTracerService.verifyAll(sendInfo.getData()
                , (List<DataSourceSystemProperty>) sendInfo.getStore(SendConstants.DATA_SOURCE_PROPERTIES_KEY)
                , sendInfo.getStore(SendConstants.TASK_MONITOR_DATA_SOURCE_KEY, DataSource.class)
                , sendInfo.getStore(SendConstants.RECORD_ID_KEY, Long.class)
                , sendInfo.getStore(SendConstants.TASK_MONITOR_BATCH_NO_KEY, String.class)
                , taskMonitoringMsgDataList);
    }

    @Override
    public int beforeOrder() {
        return HandleOrder.LOW + 1;
    }
}
