package com.trs.ai.ty.mq.receiver;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONException;
import com.alibaba.fastjson.JSONObject;
import com.trs.ai.ty.mq.config.KafkaConsumerConfig;
import com.trs.ai.ty.mq.entity.DataSource;
import com.trs.ai.ty.mq.entity.DatasourceStartParams;
import com.trs.ai.ty.mq.send.MoyeEngineService;
import com.trs.ai.ty.mq.send.SendInfo;
import com.trs.ai.ty.mq.service.KafkaConsumerMapService;
import java.time.Duration;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.atomic.AtomicInteger;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.kafka.clients.consumer.ConsumerRecords;
import org.apache.kafka.clients.consumer.KafkaConsumer;
import org.apache.kafka.clients.consumer.OffsetAndTimestamp;
import org.apache.kafka.common.TopicPartition;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version 1.0
 * @description TODO kafka消费者
 * @since 2023/3/16 17:54
 **/
@Component
@Slf4j
public class KafkaConsumerUtil {

    private final AtomicInteger threadNumber = new AtomicInteger(1);

    @Resource
    private KafkaConsumerMapService kafkaConsumerMapService;

    @Resource
    private MoyeEngineService moyeEngineService;

    public void startConsumer(DataSource dataSource, KafkaConsumerConfig kafkaConsumerConfig) {
        Thread thread = new Thread(() -> startConsumer0(dataSource.getId(), kafkaConsumerConfig));
        thread.setName(String.format("Kafka-%s-%s-线程%s"
                , dataSource.getId()
                , dataSource.getName(), threadNumber.getAndIncrement()));
        thread.start();
    }

    public void resetOffset(KafkaConsumer<String, String> consumer, DatasourceStartParams startParams, Integer datasourceId) {
        if (startParams != null && startParams.isValidOffsetResetType()) {
            Set<TopicPartition> topicPartitions = new HashSet<>();
            consumer.poll(100);
            int count = 0;
            while (topicPartitions.isEmpty() && count <= 1000) {
                topicPartitions = consumer.assignment();
                count++;
            }
            if (topicPartitions.isEmpty()) {
                log.error("获取partition失败！数据源id:{}", datasourceId);
                return;
            }

            switch (startParams.getOffsetResetType()) {
                case "TIMESTAMP":
                    // 获取指定的时间戳
                    Long timestamp = startParams.getTimestamp();
                    if (timestamp!= null && timestamp > 0) {
                        // 构造一个映射，将每个 TopicPartition 映射到指定的时间戳
                        Map<TopicPartition, Long> timestampsToSearch = new HashMap<>();
                        for (TopicPartition tp : topicPartitions) {
                            timestampsToSearch.put(tp,  timestamp);
                        }

                        // 使用 offsetsForTimes 方法获取每个分区在指定时间戳之后的最早偏移量
                        Map<TopicPartition, OffsetAndTimestamp> offsets = consumer.offsetsForTimes(timestampsToSearch);

                        for (TopicPartition tp : topicPartitions) {
                            OffsetAndTimestamp offsetAndTimestamp = offsets.get(tp);
                            if (offsetAndTimestamp!= null) {
                                try {
                                    long offset = offsetAndTimestamp.offset();
                                    consumer.seek(tp,  offset);
                                } catch (Exception e) {
                                    log.error(" 设置offset失败！", e);
                                }
                            } else {
                                log.error(" 未找到分区 {} 在时间戳 {} 之后的偏移量！数据源id: {}", tp, timestamp, datasourceId);
                            }
                        }
                    } else {
                        log.error(" 指定时间戳为空或<=0！数据源id：{}", datasourceId);
                    }
                    break;
                case "EARLIEST":
                    consumer.seekToBeginning(topicPartitions);
                    break;
                case "LATEST":
                    consumer.seekToEnd(topicPartitions);
                    break;
                case "OFFSET":
                    Long offset = startParams.getOffset();
                    if (offset != null && offset > 0) {
                        for (TopicPartition tp : topicPartitions) {
                            try {
                                consumer.seek(tp, offset);
                            } catch (Exception e) {
                                log.error("设置offset失败！", e);
                            }
                        }
                    } else {
                        log.error("指定offset为空或<=0！数据源id：{}", datasourceId);
                    }
                    break;
                default:
            }
        }
    }

    private void startConsumer0(Integer dataSourceId, KafkaConsumerConfig kafkaConsumerConfig) {
        if (Objects.nonNull(kafkaConsumerMapService.getConsumer(dataSourceId))) {
            log.warn("Kafka消费者已存在，不再重复创建，数据源id：{}", dataSourceId);
            return;
        }
        final KafkaConsumer<String, String> consumer = kafkaConsumerMapService.setConsumer(dataSourceId, kafkaConsumerConfig);
        if (consumer == null) {
            throw new RuntimeException("新建consumer失败！");
        }
        //TODO 指定consumer消费的offset
        resetOffset(consumer, kafkaConsumerConfig.getStartParams(), dataSourceId);

        Duration timeoutDuration = Duration.ofSeconds(kafkaConsumerConfig.getPollTimeoutSeconds());
        log.warn("kafka consumer开始消费！datasourceId: [{}] topic: [{}] group:[{}]",
            dataSourceId, kafkaConsumerConfig.getReceiveTopic(), kafkaConsumerConfig.getReceiveGroup());
        // 缓存中的消费者不是consumer，可能是consumer被停止了，也有可能是consumer被停止并且新的消费者已经被创建。不管哪种情况，都要关闭consumer；
        // 跳出while循环时关闭consumer
        while (kafkaConsumerMapService.getConsumer(dataSourceId) == consumer) {
            try{
                ConsumerRecords<String, String> records = consumer.poll(timeoutDuration);
                if (records.isEmpty()) {
                    log.warn("No message received from topic: {}", kafkaConsumerConfig.getReceiveTopic());
                    consumer.commitAsync();
                    Thread.sleep(kafkaConsumerConfig.getEmptyRecordsSleepMillis());
                    continue;
                }
                SendInfo sendInfo = moyeEngineService.buildSendInfo(dataSourceId);
                // 注意：processRecord方法不能抛出异常
                records.forEach(record -> processRecord(kafkaConsumerConfig, record.value(), sendInfo));
                consumer.commitAsync();
            }
            catch (Throwable e) {
                log.error(String.format("主题【%s】处理数据发生异常", kafkaConsumerConfig.getReceiveTopic()), e);
            }
        }
        try{
            log.warn("主题【{}】停止消费，消费者引用：{}", kafkaConsumerConfig.getReceiveTopic(), consumer);
            consumer.close();
        }
        catch (Throwable e){
            throw new RuntimeException(String.format("关闭主题【%s】的消费者失败", kafkaConsumerConfig.getReceiveTopic()), e);
        }
    }

    private void processRecord(KafkaConsumerConfig kafkaConsumerConfig, String message, SendInfo sendInfo){
        try {
            Object jsonData = JSON.parse(message);
            if (jsonData instanceof JSONObject){
                sendInfo.setData((JSONObject) jsonData);
                moyeEngineService.send(sendInfo);
            }
            else if (jsonData instanceof JSONArray){
                moyeEngineService.sends(sendInfo, (JSONArray) jsonData);
            }
            else {
                log.error("主题【{}】丢弃非json数据：{}", kafkaConsumerConfig.getReceiveTopic(), message);
            }
        }
        catch (JSONException e){
            log.error("主题【{}】丢弃非json数据：{}", kafkaConsumerConfig.getReceiveTopic(), message);
        }
        catch (Throwable e) {
            log.error(String.format("主题【%s】处理数据发生异常，异常的数据：%s", kafkaConsumerConfig.getReceiveTopic(), message), e);
        }
    }


    public void stopConsumer(Integer id) {
        kafkaConsumerMapService.stopConsumer(id);
    }
}

