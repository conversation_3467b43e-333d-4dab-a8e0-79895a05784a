package com.trs.ai.ty.mq.service;

import com.trs.ai.ty.base.entity.exception.BizException;
import com.trs.ai.ty.grpc.entity.DataAccessTrace;
import com.trs.ai.ty.mq.entity.DataSource;
import com.trs.ai.ty.mq.entity.DataSourceSystemProperty;
import com.trs.ai.ty.mq.entity.TaskMonitoringMsgData;
import com.trs.ai.ty.mq.enums.MqTypeEnum;
import com.trs.ai.ty.mq.enums.ResourceConnectTypeEnum;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutionException;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 2021/5/13 17:07
 **/
public interface DataTracerService {

    /**
     * MQ
     * 调用grpc服务插入全链路监控数据
     *
     * @param paramsJson 数据正文
     * @param uuid       uuid
     * @param startTime  开始时间
     * @param endTime    结束时间
     * @param server     mq服务
     * @param topic      接收topic
     * @param group      接收group
     * @param topicId    目前是数据源id
     * @param mqType mq类型
     * @param sendSuccess       是否发送成功
     * <AUTHOR>
     * @since 2021/5/13 21:25
     */
    void insertDataConversion(String paramsJson, long uuid,
                              Long startTime, Long endTime,
                              String server, String topic,
                              String group, String topicId, MqTypeEnum mqType,
                              boolean sendSuccess);

    /**
     * Db
     * 调用grpc服务插入全链路监控数据
     *
     * @param paramsJson 数据正文
     * @param uuid       uuid
     * @param startTime  开始时间
     * @param endTime    结束时间
     * @param server     db服务
     * @param dataSourceId    数据源id
     * @param sendSuccess       是否发送成功
     * <AUTHOR>
     * @since 2023/8/4 16:33
     */
    void insertDataConversionDb(String paramsJson, long uuid,
                              Long startTime, Long endTime,
                              String server, Integer dataSourceId, ResourceConnectTypeEnum resourceConnectTypeEnum,
                              boolean sendSuccess);

    /**
     * 调用grpc服务插入任务监控数据
     *
     * @param dataAccessTraces dataAccessTrace
     */
    void insertTaskMonitoringDb(List<DataAccessTrace> dataAccessTraces) throws InterruptedException;

    /**
     * 处理异常
     *
     */
     void sendTracer(List<TaskMonitoringMsgData> taskMonitoringMsgDatas);

    /**
     * 验证数据类型
     *
     * @param data 数据
     * @param properties 数据源配置
     */
     void verifyData(Map<String,Object> data , List<DataSourceSystemProperty> properties, DataSource of, Long recordId, String batchNo, List<TaskMonitoringMsgData> taskMonitoringMsgDataList) throws BizException, ExecutionException, InterruptedException;

    boolean verifyAll(Map<String,Object> map, List<DataSourceSystemProperty> properties,DataSource of,Long recordId,String batchNo,List<TaskMonitoringMsgData> taskMonitoringMsgDataList);
}
