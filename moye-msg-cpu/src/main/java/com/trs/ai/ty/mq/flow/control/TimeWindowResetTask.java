package com.trs.ai.ty.mq.flow.control;

import com.trs.ai.ty.base.schedule.ScheduleTask;
import com.trs.ai.ty.mq.entity.DataSourceFlowControlEtcdValue;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.Trigger;
import org.springframework.scheduling.support.PeriodicTrigger;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @date 2023-12-23 16:27
 */
@Slf4j
public class TimeWindowResetTask implements ScheduleTask {

    private final String taskId;

    private final String taskName;

    private final int dataSourceId;

    private final long executePeriodMillis;

    private final FlowControlManager flowControlManager;

    public TimeWindowResetTask(long executePeriodMillis, DataSourceFlowControlEtcdValue etcdValue, FlowControlManager flowControlManager){
        if (executePeriodMillis < 1){
            throw new IllegalArgumentException("执行周期不合规");
        }
        if (Objects.isNull(etcdValue.getDataSourceId())){
            throw new IllegalArgumentException("数据源id不允许为空");
        }
        this.executePeriodMillis = executePeriodMillis;
        taskId = getTaskId(etcdValue.getDataSourceId());
        taskName = "流量控制[" + etcdValue.getDataSourceName() + "]定时任务";
        dataSourceId = etcdValue.getDataSourceId();
        this.flowControlManager = flowControlManager;
    }

    @Override
    public String taskId() {
        return taskId;
    }

    @Override
    public String taskName() {
        return taskName;
    }

    @Override
    public Trigger trigger() {
        return new PeriodicTrigger(executePeriodMillis, TimeUnit.MILLISECONDS);
    }

    @Override
    public void run() {
        try{
            log.debug("【{}】准备重置流量控制管理器，数据源id：{}", taskName(), dataSourceId);
            flowControlManager.resetTimeWindow(dataSourceId);
        }
        catch (Throwable e){
            log.error("重置流量控制管理器发生异常", e);
        }
    }

    public static String getTaskId(Integer dataSourceId){
        return "流量控制定时任务-" + dataSourceId;
    }
}
