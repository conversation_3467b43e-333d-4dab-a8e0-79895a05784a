package com.trs.ai.ty.mq.service.impl;

import com.alibaba.fastjson.JSON;
import com.trs.ai.ty.base.constant.enums.DataTracerTypeEnum;
import com.trs.ai.ty.base.entity.exception.BizException;
import com.trs.ai.ty.base.entity.po.TracerData;
import com.trs.ai.ty.base.service.MonitorBufferService;
import com.trs.ai.ty.base.utils.SnowflakeIdUtil;
import com.trs.ai.ty.grpc.client.DataTracerRpcClient;
import com.trs.ai.ty.grpc.entity.DataAccessTrace;
import com.trs.ai.ty.mq.config.entity.IPBean;
import com.trs.ai.ty.mq.dao.DataSourceMapper;
import com.trs.ai.ty.mq.entity.DataSource;
import com.trs.ai.ty.mq.entity.DataSourceSystemProperty;
import com.trs.ai.ty.mq.entity.TaskMonitoringMsgData;
import com.trs.ai.ty.mq.enums.DataAccessTraceTypeEnum;
import com.trs.ai.ty.mq.enums.MqTypeEnum;
import com.trs.ai.ty.mq.enums.ResourceConnectTypeEnum;
import com.trs.ai.ty.mq.service.DataTracerService;
import com.trs.ai.ty.mq.utils.ThreadLocalUtil;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 2021/5/13 17:06
 **/
@Slf4j
@Service
public class DataTracerServiceImpl implements DataTracerService {

    @Resource
    private DataTracerRpcClient dataTracerRpcClient;

    @Resource
    private DataSourceMapper dataSourceMapper;

    @Resource
    private IPBean podIp;

    private final Map<Integer, String> dataSourceNameCache = new ConcurrentHashMap<>(16);

    private final Lock lock = new ReentrantLock();

    @Autowired
    private MonitorBufferService monitorBufferService;

    @Override
    public void insertDataConversion(String paramsJson, long uuid,
                                     Long startTime, Long endTime,
                                     String server, String topic,
                                     String group, String topicId, MqTypeEnum mqType,
                                     boolean sendSuccess) {
        TracerData tracerData = new TracerData();
        tracerData.setMsgContent(paramsJson);
        tracerData.setRecordId(uuid);
        tracerData.setProcessId(SnowflakeIdUtil.newId());
        tracerData.setReceiveGroup(group);
        tracerData.setReceiveMqPath(server);
        tracerData.setReceiveTopic(topic);
        tracerData.setDataSourceId(Integer.valueOf(topicId));
        tracerData.setDataSourceName(getDataSourceName(tracerData.getDataSourceId()));
        tracerData.setStartTime(LocalDateTime.ofInstant(Instant.ofEpochMilli(startTime), ZoneId.systemDefault()));
        tracerData.setEndTime(LocalDateTime.ofInstant(Instant.ofEpochMilli(endTime), ZoneId.systemDefault()));
        tracerData.setStorageTime(LocalDateTime.now());
        tracerData.setProcessingOrder(1);
        tracerData.setServiceName("msg-center");
        tracerData.setIsError((!sendSuccess) ? 1 : 0);
        if (!sendSuccess && Objects.nonNull(ThreadLocalUtil.get(ThreadLocalUtil.ERROR_MSG))) {
            tracerData.setErrorMsg(ThreadLocalUtil.get(ThreadLocalUtil.ERROR_MSG).toString());
        }
        tracerData.setNodeName("node");
        tracerData.setParentProcessingNode(DataTracerTypeEnum.DATA_ACCESS.getNodeType());
        tracerData.setProcessingName("接入数据");
        tracerData.setProcessingType("非数据处理");
        tracerData.setReceiveMqType(mqType.getName());
        tracerData.setProcessingTime(endTime - startTime);
        tracerData.setMasterNode(DataTracerTypeEnum.DATA_ACCESS.getNodeType());
        tracerData.setPodIp(podIp.getPodIp());
//        List<TracerData> tracerDataList = new ArrayList<>();
//        tracerDataList.add(tracerData);
//        SendTaskMonitoringDbQueueHelper.putOldDataBlocking(tracerDataList);
//        dataTracerRpcClient.sendTracerMsg(tracerDataList);
//        kafkaSender.sendTracerMsg(tracerData);
//        if (monitorBufferQueue.getTracerEnable()){
//            monitorCache.putTracer(tracerData);
//        }
        if (monitorBufferService.getTracerEnable()){
            monitorBufferService.push(tracerData);
        }
    }

    private String getDataSourceName(Integer dataSourceId){
        if (dataSourceNameCache.containsKey(dataSourceId)){
            return dataSourceNameCache.get(dataSourceId);
        }
        try{
            lock.lock();
            if (dataSourceNameCache.containsKey(dataSourceId)){
                return dataSourceNameCache.get(dataSourceId);
            }
            String dataSourceName = dataSourceMapper.getDataSourceName(dataSourceId);
            if (Objects.isNull(dataSourceName)){
                dataSourceName = "";
            }
            dataSourceNameCache.put(dataSourceId, dataSourceName);
            return dataSourceName;
        }
        finally{
            lock.unlock();
        }
    }

    @Override
    public void insertDataConversionDb(String paramsJson, long uuid, Long startTime, Long endTime, String server, Integer dataSourceId, ResourceConnectTypeEnum resourceConnectTypeEnum, boolean sendSuccess) {
        TracerData tracerData = new TracerData();
        tracerData.setMsgContent(paramsJson);
        tracerData.setRecordId(uuid);
        tracerData.setProcessId(SnowflakeIdUtil.newId());
        tracerData.setReceiveMqPath(server);
        tracerData.setDataSourceId(dataSourceId);
        tracerData.setDataSourceName(getDataSourceName(tracerData.getDataSourceId()));
        tracerData.setStartTime(LocalDateTime.ofInstant(Instant.ofEpochMilli(startTime), ZoneId.systemDefault()));
        tracerData.setEndTime(LocalDateTime.ofInstant(Instant.ofEpochMilli(endTime), ZoneId.systemDefault()));
        tracerData.setStorageTime(LocalDateTime.now());
        tracerData.setProcessingOrder(1);
        tracerData.setServiceName("msg-center");
        tracerData.setIsError((!sendSuccess) ? 1 : 0);
        if (!sendSuccess && Objects.nonNull(ThreadLocalUtil.get(ThreadLocalUtil.ERROR_MSG))) {
            tracerData.setErrorMsg(ThreadLocalUtil.get(ThreadLocalUtil.ERROR_MSG).toString());
        }
        tracerData.setNodeName("node");
        tracerData.setParentProcessingNode(DataTracerTypeEnum.DATA_ACCESS.getNodeType());
        tracerData.setProcessingName("接入数据");
        tracerData.setProcessingType("非数据处理");
        tracerData.setProcessingTime(endTime - startTime);
        tracerData.setMasterNode(DataTracerTypeEnum.DATA_ACCESS.getNodeType());
        tracerData.setPodIp(podIp.getPodIp());
//        List<TracerData> tracerDataList = new ArrayList<>();
//        tracerDataList.add(tracerData);
//        SendTaskMonitoringDbQueueHelper.putOldDataBlocking(tracerDataList);
//        dataTracerRpcClient.sendTracerMsg(tracerDataList);
//        kafkaSender.sendTracerMsg(tracerData);
//        if (monitorBufferQueue.getTracerEnable()){
//            monitorCache.putTracer(tracerData);
//        }
        if (monitorBufferService.getTracerEnable()){
            monitorBufferService.push(tracerData);
        }
    }

    @Override
    public void insertTaskMonitoringDb(List<DataAccessTrace> dataAccessTraces){
        try{
//            SendTaskMonitoringDbQueueHelper.putDataBlocking(dataAccessTraces);
//            kafkaSender.sendTaskMonitoringMsg(dataAccessTraces);
//            if (monitorBufferQueue.getTracerEnable()){
//                monitorBufferQueue.push(dataAccessTraces);
//            }
        }catch (Exception e){
            log.error("tracer消息放入队列失败!");
        }
    }

    @Override
    public void sendTracer(List<TaskMonitoringMsgData> taList){
        List<DataAccessTrace> traceList = new ArrayList<>(taList.size());
        for(TaskMonitoringMsgData taskMonitoringMsgData : taList){
            if(Objects.isNull(taskMonitoringMsgData.getRecordId())){
                taskMonitoringMsgData.setRecordId(SnowflakeIdUtil.newId());
            }
            traceList.add(record(taskMonitoringMsgData));
        }
        insertTaskMonitoringDb(traceList);
        taList.clear();
    }

    private DataAccessTrace record(TaskMonitoringMsgData taskMonitoringMsgData) {
        DataAccessTraceTypeEnum currentStepEnum = DataAccessTraceTypeEnum.valueOfName(taskMonitoringMsgData.getCurrentStep());
        boolean isSuccess = !taskMonitoringMsgData.isError();
        String status;
        String errorMessage;
        String errorMsg = taskMonitoringMsgData.getErrorMsg();
        Long parentId;
        switch (currentStepEnum) {
            case GET:
                status = isSuccess ? "成功采集数据" : "采集数据失败";
                errorMessage = isSuccess ? null : errorMsg;
                parentId = null;
                break;
            case PROPERTY_VERIFY:
                status = isSuccess ? "类型验证通过" : "类型验证未通过";
                errorMessage = isSuccess ? null : errorMsg;
                parentId = taskMonitoringMsgData.getRecordId();
                break;
            case NON_NULL_VERIFY:
                status = isSuccess ? "非空验证通过" : "非空验证未通过";
                errorMessage = isSuccess ? null : errorMsg;
                parentId = taskMonitoringMsgData.getRecordId();
                break;
            case LONG_VERIFY:
                status = isSuccess ? "长度验证通过" : "长度验证未通过";
                errorMessage = isSuccess ? null : errorMsg;
                parentId = taskMonitoringMsgData.getRecordId();
                break;
            case TURN:
                status = isSuccess ? "验证数据通过" : "验证数据失败";
                errorMessage = isSuccess ? null : errorMsg;
                parentId = null;
                break;
            case SEND:
                status = isSuccess ? "成功发送数据到ty-engine" : "发送数据到ty-engine失败";
                errorMessage = isSuccess ? null : errorMsg;
                parentId = null;
                break;
            default:
                status = "Unknown Step";
                parentId = taskMonitoringMsgData.getRecordId();
                errorMessage = null;
                break;
        }
        DataAccessTrace dataAccessTrace = ofDataAccessTrace(taskMonitoringMsgData.getRecordId(), taskMonitoringMsgData.getDataSourceConfigBeanNew(),
                podIp.getPodIp(), status, errorMessage, taskMonitoringMsgData.getBeginTime(), taskMonitoringMsgData.getEndTime(), currentStepEnum.getName(),
                taskMonitoringMsgData.getData(), taskMonitoringMsgData.getBatchNo(), parentId);
        dataAccessTrace.setDataId(String.valueOf(taskMonitoringMsgData.getRecordId()));
//        insertTaskMonitoringDb(List.of(dataAccessTrace));
        return dataAccessTrace;
    }

    public static DataAccessTrace ofDataAccessTrace(Long recordId, DataSource dataSourceConfigBean, String nodeInfo, String result,
                                                    String errMsg, LocalDateTime beginTime, LocalDateTime endTime,
                                                    String currentStep, String data, String batchNo, Long parentId){
        DataAccessTrace dataAccessTrace = new DataAccessTrace();
        dataAccessTrace.setRecordId(recordId);
        dataAccessTrace.setDataSourceId(dataSourceConfigBean.getId());
        dataAccessTrace.setDataSourceName(dataSourceConfigBean.getName());
        dataAccessTrace.setDataSourceType(dataSourceConfigBean.getReceiveClassification());
        dataAccessTrace.setDataSourceConfig(JSON.toJSONString(dataSourceConfigBean));
        dataAccessTrace.setBeginTime(beginTime);
        dataAccessTrace.setEndTime(endTime);
        dataAccessTrace.setCurrentStep(currentStep);
        if(Objects.isNull(errMsg)){
            dataAccessTrace.setIfError(false); //没有错误信息因此正常
        }else{
            dataAccessTrace.setIfError(true);
            dataAccessTrace.setErrMsg(errMsg);
        }
        dataAccessTrace.setData(data);
        dataAccessTrace.setDataId(String.valueOf(recordId));
        dataAccessTrace.setBatchNo(batchNo);
        dataAccessTrace.setStepOrder(DataAccessTraceTypeEnum.getCode(currentStep));
        dataAccessTrace.setExecuteId(String.valueOf(dataSourceConfigBean.getExecuteId())); //未来会加上
        dataAccessTrace.setNodeInfo(nodeInfo);
        dataAccessTrace.setResult(result);
        dataAccessTrace.setParentId(String.valueOf(parentId));
        return dataAccessTrace;
    }

    @Override
    public void verifyData(Map<String,Object> data , List<DataSourceSystemProperty> properties,DataSource of,Long recordId,String batchNo,List<TaskMonitoringMsgData> taskMonitoringMsgDataList) throws BizException, ExecutionException, InterruptedException {
        //1.根据用户自己配置的属性类型验证是否能对上
        if(Objects.isNull(properties) || properties.isEmpty()){
            throw new BizException(DataAccessTraceTypeEnum.PROPERTY_VERIFY.getName() + ":配置的数据源映射关系为空!");
        }
//        data.entrySet().forEach(entry-> {
//            try {
//                doVerify(data, properties, of, recordId, batchNo, entry,taskMonitoringMsgDataList);
//            } catch (BizException e) {
//                log.error("执行数据验证失败 - {}",e.getMessage());
//            }
//        });
    }

    private void doVerify(Map<String, Object> data, List<DataSourceSystemProperty> properties, DataSource of, Long recordId, String batchNo, Map.Entry<String, Object> entry,List<TaskMonitoringMsgData> taskMonitoringMsgDataList) throws BizException {
        LocalDateTime verifyBeginTime = LocalDateTime.now();
        List<DataSourceSystemProperty> collect = properties.stream().filter(e -> entry.getKey().equals(e.getEnName())).collect(Collectors.toCollection(ArrayList::new));
        if(!collect.isEmpty()){
            DataSourceSystemProperty dataSourceProperty = collect.get(0);
            String type = dataSourceProperty.getType();
            try{
                if(Objects.isNull(entry.getValue()) || "".equals(entry.getValue())){
                    return;
                }
                judgeType(entry, type);
                TaskMonitoringMsgData of1 = TaskMonitoringMsgData.of(DataAccessTraceTypeEnum.PROPERTY_VERIFY.getName(), verifyBeginTime, of, false, null, JSON.toJSONString(data), Long.parseLong(recordId.toString()), batchNo, recordId.toString(), LocalDateTime.now());
                taskMonitoringMsgDataList.add(of1);
            }catch (Exception e){
                String msg = DataAccessTraceTypeEnum.PROPERTY_VERIFY.getName() + "errMsg:字段:" + dataSourceProperty.getZhName() + "(" + dataSourceProperty.getEnName() + ")" + dataSourceProperty.getType() +"类型转换失败";
                taskMonitoringMsgDataList.add(TaskMonitoringMsgData.of(DataAccessTraceTypeEnum.PROPERTY_VERIFY.getName(),verifyBeginTime, of,true,msg, JSON.toJSONString(data),Long.parseLong(recordId.toString()), batchNo, recordId.toString(),LocalDateTime.now()));
                taskMonitoringMsgDataList.add(TaskMonitoringMsgData.of(DataAccessTraceTypeEnum.TURN.getName(),verifyBeginTime, of,true,msg, JSON.toJSONString(data),Long.parseLong(recordId.toString()), batchNo, recordId.toString(),LocalDateTime.now()));
                throw new BizException(e.toString());
            }
            LocalDateTime verifyLongBeginTime = LocalDateTime.now();
            try{
                if(Objects.isNull(entry.getValue())){
                    return;
                }
                //TODO 校验最大值 最小值
                /*Double attrMax = dataSourceProperty.getAttrMax();
                Double attrMin = dataSourceProperty.getAttrMin();
                if(Objects.nonNull(attrMax) && Objects.nonNull(attrMin)){
                    verifyLength(entry, attrMax, attrMin, type);
                }*/
                taskMonitoringMsgDataList.add(TaskMonitoringMsgData.of(DataAccessTraceTypeEnum.LONG_VERIFY.getName(),verifyLongBeginTime, of,false,null,JSON.toJSONString(data),Long.parseLong(recordId.toString()), batchNo, recordId.toString(),LocalDateTime.now()));
            }catch (Exception e){
                String mes = DataAccessTraceTypeEnum.LONG_VERIFY.getName() + "errMsg:字段:" + dataSourceProperty.getZhName() + "(" + dataSourceProperty.getEnName() + ")" + dataSourceProperty.getType() +"长度验证不位于最大值最小值区间";
                taskMonitoringMsgDataList.add(TaskMonitoringMsgData.of(DataAccessTraceTypeEnum.LONG_VERIFY.getName(),verifyBeginTime, of,true,mes, JSON.toJSONString(data),Long.parseLong(recordId.toString()), batchNo, recordId.toString(),LocalDateTime.now()));
                taskMonitoringMsgDataList.add(TaskMonitoringMsgData.of(DataAccessTraceTypeEnum.TURN.getName(),verifyBeginTime, of,true,mes, JSON.toJSONString(data),Long.parseLong(recordId.toString()), batchNo, recordId.toString(),LocalDateTime.now()));
                throw new BizException(e.toString());
            }
            LocalDateTime verifyNonNullBeginTime = LocalDateTime.now();
            try{
                Integer allowNull = dataSourceProperty.getAllowNull();
                if(Objects.nonNull(allowNull) && allowNull == 0 && (Objects.isNull(entry.getValue()) || "".equals(entry.getValue()))){
                    //非空
                    throw new BizException("非空字段验证失败");
                }
                taskMonitoringMsgDataList.add(TaskMonitoringMsgData.of(DataAccessTraceTypeEnum.NON_NULL_VERIFY.getName(),verifyNonNullBeginTime, of,false,null,JSON.toJSONString(data),Long.parseLong(recordId.toString()), batchNo, recordId.toString(),LocalDateTime.now()));
            }catch (Exception e){
                String msg = DataAccessTraceTypeEnum.NON_NULL_VERIFY.getName() + "errMsg:字段:" + dataSourceProperty.getZhName() + "(" + dataSourceProperty.getEnName() + ")" + dataSourceProperty.getType() +"长度验证不位于最大值最小值区间";
                taskMonitoringMsgDataList.add(TaskMonitoringMsgData.of(DataAccessTraceTypeEnum.NON_NULL_VERIFY.getName(),verifyBeginTime, of,true,msg, JSON.toJSONString(data),Long.parseLong(recordId.toString()), batchNo, recordId.toString(),LocalDateTime.now()));
                taskMonitoringMsgDataList.add(TaskMonitoringMsgData.of(DataAccessTraceTypeEnum.TURN.getName(),verifyBeginTime, of,true,msg, JSON.toJSONString(data),Long.parseLong(recordId.toString()), batchNo, recordId.toString(),LocalDateTime.now()));
                throw new BizException(e.toString());
            }
        }else{
            taskMonitoringMsgDataList.add(TaskMonitoringMsgData.of(DataAccessTraceTypeEnum.TURN.getName(),verifyBeginTime, of,true,"无法用enName映射对应关系,验证失败", JSON.toJSONString(data),Long.parseLong(recordId.toString()), batchNo, recordId.toString(),LocalDateTime.now()));
        }
        taskMonitoringMsgDataList.add(TaskMonitoringMsgData.of(DataAccessTraceTypeEnum.TURN.getName(),verifyBeginTime, of,false,null, JSON.toJSONString(data),Long.parseLong(recordId.toString()), batchNo, recordId.toString(),LocalDateTime.now()));
    }

    @Override
    public boolean verifyAll(Map<String,Object> map, List<DataSourceSystemProperty> properties,DataSource of,Long recordId,String batchNo,List<TaskMonitoringMsgData> taskMonitoringMsgDataList){
        LocalDateTime verifyBeginTime = LocalDateTime.now();
        taskMonitoringMsgDataList.add(TaskMonitoringMsgData.of(DataAccessTraceTypeEnum.PROPERTY_VERIFY.getName(),verifyBeginTime, of,false,null, JSON.toJSONString(map),Long.parseLong(recordId.toString()), batchNo, recordId.toString(),LocalDateTime.now()));
        taskMonitoringMsgDataList.add(TaskMonitoringMsgData.of(DataAccessTraceTypeEnum.LONG_VERIFY.getName(),verifyBeginTime, of,false,null, JSON.toJSONString(map),Long.parseLong(recordId.toString()), batchNo, recordId.toString(),LocalDateTime.now()));
        taskMonitoringMsgDataList.add(TaskMonitoringMsgData.of(DataAccessTraceTypeEnum.NON_NULL_VERIFY.getName(),verifyBeginTime, of,false,null, JSON.toJSONString(map),Long.parseLong(recordId.toString()), batchNo, recordId.toString(),LocalDateTime.now()));
        taskMonitoringMsgDataList.add(TaskMonitoringMsgData.of(DataAccessTraceTypeEnum.TURN.getName(),verifyBeginTime, of,false,null, JSON.toJSONString(map),Long.parseLong(recordId.toString()), batchNo, recordId.toString(),LocalDateTime.now()));
//        try{
//            verifyData(map, properties,of,recordId,batchNo,taskMonitoringMsgDataList);
//            return false;
//        }catch (Exception exception){
//            log.warn(exception.toString());
//            return true;
//        }
        return false;
    }

    private static void verifyLength(Map.Entry<String, Object> entry, Double attrMax, Double attrMin, String type) throws BizException, ParseException {
        switch (type.toLowerCase()){
            case "int":
            case "double":
            case "float":
            case "long":
                if((Double) entry.getValue() > attrMax || (Double) entry.getValue() < attrMin){
                    throw new BizException();
                }
                break;
            case "string":
                int length = entry.getValue().toString().length();
                if(length > attrMax || length < attrMin){
                    throw new BizException();
                }
                break;
            case "date":
                SimpleDateFormat format = new SimpleDateFormat("yyyyMMdd");
                Date parse = format.parse(entry.getValue().toString());
                long time = parse.getTime();
                if(time > attrMax || time < attrMin){
                    throw new BizException();
                }
                break;
            case "datetime":
                SimpleDateFormat formatDateTime = new SimpleDateFormat("yyyyMMddhhmmss");
                Date parseDateTime = formatDateTime.parse(entry.getValue().toString());
                long dateTime = parseDateTime.getTime();
                if(dateTime > attrMax || dateTime < attrMin){
                    throw new BizException();
                }
                break;
            default:
        }
    }

    private static void judgeType(Map.Entry<String, Object> entry, String type) throws Exception {
        switch (type.toLowerCase()){
           case "int":
                Integer.parseInt(entry.getValue().toString());
                break;
           case "float":
               Float.parseFloat(entry.getValue().toString());
               break;
            case "string":
               if(entry.getValue() instanceof String){
               }else{
                   throw new Exception("非字符串类型");
               }
               break;
            case "double":
                Double.parseDouble(entry.getValue().toString());
                break;
            case "long":
                Long.parseLong(entry.getValue().toString());
                break;
            case "boolean":
                if(entry.getValue() instanceof Boolean){
                }else{
                    throw new Exception("非布尔值类型");
                }
                break;
            case "char":
                if(entry.getValue() instanceof Character){
                }else{
                    throw new Exception("非char类型");
                }
                break;
            case "entity":
            case "date":
                //年月日
                SimpleDateFormat format = new SimpleDateFormat("yyyyMMdd");
                format.parse(entry.getValue().toString());
            case "datetime":
                //年月日时分秒
                SimpleDateFormat formatDateTime = new SimpleDateFormat("yyyyMMddhhmmss");
                formatDateTime.parse(entry.getValue().toString());
                break;
           }
    }
}
