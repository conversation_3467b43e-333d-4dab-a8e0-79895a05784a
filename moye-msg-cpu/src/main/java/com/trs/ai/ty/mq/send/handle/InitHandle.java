package com.trs.ai.ty.mq.send.handle;

import com.trs.ai.ty.base.utils.SnowflakeIdUtil;
import com.trs.ai.ty.base.utils.StringUtil;
import com.trs.ai.ty.mq.constant.SendConstants;
import com.trs.ai.ty.mq.send.HandleOrder;
import com.trs.ai.ty.mq.send.SendInfo;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Objects;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @date 2024-05-26 16:27
 */
@Slf4j
@Component
public class InitHandle implements BeforeHandle{

    @Override
    public boolean beforeHandle(SendInfo sendInfo) {
        setRecordId(sendInfo);
        sendInfo.putStore(SendConstants.BEGIN_TIME_KEY, LocalDateTime.now());
        sendInfo.putStore(SendConstants.START_TIME_MILLIS_KEY, System.currentTimeMillis());
        sendInfo.putStore(SendConstants.TASK_MONITOR_DATA_LIST_KEY, new ArrayList<>());
        sendInfo.putStore(SendConstants.TASK_MONITOR_DATA_SOURCE_KEY, sendInfo.getDataSource());
        return true;
    }

    @Override
    public int beforeOrder() {
        return HandleOrder.BUILT_IN_HIGH;
    }

    private void setRecordId(SendInfo sendInfo){
        long recordId = buildRecordId(sendInfo.getData().get(SendConstants.RECORD_ID_KEY));
        sendInfo.getData().put(SendConstants.RECORD_ID_KEY, recordId);
        sendInfo.putStore(SendConstants.RECORD_ID_KEY, recordId);
    }

    private long buildRecordId(Object recordId) {
        long uuid;
        if (Objects.isNull(recordId) || StringUtil.isEmpty(recordId.toString())) {
            uuid = SnowflakeIdUtil.newId();
        } else {
            uuid = Long.parseLong(recordId.toString());
        }
        return uuid;
    }
}
