package com.trs.ai.ty.mq.utils;

import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

/**
 * 线程上下文
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2022/01/28 17:29
 */
public class ThreadLocalUtil {
    private static final ThreadLocal<Map<String, Object>> threadLocal = ThreadLocal.withInitial(() -> new HashMap<>(10));
    public static final String ERROR_MSG = "errorMsg";

    public static Map<String, Object> getThreadLocal() {
        return threadLocal.get();
    }

    public static <T> T get(String key) {
        Map<String, Object> map = threadLocal.get();
        return get(key, null);
    }

    @SuppressWarnings("unchecked")
    public static <T> T get(String key, T defaultValue) {
        Map<String, Object> map = threadLocal.get();
        return (T) Optional.ofNullable(map.get(key)).orElse(defaultValue);
    }

    public static void set(String key, Object value, boolean append) {
        Map<String, Object> map = threadLocal.get();
        if (append && map.containsKey(key)) {
            map.put(key, map.get(key).toString() + value.toString());
        } else {
            map.put(key, value);
        }
    }

    public static void set(String key, Object value) {
        Map<String, Object> map = threadLocal.get();
        map.put(key, value);
    }

    public static void set(Map<String, Object> keyValueMap) {
        Map<String, Object> map = threadLocal.get();
        map.putAll(keyValueMap);
    }

    public static void remove() {
        threadLocal.remove();
    }

    @SuppressWarnings("unchecked")
    public static <T> T remove(String key) {
        Map<String, Object> map = threadLocal.get();
        return (T) map.remove(key);
    }

    public static void clear() {
        threadLocal.get().clear();
    }
}
