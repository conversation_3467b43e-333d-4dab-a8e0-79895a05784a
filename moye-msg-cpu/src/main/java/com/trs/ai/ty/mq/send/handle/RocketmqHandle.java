package com.trs.ai.ty.mq.send.handle;

import com.trs.ai.ty.mq.enums.MqTypeEnum;
import com.trs.ai.ty.mq.send.HandleOrder;
import com.trs.ai.ty.mq.send.SendInfo;
import java.util.Map;
import java.util.Objects;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @date 2024-05-26 22:03
 */
@Slf4j
@Component
public class RocketmqHandle implements BeforeHandle{

    private static final String UTF8_TYPE = "org.apache.avro.util.Utf8";

    @Override
    public boolean beforeHandle(SendInfo sendInfo) {
        if (!MqTypeEnum.ROCKET.getName().equalsIgnoreCase(sendInfo.getDataSource().getReceiveClassification())){
            return true;
        }
        processForMap(sendInfo.getData());
        return true;
    }

    private void processForMap(Map<String, Object> map) {
        for (Map.Entry<String, Object> mapInfo : map.entrySet()) {
            if (Objects.isNull(mapInfo.getValue())) {
                map.put(mapInfo.getKey(), "");
            } else if (UTF8_TYPE.equals(mapInfo.getValue().getClass().getName())) {
                String value = mapInfo.getValue().toString();
                map.put(mapInfo.getKey(), value);
            }
        }
    }

    @Override
    public int beforeOrder() {
        return HandleOrder.NORMAL;
    }
}
