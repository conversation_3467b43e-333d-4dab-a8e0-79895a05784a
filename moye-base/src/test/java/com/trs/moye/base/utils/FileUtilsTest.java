package com.trs.moye.base.utils;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.trs.moye.base.common.utils.FileUtils;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

class FileUtilsTest {

    private final String testDir = "testDir";
    private final String testFile = "testDir/testFile.txt";

    @BeforeEach
    public void setUp() throws IOException {
        // 创建测试目录和文件
        String testSubDir = "testDir/subDir";
        Files.createDirectories(Paths.get(testSubDir));
        Files.createFile(Paths.get(testFile));
        String testSubFile = "testDir/subDir/testSubFile.txt";
        Files.createFile(Paths.get(testSubFile));
    }

    @AfterEach
    public void tearDown() throws IOException {
        // 清理测试环境
        if (Files.exists(Paths.get(testDir))) {
            FileUtils.deleteDirectory(testDir);
        }
    }

    @Test
    void testDeleteFile() throws IOException {
        assertTrue(Files.exists(Paths.get(testFile)));
        assertTrue(FileUtils.deleteFile(testFile));
        assertFalse(Files.exists(Paths.get(testFile)));
    }

    @Test
    void testDeleteDirectory() throws IOException {
        Path path = Paths.get(testDir);
        assertTrue(Files.exists(path));
        assertTrue(FileUtils.deleteDirectory(testDir));
        assertFalse(Files.exists(path));
    }

    @Test
    void testGetFileDirectory() {
        String directoryPath = FileUtils.getFileDirectory(testFile);
        assertNotNull(directoryPath);
        assertEquals(Paths.get(testDir).toString(), directoryPath);
    }
}