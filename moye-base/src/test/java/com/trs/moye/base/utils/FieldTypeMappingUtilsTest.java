package com.trs.moye.base.utils;

import com.trs.moye.base.common.utils.FieldTypeMappingUtils;
import com.trs.moye.base.data.connection.enums.ConnectionType;
import com.trs.moye.base.common.enums.FieldType;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

/**
 * <AUTHOR>
 * @since 2024-11-12 10:25
 */
class FieldTypeMappingUtilsTest {

    @DisplayName("括号有1个数字的类型")
    @Test
    void getFieldTypeBracketOneNumber() {

        Assertions.assertEquals(FieldType.INT, FieldTypeMappingUtils.getFieldType(ConnectionType.ORACLE, "number(8)"));

        Assertions.assertEquals(FieldType.INT, FieldTypeMappingUtils.getFieldType(ConnectionType.ORACLE, "NUMBER(8 )"));
        Assertions.assertEquals(FieldType.INT, FieldTypeMappingUtils.getFieldType(ConnectionType.ORACLE, "number( 8)"));
        Assertions.assertEquals(FieldType.INT,
            FieldTypeMappingUtils.getFieldType(ConnectionType.ORACLE, "number(  8  )"));
    }

    @DisplayName("括号有两个数字的类型")
    @Test
    void getFieldTypeBracketTwoNumber() {

        Assertions.assertEquals(FieldType.DOUBLE,
            FieldTypeMappingUtils.getFieldType(ConnectionType.ORACLE, "number(20,5)"));

        Assertions.assertEquals(FieldType.DOUBLE,
            FieldTypeMappingUtils.getFieldType(ConnectionType.ORACLE, "NUMBER(20,5 )"));
        Assertions.assertEquals(FieldType.DOUBLE,
            FieldTypeMappingUtils.getFieldType(ConnectionType.ORACLE, "number(20, 5)"));
        Assertions.assertEquals(FieldType.DOUBLE,
            FieldTypeMappingUtils.getFieldType(ConnectionType.ORACLE, "number(20, 5 )"));

        Assertions.assertEquals(FieldType.DOUBLE,
            FieldTypeMappingUtils.getFieldType(ConnectionType.ORACLE, "number(20 ,5 )"));
        Assertions.assertEquals(FieldType.DOUBLE,
            FieldTypeMappingUtils.getFieldType(ConnectionType.ORACLE, "number(20 , 5)"));
        Assertions.assertEquals(FieldType.DOUBLE,
            FieldTypeMappingUtils.getFieldType(ConnectionType.ORACLE, "number(20 , 5 )"));

        Assertions.assertEquals(FieldType.DOUBLE,
            FieldTypeMappingUtils.getFieldType(ConnectionType.ORACLE, "number( 20,5 )"));
        Assertions.assertEquals(FieldType.DOUBLE,
            FieldTypeMappingUtils.getFieldType(ConnectionType.ORACLE, "number( 20, 5)"));
        Assertions.assertEquals(FieldType.DOUBLE,
            FieldTypeMappingUtils.getFieldType(ConnectionType.ORACLE, "number( 20, 5 )"));

        Assertions.assertEquals(FieldType.DOUBLE,
            FieldTypeMappingUtils.getFieldType(ConnectionType.ORACLE, "number( 20 ,5 )"));
        Assertions.assertEquals(FieldType.DOUBLE,
            FieldTypeMappingUtils.getFieldType(ConnectionType.ORACLE, "number( 20 , 5)"));
        Assertions.assertEquals(FieldType.DOUBLE,
            FieldTypeMappingUtils.getFieldType(ConnectionType.ORACLE, "number( 20 , 5 )"));
    }
}
