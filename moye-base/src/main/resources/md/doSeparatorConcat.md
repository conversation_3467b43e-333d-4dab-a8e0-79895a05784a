## 拼接字符串（带分隔符）

### 算子类型

通用算子

### 算子描述

将多个字符串按指定分隔符拼接成一个字符串。

### 输入参数说明

*   **字符串数组**

    *   英文名: `inputTexts`

    *   类型: `ARRAY(STRING)`

    *   描述: 需要拼接的字符串数组

    *   是否必填: 是

*   **分隔符**

    *   英文名: `separator`

    *   类型: `STRING`

    *   描述: 字符串间的分隔符

    *   是否必填: 是


### 输出参数说明

*   **拼接后的字符串**

    *   类型: `STRING`


### 示例场景

#### 场景 1：基础带分隔符拼接

##### 输入参数

| **参数** | **值** | **描述** |
| --- | --- | --- |
| inputTexts | \["Hello", "World"\] | 需要拼接的字符串数组 |
| separator | " " | 分隔符 |

##### 输出结果

| **参数** | **值** | **描述** |
| --- | --- | --- |
| root | Hello World | 拼接后的字符串 |

#### 场景 2：CSV格式生成

##### 输入参数

| **参数** | **值** | **描述** |
| --- | --- | --- |
| inputTexts | \["ID", "Name", "Age"\] | 需要拼接的字符串数组 |
| separator | "," | 分隔符 |

##### 输出结果

| **参数** | **值** | **描述** |
| --- | --- | --- |
| root | ID,Name,Age | 拼接后的CSV标题行 |

### 算子适用场景简要说明

适用于需要在字符串之间添加分隔符的场景，如生成以空格或逗号分隔的文本列表。

### 算子支持依赖

无特殊依赖。