## 提取微博当前发言人发言内容

### 算子类型

通用算子

### 算子描述

从微博中提取当前发言人的发言内容。

### 输入参数说明

*   **排除词表达式**

    *   英文名: `excludeWordsExpression`

    *   类型: `STRING`

    *   描述: 需要排除的关键词（正则表达式）

    *   是否必填: 是

*   **微博内容**

    *   英文名: `weiBoContent`

    *   类型: `STRING`

    *   描述: 完整的微博内容

    *   是否必填: 是


### 输出参数说明

*   **发言人内容**

    *   类型: `STRING`

    *   描述: 提取的发言人内容


### 示例场景

#### 场景 1：发言人内容提取

##### 数据

```json
{
  "excludeWordsExpression": "",
  "weiBoContent": "//@清风霁月-微尘:保持警惕，做好斗争准备。//@南海之声:#南海特快#【#美侦察船在南海高强度活动# 】自5月22日进入南海以来，美军「胜利」号海洋监视船在南沙群岛以北、黄岩岛以西及巴士海附近海域进行高强度侦察活动。「胜利」号于5月26-28日在南沙群岛以北活动，5月30日至6月4日行至黄岩岛西南海域活动，6月5日至8日在黄岩岛以北活动，目前行至黄岩岛以西海域。 （南海战略态势感知）"
}
```

##### 输入参数

| **参数** | **值** | **描述** |
| --- | --- | --- |
| excludeWordsExpression | excludeWordsExpression | 排除词表达式 |
| weiBoContent | weiBoContent | 微博内容 |

##### 输出结果

| **参数** | **值** | **描述** |
| --- | --- | --- |
| root | 保持警惕，做好斗争准备。 | 发言人内容 |

### 算子适用场景简要说明

适用于需要提取微博中发言人具体发言内容的场景，分析其言论。

### 算子支持依赖

无特殊依赖。