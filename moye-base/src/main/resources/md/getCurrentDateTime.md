## 获取当前日期时间

### 算子类型

通用算子

### 算子描述

获取当前日期时间，并根据指定格式字符串格式化。

### 输入参数说明

*   **格式字符串**

    *   英文名: `format`

    *   类型: `STRING`

    *   描述: 日期时间格式（如YYYY-MM-dd）

    *   是否必填: 是


### 输出参数说明

*   **格式化时间字符串**

    *   类型: `STRING`


### 示例场景

#### 场景 1：标准日期格式

##### 数据

```json
{
  "format": "YYYY-MM-dd HH:mm:ss"
}
```

##### 输入参数

| **参数** | **值** | **描述** |
| --- | --- | --- |
| format | YYYY-MM-dd HH:mm:ss | 日期格式 |

##### 输出结果

| **参数** | **值** | **描述** |
| --- | --- | --- |
| root | "2025-06-10 16:25:00" | 当前时间 |

### 算子适用场景简要说明

适用于需要获取当前时间并格式化的场景，如日志记录、报告生成。

### 算子支持依赖

无特殊依赖。