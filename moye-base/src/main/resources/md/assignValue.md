## 赋值

### 算子类型

通用算子

### 算子描述

该算子能将输入参数的值或固定值赋给输出参数，实现简单的数据传递和赋值操作。

### 输入参数说明

*   **待传递的字符串参数**

    *   英文名: valueStr

    *   类型: `**STRING**`

    *   描述: 需要传递的字符串值，可以是固定文本或变量

    *   是否必填: 是


### 输出参数说明

*   **传递的字符串**

    *   类型: `**STRING**`

    *   描述: 输出与输入相同的字符串值


### 示例场景

#### 场景 1：传递固定文本值

##### 数据

```json
{
  "data":"默认文本"
}
```

##### 输入参数

| **参数** | **值** | **描述** |
| --- | --- | --- |
| valueStr | data | 需要传递的字符串 |

##### 输出结果

| **参数** | **值** | **描述** |
| --- | --- | --- |
| root | "默认文本" | 传递的字符串值 |

### 算子适用场景简要说明

在流程中对变量进行简单赋值操作时使用，为后续处理提供基础数据。

### 算子支持依赖