## 关键词提取

### 算子类型

NLP算子

### 算子描述

依据输入文本和指定的关键词数量，运用专业算法精准提取文本中的关键信息，输出关键词列表。

### 输入参数说明

*   **输入文本**

    *   英文名: `inputText`

    *   类型: `STRING`

    *   描述: 需要提取关键词的文本

    *   是否必填: 是

*   **关键词数量**

    *   英文名: `keywordNum`

    *   类型: `INT`

    *   描述: 需要提取的关键词数量

    *   是否必填: 是


### 输出参数说明

*   **关键词列表**

    *   类型: `ARRAY(STRING)`

    *   描述: 提取的关键词数组


### 示例场景

#### 场景 1：新闻关键词提取

##### 数据

```json
{
  "content": "据最高人民检察院6月11日消息，十三届全国人大华侨委员会副主任委员罗保铭涉嫌受贿一案，由国家监察委员会调查终结，经最高人民检察院依法指定，由上海市人民检察院第一分院审查起诉。近日，上海市人民检察院第一分院已向上海市第一中级人民法院提起公诉。检察机关在审查起诉阶段，依法告知了被告人罗保铭享有的诉讼权利，并讯问了被告人，听取了辩护人的意见。检察机关起诉指控：被告人罗保铭利用担任天津市商业委员会主任、海南省委副书记、海南省省长、海南省委书记、十二届全国人大华侨委员会副主任委员等职务上的便利，为他人谋取利益，非法收受他人财物，数额特别巨大，依法应当以受贿罪追究其刑事责任。 发布于：北京市",
  "keywordNum": 5
}

```

##### 输入参数

| **参数** | **值** | **描述** |
| --- | --- | --- |
| inputText | content | 分析文本 |
| keywordNum | 5 | 关键词数量 |

##### 输出结果

| **参数** | **值** | **描述** |
| --- | --- | --- |
| root | \["罗保铭", "起诉", "最高人民检察院", "受贿罪", "审查"\] | 关键词列表 |

### 算子适用场景简要说明

### 在文本检索、信息摘要等场景中，能快速定位文本核心要点，提升信息处理与查找效率。

### 算子支持依赖

*   需要部署nlp-keyword服务