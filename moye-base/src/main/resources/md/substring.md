## 截取字符串

### 算子类型

通用算子

### 算子描述

从字符串中截取指定范围的子字符串，提取部分内容。

### 输入参数说明

*   **开始索引**

    *   英文名: `startIndex`

    *   类型: `INT`

    *   描述: 截取起始位置（从0开始）

    *   是否必填: 是

*   **结束索引**

    *   英文名: `endIndex`

    *   类型: `INT`

    *   描述: 截取结束位置（不包含）

    *   是否必填: 是

*   **输入文本**

    *   英文名: `inputText`

    *   类型: `STRING`

    *   描述: 原始文本

    *   是否必填: 是


### 输出参数说明

*   **截取的子字符串**

    *   类型: `STRING`


### 示例场景

#### 场景 1：基础字符串截取

##### 数据

```json
{
  "startIndex": 4,
  "endIndex": 8,
  "content": "默认值-第5字符"
}
```

##### 输入参数

| **参数** | **值** | **描述** |
| --- | --- | --- |
| startIndex | 4 | 截取起始位置 |
| endIndex | 8 | 截取结束位置 |
| inputText | content | 原始文本 |

##### 输出结果

| **参数** | **值** | **描述** |
| --- | --- | --- |
| root | 第5字 | 截取的子字符串 |

#### 场景 2：日期部分提取

##### 数据

```plaintext
{
  "startIndex": 0,
  "endIndex": 10,
  "timestamp": "2023-10-15T14:30:00Z"
}
```

##### 输入参数

| **参数** | **值** | **描述** |
| --- | --- | --- |
| startIndex | 0 | 截取起始位置 |
| endIndex | 10 | 截取结束位置 |
| inputText | timestamp | 原始文本 |

##### 输出结果

| **参数** | **值** | **描述** |
| --- | --- | --- |
| root | 2023-10-15 | 截取的日期部分 |

### 算子适用场景简要说明

适用于需要从字符串中提取特定部分的场景，如提取日期、序号等。

### 算子支持依赖

无特殊依赖。