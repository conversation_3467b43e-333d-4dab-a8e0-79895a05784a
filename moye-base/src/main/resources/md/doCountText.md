## 统计字数

### 算子类型

通用算子

### 算子描述

该算子可统计输入文本的字数，为文本长度相关的处理提供基础数据。

### 输入参数说明

*   **文本**

    *   英文名: `inputText`

    *   类型: `STRING`

    *   描述: 需要统计字数的文本

    *   是否必填: 是


### 输出参数说明

*   **文本字数**

    *   类型: `INT`

    *   描述: 文本字符数量


### 示例场景

#### 场景 1：基础字数统计

##### 输入参数

| **参数** | **值** | **描述** |
| --- | --- | --- |
| inputText | Hello World | 需要统计的文本 |

##### 输出结果

| **参数** | **值** | **描述** |
| --- | --- | --- |
| root | 11 | 文本字符数量 |

#### 场景 2：中英文混合字数统计

##### 输入参数

| **参数** | **值** | **描述** |
| --- | --- | --- |
| inputText | 你好，世界！Hello World! | 需要统计的文本 |

##### 输出结果

| **参数** | **值** | **描述** |
| --- | --- | --- |
| root | 19 | 文本字符数量 |

### 算子适用场景简要说明

需要统计文本字数时使用，如字数限制、文本长度分析等场景。

### 算子支持依赖

无特殊依赖。