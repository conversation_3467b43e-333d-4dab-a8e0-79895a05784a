## 去除文本中多余的标签

### 算子类型

通用算子

### 算子描述

去掉文本中多余的标签（去掉@，#开头的标签和全角（）及其内部的内容。备用规则：去除@开头的标签和单个#，全角左括号（ 全角逗号，全角右括号））

### 输入参数说明

*   **文本**

    *   英文名: `inputText`

    *   类型: `STRING`

    *   描述: 需要处理的文本

    *   是否必填: 是


### 输出参数说明

*   **处理后的文本**

    *   类型: `STRING`


### 示例场景

#### 场景 1：微博内容清理

##### 数据

```json
{
  "data": "《突然爱上了，怎么办//@浙江大学:#浙大心理系门口为何有一架退役飞机# #浙大教授谈心理学文理交融# 心理学也可以非常硬核，脑科学、人工智能、自动驾驶领域等硬科技都跟心理学有密切联系。比如在浙大心理系大楼门口，摆放着一架退役飞机。这架飞机是浙大心理学系工业心理学方向的实验器材，用来辅助工业心理学的学习。 心理学是文理交叉的学科，是连接人文与科技的桥梁。它可为科技赋‘心’，为人文赋‘理’。 #解密浙大王牌专业##宝藏大学安利计划# #浙江大学[超话]# 制作：查蒙 李俊元 柯溢能 [浙江大学的微博视频](http://t.cn/A6eKTmxP)》"
}
```

##### 输入参数

| **参数** | **值** | **描述** |
| --- | --- | --- |
| inputText | data | 需要处理的文本 |

##### 输出结果

```json
{
  "root":"《突然爱上了，怎么办//心理学也可以非常硬核，脑科学、人工智能、自动驾驶领域等硬科技都跟心理学有密切联系。比如在浙大心理系大楼门口，摆放着一架退役飞机。这架飞机是浙大心理学系工业心理学方向的实验器材，用来辅助工业心理学的学习。心理学是文理交叉的学科，是连接人文与科技的桥梁。它可为科技赋‘心’，为人文赋‘理’。制作：查蒙李俊元柯溢能浙江大学的微博视频》"
}
```

| **参数** | **值** | **描述** |
| --- | --- | --- |
| root | root | 清理后的文本 |

### 算子适用场景简要说明

处理包含大量标签的文本时使用，使文本内容更简洁易读。

### 算子支持依赖

无特殊依赖。