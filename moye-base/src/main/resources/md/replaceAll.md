## 替换字符

### 算子类型

通用算子

### 算子描述

替换字符串中的指定内容。匹配输入文本中满足正则表达式的部分用替换文本进行替换。

### 输入参数说明

*   **正则表达式**

    *   英文名: `regex`

    *   类型: `STRING`

    *   描述: 匹配模式的正则表达式

    *   是否必填: 是

*   **输入文本**

    *   英文名: `inputText`

    *   类型: `STRING`

    *   描述: 需要处理的文本

    *   是否必填: 是

*   **替换文本**

    *   英文名: `replacement`

    *   类型: `STRING`

    *   描述: 替换匹配内容的文本

    *   是否必填: 是


### 输出参数说明

*   **替换后的文本**

    *   类型: `STRING`


### 示例场景

#### 场景 1：基础文本替换

##### 数据

```json
{
  "regex": "example",
  "content": "This is an example text. example",
  "replacement": "sample"
}
```

##### 输入参数

| **参数** | **值** | **描述** |
| --- | --- | --- |
| regex | example | 正则表达式 |
| inputText | content | 原始文本 |
| replacement | sample | 替换文本 |

##### 输出结果

| **参数** | **值** | **描述** |
| --- | --- | --- |
| root | This is an sample text. sample | 替换后的文本 |

#### 场景 2：日期格式替换

##### 数据

```plaintext
{
  "regex": "(\d{4})-(\d{2})-(\d{2})",
  "content": "日期：2023-10-15",
  "replacement": "$2/$3/$1"
}
```

##### 输入参数

| **参数** | **值** | **描述** |
| --- | --- | --- |
| regex | (\d{4})-(\d{2})-(\d{2}) | 正则表达式 |
| inputText | content | 原始文本 |
| replacement | $2/$3/$1 | 替换文本 |

##### 输出结果

| **参数** | **值** | **描述** |
| --- | --- | --- |
| root | 日期：10/15/2023 | 替换后的文本 |

### 算子适用场景简要说明

适用于需要在文本中替换特定字符的场景，如修正文本格式错误。

### 算子支持依赖

无特殊依赖。