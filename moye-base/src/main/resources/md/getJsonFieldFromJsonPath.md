## jsonPath提取

### 算子类型

通用算子

### 算子描述

借助符合`jsonPath`规范的表达式，从复杂 JSON 数据中精准提取特定数据，支持多层嵌套结构，避免繁琐手动解析。

### 输入参数说明

*   **内容为json的文本**
    
    *   英文名: inputText
        
    *   类型: `STRING`
        
    *   描述: 输入的内容必须是严格符合 json 格式的文本，该文本包含待提取数据的 json 结构
        
    *   是否必填: 是
        
*   **json字段匹配路径**
    
    *   英文名: jsonPath
        
    *   类型: `STRING`
        
    *   描述: 遵循 jsonPath 规范的表达式，用于指定需要从 json 文本中提取的数据位置和范围
        
    *   是否必填: 是
        

### 输出参数说明

*   **提取到的内容**
    
    *   类型: `STRING`
        
    *   描述: 根据jsonPath提取到的数据
        

### 示例场景

#### 场景 1：从 JSON 对象中提取单个字段

##### 数据

```json
{
  "name": "json",
  "type": "data",
  "file_extension": ".json",
  "mime type": "application/json"
}
```

##### 输入参数

| 参数 | 值 | 描述 |
| --- | --- | --- |
| inputText | FULL\_JSON（整个json字符串） | 根据参数名称到数据中获取需要进行提取的JSON字符串 |
| jsonPath | $.name | 根据jsonPath提取数据 |

##### 输出结果

| 参数 | 值 | 描述 |
| --- | --- | --- |
| root | json | 根据参数名称到数据中获取需要进行提取的JSON字符串 |

#### 场景 2：从嵌套 JSON 对象中提取字段

##### 数据

```json
{
  "name": "json",
  "type": "data",
  "file_extension": ".json",
  "mime type": "application/json",
  "example": 
    {
      "operation": "提取 name 字段",
      "input": {
        "inputText": "name",
        "jsonPath": "$.name"
      },
      "output": "json"
    }
}
```

##### 输入参数

| 参数 | 值 | 描述 |
| --- | --- | --- |
| inputText | FULL\_JSON（整个json字符串） | 根据参数名称到数据中获取需要进行提取的JSON字符串 |
| jsonPath | $.example.operation | 根据jsonPath提取数据 |

##### 输出结果

| 参数 | 值 | 描述 |
| --- | --- | --- |
| root | 提取 name 字段 | 根据参数名称到数据中获取需要进行提取的JSON字符串 |

#### 场景 3：从 JSON 数组中提取元素

##### 数据

```json
{
  "example": [
    {
      "operation": "提取 name 字段",
      "input": {
        "inputText": "name",
        "jsonPath": "$.name"
      },
      "output": "json"
    },
    {
      "operation": "提取 type 字段",
      "input": {
        "inputText": "type",
        "jsonPath": "$.type"
      },
      "output": "json"
    }
  ]
}
```

##### 输入参数

| 参数 | 值 | 描述 |
| --- | --- | --- |
| inputText | FULL\_JSON（整个json字符串） | 根据参数名称到数据中获取需要进行提取的JSON字符串 |
| jsonPath | $.example\[1\].operation | 根据jsonPath提取数据 |

##### 输出结果

| 参数 | 值 | 描述 |
| --- | --- | --- |
| root | 提取 type 字段 | 根据参数名称到数据中获取需要进行提取的JSON字符串 |

### 算子适用场景简要说明

*   能够从 json 对象中提取数据，例如从包含多个键值对的对象中获取某个具体键对应的 value；
    
*   可以从 json 数组中提取数据，比如从数组中获取指定索引的元素或满足特定条件的元素。
    

### 算子支持依赖：

无特殊依赖 