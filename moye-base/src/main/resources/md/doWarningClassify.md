## 预警分类识别

### 算子类型

NLP算子

### 算子描述

预警分类识别，返回匹配到的预警分类列表

### 输入参数说明

*   **输入文本**

    *   英文名: `inputText`

    *   类型: `STRING`

    *   描述: 需要分类的文本

    *   是否必填: 是


### 输出参数说明

*   **分类结果列表**

    *   类型: `ARRAY(OBJECT)`

    *   属性:

        *   id: `INT` - 分类ID

        *   class1: `STRING` - 一级分类

        *   class2: `STRING` - 二级分类

        *   probability: `FLOAT` - 置信度

        *   keyword: `STRING` - 关键词


### 示例场景

#### 场景 1：金融违规预警

##### 数据

```json
{
  "content": "浙商银行股份有限公司杭州分行存在违法违规行为被罚款70万元 时任支行客户经理陈高平、 柴明明被处罚[rtt]浙商银行股份有限公司杭州分行存在违法违规行为被罚款70万元 时任支行客户经理陈高平、 柴明明被处罚"
}
```

##### 输入参数

| **参数** | **值** | **描述** |
| --- | --- | --- |
| inputText | content | 分析文本 |

##### 输出结果

```json
[
    {
        "id": 11,
        "class1": "经济领域",
        "class2": "宏观经济",
        "probability": 0.8950862
    },
    {
        "id": 13,
        "class1": "经济领域",
        "class2": "行业监管",
        "probability": 0.8914178
    },
    {
        "id": 13,
        "class1": "经济领域",
        "class2": "行业监管",
        "probability": 0.83744216
    },
    {
        "id": 6,
        "class1": "政府事务",
        "class2": "工作作风",
        "probability": 0.7494892
    },
    {
        "id": 22,
        "class1": "公共安全",
        "class2": "社会治安",
        "probability": 0.70684797
    }
]
```

| **参数** | **值** | **描述** |
| --- | --- | --- |
| root | FULL\_JSON | 分类结果 |

### 算子适用场景简要说明

通过科学分析实现风险超前反馈，为防范突发事件提供决策支持

### 算子支持依赖：

*   需要部署nlp-multi-classify服务

*   需要创建预警分类实体库（aity\_nlpFocusClass），库表中需要有实体数据