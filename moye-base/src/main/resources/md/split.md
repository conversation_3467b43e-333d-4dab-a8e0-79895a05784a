## 拆分数组
### 算子类型
通用算子
### 算子描述
根据分隔符将字符串拆分成数组。
### 输入参数说明
*   **输入文本**
    *   英文名: `inputText`
    *   类型: `STRING`
    *   描述: 需要拆分的文本
    *   是否必填: 是
*   **分隔符**
    *   英文名: `splitter`
    *   类型: `STRING`
    *   描述: 拆分使用的分隔符
    *   是否必填: 是
### 输出参数说明
*   **拆分后的数组**
    *   类型: `ARRAY(STRING)`
### 示例场景
#### 场景 1：CSV数据拆分
##### 输入参数
| 参数 | 值 | 描述 |
| --- | --- | --- |
| inputText | "apple,banana,orange" | 需要拆分的文本 |
| splitter | "," | 分隔符 |
##### 输出结果
| 参数 | 值 | 描述 |
| --- | --- | --- |
| root | ["apple","banana","orange"] | 拆分结果 |
### 算子适用场景简要说明
适用于将大字符串按相同规则分割成多个小部分。
### 算子支持依赖
无特殊依赖。 