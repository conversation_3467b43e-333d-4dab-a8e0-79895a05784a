## 去除字符

### 算子类型

通用算子

### 算子描述

使用正则表达式去除字符串中的指定字符。

### 输入参数说明

*   **正则表达式**

    *   英文名: `regex`

    *   类型: `STRING`

    *   描述: 匹配需要去除内容的正则表达式

    *   是否必填: 是

*   **输入文本**

    *   英文名: `inputText`

    *   类型: `STRING`

    *   描述: 需要处理的文本

    *   是否必填: 是


### 输出参数说明

*   **处理后的文本**

    *   类型: `STRING`


### 示例场景

#### 场景 1：移除特定单词

##### 数据

```json
{
  "regex": "ample",
  "content": "This is an example text. sample"
}
```

##### 输入参数

| **参数** | **值** | **描述** |
| --- | --- | --- |
| regex | "ample" | 需要移除的字符模式 |
| inputText | content | 原始文本 |

##### 输出结果

| **参数** | **值** | **描述** |
| --- | --- | --- |
| root | "This is an ex text. s" | 处理后的文本 |

#### 场景 2：清理特殊字符

##### 数据

```plaintext
{
  "regex": "[0-9]",
  "content": "订单号：A2023-4567 金额：$120.50"
}
```

##### 输入参数

| **参数** | **值** | **描述** |
| --- | --- | --- |
| regex | "\[0-9\]" | 匹配数字的正则 |
| inputText | content | 原始文本 |

##### 输出结果

| **参数** | **值** | **描述** |
| --- | --- | --- |
| root | "订单号：A- 金额：$." | 移除数字后的文本 |

### 算子适用场景简要说明

适用于清理文本中的多余字符或无关信息，确保文本简洁。

### 算子支持依赖

无特殊依赖。