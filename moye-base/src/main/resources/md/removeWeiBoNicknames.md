## 去除微博昵称

### 算子类型

通用算子

### 算子描述

去除微博文本中的昵称

### 输入参数说明

*   **文本**

    *   英文名: `inputText`

    *   类型: `STRING`

    *   描述: 包含昵称的微博文本

    *   是否必填: 是


### 输出参数说明

*   **处理后的文本**

    *   类型: `STRING`


### 示例场景

#### 场景 1：基础昵称去除

##### 数据

```json
{
  "content": "《[打call][打call][打call][努力]//@农民日报:习近平：吹灭别人的灯，并不会让自己更加光明；阻挡别人的路，也不会让自己行得更远#学而时习# [农民日报的微博视频](http://t.cn/A6eordM8)》"
}
```

##### 输入参数

| **参数** | **值** | **描述** |
| --- | --- | --- |
| inputText | content | 原始微博文本 |

##### 输出结果

```json
{
  "root":"《[打call][打call][打call][努力]习近平：吹灭别人的灯，并不会让自己更加光明；阻挡别人的路，也不会让自己行得更远#学而时习# 农民日报的微博视频》"
}
```

| **参数** | **值** | **描述** |
| --- | --- | --- |
| root | root | 清理后的文本 |

### 算子适用场景简要说明

适用于清理微博内容中的昵称信息，确保文本纯净。

### 算子支持依赖

无特殊依赖。