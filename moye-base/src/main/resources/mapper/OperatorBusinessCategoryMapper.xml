<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.trs.moye.base.data.model.dao.OperatorBusinessCategoryMapper">

    <select id="existByEnAbbr" resultType="java.lang.Boolean">
        select count(id)
        from operator_business_category
        where en_abbr = #{enAbbr}
    </select>

    <select id="existByZhName" resultType="java.lang.Boolean">
        select count(id)
        from operator_business_category
        where zh_name = #{zhName}
    </select>

    <select id="existById" resultType="java.lang.Boolean">
        select count(id)
        from operator_business_category
        where id = #{id}
    </select>

    <select id="selectByEnAbbrs" resultType="com.trs.moye.base.data.model.entity.OperatorBusinessCategory">
        select *
        from operator_business_category
        where en_abbr in
        <foreach item="item" index="index" collection="enAbbrs" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="selectByPid" resultType="com.trs.moye.base.data.model.entity.OperatorBusinessCategory">
        select *
        from operator_business_category
        where pid = #{pid}
    </select>

    <select id="selectChildrenIds" resultType="java.lang.Integer">
        WITH RECURSIVE category_tree AS (SELECT id, pid
                                         FROM operator_business_category
                                         WHERE id = #{id}
                                         UNION ALL
                                         SELECT c.id, c.pid
                                         FROM operator_business_category c
                                                  JOIN category_tree ct ON c.pid = ct.id)
        SELECT id
        FROM category_tree
        WHERE id != #{id}
    </select>
    <select id="selectByEnAbbr" resultType="com.trs.moye.base.data.model.entity.OperatorBusinessCategory">
        SELECT *
        FROM operator_business_category
        WHERE en_abbr = #{enAbbr} LIMIT 1
    </select>

</mapper>