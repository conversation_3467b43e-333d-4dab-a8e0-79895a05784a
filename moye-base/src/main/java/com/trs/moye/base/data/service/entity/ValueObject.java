package com.trs.moye.base.data.service.entity;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * value 对象
 *
 * <AUTHOR>
 * @since 2024/10/10 15:55:07
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ValueObject implements Serializable {

    private String key;
    private String value;

    @JsonCreator
    public ValueObject(@JsonProperty("value") String value) {
        this.value = value;
    }
}
