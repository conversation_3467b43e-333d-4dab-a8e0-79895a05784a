package com.trs.moye.base.data.source.setting.file.chosen;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.ObjectUtils;

/**
 * <AUTHOR>
 * @since 2024-09-30 15:46
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
public class AssignFileChosenConfig extends FileChosenConfig {

    /**
     * 文件名
     */
    private String fileName;

    @Override
    public boolean matchName(String notSuffixFileName) {
        return ObjectUtils.isEmpty(this.fileName) || getName().equals(notSuffixFileName);
    }

    private String getName() {
        return fileName.substring(0, fileName.lastIndexOf("."));
    }

    @Override
    public String getMatcherPattern(String extension) {
        return fileName;
    }
}
