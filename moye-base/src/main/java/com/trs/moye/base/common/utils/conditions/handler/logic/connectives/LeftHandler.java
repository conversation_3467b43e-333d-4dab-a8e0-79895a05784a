package com.trs.moye.base.common.utils.conditions.handler.logic.connectives;

/**
 * 处理左括号
 *
 * <AUTHOR>
 * @since 2024/10/14
 */
public class LeftHandler implements LogicConnectivesHandler {

    private static final String LEFT = " (";

    @Override
    public void handleNormalJdbc(StringBuilder whereClause) {
        whereClause.append(LEFT); // 添加左括号
    }


    @Override
    public void handleHyBase(StringBuilder whereClause) {
        handleNormalJdbc(whereClause);
    }
}
