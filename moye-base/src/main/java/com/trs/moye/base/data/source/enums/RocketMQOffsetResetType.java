package com.trs.moye.base.data.source.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * MQ offset重置类型
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024/10/12 17:17
 **/
@Getter
@AllArgsConstructor
public enum RocketMQOffsetResetType {

    CONSUME_FROM_FIRST_OFFSET("从第一个offset开始消费"),
    CONSUME_FROM_LAST_OFFSET("从最后一个offset开始消费"),
    CONSUME_FROM_GROUP_OFFSETS("从消费者组的offset开始消费"),
    CONSUME_FROM_TIMESTAMP("从指定时间开始消费");
    private final String name;

}
