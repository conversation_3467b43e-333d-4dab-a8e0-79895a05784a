package com.trs.moye.base.common.enums.status;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @since 2025-03-12 17:17
 */
@Getter
@AllArgsConstructor
public enum StartStop {

    START("启动", true),
    STOP("停止", false);

    private final String label;

    private final boolean isStart;

    /**
     * 判断是否停止
     *
     * @return 是否停止
     */
    public boolean isStop() {
        return !isStart;
    }
}
