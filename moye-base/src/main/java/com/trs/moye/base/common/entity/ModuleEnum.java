package com.trs.moye.base.common.entity;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 模块枚举
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum ModuleEnum {

    /**
     * 数据建模
     */
    DATA_MODELING("数据建模", "表"),
    /**
     * 数据标准
     */
    DATA_STANDARD("数据标准", "字段"),

    ENTITY_BASE("实体库", "实体库"),

    TAG_BASE("标签库", "标签库"),

    COMPOUND_TYPE("复合类型", "复合类型"),

    /**
     * 数据服务
     */
    DATA_SERVICE("数据服务", "数据服务"),
    /**
     * 算子库
     */
    OPERATOR("算子库", "算子"),
    /**
     * 数据存储
     */
    DATA_STORAGE("数据存储", "存储"),
    /**
     * 数据源
     */
    DATA_SOURCE("数据来源", "数据源"),

    /**
     * 用户管理
     */
    USER_MANAGE("用户管理", "用户"),

    /**
     * 数据分析
     */
    VISUAL_ANALYSIS("可视化分析", "可视化分析"),
    ;

    private final String moduleName;

    private final String objectName;

}
