package com.trs.moye.base.data.model.field.mapping;

import com.trs.moye.base.data.model.entity.DataModelField;
import com.trs.moye.base.common.enums.FieldType;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 存储引擎查询库表表字段的字段信息
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
public class MoyeFieldResponse extends BaseColumnResponse {

    /**
     * 字段类型
     */
    private FieldType type;

    /**
     * 字段类型名称
     */
    private String typeName;

    /**
     * 转换为数据建模字段
     *
     * @param dataModelId 数据建模ID
     * @return {@link  DataModelField}
     */
    public DataModelField toDataModelField(Integer dataModelId) {
        DataModelField field = new DataModelField();
        field.setDataModelId(dataModelId);
        field.setZhName(zhName);
        field.setEnName(enName);
        field.setType(type);
        field.setTypeName(typeName);
        field.setPrimaryKey(isPrimaryKey);
        field.setAdvanceConfig(advanceConfig);
        return field;
    }
}
