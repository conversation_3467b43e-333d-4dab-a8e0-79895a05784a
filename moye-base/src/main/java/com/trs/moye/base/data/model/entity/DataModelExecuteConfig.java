package com.trs.moye.base.data.model.entity;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.trs.moye.base.common.entity.AuditBaseEntity;
import com.trs.moye.base.common.typehandler.CustomJacksonTypeHandler;
import com.trs.moye.base.common.typehandler.PolymorphismTypeHandler;
import com.trs.moye.base.data.connection.enums.ConnectionType;
import com.trs.moye.base.data.execute.ExecuteParams;
import com.trs.moye.base.data.execute.KafkaExecuteParams;
import com.trs.moye.base.data.source.enums.KafkaOffsetResetType;
import com.trs.moye.base.data.storage.IncrementInfo;
import java.util.Objects;
import javax.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * DataModelExecuteConfig实体类
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/09/26 17:15
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@TableName(value = "data_model_execute_config", autoResultMap = true)
public class DataModelExecuteConfig extends AuditBaseEntity {

    /**
     * 数据建模id
     */
    @NotNull(message = "dataModelId不允许空")
    private Integer dataModelId;

    /**
     * 执行参数：执行参数
     */
    @TableField(typeHandler = PolymorphismTypeHandler.class, updateStrategy = FieldStrategy.ALWAYS)
    private ExecuteParams executeParams;

    /**
     * 批处理的spark配置
     */
    @TableField(typeHandler = CustomJacksonTypeHandler.class, updateStrategy = FieldStrategy.ALWAYS)
    private BatchProcessSparkConfig sparkConfig;

    /**
     * 增量信息：增量信息
     */
    @TableField(typeHandler = CustomJacksonTypeHandler.class)
    private IncrementInfo incrementInfo;

    public DataModelExecuteConfig(Integer dataModelId,
        ExecuteParams executeParams) {
        this.dataModelId = dataModelId;
        this.executeParams = executeParams;
    }

    /**
     * 是否是增量任务
     *
     * @return 是否是增量任务
     */
    public boolean isIncrement() {
        return incrementInfo != null;
    }

    /**
     * 根据增量类型构建增量信息
     *
     * @return 增量信息
     */
    public IncrementInfo buildIncrementByType() {
        if (Objects.nonNull(executeParams) && ConnectionType.KAFKA == executeParams.getConnectionType()) {
            //kafka offset 默认给个增量信息
            KafkaExecuteParams kafkaExecuteParams = (KafkaExecuteParams) executeParams;
            KafkaOffsetResetType offsetResetType = kafkaExecuteParams.getOffsetResetType();
            if (KafkaOffsetResetType.GROUP_OFFSETS.equals(offsetResetType) || KafkaOffsetResetType.LATEST.equals(
                offsetResetType)) {
                return new IncrementInfo();
            }
        }
        return incrementInfo;
    }
}