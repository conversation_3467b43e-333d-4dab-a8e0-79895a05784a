package com.trs.moye.base.knowledgebase.entity;

import java.util.List;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 2024-08-01 01:26
 */
@Data
@NoArgsConstructor
public class TableInsertDataParam {

    /**
     * 数据id，用于接收数据库insert方法产生的自增id
     */
    private Integer id;

    private String tableName;

    private List<EntityColumn> keyValueList;

    public TableInsertDataParam(String tableName, List<EntityColumn> keyValueList) {
        this.tableName = tableName;
        this.keyValueList = keyValueList;
    }
}
