package com.trs.moye.base.common.serializer;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.fasterxml.jackson.databind.ser.std.StdSerializer;
import java.io.IOException;
import java.sql.Date;
import java.util.Calendar;

/**
 * java.sql.Date序列化
 *
 * <AUTHOR>
 * @since 2020/7/8
 **/
public class JavaSqlDateSerializer extends StdSerializer<Date> {

    /**
     * 构造器
     *
     * @param t Temporal对象
     */
    public JavaSqlDateSerializer(Class<Date> t) {
        super(t);
    }

    /**
     * 默认构造器
     */
    public JavaSqlDateSerializer() {
        this(null);
    }

    @Override
    public void serialize(Date date, JsonGenerator jsonGenerator, SerializerProvider serializerProvider)
        throws IOException {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        jsonGenerator.writeString(
            calendar.get(Calendar.YEAR) + "-" + fillZero(calendar.get(Calendar.MONTH) + 1) + "-" + fillZero(calendar.get(
                Calendar.DAY_OF_MONTH)));
    }

    private String fillZero(int num) {
        return num < 10 ? "0" + num : String.valueOf(num);
    }
}
