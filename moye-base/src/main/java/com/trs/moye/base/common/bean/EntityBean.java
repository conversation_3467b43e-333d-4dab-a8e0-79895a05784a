package com.trs.moye.base.common.bean;

import com.trs.moye.base.common.exception.ReflectException;
import java.beans.BeanInfo;
import java.beans.IntrospectionException;
import java.beans.Introspector;
import java.beans.PropertyDescriptor;
import java.lang.reflect.InvocationTargetException;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.Set;

/**
 * 实体类JavaBean封装类，用于反射操作实体类的属性。
 */
public class EntityBean implements JavaBean {

    private final Class<?> type;

    private final Map<String, PropertyDescriptor> descriptorMap = new LinkedHashMap<>();

    public EntityBean(Class<?> beanClass) {
        this.type = beanClass;
        BeanInfo beanInfo;
        try {
            beanInfo = Introspector.getBeanInfo(beanClass);
        } catch (IntrospectionException e) {
            throw new ReflectException(
                String.format("获取【%s】setter失败，原因是获取【%s】BeanInfo发生异常，异常信息：%s", beanClass, beanClass, e.getMessage()));
        }
        PropertyDescriptor[] propertyDescriptors = beanInfo.getPropertyDescriptors();
        if (propertyDescriptors != null) {
            for (PropertyDescriptor descriptor : propertyDescriptors) {
                if (descriptor.getWriteMethod() != null && descriptor.getReadMethod() != null) {
                    descriptorMap.put(descriptor.getName(), descriptor);
                }
            }
        }
    }


    @Override
    public Object getFieldValue(Object data, String fieldName) {
        if (!descriptorMap.containsKey(fieldName)) {
            throw new ReflectException(String.format("【%s】类没有【%s】属性", type, fieldName));
        }
        try {
            return descriptorMap.get(fieldName).getReadMethod().invoke(data);
        } catch (IllegalAccessException e) {
            throw new ReflectException(String.format("EntityBean获取【%s】类型的【%s】属性值失败：没有getter方法访问权限", type, fieldName));
        } catch (InvocationTargetException e) {
            throw new ReflectException(
                String.format("EntityBean获取【%s】类型的【%s】属性值失败：getter方法执行时发生异常；异常信息：【%s】", type, fieldName,
                    e.getMessage()));
        } catch (Exception e) {
            throw new ReflectException(String.format("EntityBean获取【%s】类型的【%s】属性值发生异常", type, fieldName), e);
        }
    }

    @Override
    public void setFieldValue(Object data, String fieldName, Object value) {
        if (!descriptorMap.containsKey(fieldName)) {
            throw new ReflectException(String.format("【%s】类没有【%s】属性", type.toString(), fieldName));
        }
        try {
            descriptorMap.get(fieldName).getWriteMethod().invoke(data, value);
        } catch (IllegalAccessException e) {
            throw new ReflectException(String.format("EntityBean设置【%s】类型的【%s】属性值失败：没有setter方法访问权限", type, fieldName));
        } catch (InvocationTargetException e) {
            throw new ReflectException(
                String.format("EntityBean设置【%s】类型的【%s】属性值失败：setter方法执行时发生异常；异常信息：【%s】", type, fieldName,
                    e.getMessage()));
        } catch (Exception e) {
            throw new ReflectException(String.format("EntityBean设置【%s】类型的【%s】属性值失败，属性值【%s】", type, fieldName, value),
                e);
        }
    }

    @Override
    public boolean containsField(String fieldName) {
        return descriptorMap.containsKey(fieldName);
    }

    @Override
    public Set<String> getFieldSet() {
        return descriptorMap.keySet();
    }

    @Override
    public Class<?> getClazz() {
        return type;
    }

    @Override
    public Class<?> getFieldType(String fieldName) {
        return descriptorMap.containsKey(fieldName) ? descriptorMap.get(fieldName).getReadMethod().getReturnType()
            : null;
    }


}
