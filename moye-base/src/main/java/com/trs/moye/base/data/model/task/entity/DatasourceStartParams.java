package com.trs.moye.base.data.model.task.entity;

import com.trs.moye.base.data.execute.ExecuteParams;
import com.trs.moye.base.data.execute.KafkaExecuteParams;
import com.trs.moye.base.data.execute.RocketMQExecuteParams;
import java.util.Objects;
import lombok.Data;

/**
 * 数据源其他一次性启动参数
 *
 * <AUTHOR>
 */
@Data
public class DatasourceStartParams {

    /**
     * 时间戳：按时间戳类型消费的时候----毫秒
     */
    private Long timestamp;

    /**
     * mq消费指定offset
     */
    private Long offset;
    /**
     * offset重置类型 EARLIEST/LATEST/OFFSET/TIMESTAMP
     */
    private String offsetResetType;

    /**
     * 创建MQ启动参数
     *
     * @param params 参数
     * @return {@link DatasourceStartParams }
     */
    public static DatasourceStartParams createMqStartParams(ExecuteParams params) {
        if (Objects.isNull(params) || !params.isMqType()) {
            return null;
        }
        DatasourceStartParams startParams = new DatasourceStartParams();
        if (params instanceof RocketMQExecuteParams) {
            RocketMQExecuteParams mqParams = (RocketMQExecuteParams) params;
            startParams.setTimestamp(mqParams.getTimestamp());
            startParams.setOffsetResetType(mqParams.getOffsetResetType().name());
        } else if (params instanceof KafkaExecuteParams) {
            KafkaExecuteParams kafkaParams = (KafkaExecuteParams) params;
            startParams.setTimestamp(kafkaParams.getTimestamp());
            startParams.setOffsetResetType(kafkaParams.getOffsetResetType().name());
        }
        return startParams;
    }

}