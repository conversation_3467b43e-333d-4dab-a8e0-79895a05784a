package com.trs.moye.base.data.model.entity;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 批处理任务配置项: 自定义可替换参数
 *
 * <AUTHOR>
 * @since 2025-07-03 13:50
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class CodeParameterItem {

    /**
     * 配置项的key
     */
    private String key;

    /**
     * 配置项的value
     */
    private String value;


    /**
     * Converts a list of CodeParameterItem objects into a Map using toMapEntry.
     *
     * @param items List of CodeParameterItem
     * @return Map with key as CodeParameterItem.key and value as CodeParameterItem.value
     */
    public static Map<String, String> convertToMap(List<CodeParameterItem> items) {
        if (items == null || items.isEmpty()) {
            return new HashMap<>();
        }
        return items.stream().collect(Collectors.toMap(CodeParameterItem::getKey, CodeParameterItem::getValue));
    }
}
