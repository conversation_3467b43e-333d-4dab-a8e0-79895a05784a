package com.trs.moye.base.common.deserializer;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.BeanProperty;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JavaType;
import com.fasterxml.jackson.databind.JsonDeserializer;
import com.fasterxml.jackson.databind.deser.ContextualDeserializer;
import com.trs.moye.base.common.utils.DateTimeUtils;
import java.io.IOException;
import java.time.LocalDate;

/**
 * jackson时间戳转日期的反序列化器
 *
 * <AUTHOR> lai.yi
 * @since : 2020/7/8
 **/
public class UtcToDateDeserializer extends JsonDeserializer<LocalDate> implements ContextualDeserializer {

    private JavaType valueType;

    @Override
    public JsonDeserializer<?> createContextual(DeserializationContext context, BeanProperty property) {
        UtcToDateDeserializer deserializer = new UtcToDateDeserializer();
        deserializer.valueType = property != null ? property.getType() : context.getContextualType();
        return deserializer;
    }

    @Override
    public LocalDate deserialize(JsonParser jsonParser, DeserializationContext deserializationContext)
        throws IOException {
        if (valueType.getRawClass().equals(LocalDate.class)) {
            String date = jsonParser.getValueAsString();
            if (date == null || date.isEmpty()) {
                return null;
            }
            return LocalDate.parse(date, DateTimeUtils.Formatter.YYYY_MM_DD.getDateTimeFormatter());
        } else {
            return null;
        }
    }
}
