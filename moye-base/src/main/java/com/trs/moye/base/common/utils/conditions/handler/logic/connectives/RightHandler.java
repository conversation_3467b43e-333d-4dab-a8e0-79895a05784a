package com.trs.moye.base.common.utils.conditions.handler.logic.connectives;

/**
 * 处理右括号
 *
 * <AUTHOR>
 * @since 2024/10/14
 */
public class RightHandler implements LogicConnectivesHandler {

    private static final String RIGHT = ") ";

    @Override
    public void handleNormalJdbc(StringBuilder whereClause) {
        whereClause.append(RIGHT); // 添加右括号
    }


    @Override
    public void handleHyBase(StringBuilder whereClause) {
        handleNormalJdbc(whereClause);
    }
}
