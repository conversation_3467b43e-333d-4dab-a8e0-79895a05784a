package com.trs.moye.base.common.annotaion.validation;

import com.trs.moye.base.common.bean.EntityBean;
import com.trs.moye.base.common.bean.JavaBean;
import com.trs.moye.base.common.entity.TwoTuple;
import com.trs.moye.base.common.help.RepeatChecker;
import com.trs.moye.base.common.utils.AssertUtils;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.List;
import java.util.Map.Entry;
import java.util.stream.Collectors;
import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;
import org.apache.commons.lang3.ObjectUtils;

/**
 * 状态值注解校验
 *
 * <AUTHOR>
 * @since 2024/7/12 14:06
 **/
public class NotRepeatValidator implements ConstraintValidator<NotRepeat, Collection<?>> {

    private RepeatValidator validator;

    private NotRepeat annotation;

    @Override
    public void initialize(NotRepeat annotation) {
        this.annotation = annotation;
        List<String> fields = Arrays.stream(annotation.fields()).collect(Collectors.toList());
        if (ObjectUtils.isEmpty(fields)) {
            validator = new ElementRepeatValidator();
        } else {
            validator = new ElementFieldRepeatValidator(fields);
        }
    }

    @Override
    public boolean isValid(Collection<?> collection, ConstraintValidatorContext context) {
        if (ObjectUtils.isEmpty(collection)) {
            return true;
        }
        return validator.isValid(collection, annotation, context);
    }

    private interface RepeatValidator {

        /**
         * 校验集合中元素是否重复
         *
         * @param collection 集合对象
         * @param annotation 注解
         * @param context    校验上下文环境
         * @return true 不重复，false 重复
         */
        boolean isValid(Collection<?> collection, NotRepeat annotation, ConstraintValidatorContext context);

    }

    private static class ElementRepeatValidator implements RepeatValidator {

        @Override
        public boolean isValid(Collection<?> collection, NotRepeat annotation, ConstraintValidatorContext context) {
            RepeatChecker<Object> checker = new RepeatChecker<>();
            for (Object element : collection) {
                checker.countKey(element);
            }
            if (checker.getRepeatEntries().isEmpty()) {
                return true;
            }
            // 动态构建错误信息
            context.disableDefaultConstraintViolation();
            context.buildConstraintViolationWithTemplate(buildMessage(annotation, checker)).addConstraintViolation();
            return false;
        }

        private String buildMessage(NotRepeat annotation, RepeatChecker<Object> checker) {
            StringBuilder builder = new StringBuilder();
            builder.append(annotation.message()).append("\n");
            for (Entry<Object, Integer> repeatEntry : checker.getRepeatEntries()) {
                builder.append("\t").append("【").append(repeatEntry.getKey()).append("】重复【")
                    .append(repeatEntry.getValue()).append("】次\n");
            }
            return builder.toString();
        }
    }

    private static class ElementFieldRepeatValidator implements RepeatValidator {

        private final List<String> fields;

        public ElementFieldRepeatValidator(List<String> fields) {
            this.fields = fields;
        }

        @Override
        public boolean isValid(Collection<?> collection, NotRepeat annotation, ConstraintValidatorContext context) {
            Class<?> elementClass = collection.iterator().next().getClass();
            JavaBean javaBean = new EntityBean(elementClass);
            List<TwoTuple<String, RepeatChecker<Object>>> checkerResultList = new ArrayList<>();
            boolean hasRepeat = false;
            for (String field : fields) {
                TwoTuple<String, String> fieldInfo = parseField(field);
                RepeatChecker<Object> checker = new RepeatChecker<>();
                for (Object element : collection) {
                    Object fieldValue = javaBean.getFieldValue(element, fieldInfo.getFirst());
                    checker.countKey(fieldValue);
                }
                if (!hasRepeat && !checker.getRepeatEntries().isEmpty()) {
                    hasRepeat = true;
                }
                checkerResultList.add(new TwoTuple<>(fieldInfo.getSecond(), checker));
            }
            if (!hasRepeat) {
                return true;
            }
            // 动态构建错误信息
            context.disableDefaultConstraintViolation();
            String message = fields.size() == 1 ? buildMessage(annotation, checkerResultList.get(0))
                : buildMessage(annotation, checkerResultList);
            context.buildConstraintViolationWithTemplate(message).addConstraintViolation();
            return false;
        }

        private TwoTuple<String, String> parseField(String field) {
            AssertUtils.notEmpty(field, "字段表达式不能为空");
            String[] split = field.split(":");
            if (split.length != 1 && split.length != 2) {
                throw new IllegalArgumentException("字段表达式格式不正确，应为：【属性名】或【属性名:属性别名】格式");
            }
            return new TwoTuple<>(split[0], split.length == 1 ? split[0] : split[1]);
        }

        private String buildMessage(NotRepeat annotation,
            List<TwoTuple<String, RepeatChecker<Object>>> checkerResultList) {
            StringBuilder builder = new StringBuilder();
            builder.append(annotation.message()).append("\n");
            for (TwoTuple<String, RepeatChecker<Object>> result : checkerResultList) {
                builder.append("\t").append("【").append(result.getFirst()).append("】字段的值存在重复：\n");
                for (Entry<Object, Integer> repeatEntry : result.getSecond().getRepeatEntries()) {
                    builder.append("\t\t").append("【").append(repeatEntry.getKey()).append("】重复【")
                        .append(repeatEntry.getValue()).append("】次\n");
                }
            }
            return builder.toString();
        }

        private String buildMessage(NotRepeat annotation, TwoTuple<String, RepeatChecker<Object>> checkerResult) {
            StringBuilder builder = new StringBuilder();
            builder.append(annotation.message()).append("\n");
            for (Entry<Object, Integer> repeatEntry : checkerResult.getSecond().getRepeatEntries()) {
                builder.append("\t").append("【").append(repeatEntry.getKey()).append("】重复【")
                    .append(repeatEntry.getValue()).append("】次\n");
            }
            return builder.toString();
        }
    }
}
