package com.trs.moye.base.monitor.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 数据处理监控类型
 *
 * <AUTHOR>
 * @since 2025-03-11 18:09
 */
@Getter
@AllArgsConstructor
public enum DataProcessMonitorType {

    DATA_PROCESS("数据处理"),

    DATA_STORAGE("数据存储"),

    STREAM_PROCESS_V1("流处理"),

    /**
     * 贴源库调度监控使用
     */
    ODS_COMPLETE_LOG("贴源库"),
    ;

    private final String label;
}
