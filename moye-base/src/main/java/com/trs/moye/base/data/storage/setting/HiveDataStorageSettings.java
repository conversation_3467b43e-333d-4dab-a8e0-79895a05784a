package com.trs.moye.base.data.storage.setting;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.trs.moye.base.common.enums.TimeUnit;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * Hive建表设置
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class HiveDataStorageSettings extends DataStorageSettings {

    /**
     * 存储格式 parquet orc
     */
    private String storedAs;

    /**
     * 表注释
     */
    private String tableComment;

    /**
     * 集合分割方式
     */
    private String collectionTerminal;

    /**
     * 字段分割方式
     */
    private String fieldsTerminal;

    /**
     * map元素分割方式
     */
    private String mapTerminal;

    /**
     * 分区字段名称
     */
    private String partitionField;

    /**
     * 分区粒度
     */
    private TimeUnit partitionGranularity;

    /**
     * 分区来源
     */
    private String partitionBy;

    /**
     * 分区保留数量
     */
    private Integer partitionKeepNum = 3;

    @JsonIgnore
    @Override
    public boolean isPartitioned() {
        return partitionField != null && !partitionField.isEmpty();
    }

    /**
     * 获取分区格式
     *
     * @return 分区格式字符串
     */
    @JsonIgnore
    public String getPartitionFormat() {
        switch (partitionGranularity) {
            case YEAR:
                return "yyyy";
            case MONTH:
                return "yyyyMM";
            case DAY:
                return "yyyyMMdd";
            case HOUR:
                return "yyyyMMddHH";
            case MINUTE:
                return "yyyyMMddHHmm";
            default:
                return "yyyyMMddHHmmss";
        }
    }
}
