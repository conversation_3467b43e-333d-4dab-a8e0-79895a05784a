package com.trs.moye.base.data.storage.setting;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 数据源配置
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-09-26 22:09
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class KafkaDataStorageSettings extends DataStorageSettings {

    /**
     * 分区数
     */
    private int partitions = 20;

    /**
     * 副本数
     */
    private short replicas = 1;

    /**
     * 保存时长（毫秒） 默认7天（604800000L）
     */
    private long saveDuration = 604800000L;

    @Override
    public boolean isPartitioned() {
        return false;
    }
}
