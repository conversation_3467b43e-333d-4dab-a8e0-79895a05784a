package com.trs.moye.base.common.deserializer;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import java.io.IOException;
import java.time.DayOfWeek;

/**
 * 星期反序列化器
 *
 * <AUTHOR>
 * @since 2025/05/20 16:39:31
 */
public class DayOfWeekDeserializer extends JsonDeserializer<DayOfWeek> {

    @Override
    public DayOfWeek deserialize(JsonParser p, DeserializationContext ctxt) throws IOException {
        String value = p.getValueAsString();
        if (value == null || value.isEmpty()) {
            return null;
        }

        try {
            int dayValue = Integer.parseInt(value);
            return DayOfWeek.of(dayValue);
        } catch (Exception e) {
            try {
                return DayOfWeek.valueOf(value.toUpperCase());
            } catch (Exception ex) {
                throw new com.fasterxml.jackson.core.JsonParseException(p, "无法解析DayOfWeek: " + value, ex);
            }
        }
    }
}