package com.trs.moye.base.common.annotaion.validation;

import java.util.regex.Pattern;
import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;
import org.apache.commons.lang3.StringUtils;

/**
 * sql字段名校验器
 */
public class SafeSqlFieldValidator implements ConstraintValidator<SafeSqlField, String> {

    // SQL关键字列表
    private static final String[] SQL_KEYWORDS = {
        "and", "insert", "select", "drop", "alter",
        "delete", "update", "truncate",
        "char", "declare", "or", "union", "join", "where", "like", "on"
    };
    private static final String SQL_KEYWORDS_REGEX = String.format("(\\b(%s)\\b)", String.join("|", SQL_KEYWORDS));

    // 特殊字符正则
    private static final String SPECIAL_CHARS = "([;+'%\"\\\\<>=-])";

    // sql注入正则
    private static final Pattern DANGEROUS_CHARS = Pattern.compile(
        String.format("%s|%s", SQL_KEYWORDS_REGEX, SPECIAL_CHARS),
        Pattern.CASE_INSENSITIVE);

    @Override
    public boolean isValid(String value, ConstraintValidatorContext context) {
        if (StringUtils.isBlank(value)) {
            return true; // 使用 @NotNull 处理空值
        }

        return !DANGEROUS_CHARS.matcher(value).find();
    }
}
