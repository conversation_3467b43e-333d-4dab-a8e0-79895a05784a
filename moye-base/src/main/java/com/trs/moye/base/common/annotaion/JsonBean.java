package com.trs.moye.base.common.annotaion;

import com.fasterxml.jackson.databind.JsonDeserializer;
import com.fasterxml.jackson.databind.JsonSerializer;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * JsonBean：标记类与Json序列化与反序列化相关
 *
 * <AUTHOR>
 * @since 2025-02-25 17:12
 */
@Retention(RetentionPolicy.RUNTIME)
@Target({ElementType.TYPE})
public @interface JsonBean {

    /**
     * 反序列化器，默认为None，即不使用自定义反序列化器。
     *
     * @return 自定义反序列化器类
     */
    Class<? extends JsonDeserializer> deserializer() default JsonDeserializer.None.class;

    /**
     * 序列化器，默认为None，即不使用自定义序列化器。
     *
     * @return 自定义序列化器类
     */
    Class<? extends JsonSerializer> serializer() default JsonSerializer.None.class;
}
