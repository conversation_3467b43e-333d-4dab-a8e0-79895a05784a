package com.trs.moye.base.data.indicator.entity;

import com.trs.moye.base.data.indicator.enums.IndicatorFieldType;
import com.trs.moye.base.data.model.entity.DataModelField;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 数据模型指标字段响应
 *
 * <AUTHOR>
 * @since 2025/05/28 11:30:46
 */
@EqualsAndHashCode(callSuper = true)
@AllArgsConstructor
@Data
public class DataModelIndicatorFieldResponse extends DataModelIndicatorField {

    private boolean isCreateTable;

    private Boolean isSupportSort = true;

    /**
     * 顺序
     */
    private Integer executeOrder;

    public DataModelIndicatorFieldResponse(DataModelField field,
        IndicatorFieldType indicatorType, boolean isEnable, String originalZhName, IndicatorFieldConfig config,
        boolean isCreateTable, Integer executeOrder) {
        super(field, indicatorType, isEnable, originalZhName, config, executeOrder);
        setCreateTable(isCreateTable);
    }
}
