package com.trs.moye.base.data.connection.entity.params;

import com.trs.moye.base.data.connection.entity.KerberosCertificate;
import java.util.Objects;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * mysql连接信息
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ClickhouseConnectionParams extends JdbcDatabaseConnectionParams {

    @Override
    public String getJdbcUrl(KerberosCertificate kerberos) {
        return "jdbc:clickhouse://" + host + ":" + port + "/" + database + "?serverTimezone=Asia/Shanghai";
    }

    @Override
    public String getJdbcDriverName(KerberosCertificate kerberos) {
        if (Objects.nonNull(kerberos)) {
            return "ru.yandex.clickhouse.ClickHouseDriver";
        } else {
            return "com.clickhouse.jdbc.ClickHouseDriver";
        }
    }
}
