package com.trs.moye.base.common.annotaion.validation;

import javax.validation.Constraint;
import javax.validation.Payload;
import java.lang.annotation.*;
/**
 * 条件表达式校验注解
 * <p>
 * 用于验证字段或参数是否符合条件表达式的语法规则。
 * 该注解会使用 {@link ValidConditionValidator} 进行具体的验证处理。
 * </p>
 */
@Documented
@Constraint(validatedBy = ValidConditionValidator.class)
@Target({ElementType.FIELD, ElementType.PARAMETER})
@Retention(RetentionPolicy.RUNTIME)
public @interface ValidCondition {
    /**
     * 获取验证失败时的错误消息
     *
     * @return 验证失败时的错误消息
     */
    String message() default "条件表达式不合法";

    /**
     * 获取验证分组
     *
     * @return 验证分组类数组
     */
    Class<?>[] groups() default {};

    /**
     * 获取载荷信息
     *
     * @return 载荷类数组
     */
    Class<? extends Payload>[] payload() default {};
}