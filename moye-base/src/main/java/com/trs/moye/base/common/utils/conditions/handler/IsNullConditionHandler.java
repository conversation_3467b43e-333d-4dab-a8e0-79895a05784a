package com.trs.moye.base.common.utils.conditions.handler;

import com.fasterxml.jackson.databind.node.ObjectNode;
import com.trs.moye.base.common.enums.FieldType;
import com.trs.moye.base.data.connection.enums.ConnectionType;
import com.trs.moye.base.data.service.entity.Condition;
import com.trs.moye.base.data.service.entity.ValueObject;
import java.util.List;

/**
 * isNull条件处理 为null
 *
 * <AUTHOR>
 * @since 2024/9/12
 */
public class IsNullConditionHandler implements SqlConditionHandler {

    @Override
    public void handleSql(StringBuilder whereClause, ConnectionType connectionType, Condition condition) {
        StringBuilder conditionSqlBuilder = new StringBuilder();
        String key = condition.getKey().getEnName(); // 获取字段名
        FieldType type = condition.getKey().getType(); // 获取字段类型
        if (connectionType.notNeedDoubleQuotes()) {
            conditionSqlBuilder.append(key).append(" IS NULL ");
            if (isStringType(type)) {
                conditionSqlBuilder.append(" OR ").append(key).append(" = '' ");
            }
        } else {
            conditionSqlBuilder.append("\"").append(key).append("\" IS NULL "); // 使用双引号包裹字段名
            if (isStringType(type) && !(ConnectionType.ORACLE.equals(connectionType))) { //oracle加'' 查不出来
                conditionSqlBuilder.append(" OR ").append("\"").append(key).append("\" = ''"); // 使用双引号包裹字段名
            }
        }
        whereClause.append("(").append(conditionSqlBuilder).append(")");
    }


    @Override
    public void handleEs(ObjectNode conditionJson, String key, List<ValueObject> values, Boolean isNotStringType) {
        throw new UnsupportedOperationException("isNull条件不支持es");
    }


    @Override
    public void handleHyBase(StringBuilder clause, String key, List<ValueObject> values) {
        clause.append(key).append(":\"\"");
    }
}
