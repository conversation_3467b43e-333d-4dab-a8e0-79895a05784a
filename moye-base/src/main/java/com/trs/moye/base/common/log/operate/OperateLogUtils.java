package com.trs.moye.base.common.log.operate;

import com.trs.moye.base.common.utils.AssertUtils;

/**
 * <AUTHOR>
 * @since 2025-03-04 18:05
 */
public class OperateLogUtils {

    private static final ThreadLocal<OperateLog> LOCAL = new ThreadLocal<>();

    private OperateLogUtils() {
    }

    /**
     * 设置当前线程的apiLog
     *
     * @param apiLog apiLog
     */
    public static void set(OperateLog apiLog) {
        LOCAL.set(apiLog);
    }

    /**
     * 移除当前线程的apiLog
     */
    public static void remove() {
        LOCAL.remove();
    }

    /**
     * 追加详情
     *
     * @param detailKey 详情key
     * @param detailValue 详情值
     */
    public static void appendDetail(String detailKey, Object detailValue) {
        OperateLog operateLog = LOCAL.get();
        if (operateLog == null){
            return;
        }
        AssertUtils.notEmpty(detailKey, "detailKey不能为空");
        operateLog.getDetails().put(detailKey, detailValue);
    }


}
