package com.trs.moye.base.data.indicator.entity;

import com.trs.moye.base.data.indicator.enums.IndicatorFieldType;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 指标应用程序字段配置
 *
 * <AUTHOR>
 * @since 2025/05/27 18:20:06
 */
@EqualsAndHashCode(callSuper = true)
@AllArgsConstructor
@NoArgsConstructor
@Data
public class IndicatorMetaFieldConfig extends IndicatorFieldConfig {

    /**
     * 是否计算字段
     */
    private Boolean isCompute;

    public IndicatorMetaFieldConfig(IndicatorFieldType type, Boolean isCompute) {
        setType(type);
        setIsCompute(isCompute);
    }

}
