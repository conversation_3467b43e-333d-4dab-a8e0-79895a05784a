package com.trs.moye.base.common.deserializer;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import com.trs.moye.base.data.operator.enums.OperatorFieldTypeEnum;
import com.trs.moye.base.common.enums.AbstractType;
import com.trs.moye.base.common.enums.FieldType;
import java.io.IOException;

/**
 * 字段类型反序列化器
 *
 * <AUTHOR>
 */
public class AbstractTypeDeserializer extends JsonDeserializer<AbstractType> {


    @Override
    public AbstractType deserialize(JsonParser p, DeserializationContext context) throws IOException {
        String value = p.getValueAsString();
        for (FieldType f : FieldType.values()) {
            if (f.name().equalsIgnoreCase(value)) {
                return f;
            }
        }
        return OperatorFieldTypeEnum.typeOf(value);
    }
}
