package com.trs.moye.base.data.connection.entity;

import com.trs.moye.base.data.connection.enums.JsonFieldTypeEnum;
import java.util.List;
import lombok.Data;

/**
 * json字段结构定义，解析json字符串里面的对象里面的属性得到，包括该属性下面所有孩子的结构定义
 *
 * <AUTHOR>
 * @since 2024/8/10 10:38
 **/
@Data
public class JsonFieldStruct {

    /**
     * 字段名称
     **/
    private String name;

    /**
     * 字段唯一编号
     **/
    private int id;

    /**
     * 孩子节点
     **/
    private List<JsonFieldStruct> children;

    /**
     * 是否禁用
     **/
    private boolean disabled;

    /**
     * 字段类型，参见 {@link JsonFieldTypeEnum}
     **/
    private String type;

}
