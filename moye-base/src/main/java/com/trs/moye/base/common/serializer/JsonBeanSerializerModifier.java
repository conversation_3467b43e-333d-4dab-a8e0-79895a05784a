package com.trs.moye.base.common.serializer;

import com.fasterxml.jackson.databind.BeanDescription;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializationConfig;
import com.fasterxml.jackson.databind.ser.BeanSerializerModifier;
import com.trs.moye.base.common.annotaion.JsonBean;
import com.trs.moye.base.common.exception.DeserializeException;
import com.trs.moye.base.common.utils.ReflectUtils;
import java.lang.reflect.Constructor;

/**
 * <AUTHOR>
 * @since 2020/7/8
 **/
public class JsonBeanSerializerModifier extends BeanSerializerModifier {

    @Override
    public JsonSerializer<?> modifySerializer(SerializationConfig config, BeanDescription beanDesc,
        JsonSerializer<?> serializer) {
        Class<?> beanClass = beanDesc.getBeanClass();
        JsonBean jsonBean = beanClass.getAnnotation(JsonBean.class);
        if (jsonBean != null && jsonBean.serializer() != JsonSerializer.None.class) {
            return createSerializer(beanClass, jsonBean);
        }
        return super.modifySerializer(config, beanDesc, serializer);
    }

    private JsonSerializer<?> createSerializer(Class<?> beanClass, JsonBean jsonBean) {
        Class<? extends JsonSerializer> serializer = jsonBean.serializer();
        Constructor<?> constructor = serializer.getConstructors()[0];
        if (constructor.getParameterCount() == 0) {
            return (JsonSerializer<?>) ReflectUtils.invokeConstructor(constructor);
        } else if (constructor.getParameterCount() == 1) {
            return (JsonSerializer<?>) ReflectUtils.invokeConstructor(constructor, beanClass);
        } else {
            throw new DeserializeException("自定义序列化器【%s】构造方法参数个数不正确，自定义序列化器构造函数仅支持0个或1个"
                , serializer.getName());
        }
    }
}
