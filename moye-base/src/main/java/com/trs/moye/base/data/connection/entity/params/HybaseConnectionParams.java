package com.trs.moye.base.data.connection.entity.params;

import com.fasterxml.jackson.annotation.JsonIgnore;
import java.util.Arrays;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * hybase连接信息
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class HybaseConnectionParams extends ConnectionParams {

    /**
     * 库名
     */
    private String database;

    @Override
    public String getIdentification() {
        return super.getIdentification() + "&database=" + (database == null ? "" : database);
    }

    @Override
    public String getCatalogPath() {
        return database;
    }

    /**
     * 获取主机名，按照 protocol://host:port 格式返回
     *
     * @return 格式化后的连接字符串
     */
    @JsonIgnore
    public String[] getHosts() {
        return Arrays.stream(host.split(","))
            .map(host -> String.format(URL_FORMAT, protocol, host, port))
            .toArray(String[]::new);
    }
}
