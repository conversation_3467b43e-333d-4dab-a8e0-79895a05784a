package com.trs.moye.base.common.utils;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @since 2017/5/18
 */
public class StringUtils {

    private static final Pattern BLANK_PATTERN = Pattern.compile("[\\s\t\r\n　]");

    private static final Pattern ID_PREFIX_PATTERN = Pattern.compile("(0{2})*$");

    private static final Pattern NUMBER_OF_TEXT_PATTERN = Pattern.compile("[^0-9]");

    private static final Pattern NUMBER_OF_START_PATTERN = Pattern.compile("[0-9]+");

    private static final Pattern NUMBER_OF_ALL_PATTERN = Pattern.compile("[0-9]*");

    private static final Pattern NUMBER_OF_FLOAT_DOUBLE_PATTERN = Pattern.compile("^[-\\+]?\\d*[.]\\d+$");


    private StringUtils() {
    }

    /**
     * 判断字符串是否为空
     *
     * @param str 字符串
     * @return true:为空
     */
    public static boolean isEmpty(String str) {
        return str == null || str.trim().isEmpty();
    }

    /**
     * 判断字符串是否不为空
     *
     * @param str str 字符串
     * @return true:不为空
     */
    public static boolean isNotEmpty(String str) {
        return !isEmpty(str);
    }

    /**
     * 处理接受参数中的特殊字符
     *
     * @param str 字符串
     * @return 处理后的字符串
     */
    public static String escape(String str) {
        if (isEmpty(str)) {
            return "";
        }
        return str.replaceAll("((?=[\\x21-\\x7e]+)[^A-Za-z0-9])", "\\\\$1");
    }

    /**
     * 去掉接受参数中的特殊字符
     *
     * @param str 字符串
     * @return 处理后的字符串
     */
    public static String removeSpecialChar(String str) {
        if (isEmpty(str)) {
            return "";
        }
        return str.replaceAll("[^\\u4e00-\\u9fa5a-zA-Z0-9]", "");
    }

    /**
     * 去掉接受参数中的空白字符、换行符
     *
     * @param str 字符串
     * @return 处理后的字符串
     */
    public static String removeBlank(String str) {
        if (isEmpty(str)) {
            return "";
        } else {
            Matcher m = BLANK_PATTERN.matcher(str);
            return m.replaceAll("");
        }
    }

    /**
     * 提取字符串中的数字
     *
     * @param text 文本
     * @return 数字
     */
    public static String getNumberStrFromText(String text) {
        if (StringUtils.isEmpty(text)) {
            return "";
        }
        Matcher matcher = NUMBER_OF_TEXT_PATTERN.matcher(text);
        return matcher.replaceAll("");
    }

    /**
     * 判断字符串是否为数字开头
     *
     * @param name 字符串
     * @return true:数字开头
     */
    public static boolean isStartOfNumber(String name) {
        Matcher matcher = NUMBER_OF_START_PATTERN.matcher(name.substring(0, 1));
        return matcher.matches();
    }


    /**
     * 判断字符串是否全是数字
     *
     * @param str 字符串
     * @return boolean
     * <AUTHOR>
     * @since 2020/11/25 14:49
     */
    public static boolean isNumeric(String str) {
        return NUMBER_OF_ALL_PATTERN.matcher(str).matches();
    }

    /**
     * 判断字符串是否是float或double
     *
     * @param str 字符串
     * @return boolean
     * <AUTHOR>
     * @since 2021/2/5 14:32
     */
    public static boolean isFloatOrDouble(String str) {
        return NUMBER_OF_FLOAT_DOUBLE_PATTERN.matcher(str).matches();
    }
}
