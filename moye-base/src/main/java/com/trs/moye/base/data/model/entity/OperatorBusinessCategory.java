package com.trs.moye.base.data.model.entity;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.trs.moye.base.common.entity.AuditBaseEntity;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 算子业务分类(OperatorBusinessCategory)数据访问类
 *
 * <AUTHOR>
 * @since 2024-09-24 17:53:38
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@TableName(value = "operator_business_category", autoResultMap = true)
public class OperatorBusinessCategory extends AuditBaseEntity implements Serializable {

    private static final long serialVersionUID = -84035043263427929L;

    /**
     * 父分类ID，顶级分类的父ID为0
     */
    private Integer pid;

    /**
     * 英文缩写
     */
    private String enAbbr;

    /**
     * 中文名
     */
    private String zhName;

    /**
     * 描述
     */
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String description;
}