package com.trs.moye.base.data.connection.enums;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.trs.moye.base.common.annotaion.PolymorphismMapping;
import com.trs.moye.base.common.utils.JsonUtils;
import com.trs.moye.base.data.connection.entity.DataConnection;
import com.trs.moye.base.data.connection.entity.params.APIConnectionParams;
import com.trs.moye.base.data.connection.entity.params.ClickhouseConnectionParams;
import com.trs.moye.base.data.connection.entity.params.ConnectionParams;
import com.trs.moye.base.data.connection.entity.params.DorisConnectionParams;
import com.trs.moye.base.data.connection.entity.params.ElasticSearchConnectionParams;
import com.trs.moye.base.data.connection.entity.params.FileConnectionParams;
import com.trs.moye.base.data.connection.entity.params.HiveConnectionParams;
import com.trs.moye.base.data.connection.entity.params.HybaseConnectionParams;
import com.trs.moye.base.data.connection.entity.params.KafkaConnectionParams;
import com.trs.moye.base.data.connection.entity.params.MysqlConnectionParams;
import com.trs.moye.base.data.connection.entity.params.NebulaConnectionParams;
import com.trs.moye.base.data.connection.entity.params.OracleConnectionParams;
import com.trs.moye.base.data.connection.entity.params.PostgresqlConnectionParams;
import com.trs.moye.base.data.connection.entity.params.RocketMqConnectionParams;
import com.trs.moye.base.data.execute.DatabaseExecuteParams;
import com.trs.moye.base.data.execute.DefaultExecuteParams;
import com.trs.moye.base.data.execute.ExecuteParams;
import com.trs.moye.base.data.execute.FTPExecuteParams;
import com.trs.moye.base.data.execute.KafkaExecuteParams;
import com.trs.moye.base.data.execute.RocketMQExecuteParams;
import com.trs.moye.base.data.model.entity.DataModelField;
import com.trs.moye.base.data.model.enums.AuditFieldEnum;
import com.trs.moye.base.data.source.setting.DataSourceSettings;
import com.trs.moye.base.data.source.setting.DataSourceSettings.DefaultDataSourceSettings;
import com.trs.moye.base.data.source.setting.file.FtpDataSourceSettings;
import com.trs.moye.base.data.source.setting.http.HttpDataSourceSettings;
import com.trs.moye.base.data.storage.setting.DataStorageSettings;
import com.trs.moye.base.data.storage.setting.DefaultDataStorageSettings;
import com.trs.moye.base.data.storage.setting.DorisDataStorageSettings;
import com.trs.moye.base.data.storage.setting.ElasticsearchDataStorageSettings;
import com.trs.moye.base.data.storage.setting.HiveDataStorageSettings;
import com.trs.moye.base.data.storage.setting.KafkaDataStorageSettings;
import com.trs.moye.base.data.storage.setting.RocketmqDataStorageSettings;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.function.Predicate;
import java.util.stream.Collectors;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 数据源类型
 */
@Getter
@AllArgsConstructor
public enum ConnectionType {
    /**
     * http
     */
    HTTP("Http",
        DataSourceCategory.API,
        SourceStructureType.OTHER,
        APIConnectionParams.class,
        HttpDataSourceSettings.class,
        DefaultDataStorageSettings.class,
        DefaultExecuteParams.class,
        true,
        false),
    MYSQL("Mysql",
        DataSourceCategory.DATA_BASE,
        SourceStructureType.STRUCTURED,
        MysqlConnectionParams.class,
        DefaultDataSourceSettings.class,
        DefaultDataStorageSettings.class,
        DatabaseExecuteParams.class,
        true,
        true),
    ORACLE("Oracle",
        DataSourceCategory.DATA_BASE,
        SourceStructureType.STRUCTURED,
        OracleConnectionParams.class,
        DefaultDataSourceSettings.class,
        DefaultDataStorageSettings.class,
        DatabaseExecuteParams.class,
        true,
        true),
    POSTGRESQL("Postgresql",
        DataSourceCategory.DATA_BASE,
        SourceStructureType.STRUCTURED,
        PostgresqlConnectionParams.class,
        DefaultDataSourceSettings.class,
        DefaultDataStorageSettings.class,
        DatabaseExecuteParams.class,
        true,
        true),
    HIVE("Hive",
        DataSourceCategory.DATA_BASE,
        SourceStructureType.STRUCTURED,
        HiveConnectionParams.class,
        DefaultDataSourceSettings.class,
        HiveDataStorageSettings.class,
        DatabaseExecuteParams.class,
        true,
        true),
    CLICK_HOUSE("ClickHouse",
        DataSourceCategory.DATA_BASE,
        SourceStructureType.STRUCTURED,
        ClickhouseConnectionParams.class,
        DefaultDataSourceSettings.class,
        DefaultDataStorageSettings.class,
        DatabaseExecuteParams.class,
        true,
        true),
    ELASTIC_SEARCH("ES",
        DataSourceCategory.DATA_BASE,
        SourceStructureType.STRUCTURED,
        ElasticSearchConnectionParams.class,
        DefaultDataSourceSettings.class,
        ElasticsearchDataStorageSettings.class,
        DatabaseExecuteParams.class,
        true,
        true),
    HY_BASE("海贝",
        DataSourceCategory.DATA_BASE,
        SourceStructureType.STRUCTURED,
        HybaseConnectionParams.class,
        DefaultDataSourceSettings.class,
        DefaultDataStorageSettings.class,
        DatabaseExecuteParams.class,
        true,
        true),
    DORIS("Doris",
        DataSourceCategory.DATA_BASE,
        SourceStructureType.STRUCTURED,
        DorisConnectionParams.class,
        DefaultDataSourceSettings.class,
        DorisDataStorageSettings.class,
        DatabaseExecuteParams.class,
        true,
        true),
    NEBULA("Nebula",
        DataSourceCategory.DATA_BASE,
        SourceStructureType.VIEW,
        NebulaConnectionParams.class,
        DefaultDataSourceSettings.class,
        DefaultDataStorageSettings.class,
        DatabaseExecuteParams.class,
        true,
        true),
    KAFKA("Kafka",
        DataSourceCategory.MQ,
        SourceStructureType.OTHER,
        KafkaConnectionParams.class,
        DefaultDataSourceSettings.class,
        KafkaDataStorageSettings.class,
        KafkaExecuteParams.class,
        true,
        true),
    ROCKETMQ("Rocketmq",
        DataSourceCategory.MQ,
        SourceStructureType.OTHER,
        RocketMqConnectionParams.class,
        DefaultDataSourceSettings.class,
        RocketmqDataStorageSettings.class,
        RocketMQExecuteParams.class,
        true,
        true),
    SFTP("Sftp",
        DataSourceCategory.FILE,
        SourceStructureType.OTHER,
        FileConnectionParams.class,
        FtpDataSourceSettings.class,
        DefaultDataStorageSettings.class,
        FTPExecuteParams.class,
        true,
        false),
    FTP("Ftp",
        DataSourceCategory.FILE,
        SourceStructureType.OTHER,
        FileConnectionParams.class,
        FtpDataSourceSettings.class,
        DefaultDataStorageSettings.class,
        FTPExecuteParams.class,
        true,
        false),
    MINIO("Minio",
        DataSourceCategory.FILE,
        SourceStructureType.OTHER,
        FileConnectionParams.class,
        FtpDataSourceSettings.class,
        DefaultDataStorageSettings.class,
        DefaultExecuteParams.class,
        true,
        false);

    /**
     * 标签：备注用
     */
    private final String label;

    /**
     * 数据源所属类别
     */
    private final DataSourceCategory category;

    /**
     * 数据源结构类型
     */
    private final SourceStructureType sourceStructureType;

    @PolymorphismMapping
    private final Class<? extends ConnectionParams> connectionParamsClazz;

    @PolymorphismMapping
    private final Class<? extends DataSourceSettings> dataSourceSettingsClazz;

    @PolymorphismMapping
    private final Class<? extends DataStorageSettings> createTableSettingClazz;

    @PolymorphismMapping
    private final Class<? extends ExecuteParams> executeParamClazz;

    /**
     * 支持数据源
     */
    private final boolean supportSource;

    /**
     * 支持数据存储
     */
    private final boolean supportStorage;

    /**
     * 从json字符串中解析创建表设置
     *
     * @param connectionType  连接类型
     * @param createTableJson 创建表json
     * @return 创建表设置
     */
    public static DataStorageSettings getCreateTableSettings(String connectionType, String createTableJson) {
        ConnectionType type = ConnectionType.getByName(connectionType);
        return JsonUtils.parseObject(createTableJson, type.createTableSettingClazz);
    }


    /**
     * 连接类型反序列化出连接信息
     *
     * @param categoryString 连接类型字符串
     * @param jsonString     连接信息json
     * @return 连接信息
     */
    public static ConnectionParams getConnectionInfo(String categoryString, String jsonString) {
        ConnectionType type = ConnectionType.valueOf(categoryString);
        return JsonUtils.parseObject(jsonString, type.connectionParamsClazz);
    }

    /**
     * 过滤连接类型
     *
     * @param filter 过滤函数
     * @return 连接类型列表
     */
    public static List<ConnectionType> filterConnectionType(Predicate<ConnectionType> filter) {
        List<ConnectionType> types = new ArrayList<>();
        for (ConnectionType value : values()) {
            if (filter.test(value)) {
                types.add(value);
            }
        }
        return types;
    }

    /**
     * 根据name获取枚举
     *
     * @param name name
     * @return 枚举
     */
    @JsonCreator
    public static ConnectionType getByName(String name) {
        for (ConnectionType value : ConnectionType.values()) {
            if (value.name().equals(name)) {
                return value;
            }
        }
        throw new IllegalArgumentException("Unknown DataSourceType name: " + name);
    }

    /**
     * 连接类型反序列化出连接信息
     *
     * @param connectionType 连接类型字符串
     * @param jsonString     连接信息json
     * @return 连接信息
     */
    public static ConnectionParams getConnectionParams(String connectionType, String jsonString) {
        ConnectionType type = ConnectionType.getByName(connectionType);
        return JsonUtils.parseObject(jsonString, type.connectionParamsClazz);
    }

    /**
     * 获取执行参数
     *
     * @param connectionType    连接类型
     * @param executeParamsJson 执行参数json
     * @return 执行参数
     */
    public static ExecuteParams getExecuteParams(String connectionType, String executeParamsJson) {
        ConnectionType type = ConnectionType.getByName(connectionType);
        return JsonUtils.parseObject(executeParamsJson, type.executeParamClazz);
    }

    /**
     * 获取数据源配置
     *
     * @param connectionType 连接类型
     * @param jsonString     json字符串
     * @return {@link DataSourceSettings}
     */
    public static DataSourceSettings getDataSourceSettings(String connectionType, String jsonString) {
        ConnectionType type = ConnectionType.getByName(connectionType);
        return JsonUtils.parseObject(jsonString, type.dataSourceSettingsClazz);
    }


    /**
     * 是否为文件连接类型
     *
     * @return 是否为文件连接类型
     */
    public boolean isFileType() {
        return DataSourceCategory.FILE.equals(category);
    }

    /**
     * 是否为数据库连接类型
     *
     * @return 是否为数据库连接类型
     */
    public boolean isDBType() {
        return Arrays.asList(MYSQL, ORACLE, POSTGRESQL, HIVE, CLICK_HOUSE, ELASTIC_SEARCH, HY_BASE).contains(this);
    }

    /**
     * 是否为HTTP类型
     *
     * @return 是否为HTTP连接类型
     */
    public boolean isHttpType() {
        return Objects.equals(HTTP, this);
    }

    /**
     * 是否为MQ类型
     *
     * @return 是否为MQ连接类型
     */
    public boolean isMqType() {
        return Arrays.asList(ROCKETMQ, KAFKA).contains(this);
    }

    /**
     * 不需要双引号
     *
     * @return 是否不需要双引号
     */
    public boolean notNeedDoubleQuotes() {
        return !ConnectionType.POSTGRESQL.equals(this) && !ConnectionType.ORACLE.equals(this);
    }

    /**
     * 是否为HyBase类型
     *
     * @return 是否为HyBase类型
     */
    public boolean isHyBaseType() {
        return Objects.equals(HY_BASE, this);
    }

    /**
     * 处理PG数据库查询拼接schema
     *
     * @param dataConnection 连接信息
     * @return 处理好的表名
     */
    public String getPostgresqlSchema(DataConnection dataConnection) {
        if (!ConnectionType.POSTGRESQL.equals(this)) {
            return null;
        }
        return ((PostgresqlConnectionParams) dataConnection.getConnectionParams()).getSchema();
    }

    /**
     * 生成insert语句(这里要先把审计字段拿出来, 再按照seatunnel生成的顺序去生成insert,否则会错位)
     *
     * @param table           表名
     * @param dataModelFields 字段名
     * @return insert语句
     */
    public String buildInsert(String table, List<DataModelField> dataModelFields) {
        // 过滤出需要的字段，排除特定的系统字段
        List<DataModelField> filteredFields = dataModelFields.stream()
            .filter(field -> !Arrays.asList(
                AuditFieldEnum.TRS_MOYE_INPUT_TIME.getName(),
                AuditFieldEnum.TRS_MOYE_BATCH_NO.getName(),
                AuditFieldEnum.TRS_MOYE_TASK_ID.getName()).contains(field.getEnName()))
            .collect(Collectors.toList());
        // 构建主字段的列名和占位符
        String columns = filteredFields.stream()
            .map(field -> "\"" + field.getEnName() + "\"")
            .collect(Collectors.joining(", "));
        String placeholders = filteredFields.stream()
            .map(field -> "?")
            .collect(Collectors.joining(", "));
        // 添加最后三个字段及其占位符
        String lastColumns = String.join(", ",
            "\"" + AuditFieldEnum.TRS_MOYE_TASK_ID.getName() + "\"",
            "\"" + AuditFieldEnum.TRS_MOYE_BATCH_NO.getName() + "\"",
            "\"" + AuditFieldEnum.TRS_MOYE_INPUT_TIME.getName() + "\"");
        String lastPlaceholders = "?, ?, ?";
        // 组合所有字段和占位符
        columns += ", " + lastColumns;
        placeholders += ", " + lastPlaceholders;
        // 返回完整的 INSERT 语句
        return String.format("INSERT INTO %s (%s) VALUES (%s)", table, columns, placeholders);
    }

    /**
     * 为标识符加上转义字符
     *
     * @param identifier 标识符
     * @return 转义后的标识符
     */
    public String escapeIdentifier(String identifier) {
        String escapeCharacter;
        switch (this) {
            case MYSQL:
            case HIVE:
            case CLICK_HOUSE:
                escapeCharacter = "`";
                break;
            case ORACLE:
            case POSTGRESQL:
                escapeCharacter = "\"";
                break;
            default:
                escapeCharacter = "";
                break;
        }
        return escapeCharacter + identifier + escapeCharacter;
    }

    /**
     * 获取Oracle的Schema
     *
     * @param dataConnection 连接信息
     * @return Oracle的Schema，如果不是Oracle连接类型则返回null
     */
    public String getOracleSchema(DataConnection dataConnection) {
        if (!ConnectionType.ORACLE.equals(this)) {
            return null;
        }
        return ((OracleConnectionParams) dataConnection.getConnectionParams()).getSchema();
    }
}
