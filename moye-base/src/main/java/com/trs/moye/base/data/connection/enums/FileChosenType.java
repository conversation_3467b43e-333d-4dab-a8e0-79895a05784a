package com.trs.moye.base.data.connection.enums;

import com.trs.moye.base.common.annotaion.PolymorphismMapping;
import com.trs.moye.base.data.source.setting.file.chosen.AllFileChosenConfig;
import com.trs.moye.base.data.source.setting.file.chosen.AssignFileChosenConfig;
import com.trs.moye.base.data.source.setting.file.chosen.FileChosenConfig;
import com.trs.moye.base.data.source.setting.file.chosen.NamingNotationFileChosenConfig;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 文件帅选类型
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-09-27 22:35
 */
@Getter
@AllArgsConstructor
public enum FileChosenType {

    ALL_FILE("全部文件") {
        @Override
        public Class<? extends FileChosenConfig> deserializeTargetClass() {
            return AllFileChosenConfig.class;
        }
    },

    ASSIGN_FILE("指定文件") {
        @Override
        public Class<? extends FileChosenConfig> deserializeTargetClass() {
            return AssignFileChosenConfig.class;
        }
    },

    REGULAR_EXPRESSION_FILE("符合命名规范的文件") {
        @Override
        public Class<? extends FileChosenConfig> deserializeTargetClass() {
            return NamingNotationFileChosenConfig.class;
        }
    },
    ;

    private final String label;

    /**
     * 反序列化目标类
     *
     * @return 目标类
     */
    @PolymorphismMapping
    public abstract Class<? extends FileChosenConfig> deserializeTargetClass();
}
