package com.trs.moye.base.common.response;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2025-07-10 11:35
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public abstract class AbstractRemarkResponse {

    protected String remark;

    /**
     * 追加备注
     *
     * @param remark 备注信息
     */
    public void appendRemark(String remark) {
        if (remark == null || remark.isEmpty()) {
            return;
        }
        if (this.remark == null || this.remark.isEmpty()) {
            this.remark = remark;
            return;
        }
        this.remark = this.remark + "\n" + remark;
    }
}
