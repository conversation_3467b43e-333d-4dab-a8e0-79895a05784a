package com.trs.moye.base.common.utils.conditions.handler;


import com.fasterxml.jackson.databind.node.ObjectNode;
import com.trs.moye.base.common.enums.FieldType;
import com.trs.moye.base.data.connection.enums.ConnectionType;
import com.trs.moye.base.data.service.entity.Condition;
import com.trs.moye.base.data.service.entity.ValueObject;
import java.util.List;

/**
 * isNotNull条件处理 不为null
 *
 * <AUTHOR>
 * @since 2024/9/12
 */
public class IsNotNullConditionHandler implements SqlConditionHandler {

    @Override
    public void handleSql(StringBuilder whereClause, ConnectionType connectionType, Condition condition) {
        StringBuilder conditionSqlBuilder = new StringBuilder();
        // 获取字段名
        String key = condition.getKey().getEnName();
        // 获取字段类型
        FieldType type = condition.getKey().getType();
        if (connectionType.notNeedDoubleQuotes()) {
            conditionSqlBuilder.append(key).append(" IS NOT NULL");
            if (isStringType(type)) {
                conditionSqlBuilder.append(" AND ").append(key).append(" != ''");
            }
        } else {
            // 使用双引号包裹字段名
            conditionSqlBuilder.append("\"").append(key).append("\" IS NOT NULL");
            if (isStringType(type) && !(ConnectionType.ORACLE.equals(connectionType))) {
                conditionSqlBuilder.append(" AND ").append("\"").append(key).append("\" !=''");
            }
        }
        whereClause.append("(").append(conditionSqlBuilder).append(")");
    }

    @Override
    public void handleEs(ObjectNode conditionJson, String key, List<ValueObject> values,
        Boolean isNumberOrTimeOrVectorType) {
        throw new UnsupportedOperationException("IsNotNullConditionHandler不支持ES");
    }

    @Override
    public void handleHyBase(StringBuilder clause, String key, List<ValueObject> values) {
        clause.append("*:* -").append(key).append(":\"\"");
    }
}
