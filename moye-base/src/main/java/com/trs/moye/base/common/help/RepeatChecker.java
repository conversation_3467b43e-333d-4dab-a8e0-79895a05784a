package com.trs.moye.base.common.help;

import com.trs.moye.base.common.exception.BizException;
import java.util.ArrayList;
import java.util.Collection;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.function.Function;
import org.apache.commons.lang3.ObjectUtils;

/**
 * 重复检查器
 *
 * @param <K> 检查重复的元素的key
 * <AUTHOR>
 * @version 1.0
 * @since 2024-08-08 22:27
 */
public class RepeatChecker<K> {

    private final Map<K, Integer> countMap = new LinkedHashMap<>();

    public RepeatChecker() {
    }

    public <E> RepeatChecker(Collection<E> objects, Function<E, K> keyMapper) {
        batchCount(objects, keyMapper);
    }

    /**
     * key计数
     *
     * @param key key
     * @return 有重复返回true，否则返回false
     */
    public boolean countKey(K key) {
        // 忽略空key
        if (ObjectUtils.isEmpty(key)) {
            return false;
        }
        if (countMap.containsKey(key)) {
            countMap.put(key, countMap.get(key) + 1);
            return true;
        } else {
            countMap.put(key, 1);
            return false;
        }
    }

    /**
     * 批量计数
     *
     * @param objects   检查对象列表
     * @param keyMapper key映射器
     * @param <E>       检查对象的类型
     */
    public <E> void batchCount(Collection<E> objects, Function<E, K> keyMapper) {
        for (E e : objects) {
            countKey(keyMapper.apply(e));
        }
    }

    /**
     * 判断key重复
     *
     * @param key key
     * @return 布尔
     */
    public boolean isRepeatKey(K key) {
        return countMap.containsKey(key) && countMap.get(key) > 1;
    }

    /**
     * 获取重复的条目列表
     *
     * @return 重复的条目列表
     */
    public List<Entry<K, Integer>> getRepeatEntries() {
        List<Entry<K, Integer>> entries = new ArrayList<>();
        for (Entry<K, Integer> entry : countMap.entrySet()) {
            if (entry.getValue() > 1) {
                entries.add(entry);
            }
        }
        return entries;
    }

    /**
     * 计数并检查key，如果遇到重复元素直接报错
     *
     * @param key        检查key
     * @param format     异常信息格式
     * @param formatArgs 异常信息参数
     */
    public void countAndCheckKey(K key, String format, Object... formatArgs) {
        if (countKey(key)) {
            throw new BizException(format, formatArgs);
        }
    }

    /**
     * 结束时检查重复：有重复元素会抛出异常，一般配合count()方法使用
     *
     * @param errorMsgPrefix 错误提示信息前缀
     */
    public void checkAll(String errorMsgPrefix) {
        StringBuilder builder = new StringBuilder();
        for (Entry<K, Integer> entry : getRepeatEntries()) {
            builder.append("【").append(entry.getKey()).append("】重复数量【").append(entry.getValue()).append("】\n");
        }
        if (builder.length() > 0) {
            throw new BizException(errorMsgPrefix + "\n" + builder);
        }
    }

    /**
     * 检查集合元素重复
     *
     * @param collection     检查的集合
     * @param errorMsgPrefix 异常信息前缀
     * @param keyGenerator   key生成器
     * @param <K>            k类型
     * @param <E>            集合元素类型
     */
    public static <K, E> void checkCollection(Collection<E> collection, String errorMsgPrefix,
        Function<E, K> keyGenerator) {
        if (ObjectUtils.isEmpty(collection)) {
            return;
        }
        new RepeatChecker<>(collection, keyGenerator).checkAll(errorMsgPrefix);
    }
}
