package com.trs.moye.base.monitor.entity;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * MonitorOdsCutoff实体类
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/08/28 16:49
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
public class MonitorOdsCutoff extends MonitorOds {

    /**
     * 监控值：监控值
     */
    @NotNull(message = "监控值不允许空")
    private Long monitorValue;

    /**
     * 监控详情：监控详情：对于监控数据的补充说明，可以为空
     */
    @NotEmpty(message = "监控详情不允许空")
    private String monitorDetail;

    /**
     * 是否断流：是否断流：1-断流，0-不断流；监控值与上一次比较无变化被认为是断流，否则不是断流（数据库用数字，代码用布尔）
     */
    @NotNull(message = "是否断流不允许空")
    private boolean isCutoff;

    /**
     * 增量字段：增量字段
     */
    @NotEmpty(message = "增量字段不允许空")
    private String incrementColumn;

    /**
     * 增量字段值：增量字段值
     */
    @NotEmpty(message = "增量字段值不允许空")
    private String incrementValue;

}