package com.trs.moye.base.monitor.entity;

import com.trs.moye.base.common.log.enums.DataTracerTypeEnum;
import com.trs.moye.base.data.model.entity.DataModel;
import com.trs.moye.base.data.storage.DataStorage;
import java.util.ArrayList;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 趋势图-线条模型
 *
 * <AUTHOR>
 * @since 2025-07-09 09:11
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class TrendChartLine {

    /**
     * 趋势图id，对于数据接入和数据处理是数据建模id，对于数据存储是存储点id
     */
    private Integer id;

    private DataTracerTypeEnum type;

    private String name;

    private List<TrendChartPoint> chartPoints;

    /**
     * 添加趋势图点
     *
     * @param point 趋势图点
     */
    public void appendChartPoint(TrendChartPoint point) {
        if (chartPoints == null) {
            chartPoints = new ArrayList<>();
        }
        this.chartPoints.add(point);
    }

    public TrendChartLine(DataModel dataModel, DataTracerTypeEnum type) {
        this.id = dataModel.getId();
        this.type = type;
        this.name = type.getNodeName();
    }

    public TrendChartLine(DataModel dataModel, DataStorage storage) {
        this.id = storage.getId();
        this.type = DataTracerTypeEnum.DATA_STORAGE;
        String tempName = "存储-" + storage.getConnection().getName();
        // 如果存储点和数据模型同名，则只显示存储点连接名称，否则显示存储点连接名称+存储表名（选择已有表时，存储表名与建模名不一致）
        this.name = storage.getEnName().equals(dataModel.getEnName()) ? tempName : tempName + "-" + storage.getZhName();
    }
}
