package com.trs.moye.base.common.annotaion.processor.fieldsign;

import java.util.function.Supplier;

/**
 * <AUTHOR>
 * @since 2025-03-05 11:08
 */
public class DefaultSignFieldReader implements SignFieldReader {

    private final Supplier<Object> supplier;

    public DefaultSignFieldReader(Supplier<Object> supplier) {
        this.supplier = supplier;
    }

    @Override
    public Object reader(Object[] args) {
        return supplier.get();
    }
}
