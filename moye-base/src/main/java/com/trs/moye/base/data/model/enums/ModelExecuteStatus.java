package com.trs.moye.base.data.model.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 数据建模执行状态
 *
 * <AUTHOR>
 * @since 2024/9/26 17:15
 */

@Getter
@AllArgsConstructor
public enum ModelExecuteStatus {

    /**
     * 停止
     */
    STOP("停止"),
    /**
     * 启动
     */
    START("启动"),
    /**
     * 暂停
     */
    PAUSE("暂停");

    private final String label;

    /**
     * 判断执行状态是否为开启
     *
     * @return 是否开启
     */
    public boolean isStart() {
        return this.equals(START);
    }

}
