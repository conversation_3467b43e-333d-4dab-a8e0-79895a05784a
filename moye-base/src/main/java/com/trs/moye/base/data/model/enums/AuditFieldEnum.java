package com.trs.moye.base.data.model.enums;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 审计字段枚举
 *
 * <AUTHOR>
 * @since 2024/11/13
 */
@Getter
@AllArgsConstructor
public enum AuditFieldEnum {

    /**
     * 入库时间
     */
    TRS_MOYE_INPUT_TIME("trs_moye_input_time"),
    /**
     * 批次号
     */
    TRS_MOYE_BATCH_NO("trs_moye_batch_no"),
    /**
     * 任务id
     */
    TRS_MOYE_TASK_ID("trs_moye_task_id"),

    /**
     * 数据recordId
     */
    TRS_MOYE_RECORD_ID("trs_moye_record_id"),

    /**
     * 文件名
     */
    TRS_MOYE_FILE_NAME("trs_moye_ftp_file_name");

    private final String name;

    /**
     * 根据名字获取枚举
     *
     * @param name 名字
     * @return 枚举值
     */
    public static AuditFieldEnum valueOfName(String name) {
        for (AuditFieldEnum auditFieldEnum : values()) {
            if (auditFieldEnum.getName().equals(name) || name.startsWith(auditFieldEnum.getName())) {
                return auditFieldEnum;
            }
        }
        return null;
    }

    /**
     * 获取审计字段名称列表
     *
     * @return 审计字段
     */
    public static List<String> names() {
        return Arrays.stream(AuditFieldEnum.values()).map(AuditFieldEnum::getName).collect(Collectors.toList());
    }

}
