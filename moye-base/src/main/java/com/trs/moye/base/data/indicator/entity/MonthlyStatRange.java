package com.trs.moye.base.data.indicator.entity;

import com.fasterxml.jackson.databind.JsonNode;
import java.time.LocalTime;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 按月统计范围（支持动态月末）
 */
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
public class MonthlyStatRange extends StatRange {

    /**
     * 开始日
     */
    private Integer startDay;
    /**
     * 结束日，"last_day" 表示月末
     */
    private String endDay;

    public MonthlyStatRange(Integer startDay, LocalTime startTime, String endDay, LocalTime endTime) {
        this.startDay = startDay;
        this.startTime = startTime;
        this.endDay = endDay;
        this.endTime = endTime;
    }

    @Override
    public MonthlyStatRange fromJson(JsonNode node) {
        super.fromJson(node);
        this.startDay = node.has("startDay") ? node.get("startDay").asInt() : null;
        this.endDay = node.has("endDay") ? node.get("endDay").asText() : null;
        return this;
    }

}