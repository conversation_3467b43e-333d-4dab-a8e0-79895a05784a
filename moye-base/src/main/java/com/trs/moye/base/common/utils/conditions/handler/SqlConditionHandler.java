package com.trs.moye.base.common.utils.conditions.handler;

import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.JsonNodeFactory;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.trs.moye.base.common.enums.FieldType;
import com.trs.moye.base.data.connection.enums.ConnectionType;
import com.trs.moye.base.data.service.entity.Condition;
import com.trs.moye.base.data.service.entity.ValueObject;
import java.util.List;

/**
 * sql条件处理
 *
 * <AUTHOR>
 * @since 2024/9/12
 */
public interface SqlConditionHandler {

    /**
     * 处理拼接where的条件
     *
     * @param whereClause    where
     * @param connectionType 连接类型
     * @param condition      条件
     */
    void handleSql(StringBuilder whereClause, ConnectionType connectionType, Condition condition);

    /**
     * 处理 Elasticsearch 的条件
     *
     * @param conditionJson   Elasticsearch 查询条件
     * @param key             字段名
     * @param values          字段值
     * @param isNotStringType 是否不是字符串类型
     */
    void handleEs(ObjectNode conditionJson, String key, List<ValueObject> values, Boolean isNotStringType);

    /**
     * 处理 HyBase 的条件
     *
     * @param clause 子句
     * @param key    字段名
     * @param values 字段值
     * <AUTHOR>
     * @since 2024/10/17 15:14:54
     */
    void handleHyBase(StringBuilder clause, String key, List<ValueObject> values);

    /**
     * 创建boolNode
     *
     * @param key    查询字段
     * @param values 值
     * @return boolNode
     */
    default ObjectNode createBoolNode(String key, List<ValueObject> values) {
        // 创建一个新的 ObjectNode 来表示 bool 查询
        ObjectNode boolNode = new ObjectNode(JsonNodeFactory.instance);
        // 创建一个 ArrayNode 来存储多个 match 查询
        ArrayNode mustArray = boolNode.putArray("should");
        // 遍历 values 数组，创建 match 查询
        for (ValueObject value : values) {
            // 创建一个新的 ObjectNode 来表示 match 查询
            ObjectNode matchNode = new ObjectNode(JsonNodeFactory.instance);
            matchNode.set("match_phrase", new ObjectNode(JsonNodeFactory.instance)
                .putPOJO(key, value.getValue()));

            // 将 match 查询添加到 must 数组中
            mustArray.add(matchNode);
        }
        return boolNode;
    }

    /**
     * 创建是否包含boolNode
     *
     * @param key    查询字段
     * @param values 值
     * @return boolNode
     */
    default ObjectNode createContainBoolNode(String key, List<ValueObject> values) {
        // 创建一个新的 ObjectNode 来表示 bool 查询
        ObjectNode boolNode = new ObjectNode(JsonNodeFactory.instance);
        // 创建一个 ArrayNode 来存储多个 match 查询
        ArrayNode mustArray = boolNode.putArray("must_not");

        // 遍历 values 数组，创建 match 查询
        for (ValueObject value : values) {
            // 创建一个新的 ObjectNode 来表示 match 查询
            ObjectNode matchNode = new ObjectNode(JsonNodeFactory.instance);

            matchNode.set("match", new ObjectNode(JsonNodeFactory.instance)
                .putPOJO(key, value.getValue()));

            // 将 match 查询添加到 must 数组中
            mustArray.add(matchNode);
        }
        return boolNode;
    }

    /**
     * 该方法判断是否不是String类型
     *
     * @param type 类型
     * @return true-是string   false-不是string
     */
    default boolean isStringType(FieldType type) {
        return (FieldType.TEXT.equals(type) || FieldType.STRING.equals(type) || FieldType.CHAR.equals(type));
    }

}
