package com.trs.moye.base.common.response;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2025-07-10 11:38
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
public class CountRemarkResponse extends AbstractRemarkResponse {

    private long count;

    public CountRemarkResponse(long count) {
        this.count = count;
    }

    public CountRemarkResponse(long count, String remark) {
        super(remark);
        this.count = count;
    }

    /**
     * 构造一个空的响应，附带备注信息
     *
     * @param remark 备注信息
     * @return CountRemarkResponse 实例，附带备注信息
     */
    public static CountRemarkResponse ofEmpty(String remark) {
        CountRemarkResponse response = new CountRemarkResponse(0L);
        response.appendRemark(remark);
        return response;
    }

    /**
     * 构造一个空的响应，不带备注信息
     *
     * @return CountRemarkResponse 实例，不带备注信息
     */
    public static CountRemarkResponse ofEmpty() {
        return ofEmpty(null);
    }
}
