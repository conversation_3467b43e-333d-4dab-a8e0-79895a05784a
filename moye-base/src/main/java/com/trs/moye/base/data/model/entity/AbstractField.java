package com.trs.moye.base.data.model.entity;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.trs.moye.base.common.entity.AuditBaseEntity;
import com.trs.moye.base.common.entity.field.FieldAdvanceConfig;
import com.trs.moye.base.common.enums.FieldType;
import com.trs.moye.base.common.typehandler.PolymorphismTypeHandler;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * DataStandardField实体类
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/09/19 10:31
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
@SuperBuilder
public abstract class AbstractField extends AuditBaseEntity {

    /**
     * 中文名：系统属性中文名
     */
    protected String zhName;

    /**
     * 系统属性英文名：系统属性英文名
     */
    protected String enName;

    /**
     * 系统属性类型：系统属性类型
     */
    protected FieldType type;

    /**
     * 类型中文名称
     */
    protected String typeName;

    /**
     * 允许多值。1：是否允许多值。1：是；0：否；默认否
     */
    protected boolean isMultiValue;

    /**
     * 允许空
     */
    protected boolean isNullable = true;

    /**
     * 系统属性描述：系统属性描述
     */
    protected String description;

    /**
     * 高级属性：高级属性
     */
    @TableField(typeHandler = PolymorphismTypeHandler.class, updateStrategy = FieldStrategy.ALWAYS)
    protected FieldAdvanceConfig advanceConfig;

    /**
     * 默认值
     */
    protected String defaultValue;
}