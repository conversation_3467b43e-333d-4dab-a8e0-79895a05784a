package com.trs.moye.base.common.log.data.service;

/**
 * <AUTHOR>
 * @since 2025/4/17
 **/
public class DataServiceLogUtils {


    private static final ThreadLocal<DataServiceLog> LOCAL = new ThreadLocal<>();

    private DataServiceLogUtils() {
    }


    /**
     * 设置当前线程的apiLog
     *
     * @param log apiLog
     */
    public static void set(DataServiceLog log) {
        LOCAL.set(log);
    }

    /**
     * 移除当前线程的apiLog
     */
    public static void remove() {
        LOCAL.remove();
    }

    /**
     * 获取当前线程的apiLog
     *
     * @return apiLog
     */
    public static DataServiceLog get() {
        return LOCAL.get();
    }

}
