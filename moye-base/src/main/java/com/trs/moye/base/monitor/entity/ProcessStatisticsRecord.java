package com.trs.moye.base.monitor.entity;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2025-07-14 17:37
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ProcessStatisticsRecord {

    /**
     * 时间点
     */
    private LocalDateTime timePoint;

    /**
     * 接入量总和
     */
    private long accessCountSum;

    /**
     * 处理量总和
     */
    private long processCountSum;

    /**
     * 平均处理积压量
     */
    private long processLagCountAvg;

    /**
     * 时长总和
     */
    private long timeDiffSum;

    /**
     * 接入TPS
     *
     * @return 接入TPS
     */
    public BigDecimal getAccessTps() {
        if (timeDiffSum <= 0){
            return BigDecimal.ZERO;
        }
        return BigDecimal.valueOf(accessCountSum).divide(BigDecimal.valueOf(timeDiffSum), 2, BigDecimal.ROUND_HALF_UP);
    }

    /**
     * 处理TPS
     *
     * @return 处理TPS
     */
    public BigDecimal getProcessTps() {
        if (timeDiffSum <= 0){
            return BigDecimal.ZERO;
        }
        return BigDecimal.valueOf(processCountSum).divide(BigDecimal.valueOf(timeDiffSum), 2, BigDecimal.ROUND_HALF_UP);
    }
}
