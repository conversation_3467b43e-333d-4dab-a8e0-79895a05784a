package com.trs.moye.base.monitor.entity;

import com.trs.moye.base.common.entity.mq.MqConsumeInfoResponse;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2025-06-04 17:09
 */
@Data
@NoArgsConstructor
public class TodayMonitorMetric {

    /**
     * 接入总量
     */
    protected Long accessCount;

    /**
     * 处理总量
     */
    protected Long processCount;

    /**
     * 今日接入量
     */
    protected Long todayAccessCount;

    /**
     * 今日处理量
     */
    protected Long todayProcessCount;

    public TodayMonitorMetric(MqConsumeInfoResponse consumeInfo, long todayStartOffset) {
        this.accessCount = consumeInfo.getEndOffset();
        this.processCount = consumeInfo.getCurrentOffset();
        this.todayAccessCount = Math.max(0, this.accessCount - todayStartOffset);
        this.todayProcessCount = Math.max(0, this.processCount - todayStartOffset);
    }

    /**
     * 获取一个全是0的监控指标
     *
     * @return 监控指标对象，所有值均为0
     */
    public static TodayMonitorMetric fromZeroMetric() {
        TodayMonitorMetric metric = new TodayMonitorMetric();
        metric.setAccessCount(0L);
        metric.setProcessCount(0L);
        metric.setTodayAccessCount(0L);
        metric.setTodayProcessCount(0L);
        return metric;
    }
}
