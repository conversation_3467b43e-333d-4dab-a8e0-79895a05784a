package com.trs.moye.base.common.deserializer;

import com.fasterxml.jackson.databind.BeanDescription;
import com.fasterxml.jackson.databind.DeserializationConfig;
import com.fasterxml.jackson.databind.JsonDeserializer;
import com.fasterxml.jackson.databind.deser.BeanDeserializerModifier;
import com.trs.moye.base.common.annotaion.JsonBean;
import com.trs.moye.base.common.exception.DeserializeException;
import com.trs.moye.base.common.utils.ReflectUtils;
import java.lang.reflect.Constructor;

/**
 * 多态Bean反序列化器修改器：把使用了@PolymorphismBean注解的类的Bean反序列化器修改为PolymorphismDeserializer 自定义
 *
 * <AUTHOR>
 * @since 2024-11-27 17:45
 */
public class JsonBeanDeserializerModifier extends BeanDeserializerModifier {

    @Override
    public JsonDeserializer<?> modifyDeserializer(DeserializationConfig config, BeanDescription beanDesc,
        JsonDeserializer<?> deserializer) {
        Class<?> beanClass = beanDesc.getBeanClass();
        JsonBean jsonBean = beanClass.getAnnotation(JsonBean.class);
        if (jsonBean != null && jsonBean.deserializer() != JsonDeserializer.None.class) {
            return createDeserializer(beanClass, jsonBean);
        }
        return super.modifyDeserializer(config, beanDesc, deserializer);
    }

    private JsonDeserializer<?> createDeserializer(Class<?> beanClass, JsonBean jsonBean) {
        Class<? extends JsonDeserializer> deserializer = jsonBean.deserializer();
        Constructor<?> constructor = deserializer.getConstructors()[0];
        if (constructor.getParameterCount() == 0) {
            return (JsonDeserializer<?>) ReflectUtils.invokeConstructor(constructor);
        } else if (constructor.getParameterCount() == 1) {
            return (JsonDeserializer<?>) ReflectUtils.invokeConstructor(constructor, beanClass);
        } else {
            throw new DeserializeException("自定义反序列化器【%s】构造方法参数个数不正确，自定义反序列化器构造函数仅支持0个或1个"
                , deserializer.getName());
        }
    }
}
