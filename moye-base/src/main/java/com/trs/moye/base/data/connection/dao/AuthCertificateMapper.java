package com.trs.moye.base.data.connection.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.trs.moye.base.data.connection.entity.KerberosCertificate;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 认证证书Mapper
 */
@Mapper
public interface AuthCertificateMapper extends BaseMapper<KerberosCertificate> {

    /**
     * 通过id获取认证证书
     *
     * @param id 认证证书id
     * @return {@link KerberosCertificate}
     * <AUTHOR>
     * @since 2024/9/18 18:21
     **/
    KerberosCertificate selectById(Integer id);

    /**
     * 获取全部证书
     *
     * @return {@link KerberosCertificate}
     * <AUTHOR>
     * @since 2024/9/18 18:21
     **/
    List<KerberosCertificate> selectAll();

    /**
     * 用户证书列表
     *
     * @param userId 用户id
     * @return 创建人
     */
    List<KerberosCertificate> selectByUserId(@Param("userId") Integer userId);
}
