package com.trs.moye.base.common.utils.conditions.handler.logic.connectives;

/**
 * 处理AND
 *
 * <AUTHOR>
 * @since 2024/10/14
 */
public class AndHandler implements LogicConnectivesHandler {

    private static final String AND = " AND ";

    @Override
    public void handleNormalJdbc(StringBuilder whereClause) {
        whereClause.append(AND); // 添加 AND
    }


    @Override
    public void handleHyBase(StringBuilder whereClause) {
        handleNormalJdbc(whereClause);
    }
}
