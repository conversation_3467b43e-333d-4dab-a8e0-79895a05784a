//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by Fern<PERSON>lower decompiler)
//

package com.trs.moye.base.common.exception;

/**
 * 参数验证异常
 *
 */
public class ParamValidateException extends RuntimeException {
    public ParamValidateException() {
    }

    public ParamValidateException(String message) {
        super(message);
    }

    public ParamValidateException(String message, Throwable cause) {
        super(message, cause);
    }

    public ParamValidateException(Throwable cause) {
        super(cause);
    }

    public ParamValidateException(String format, Object... objects) {
        super(String.format(format, objects));
    }

    public ParamValidateException(Throwable cause, String message) {
        super(message, cause);
    }

    public ParamValidateException(Throwable cause, String format, Object... objects) {
        super(String.format(format, objects), cause);
    }
}
