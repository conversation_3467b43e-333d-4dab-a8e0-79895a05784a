package com.trs.moye.base.data.model.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.trs.moye.base.data.model.entity.CategoryOrderConfig;
import com.trs.moye.base.data.model.enums.CategoryOrderType;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;


/**
 * 分类次序配置映射
 *
 * <AUTHOR>
 * @since 2025/06/19 15:40:41
 */
@Mapper
public interface CategoryOrderConfigMapper extends BaseMapper<CategoryOrderConfig> {

    /**
     * 根据类型和分类id查询分类次序配置
     *
     * @param type       类型
     * @param categoryId 分类id
     * @return {@link CategoryOrderConfig }
     * <AUTHOR>
     * @since 2025/06/19 16:00:40
     */
    CategoryOrderConfig selectByTypeAndCategoryId(@Param("type") CategoryOrderType type,
        @Param("categoryId") Integer categoryId);

    /**
     * 根据类型查询分类次序配置
     *
     * @param type 类型
     * @return {@link List }<{@link CategoryOrderConfig }>
     * <AUTHOR>
     * @since 2025/06/19 16:00:40
     */
    List<CategoryOrderConfig> selectByType(@Param("type") CategoryOrderType type);

}
