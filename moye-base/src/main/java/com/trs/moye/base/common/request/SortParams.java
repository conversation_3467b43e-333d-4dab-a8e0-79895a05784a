package com.trs.moye.base.common.request;

import com.trs.moye.base.common.annotaion.validation.SafeSqlField;
import com.trs.moye.base.common.enums.SortOrder;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 排序参数
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class SortParams {

    /**
     * 排序字段名称
     */
    @SafeSqlField
    private String field;

    /**
     * 排序方向
     */
    private SortOrder order;
}
