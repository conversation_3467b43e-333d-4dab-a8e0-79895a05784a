package com.trs.moye.base.common.request;

import com.trs.moye.base.common.annotaion.validation.SafeSqlField;
import java.util.List;
import lombok.Data;
import lombok.RequiredArgsConstructor;

/**
 * 检索参数
 *
 * <AUTHOR>
 */
@Data
@RequiredArgsConstructor
public class SearchParams {

    /**
     * 检索字段
     */
    private List<@SafeSqlField String> fields;

    /**
     * 检索关键字
     */
    private Object keyword;
}
