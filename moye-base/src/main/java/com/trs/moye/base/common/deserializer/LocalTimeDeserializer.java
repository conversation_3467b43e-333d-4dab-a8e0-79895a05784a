package com.trs.moye.base.common.deserializer;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import java.io.IOException;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;

/**
 * 本地时间反序列化器
 *
 * <AUTHOR>
 * @since 2025/05/19 17:13:24
 */
public class LocalTimeDeserializer extends JsonDeserializer<LocalTime> {

    private static final DateTimeFormatter TIME_FORMATTER = DateTimeFormatter.ofPattern("HH:mm:ss");

    @Override
    public LocalTime deserialize(JsonParser p, DeserializationContext ctxt) throws IOException {
        String timeStr = p.getValueAsString();
        if (timeStr == null || timeStr.isEmpty()) {
            return null;
        }

        // 将 "23:59:59" 解析为 LocalTime.MAX
        if ("23:59:59".equals(timeStr)) {
            return LocalTime.MAX;
        }

        // 也处理完整表示形式（兼容性）
        if ("23:59:59.999999999".equals(timeStr)) {
            return LocalTime.MAX;
        }
        return LocalTime.parse(timeStr, TIME_FORMATTER);
    }
}