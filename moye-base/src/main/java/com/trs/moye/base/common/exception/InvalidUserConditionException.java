package com.trs.moye.base.common.exception;

/**
 * 表示用户条件无效时抛出的异常。 这个异常类扩展了 {@link RuntimeException}，并增加了建议信息的功能。
 */
public class InvalidUserConditionException extends RuntimeException {

    /**
     * 向用户提供的建议信息
     */
    private String suggestion;

    /**
     * 使用指定的错误消息构造一个新的异常实例。
     *
     * @param message 详细描述这个异常的消息
     */
    public InvalidUserConditionException(String message) {
        super(message);
    }

    /**
     * 使用指定的错误消息和建议信息构造一个新的异常实例。
     *
     * @param message    详细描述这个异常的消息
     * @param suggestion 向用户提供的建议信息
     */
    public InvalidUserConditionException(String message, String suggestion) {
        super(message);
        this.suggestion = suggestion;
    }

    /**
     * 获取异常的建议信息。
     *
     * @return 建议信息，如果未设置则返回 null
     */
    public String getSuggestion() {
        return suggestion;
    }
}