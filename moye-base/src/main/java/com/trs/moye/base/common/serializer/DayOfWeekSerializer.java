package com.trs.moye.base.common.serializer;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;
import java.io.IOException;
import java.time.DayOfWeek;

/**
 * 星期序列化列器
 *
 * <AUTHOR>
 * @since 2025/05/20 16:40:27
 */
public class DayOfWeekSerializer extends JsonSerializer<DayOfWeek> {

    @Override
    public void serialize(DayOfWeek value, JsonGenerator gen, SerializerProvider serializers) throws IOException {
        if (value != null) {
            gen.writeString(String.valueOf(value.getValue()));
        } else {
            gen.writeNull();
        }
    }
}