package com.trs.moye.base.knowledgebase.entity;


import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2023/12/12
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
public class ImportEntityDataResult {

    /**
     * 导入成功的数据
     */
    List<Object> successData;

    /**
     * 导入失败的数据
     */
    List<FailReason> failData;


    /**
     * 失败原因
     *
     * <AUTHOR>
     * @since 2024/11/27 15:49
     */

    @Data
    public static class FailReason {

        /**
         * 导入失败的数据
         */
        private Object data;

        /**
         * 导入失败的原因
         */
        private String failReason;


        /**
         * 构造一个失败原因
         *
         * @param data    数据
         * @param message 信息
         * <AUTHOR>
         * @since 2024/11/27 15:42
         */
        public FailReason(List<String> data, String message) {
            this.data = data;
            this.failReason = message;
        }
    }
}
