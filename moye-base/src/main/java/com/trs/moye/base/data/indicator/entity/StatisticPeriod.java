package com.trs.moye.base.data.indicator.entity;

import com.trs.moye.base.data.indicator.enums.IndicatorPeriodType;
import java.time.LocalDateTime;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 统计周期
 *
 * <AUTHOR>
 * @since 2025/05/20 10:41:12
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class StatisticPeriod {

    private LocalDateTime startTime;
    private LocalDateTime endTime;

    /**
     * 减去指定周期数的时间
     *
     * @param periodType 期间类型
     * @param minusNum   减去的周期数
     * @param time       时间
     * @return {@link LocalDateTime }
     * <AUTHOR>
     * @since 2025/06/13 20:23:32
     */
    public static LocalDateTime minusPeriod(IndicatorPeriodType periodType, Long minusNum,
        LocalDateTime time) {
        switch (periodType) {
            case DAILY:
                return time.minusDays(minusNum);
            case WEEKLY:
                return time.minusWeeks(minusNum);
            case MONTHLY:
            case QUARTERLY:
            case SEMIANNUALLY:
                return time.minusMonths(minusNum);
            case YEARLY:
                return time.minusYears(minusNum);
            default:
                return time;
        }
    }

    /**
     * 增加指定周期数的时间
     *
     * @param periodType 期间类型
     * @param addNum     增加的周期数
     * @param time       时间
     * @return {@link LocalDateTime }
     * <AUTHOR>
     * @since 2025/06/13 21:15:42
     */
    public static LocalDateTime plusPeriod(IndicatorPeriodType periodType, Long addNum,
        LocalDateTime time) {
        switch (periodType) {
            case DAILY:
                return time.plusDays(addNum);
            case WEEKLY:
                return time.plusWeeks(addNum);
            case MONTHLY:
            case QUARTERLY:
            case SEMIANNUALLY:
                return time.plusMonths(addNum);
            case YEARLY:
                return time.plusYears(addNum);
            default:
                return time;
        }
    }
}