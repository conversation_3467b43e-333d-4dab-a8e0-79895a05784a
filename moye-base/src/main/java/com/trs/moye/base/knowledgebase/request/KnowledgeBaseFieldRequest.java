package com.trs.moye.base.knowledgebase.request;

import com.baomidou.mybatisplus.annotation.TableField;
import com.trs.moye.base.common.annotaion.validation.NotSupportValue;
import com.trs.moye.base.common.entity.field.FieldAdvanceConfig;
import com.trs.moye.base.common.enums.FieldType;
import com.trs.moye.base.common.typehandler.PolymorphismTypeHandler;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 2024-11-20 21:40
 */
@Data
@NoArgsConstructor
public class KnowledgeBaseFieldRequest {

    /**
     * 中文名：系统属性中文名
     */
    @NotBlank(message = "字段中文名不能为空")
    protected String zhName;

    /**
     * 系统属性英文名：系统属性英文名
     */
    @NotBlank(message = "字段英文名不能为空")
    @NotSupportValue(values = "id", message = "【id】不支持作为知识库属性英文名")
    protected String enName;

    /**
     * 系统属性类型：系统属性类型
     */
    @NotNull(message = "字段类型不能为空")
    protected FieldType type;

    /**
     * 类型中文名称
     */
    @NotBlank(message = "字段类型不能为空")
    protected String typeName;

    /**
     * 系统属性描述：系统属性描述
     */
    protected String description;

    /**
     * 是否唯一 属于：实体库、标签库
     */
    private boolean isUnique;

    /**
     * 允许多值 属于：复合类型
     */
    protected boolean isMultiValue;

    /**
     * 高级属性 属于：复合类型
     */
    @TableField(typeHandler = PolymorphismTypeHandler.class)
    protected FieldAdvanceConfig advanceConfig;

}
