package com.trs.moye.base.common.help;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.Getter;
import org.apache.commons.lang3.ObjectUtils;

/**
 * 增删改数据分类器
 *
 * @param <T> 新数据类型
 * @param <K> 键类型
 * <AUTHOR>
 * @since 2025-01-06 17:29
 */
@Getter
public class AddUpdateDeleteSeparator<T, K> {

    private final List<T> addDataList;

    private final List<T> updateDataList;

    private final List<K> deleteKeyList;

    /**
     * 构造器
     *
     * @param oldKeyList 旧数据键列表
     * @param newDataList 新数据列表
     * @param keyMapper 数据键映射函数
     */
    public AddUpdateDeleteSeparator(Collection<K> oldKeyList, Collection<T> newDataList, Function<T, K> keyMapper) {
        if (oldKeyList == null) {
            oldKeyList = new ArrayList<>();
        }
        if (newDataList == null) {
            newDataList = new ArrayList<>();
        }
        addDataList = new ArrayList<>();
        updateDataList = new ArrayList<>();
        deleteKeyList = new ArrayList<>(oldKeyList);
        for (T newData : newDataList) {
            K key = keyMapper.apply(newData);
            if (ObjectUtils.isEmpty(key)) {
                addDataList.add(newData);
            } else {
                updateDataList.add(newData);
                deleteKeyList.remove(key);
            }
        }
    }

    /**
     * 创建分离器
     *
     * @param oldDataList 旧数据列表
     * @param oldKeyMapper 旧数据键映射
     * @param newDataList 新数据列表
     * @param keyMapper 新数据键映射
     * @param <O> 旧数据类型
     * @param <T> 新数据类型
     * @param <K> 键类型
     * @return 分离器
     */
    public static <O, T, K> AddUpdateDeleteSeparator<T, K> create(Collection<O> oldDataList,
        Function<O, K> oldKeyMapper, Collection<T> newDataList,
        Function<T, K> keyMapper) {
        return new AddUpdateDeleteSeparator<>(
            oldDataList == null ? new ArrayList<>() : oldDataList.stream().map(oldKeyMapper).collect(Collectors.toSet())
            , newDataList, keyMapper);
    }
}
