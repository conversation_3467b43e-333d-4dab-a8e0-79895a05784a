package com.trs.moye.base.common.deserializer;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.BeanProperty;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.deser.ContextualDeserializer;
import com.trs.moye.base.common.annotaion.processor.PolymorphismDeserializeProcessor;
import java.io.IOException;

/**
 * 多态反序列化器：用于处理多态反序列化
 *  这个反序列化器的作用是避免每个抽象基类都要写一个反序列化器。但是与单独定义一个普通多态反序列化器的操作步骤是一样的。
 *
 *  使用时需要配合@DeserializeConfig注解使用不可用
 *
 * @param <T> 多态基类类型
 * <AUTHOR>
 * @version 1.0
 * @since 2024-09-24 22:05
 */
public class PolymorphismDeserializer<T> extends JsonDeserializer<T> implements ContextualDeserializer {

    private final Class<T> clazz;

    public PolymorphismDeserializer(Class<T> clazz) {
        this.clazz = clazz;
    }

    @Override
    public T deserialize(JsonParser p, DeserializationContext ctxt)
        throws IOException {
        JsonNode treeNode = p.getCodec().readTree(p);
        return PolymorphismDeserializeProcessor.parseJsonNode(clazz, treeNode);
    }

    @Override
    public JsonDeserializer<?> createContextual(DeserializationContext ctxt, BeanProperty property) {
        return this;
    }
}
