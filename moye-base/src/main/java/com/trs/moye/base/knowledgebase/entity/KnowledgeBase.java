package com.trs.moye.base.knowledgebase.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.trs.moye.base.common.entity.AuditBaseEntity;
import com.trs.moye.base.common.typehandler.CustomJacksonTypeHandler;
import com.trs.moye.base.knowledgebase.enums.KnowledgeBaseType;
import java.time.LocalDateTime;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 2024-11-20 21:40
 */
@TableName(value = "knowledge_base", autoResultMap = true)
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
public class KnowledgeBase extends AuditBaseEntity {

    /**
     * 实体中文名称
     */
    private String zhName;

    /**
     * 实体英文名称
     */
    private String enName;

    /**
     * 描述信息
     */
    @TableField(value = "description")
    private String desc;

    /**
     * 实体标签 用于分组
     */
    @TableField(
        value = "tags",
        typeHandler = CustomJacksonTypeHandler.class // 指定你的自定义处理器
    )
    private List<String> tags;

    /**
     * 知识库类型
     */
    private KnowledgeBaseType type;

    /**
     * 判断是否是系统资源： 1：是；0：否
     */
    private boolean isSysSource;

    /**
     * 是否备份 1：是； 0：否
     */
    private boolean isBackup;

    /**
     * 最近备份时间
     */
    private LocalDateTime recentBackupTime;

    /**
     * 字段
     */
    @TableField(exist = false)
    private List<KnowledgeBaseField> fields;

    /**
     * 构造表名
     *
     * @return 表名
     */
    public String buildTableName() {
        return type.buildTableName(getEnName());
    }

}
