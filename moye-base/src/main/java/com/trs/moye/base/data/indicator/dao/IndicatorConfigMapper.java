package com.trs.moye.base.data.indicator.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.trs.moye.base.data.indicator.entity.IndicatorConfig;
import org.apache.ibatis.annotations.Mapper;

/**
 * IndicatorConfig数据访问层
 *
 * <AUTHOR>
 * @since 2025/05/20
 */
@Mapper
public interface IndicatorConfigMapper extends BaseMapper<IndicatorConfig> {

    /**
     * 根据数据建模id查询
     *
     * @param dataModelId 数据建模id
     * @return {@link IndicatorConfig }
     * <AUTHOR>
     * @since 2025/05/20 16:59:35
     */
    IndicatorConfig selectByDataModelId(Integer dataModelId);

    /**
     * 根据数据建模id删除
     *
     * @param dataModelId 数据型id
     * <AUTHOR>
     * @since 2025/05/20 19:03:57
     */
    void deleteByDataModelId(Integer dataModelId);

    /**
     * 更新指标配置
     *
     * @param indicatorConfig 指标配置实体
     * <AUTHOR>
     * @since 2025/05/20
     */
    void updateByDataModelId(IndicatorConfig indicatorConfig);

}
