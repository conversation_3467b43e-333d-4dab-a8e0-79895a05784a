package com.trs.moye.base.data.source.setting.file.chosen;

import com.trs.moye.base.common.annotaion.JsonBean;
import com.trs.moye.base.common.annotaion.PolymorphismSign;
import com.trs.moye.base.data.connection.enums.FileChosenType;
import com.trs.moye.base.common.deserializer.PolymorphismDeserializer;
import javax.validation.constraints.NotNull;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2024-09-30 15:46
 */
@JsonBean(deserializer = PolymorphismDeserializer.class)
@Data
public abstract class FileChosenConfig {

    /**
     * 选择类型
     */
    @NotNull(message = "文件选择类型不能为空")
    @PolymorphismSign
    protected FileChosenType fileChosenType;

    /**
     * 匹配文件名
     *
     * @param notSuffixFileName 文件名(不包含后缀)
     * @return 布尔
     */
    public abstract boolean matchName(String notSuffixFileName);

    /**
     * 获取匹配正则
     *
     * @param extension 文件扩展名
     * @return 匹配正则
     */
    public abstract String getMatcherPattern(String extension);
}
