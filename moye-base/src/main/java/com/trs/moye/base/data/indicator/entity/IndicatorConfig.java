package com.trs.moye.base.data.indicator.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.trs.moye.base.common.entity.AuditBaseEntity;
import com.trs.moye.base.common.typehandler.CustomJacksonTypeHandler;
import com.trs.moye.base.data.service.entity.Condition;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 指标库详细信息
 *
 * <AUTHOR>
 * @since 2025/05/15 17:01:49
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName("data_model_indicator_config")
public class IndicatorConfig extends AuditBaseEntity {

    /**
     * 数据建模id
     */
    private Integer dataModelId;

    /**
     * 统计策略
     */
    @TableField(typeHandler = CustomJacksonTypeHandler.class)
    private IndicatorStatisticStrategyInfo statisticStrategyInfo;

    /**
     * 业务逻辑条件
     */
    @TableField(typeHandler = CustomJacksonTypeHandler.class)
    private Condition[] conditions;

    /**
     * 统计 SQL
     */
    private String statisticSql;
}
