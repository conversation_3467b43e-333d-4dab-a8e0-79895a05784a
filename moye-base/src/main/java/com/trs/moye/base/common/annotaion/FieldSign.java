package com.trs.moye.base.common.annotaion;

import com.trs.moye.base.common.annotaion.enums.SignField;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * <AUTHOR>
 * @since 2025-03-04 17:44
 */
@Retention(RetentionPolicy.RUNTIME)
@Target({ElementType.FIELD, ElementType.PARAMETER})
public @interface FieldSign {

    /**
     * 标记字段
     *
     * @return 字段枚举
     */
    SignField field();
}
