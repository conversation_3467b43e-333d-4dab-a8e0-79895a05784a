package com.trs.moye.base.common.help.container;

import java.util.function.Function;

/**
 * 简单的同步容器：
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-07-15 14:04
 * @param <K> 元素保存在容器中的索引类型
 * @param <E> 容器元素类型
 */
public class SimpleSyncContainer<K, E> extends AbstractSyncContainer<K, K, E> {

    private final Function<K, E> elementGenerator;

    public SimpleSyncContainer(Function<K, E> elementGenerator) {
        this.elementGenerator = elementGenerator;
    }

    @Override
    public K generateKey(K key) {
        return key;
    }

    @Override
    public E generateElement(K key) {
        return elementGenerator.apply(key);
    }
}
