package com.trs.moye.base.common.entity.field;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 时间或日期字段高级配置
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-09-19 10:57
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class DateOrTimeAdvanceConfig extends FieldAdvanceConfig {

    /**
     * 最小值
     */
    private String minValue;

    /**
     * 最大值
     */
    private String maxValue;
}
