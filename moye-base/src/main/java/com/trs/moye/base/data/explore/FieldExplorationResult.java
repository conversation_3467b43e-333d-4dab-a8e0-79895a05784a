package com.trs.moye.base.data.explore;

import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 字段数据探查结果
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FieldExplorationResult {

    /**
     * 字段英文名
     */
    private String fieldName;

    /**
     * 字段类型
     */
    private String fieldType;

    /**
     * 字段中文名
     */
    private String fieldZhName;

    /**
     * 总记录数
     */
    private long totalRecords;

    /**
     * NULL值数量
     */
    private long nullCount;

    /**
     * 唯一值数量
     */
    private long distinctCount;

    /**
     * 最小值
     */
    private Object minValue;

    /**
     * 最大值
     */
    private Object maxValue;

    /**
     * 最常见的值
     */
    private List<String> mostCommonValues;

    /**
     * 最小长度 (仅字符串类型)
     */
    private Integer minLength;

    /**
     * 最大长度 (仅字符串类型)
     */
    private Integer maxLength;

    /**
     * 是否所有值都像数字 (仅字符串类型)
     */
    private Boolean allNumeric;
}
