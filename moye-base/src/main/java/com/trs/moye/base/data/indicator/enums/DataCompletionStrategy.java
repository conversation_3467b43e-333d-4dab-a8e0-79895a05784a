package com.trs.moye.base.data.indicator.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 数据补齐策略枚举
 * <p>
 * 定义了数据补齐功能支持的不同策略模式，用于控制如何基于历史数据进行数据补齐
 * </p>
 *
 * <AUTHOR>
 * @since 2025/07/24
 */
@AllArgsConstructor
@Getter
public enum DataCompletionStrategy {

    /**
     * 去年同期补齐策略
     * <p>
     * 根据当前数据周期确定去年对应的同期范围进行补齐。
     * </p>
     * <p>
     * 示例：当前周期为1-5月
     * <ul>
     *   <li>去年同期范围：去年1-5月</li>
     *   <li>如果今年实际数据只有1-2月，而去年同期有1-5月的完整数据</li>
     *   <li>则补齐今年缺失的3-5月数据</li>
     * </ul>
     * </p>
     */
    SAME_PERIOD_LAST_YEAR("去年同期补齐"),

    /**
     * 去年全年补齐策略
     * <p>
     * 无论当前周期范围如何，都基于去年全年数据进行补齐。
     * </p>
     * <p>
     * 示例：当前周期为1-5月
     * <ul>
     *   <li>如果今年实际数据只有1-2月，则补齐3-12月（基于去年对应月份）</li>
     *   <li>如果今年实际数据有1-5月，则补齐6-12月（基于去年对应月份）</li>
     * </ul>
     * </p>
     */
    FULL_YEAR_LAST_YEAR("去年全年补齐");

    /**
     * 策略描述
     */
    private final String description;

    /**
     * 判断是否为去年全年补齐策略
     *
     * @return 如果是去年全年补齐策略返回true，否则返回false
     */
    public boolean isFullYearLastYear() {
        return this == FULL_YEAR_LAST_YEAR;
    }

    /**
     * 获取数据类型描述
     *
     * @return {@link String }
     * <AUTHOR>
     * @since 2025/07/24 19:49:24
     */
    public String getDataTypeDescription() {
        if (isFullYearLastYear()) {
            return "去年全年数据量";
        } else {
            return "去年同期数据量";
        }
    }
}
