package com.trs.moye.base.common.utils.conditions.handler;

import com.fasterxml.jackson.databind.node.ObjectNode;
import com.trs.moye.base.data.connection.enums.ConnectionType;
import com.trs.moye.base.data.service.entity.Condition;
import com.trs.moye.base.data.service.entity.ValueObject;
import java.util.List;

/**
 * 正则条件
 *
 * <AUTHOR>
 * @since 2025/06/17
 */
public class RegexConditionHandler implements SqlConditionHandler {

    @Override
    public void handleSql(StringBuilder whereClause, ConnectionType connectionType, Condition condition) {
        String key = connectionType.escapeIdentifier(condition.getKey().getEnName());
        String regexPattern = condition.getValues().get(0).getValue();

        switch(connectionType) {
            case MYSQL:
            case DORIS:
            case HIVE:
                // Hive更推荐使用RLIKE，但是这里实际更多的是spark假借hive type
                whereClause.append(key).append(" REGEXP '").append(regexPattern).append("'");
                break;
            case CLICK_HOUSE:
                // ClickHouse推荐使用match函数
                whereClause.append("match(").append(key).append(", '").append(regexPattern).append("')");
                break;
            case ORACLE:
                // Oracle使用REGEXP_LIKE函数
                whereClause.append("REGEXP_LIKE(").append(key).append(", '").append(regexPattern).append("')");
                break;
            case POSTGRESQL:
                // PostgreSQL使用~运算符
                whereClause.append(key).append(" ~ '").append(regexPattern).append("'");
                break;
            default:
                // ES和HyBase不支持在SQL中使用正则，抛出异常
                throw new UnsupportedOperationException(connectionType.name() + "不支持正则表达式条件");
        }
    }


    @Override
    public void handleEs(ObjectNode conditionJson, String key, List<ValueObject> values, Boolean isNotStringType) {
        throw new UnsupportedOperationException("正则表达式条件不支持es");
    }


    @Override
    public void handleHyBase(StringBuilder clause, String key, List<ValueObject> values) {
        throw new UnsupportedOperationException("正则表达式条件不支持hybase");
    }
}
