package com.trs.moye.base.data.connection.entity.params;

import com.trs.moye.base.data.connection.entity.KerberosCertificate;
import java.util.Objects;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;

/**
 * Hive连接参数
 *
 * <AUTHOR>
 * @since 2024/11/21
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Slf4j
public class HiveConnectionParams extends JdbcDatabaseConnectionParams {

    /**
     * 元数据存储地址
     */
    private String metastoreUri;

    /**
     * HDFS配置文件路径
     */
    private String hdfsSitePath;

    /**
     * Hive配置文件路径
     */
    private String hiveSitePath;

    /**
     * 创建hive连接的jdbcUrl
     *
     * @return jdbcUrl
     */
    @Override
    public String getJdbcUrl(KerberosCertificate kerberos) {
        String url = "jdbc:hive2://" + getHost() + ":" + getPort() + "/" + database;
        if (Objects.nonNull(kerberos)) {
            url += ";serviceDiscoveryMode=zooKeeper;zooKeeperNamespace=hiveserver2;";
            String bigdataPlatform = System.getenv("BIGDATA_PLATFORM");
            log.info("bigdataPlatform:{}", bigdataPlatform);
            if ("huawei".equalsIgnoreCase(bigdataPlatform)) {
                url += String.format(
                    "sasl.qop=auth-conf;auth=KERBEROS;principal=hive/<EMAIL>;user.principal=%s;user.keytab=%s",
                    kerberos.getPrincipal(), kerberos.getKeytabPath());
            }
        }
        return url;
    }

    @Override
    public String getJdbcDriverName(KerberosCertificate kerberos) {
        return "org.apache.hive.jdbc.HiveDriver";
    }
}
