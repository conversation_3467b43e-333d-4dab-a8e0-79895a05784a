package com.trs.moye.base.monitor.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.trs.moye.base.common.entity.AuditBaseEntity;
import com.trs.moye.base.common.enums.TimeUnit;
import com.trs.moye.base.common.enums.status.StartStop;
import com.trs.moye.base.monitor.enums.DataProcessMonitorType;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * DataProcessMonitorConfig实体类
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025/03/13 10:11
 */
@TableName(value = "data_process_monitor_config", autoResultMap = true)
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
public class DataProcessMonitorConfig extends AuditBaseEntity {

    /**
     * 元数据id
     */
    private Integer dataModelId;

    /**
     * 类型：DATA_PROCESS(数据处理)、DATA_STORAGE(数据存储)
     */
    private DataProcessMonitorType type;

    /**
     * 周期
     */
    private Integer periodValue;

    /**
     * 周期单位：周期单位：SECOND(秒),MINUTE(分钟)
     */
    private TimeUnit periodUnit;

    /**
     * 启用状态；1-启用，0-关闭
     */
    private boolean isEnable;

    /**
     * 调度状态：START(启动),STOP(停止)
     */
    private StartStop scheduleStatus;

    /**
     * 是否记录完整日志：是否记录完整日志
     */
    private boolean recordCompleteLogs;

    /**
     * xxl-job任务id
     */
    private Integer xxlJobId;
}