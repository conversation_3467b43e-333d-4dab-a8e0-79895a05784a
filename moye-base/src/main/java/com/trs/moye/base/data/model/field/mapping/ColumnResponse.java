package com.trs.moye.base.data.model.field.mapping;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

/**
 * 存储引擎查询库表表字段的字段信息
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
public class ColumnResponse extends BaseColumnResponse {

    /**
     * 字段类型
     */
    private String type;

    /**
     * 将名字中的-替换为_
     */
    public void replaceDashesInTableNames() {
        if (enName != null) {
            enName = enName.replace("-", "_");
        }
    }

    /**
     * 检查中文名是否为空，为空则使用英文名
     */
    public void checkColumnZhName() {
        zhName = StringUtils.isEmpty(StringUtils.trim(zhName)) ? enName : StringUtils.trim(zhName);
    }
}
