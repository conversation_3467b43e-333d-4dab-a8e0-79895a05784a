package com.trs.moye.ability.base;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;

import java.util.stream.Stream;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;

/**
 * JsonAbilityTest
 *
 * <AUTHOR>
 * @since 2025/3/17 15:17
 */
class JsonAbilityTest {


    @ParameterizedTest
    @MethodSource("jsonArrayJoinProvider")
    void testJsonArrayJoin(String inputText, String delimiter, String expected) {
        assertEquals(expected, JsonAbility.jsonArrayJoin(inputText, delimiter));
    }

    private static Stream<Arguments> jsonArrayJoinProvider() {
        return Stream.of(Arguments.of("", ",", ""),          // 空输入
            Arguments.of("[1,2,3]", ",", "1,2,3"), // 有效JSON数组
            Arguments.of("[1,2,3]", null, "123") // 空分隔符
        );
    }

    @Test
    void jsonArrayJoin_InvalidInput_ThrowsException() {
        assertThrows(RuntimeException.class, () -> JsonAbility.jsonArrayJoin("invalid json", ","));
    }


    @ParameterizedTest
    @MethodSource("getJsonFieldFromJsonPathProvider")
    void testGetJsonFieldFromJsonPath(String inputText, String jsonPath, String expected) {
        assertEquals(expected, JsonAbility.getJsonFieldFromJsonPath(inputText, jsonPath));
    }

    private static Stream<Arguments> getJsonFieldFromJsonPathProvider() {
        return Stream.of(Arguments.of("{\"name\": \"张三\"}", "$.name", "张三"), // 正常情况
            Arguments.of("", "$.name", "")      // 空输入
        );
    }

    @Test
    void testGetJsonFieldFromJsonPath_InvalidPath_ThrowsException() {
        String inputText = "{\"name\": \"张三\"}";
        String invalidPath = "$.invalid";

        Exception exception = assertThrows(RuntimeException.class, () -> JsonAbility.getJsonFieldFromJsonPath(inputText, invalidPath));

        assertEquals("No results for path: $['invalid']", exception.getMessage());
    }

    @ParameterizedTest
    @MethodSource("testGetDataByJsonPathProvider")
    void testGetDataByJsonPath(String json, String jsonPath, String isArray, Object expected) {
        Object result = JsonAbility.getDataByJsonPath(json, jsonPath, isArray);
        Assertions.assertEquals(expected, result);
    }

    private static Stream<Arguments> testGetDataByJsonPathProvider() {
        String JSON_DATA = "{\n" +
                           "  \"data\": {\n" +
                           "    \"name\": \"张三\",\n" +
                           "    \"age\": 18,\n" +
                           "    \"address\": {\n" +
                           "      \"province\": \"湖南省\",\n" +
                           "      \"city\": \"长沙市\"\n" +
                           "    },\n" +
                           "    \"hobbies\": [\n" +
                           "      \"篮球\",\n" +
                           "      \"足球\",\n" +
                           "      \"乒乓球\",\n" +
                           "      null\n" +
                           "    ]\n" +
                           "  }\n" +
                           "}\n";

        String JSON_ARRAY = "[\n" +
                            "    {\n" +
                            "        \"data\": {\n" +
                            "            \"name\": \"张三\",\n" +
                            "            \"age\": 18,\n" +
                            "            \"address\": {\n" +
                            "                \"province\": \"湖南省\",\n" +
                            "                \"city\": \"长沙市\"\n" +
                            "            },\n" +
                            "            \"hobbies\": [\n" +
                            "                \"篮球\",\n" +
                            "                \"足球\",\n" +
                            "                \"乒乓球\"\n" +
                            "            ]\n" +
                            "        }\n" +
                            "    },\n" +
                            "    {\n" +
                            "        \"data\": {\n" +
                            "            \"name\": \"李四\",\n" +
                            "            \"age\": 20,\n" +
                            "            \"address\": {\n" +
                            "                \"province\": \"湖北省\",\n" +
                            "                \"city\": \"武汉市\"\n" +
                            "            },\n" +
                            "            \"hobbies\": [\n" +
                            "                \"游泳\",\n" +
                            "                \"跑步\",\n" +
                            "                \"骑行\"\n" +
                            "            ]\n" +
                            "        }\n" +
                            "    }\n" +
                            "]\n";
        return Stream.of(
            // 测试单个 JSON 对象
            Arguments.of(JSON_DATA, "data.name", "false", "张三"),
            Arguments.of(JSON_DATA, "data.address.city", "false", "长沙市"),
            Arguments.of(JSON_DATA, "data.hobbies[0]", "false", "篮球"),
            Arguments.of(JSON_DATA, "data.hobbies[1]", "false", "足球"),
            Arguments.of(JSON_DATA, "data.hobbies[2]", "false", "乒乓球"),
            // 测试 JSON 数组
            Arguments.of(JSON_ARRAY, "data.name", "true", "[\"张三\",\"李四\"]"),
            Arguments.of(JSON_ARRAY, "data.address.city", "true",  "[\"长沙市\",\"武汉市\"]"),
            Arguments.of(JSON_ARRAY, "data.hobbies[0]", "true", "[\"篮球\",\"游泳\"]"),
            //测试null
            Arguments.of(JSON_DATA, "data.hobbies[3]", "false", "")
        );
    }
}
