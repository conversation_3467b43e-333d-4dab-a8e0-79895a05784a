package com.trs.moye.ability.invoker;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;

import com.fasterxml.jackson.databind.JsonNode;
import com.trs.moye.ability.entity.Ability;
import com.trs.moye.ability.entity.HttpRequestConfig;
import com.trs.moye.ability.entity.HttpRequestConfig.HttpContentType;
import com.trs.moye.ability.entity.InputBind;
import com.trs.moye.ability.entity.Schema;
import com.trs.moye.ability.entity.Schema.ObjectTypeSchema;
import com.trs.moye.ability.exception.AbilityInvokeException;
import com.trs.moye.base.common.utils.JsonUtils;
import java.io.IOException;
import okhttp3.OkHttpClient;
import okhttp3.mockwebserver.MockResponse;
import okhttp3.mockwebserver.MockWebServer;
import okhttp3.mockwebserver.RecordedRequest;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class RemoteAbilityInvokerTest {

    private RemoteAbilityInvoker remoteAbilityInvoker;
    private MockWebServer mockWebServer;
    private OkHttpClient okHttpClient;

    @BeforeEach
    public void setUp() throws IOException {
        // 启动 MockWebServer
        mockWebServer = new MockWebServer();
        mockWebServer.start(8080);

        // 创建 OkHttpClient
        okHttpClient = new OkHttpClient();

        // 创建要测试的 HttpClientService 实例
        remoteAbilityInvoker = new RemoteAbilityInvoker(okHttpClient);
    }

    @AfterEach
    public void tearDown() throws IOException {
        // 关闭 MockWebServer
        mockWebServer.shutdown();
    }

    @Test
    void shouldInvokeHttpAbility() throws InterruptedException {
        // 准备测试数据
        Ability ability = new Ability();
        ability.setEnName("httpAbility");
        HttpRequestConfig httpRequestConfig = HttpRequestConfig.builder()
            .url("http://localhost:8080/api")
            .method("POST")
            .contentType(HttpContentType.JSON)
            .headers(JsonUtils.parseObject(
                "{\"type\":\"OBJECT\",\"properties\":{\"Authorization\":{\"type\":\"STRING\",\"enName\":\"Authorization\"}}}",
                ObjectTypeSchema.class))
            .queryParams(JsonUtils.parseObject(
                "{\"type\":\"OBJECT\",\"properties\":{\"type\":{\"type\":\"STRING\",\"enName\":\"type\"}}}", ObjectTypeSchema.class))
            .build();
        ability.setHttpRequestConfig(httpRequestConfig);
        Schema inputSchema = JsonUtils.parseObject(
            "{\"type\":\"OBJECT\",\"properties\":{\"a\":{\"type\":\"STRING\",\"enName\":\"a\"}}}", Schema.class);
        ability.setInputSchema(inputSchema);
        Schema outputSchema = JsonUtils.parseObject("{\"type\":\"STRING\"}", Schema.class);
        ability.setOutputSchema(outputSchema);

        JsonNode input = JsonUtils.toJsonNode("{\"key\":\"value\",\"type\":\"t1\"}");
        // 定义绑定关系（JSON字段 -> 方法参数名）
        InputBind inputBind = new InputBind().addBinding("a", "/key")
            .addBinding("type", "/type")
            .addFixedValueBinding("Authorization", "trs86302457");
        // 设置 MockWebServer 的响应
        String responseBody = "{\"result\":\"success\"}\n";
        mockWebServer.enqueue(new MockResponse().setResponseCode(200).setBody(responseBody));
        // 执行能力方法
        Object result = remoteAbilityInvoker.invoke(ability, input, inputBind);
        // 获取 MockWebServer 接收到的请求
        RecordedRequest recordedRequest = mockWebServer.takeRequest();
        // 验证请求方法
        assertThat(recordedRequest.getMethod()).isEqualTo("POST");
        // 验证请求 URL
        assertThat(recordedRequest.getPath()).isEqualTo("/api?type=t1");
        // 验证请求头
        assertThat(recordedRequest.getHeader("Authorization")).isEqualTo("trs86302457");
        // 验证请求体
        assertThat(recordedRequest.getBody().readUtf8()).isEqualTo("{\"a\":\"value\"}");
        // 验证方法执行结果
        assertThat(result).isInstanceOf(JsonNode.class);
        JsonNode jsonNode = (JsonNode) result;
        assertThat(jsonNode.path("result").asText()).isEqualTo("success");
    }

    @Test
    void shouldThrowExceptionWhenHttpRequestConfigIsMissing() {
        // 准备测试数据
        Ability ability = new Ability();
        ability.setEnName("httpAbility");
        JsonNode input = JsonUtils.toJsonNode("{\"key\":\"value\"}");
        InputBind inputBind = new InputBind().addBinding("a", "/key");
        // 执行能力方法
        assertThatThrownBy(() -> remoteAbilityInvoker.invoke(ability, input, inputBind))
            .isInstanceOf(IllegalArgumentException.class)
            .hasMessage("ability HTTP request config is missing");
    }

    @Test
    void shouldThrowExceptionWhenHttpRequestFailed() {
        // 准备测试数据
        Ability ability = new Ability();
        ability.setEnName("httpAbility");
        HttpRequestConfig httpRequestConfig = HttpRequestConfig.builder()
            .url("http://localhost:8080/api")
            .method("POST")
            .contentType(HttpContentType.JSON)
            .build();
        ability.setHttpRequestConfig(httpRequestConfig);
        JsonNode input = JsonUtils.toJsonNode("{\"key\":\"value\"}");
        InputBind inputBind = new InputBind().addBinding("a", "/key");
        // 设置 MockWebServer 的响应
        mockWebServer.enqueue(new MockResponse().setResponseCode(500).setBody("Internal Server Error"));
        // 执行能力方法
        assertThatThrownBy(() -> remoteAbilityInvoker.invoke(ability, input, inputBind))
            .isInstanceOf(AbilityInvokeException.class)
            .hasMessage("Failed to execute HTTP request\n"
                + "[cause]:com.trs.moye.ability.exception.AbilityInvokeException: HTTP request failed: 500 Internal Server Error");
    }
}