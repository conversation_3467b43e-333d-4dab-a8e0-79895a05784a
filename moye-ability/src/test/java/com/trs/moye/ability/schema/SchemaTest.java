package com.trs.moye.ability.schema;

import static org.assertj.core.api.Assertions.assertThat;

import com.trs.moye.ability.entity.Schema;
import com.trs.moye.ability.enums.AbilityFieldType;
import com.trs.moye.base.common.utils.JsonUtils;
import org.junit.jupiter.api.Test;

class SchemaTest {

    @Test
    void testDeserializeStringType() {
        String json = "{\"type\":\"STRING\",\"description\":\"test\"}";
        Schema schema = JsonUtils.parseObject(json, Schema.class);
        assertThat(schema).isNotNull();
        assertThat(schema.getType()).isEqualTo(AbilityFieldType.STRING);
        assertThat(schema.getDescription()).isEqualTo("test");
    }

    @Test
    void testDeserializeObjectType() {
        String json = "{\"type\":\"OBJECT\",\"description\":\"test\",\"properties\":{\"name\":{\"type\":\"STRING\",\"description\":\"name\"}}}";
        Schema schema = JsonUtils.parseObject(json, Schema.class);
        assertThat(schema).isNotNull();
        assertThat(schema.getType()).isEqualTo(AbilityFieldType.OBJECT);
        assertThat(schema.getDescription()).isEqualTo("test");
        assertThat(schema).isInstanceOf(Schema.ObjectTypeSchema.class);
        Schema.ObjectTypeSchema objectTypeSchema = (Schema.ObjectTypeSchema) schema;
        assertThat(objectTypeSchema.getProperties()).hasSize(1);
        assertThat(objectTypeSchema.getProperties().get("name").getType()).isEqualTo(AbilityFieldType.STRING);
        assertThat(objectTypeSchema.getProperties().get("name").getDescription()).isEqualTo("name");
    }

    @Test
    void testDeserializeArrayType() {
        String json = "{\"type\":\"ARRAY\",\"description\":\"test\",\"items\":{\"type\":\"STRING\",\"description\":\"name\"}}";
        Schema schema = JsonUtils.parseObject(json, Schema.class);
        assertThat(schema).isNotNull();
        assertThat(schema.getType()).isEqualTo(AbilityFieldType.ARRAY);
        assertThat(schema.getDescription()).isEqualTo("test");
        assertThat(schema).isInstanceOf(Schema.ArrayTypeSchema.class);
        Schema.ArrayTypeSchema arrayTypeSchema = (Schema.ArrayTypeSchema) schema;
        assertThat(arrayTypeSchema.getItems().getType()).isEqualTo(AbilityFieldType.STRING);
        assertThat(arrayTypeSchema.getItems().getDescription()).isEqualTo("name");
    }

    @Test
    void testDeserializeNestedObjectType() {
        String json = "{\"type\":\"OBJECT\",\"description\":\"test\",\"properties\":{\"name\":{\"type\":\"STRING\",\"description\":\"name\"},\"address\":{\"type\":\"OBJECT\",\"description\":\"address\",\"properties\":{\"city\":{\"type\":\"STRING\",\"description\":\"city\"}}}}}";
        Schema schema = JsonUtils.parseObject(json, Schema.class);
        assertThat(schema).isNotNull();
        assertThat(schema.getType()).isEqualTo(AbilityFieldType.OBJECT);
        assertThat(schema.getDescription()).isEqualTo("test");
        assertThat(schema).isInstanceOf(Schema.ObjectTypeSchema.class);
        Schema.ObjectTypeSchema objectTypeSchema = (Schema.ObjectTypeSchema) schema;
        assertThat(objectTypeSchema.getProperties()).hasSize(2);
        assertThat(objectTypeSchema.getProperties().get("name").getType()).isEqualTo(AbilityFieldType.STRING);
        assertThat(objectTypeSchema.getProperties().get("name").getDescription()).isEqualTo("name");
        assertThat(objectTypeSchema.getProperties().get("address").getType()).isEqualTo(AbilityFieldType.OBJECT);
        assertThat(objectTypeSchema.getProperties().get("address").getDescription()).isEqualTo("address");
        assertThat(objectTypeSchema.getProperties().get("address")).isInstanceOf(Schema.ObjectTypeSchema.class);
        Schema.ObjectTypeSchema addressSchema = (Schema.ObjectTypeSchema) objectTypeSchema.getProperties()
            .get("address");
        assertThat(addressSchema.getProperties()).hasSize(1);
        assertThat(addressSchema.getProperties().get("city").getType()).isEqualTo(AbilityFieldType.STRING);
        assertThat(addressSchema.getProperties().get("city").getDescription()).isEqualTo("city");
    }

    @Test
    void testDeserializeNestedArrayType() {
        String json = "{\"type\":\"ARRAY\",\"description\":\"test\",\"items\":{\"type\":\"OBJECT\",\"description\":\"object\",\"properties\":{\"name\":{\"type\":\"STRING\",\"description\":\"name\"},\"address\":{\"type\":\"OBJECT\",\"description\":\"address\",\"properties\":{\"city\":{\"type\":\"STRING\",\"description\":\"city\"}}}}}}";
        System.out.println(json);
        Schema schema = JsonUtils.parseObject(json, Schema.class);
        assertThat(schema).isNotNull();
        assertThat(schema.getType()).isEqualTo(AbilityFieldType.ARRAY);
        assertThat(schema.getDescription()).isEqualTo("test");
        assertThat(schema).isInstanceOf(Schema.ArrayTypeSchema.class);
        Schema.ArrayTypeSchema arrayTypeSchema = (Schema.ArrayTypeSchema) schema;
        assertThat(arrayTypeSchema.getItems().getType()).isEqualTo(AbilityFieldType.OBJECT);
        assertThat(arrayTypeSchema.getItems().getDescription()).isEqualTo("object");
        assertThat(arrayTypeSchema.getItems()).isInstanceOf(Schema.ObjectTypeSchema.class);
        Schema.ObjectTypeSchema objectTypeSchema = (Schema.ObjectTypeSchema) arrayTypeSchema.getItems();
        assertThat(objectTypeSchema.getProperties()).hasSize(2);
        assertThat(objectTypeSchema.getProperties().get("name").getType()).isEqualTo(AbilityFieldType.STRING);
        assertThat(objectTypeSchema.getProperties().get("name").getDescription()).isEqualTo("name");
        assertThat(objectTypeSchema.getProperties().get("address").getType()).isEqualTo(AbilityFieldType.OBJECT);
        assertThat(objectTypeSchema.getProperties().get("address").getDescription()).isEqualTo("address");
        assertThat(objectTypeSchema.getProperties().get("address")).isInstanceOf(Schema.ObjectTypeSchema.class);
        Schema.ObjectTypeSchema addressSchema = (Schema.ObjectTypeSchema) objectTypeSchema.getProperties()
            .get("address");
        assertThat(addressSchema.getProperties()).hasSize(1);
        assertThat(addressSchema.getProperties().get("city").getType()).isEqualTo(AbilityFieldType.STRING);
        assertThat(addressSchema.getProperties().get("city").getDescription()).isEqualTo("city");
    }
}