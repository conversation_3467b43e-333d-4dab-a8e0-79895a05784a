package com.trs.moye.ability.base;

import static org.junit.jupiter.api.Assertions.assertEquals;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.stream.Stream;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;

/**
 * RegexAbilityTest
 *
 * <AUTHOR>
 * @since 2025/3/17 11:08
 */
class RegexAbilityTest {

    @ParameterizedTest
    @MethodSource("testExtractWithRegexProvider")
    void testExtractWithRegex(String inputText, String regex, List<String> expected) {
        assertEquals(expected, RegexAbility.extractWithRegex(inputText, regex));
    }

    private static Stream<Arguments> testExtractWithRegexProvider() {
        String inputText = "请联系我：123-45-6789，或者发送邮件到 <EMAIL>。\n" +
                           "另一个号码是 987-65-4321，另一封邮件是 <EMAIL>。";

        return Stream.of(
            // 空输入测试
            Arguments.of("", "\\d+", Collections.emptyList()),

            // 无匹配项测试
            Arguments.of("This is a test", "\\d+", Collections.emptyList()),

            // 单匹配项测试
            Arguments.of("123abc", "\\d+", Collections.singletonList("123")),

            // 中文提取测试
            Arguments.of(inputText, "[\\u4E00-\\u9FFF\\u3400-\\u4DBF\\uF900-\\uFAFF]+",
                Arrays.asList("请联系我", "或者发送邮件到", "另一个号码是", "另一封邮件是")),

            // 电话号码格式匹配
            Arguments.of(inputText, "\\d{3}-\\d{2}-\\d{4}", Arrays.asList("123-45-6789", "987-65-4321")));
    }

}