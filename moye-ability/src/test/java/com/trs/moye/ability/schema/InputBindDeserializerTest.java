package com.trs.moye.ability.schema;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.module.SimpleModule;
import com.trs.moye.ability.deserializer.InputBindDeserializer;
import com.trs.moye.ability.entity.InputBind;
import java.util.Arrays;
import org.junit.jupiter.api.Test;

class InputBindDeserializerTest {

    private final ObjectMapper objectMapper = new ObjectMapper();

    {
        // 注册自定义反序列化器
        SimpleModule module = new SimpleModule();
        module.addDeserializer(InputBind.class, new InputBindDeserializer());
        objectMapper.registerModule(module);
    }

    @Test
    void shouldDeserializePropertyBinding() throws Exception {
        // 准备 JSON 数据
        String json = "{\"a\":{\"jsonPath\":\"/a\"}}";

        // 反序列化
        InputBind inputBind = objectMapper.readValue(json, InputBind.class);

        // 验证路径绑定
        assertThat(inputBind.getBinding("a"))
            .isInstanceOf(InputBind.PropertyBinding.class)
            .extracting(b -> ((InputBind.PropertyBinding) b).getJsonPath())
            .isEqualTo("/a");
    }

    @Test
    void shouldDeserializeFixedValueBinding() throws Exception {
        // 准备 JSON 数据
        String json = "{\"b\":{\"fixedValue\":123}}";

        // 反序列化
        InputBind inputBind = objectMapper.readValue(json, InputBind.class);

        // 验证固定值绑定
        assertThat(inputBind.getBinding("b"))
            .isInstanceOf(InputBind.FixedValueBinding.class)
            .extracting(b -> ((InputBind.FixedValueBinding) b).getFixedValue())
            .isEqualTo(123);
    }

    @Test
    void shouldDeserializeArrayBinding() throws Exception {
        // 准备 JSON 数据
        String json = "{\"c\":{\"jsonPaths\":[\"/c1\",\"/c2\",\"/c3\"]}}";

        // 反序列化
        InputBind inputBind = objectMapper.readValue(json, InputBind.class);

        // 验证数组绑定
        assertThat(inputBind.getBinding("c"))
            .isInstanceOf(InputBind.ArrayBinding.class)
            .extracting(b -> ((InputBind.ArrayBinding) b).getJsonPaths())
            .isEqualTo(Arrays.asList("/c1", "/c2", "/c3"));
    }

    @Test
    void shouldThrowExceptionForUnknownBindingType() {
        // 准备 JSON 数据（未知绑定类型）
        String json = "{\"d\":{\"unknownField\":\"value\"}}";

        // 验证异常
        assertThatThrownBy(() -> objectMapper.readValue(json, InputBind.class))
            .isInstanceOf(IllegalArgumentException.class)
            .hasMessageContaining("Unknown binding type for parameter");
    }

    @Test
    void shouldDeserializeMixedBindings() throws Exception {
        // 准备 JSON 数据（混合绑定类型）
        String json = "{\n" +
                      "  \"a\": {\n" +
                      "    \"jsonPath\": \"/a\"\n" +
                      "  },\n" +
                      "  \"b\": {\n" +
                      "    \"fixedValue\": 123\n" +
                      "  },\n" +
                      "  \"c\": {\n" +
                      "    \"jsonPaths\": [\"/c1\", \"/c2\", \"/c3\"]\n" +
                      "  }\n" +
                      "}";

        // 反序列化
        InputBind inputBind = objectMapper.readValue(json, InputBind.class);

        // 验证路径绑定
        assertThat(inputBind.getBinding("a"))
            .isInstanceOf(InputBind.PropertyBinding.class)
            .extracting(b -> ((InputBind.PropertyBinding) b).getJsonPath())
            .isEqualTo("/a");

        // 验证固定值绑定
        assertThat(inputBind.getBinding("b"))
            .isInstanceOf(InputBind.FixedValueBinding.class)
            .extracting(b -> ((InputBind.FixedValueBinding) b).getFixedValue())
            .isEqualTo(123);

        // 验证数组绑定
        assertThat(inputBind.getBinding("c"))
            .isInstanceOf(InputBind.ArrayBinding.class)
            .extracting(b -> ((InputBind.ArrayBinding) b).getJsonPaths())
            .isEqualTo(Arrays.asList("/c1", "/c2", "/c3"));
    }
}