package com.trs.moye.ability.base;

import com.trs.moye.ability.annotation.SchemaCategory;
import com.trs.moye.ability.annotation.SchemaDefinition;
import com.trs.moye.ability.annotation.SchemaProperty;
import com.trs.moye.ability.exception.PipelineInterruptedException;
import com.trs.moye.ability.response.ExtractOriginalTextResponse;
import com.trs.moye.ability.service.TextHandlingService;
import com.trs.moye.ability.utils.SimplifiedChineseUtil;
import com.trs.moye.ability.utils.WeiboMsgUtils;
import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.HashSet;
import java.util.Objects;
import java.util.Set;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;

/**
 * 字符串能力
 *
 * <AUTHOR>
 * @since 2025/03/06 11:12:01
 */
@SchemaCategory(enAbbr = "string", name = "文本处理", description = "文本处理")
@Slf4j
public class StringAbility {

    private StringAbility() {
    }

    /**
     * 执行内容拼接操作
     *
     * @param inputTexts 输入文本
     * @return {@link String }
     */
    @SchemaDefinition(zhName = "拼接字符串", description = "拼接字符串")
    public static String doConcat(
        @SchemaProperty(name = "inputTexts", description = "字符串数组") String[] inputTexts) {
        String value = "";
        for (String it : inputTexts) {
            if (Objects.nonNull(it)) {
                value = value.concat(it);
            }
        }
        return value;
    }

    /**
     * 字符串拼接操作，带分隔符
     *
     * @param inputTexts 输入文本
     * @param separator  分隔符
     * @return {@link String }
     */
    @SchemaDefinition(zhName = "拼接字符串(带分隔符)", description = "拼接字符串(带分隔符)")
    public static String doSeparatorConcat(
        @SchemaProperty(name = "inputTexts", description = "字符串数组") String[] inputTexts,
        @SchemaProperty(name = "separator", description = "分隔符") String separator) {

        if (inputTexts == null || inputTexts.length == 0) {
            return "";
        }
        // 第一个有值的索引
        int firstHasValueIndex = getFirstHasValueIndex(inputTexts);
        if (firstHasValueIndex < 0) {
            return "";
        }
        // 计算长度，StringBuilder避免扩容多次。
        int length = getInputTextsLength(inputTexts);
        boolean isNotEmptySeparator = separator != null && !separator.isEmpty();
        if (isNotEmptySeparator) {
            length += separator.length() * (inputTexts.length - 1);
        }
        // 拼接
        StringBuilder value = new StringBuilder(length);
        value.append(inputTexts[firstHasValueIndex]);
        for (int i = firstHasValueIndex + 1; i < inputTexts.length; i++) {
            String inputText = inputTexts[i];
            if (inputText == null || inputText.isEmpty()) {
                continue;
            }
            if (isNotEmptySeparator) {
                value.append(separator);
            }
            value.append(inputText);
        }
        return value.toString();
    }

    private static int getFirstHasValueIndex(String[] inputTexts) {
        for (int i = 0; i < inputTexts.length; i++) {
            String inputText = inputTexts[i];
            if (inputText == null || inputText.isEmpty()) {
                continue;
            }
            return i;
        }
        return -1;
    }

    private static int getInputTextsLength(String[] inputTexts) {
        int length = 0;
        for (String inputText : inputTexts) {
            if (inputText == null || inputText.isEmpty()) {
                continue;
            }
            length += inputText.length();
        }
        return length;
    }


    /**
     * 去掉标题文本中多余的标签（去掉@，#,()等关键词标签）
     *
     * @param inputText 输入文本
     * @return {@link String }
     */
    @SchemaDefinition(zhName = "去掉文本中多余的标签", description = "去掉文本中多余的标签（去掉@，#,()等关键词标签）")
    public static String rmExtraTextLabel(
        @SchemaProperty(name = "inputText", description = "输入文本") String inputText) {
        if (StringUtils.isBlank(inputText)) {
            return "";
        }
        String resultText = inputText.replaceAll("#\\S+|@\\S+|（[^）]*+）| ", "");
        if (StringUtils.isBlank(resultText)) {
            resultText = inputText.replaceAll("(@\\S+)|[#（，）]", "");
        }
        return resultText;
    }

    /**
     * 去除html标签
     *
     * @param inputText 输入文本
     * @return {@link String }
     */
    @SchemaDefinition(zhName = "去除html标签", description = "去除html标签")
    public static String doRemoveHtmlTag(
        @SchemaProperty(name = "inputText", description = "输入文本") String inputText) {
        if (StringUtils.isBlank(inputText)) {
            return "";
        }
        return TextHandlingService.removeHtmlTagByRegex(inputText);
    }

    /**
     * 统计字数
     *
     * @param inputText 输入文本
     * @return int
     */
    @SchemaDefinition(zhName = "统计字数", description = "统计字数")
    public static int doCountText(@SchemaProperty(name = "inputText", description = "输入文本") String inputText) {
        if (StringUtils.isBlank(inputText)) {
            return 0;
        }
        return TextHandlingService.countText(inputText);
    }

    /**
     * 提取微博标题
     *
     * @param inputText 输入文本
     * @return {@link String }
     */
    @SchemaDefinition(zhName = "提取微博标题", description = "从输入（内容）提取微博标题")
    public static String doWeiboTitleExtract(
        @SchemaProperty(name = "inputText", description = "输入文本") String inputText) {
        if (StringUtils.isBlank(inputText)) {
            return "";
        }
        String resultText;
        try {
            if (inputText.length() > 139) {
                resultText = inputText.substring(0, 140);
            } else {
                resultText = inputText;
            }
        } catch (Exception e) {
            log.error("execute do weibo title extract is error !!!", e);
            return "";
        }
        return resultText;
    }

    /**
     * 微博话题提取
     *
     * @param inputText 输入文本
     * @return {@link String }
     */
    @SchemaDefinition(zhName = "微博话题提取", description = "微博话题提取")
    public static String doWeiboTopicExtract(
        @SchemaProperty(name = "inputText", description = "输入文本") String inputText) {
        if (StringUtils.isBlank(inputText)) {
            return "";
        }
        return TextHandlingService.matchWeiboTopic(inputText).toString();
    }

    /**
     * 提取微博当前发言人发言内容
     *
     * @param weiBoContent           微博内容
     * @param excludeWordsExpression 排除词表达式，多个词用“|”分隔，例如：转发|转发微博
     * @return java.lang.String
     */
    @SchemaDefinition(zhName = "提取微博当前发言人发言内容", description = "提取微博当前发言人发言内容")
    public static String extractWeiBoCurrentPersonSpeakContent(
        @SchemaProperty(name = "weiBoContent", description = "微博博文") String weiBoContent,
        @SchemaProperty(name = "excludeWordsExpression", description = "排除词") String excludeWordsExpression) {
        if (StringUtils.isBlank(weiBoContent)) {
            return "";
        }
        // 排除词集合
        Set<String> excludeWordsSet = new HashSet<>(Arrays.asList(excludeWordsExpression.split("\\|")));
        // 第一段比较特殊（可能没有“:”号，其他段处理需要“:”号做分隔符做截取索引），优先处理第一段
        String[] weiBoItems = weiBoContent.split("//");
        if (ObjectUtils.isEmpty(weiBoItems)) {
            return "";
        }
        String weiBoItem0 = weiBoItems[0].trim();
        if (StringUtils.isNotBlank(weiBoItem0) && !excludeWordsSet.contains(weiBoItem0)) {
            return weiBoItem0;
        }
        String currentPersonSpeakContent = null;
        // 循环寻找发言内容
        for (int i = 1; i < weiBoItems.length; i++) {
            String weiBoItem = weiBoItems[i];
            int speakContentStartIndex = weiBoItem.indexOf(":");
            if (speakContentStartIndex < 1 || speakContentStartIndex + 1 >= weiBoItem.length()) {
                continue;
            }
            String speakContent = weiBoItem.substring(speakContentStartIndex + 1).trim();
            if (!speakContent.isEmpty() && !excludeWordsSet.contains(speakContent)) {
                currentPersonSpeakContent = speakContent;
                break;
            }
        }
        return currentPersonSpeakContent == null ? "" : currentPersonSpeakContent;
    }

    /**
     * 贵州环境使用 英文大写转小写
     *
     * @param inputText 输入文本
     * @return {@link String }
     */
    @SchemaDefinition(zhName = "英文大写转小写", description = "英文大写转小写")
    public static String englishUppercaseToLowercase(
        @SchemaProperty(name = "inputText", description = "输入文本") String inputText) {
        if (StringUtils.isEmpty(inputText)) {
            return "";
        }
        return inputText.toLowerCase();
    }

    /**
     * 贵州环境使用 繁体中文转简体
     *
     * @param inputText 输入文本
     * @return {@link String }
     */
    @SchemaDefinition(zhName = "繁体中文转简体", description = "繁体中文转简体")
    public static String chineseTraditionalToSimplify(
        @SchemaProperty(name = "inputText", description = "输入文本") String inputText) {
        if (StringUtils.isEmpty(inputText)) {
            return "";
        }
        return SimplifiedChineseUtil.isSimpleOrComplex(inputText);
    }

    /**
     * 停止算子
     *
     * @throws PipelineInterruptedException 编排停止异常用于停止算子
     */
    @SchemaDefinition(zhName = "停止执行", description = "停止执行")
    public static void stopExecution() throws PipelineInterruptedException {
        throw new PipelineInterruptedException("停止算子，处理流程停止执行");
    }

    /**
     * 去除文本中表情算子
     *
     * @param inputText 输入文本
     * @return {@link String }
     * <AUTHOR>
     * @since 2024/2/6 14:04
     */
    @SchemaDefinition(zhName = "去除文本中表情", description = "去除文本中表情")
    public static String doRemoveEmoji(@SchemaProperty(name = "inputText", description = "输入文本") String inputText) {
        if (StringUtils.isEmpty(inputText)) {
            return "";
        }
        // 使用 Unicode 块和属性类匹配 emoji，避免转义字符
        Pattern emojiPattern = Pattern.compile("[☀-⟿⌀-⏿─-◿℀-⇿⬀-⯿ⴆ〰]" +                   // 符号其他类
                "|[\\p{So}\\p{Sk}]" +                        // 符号其他类 + 修饰符号
                "|\\p{InMiscellaneous_Symbols_And_Pictographs}" + // U+1F300–U+1F5FF
                "|\\p{InSupplemental_Symbols_And_Pictographs}" +  // U+1F900–U+1F9FF
                "|\\p{InEmoticons}" +                       // U+1F600–U+1F64F
                "|\\p{InTransport_And_Map_Symbols}" +       // U+1F680–U+1F6FF
                "|\\p{InMiscellaneous_Symbols}" +            // U+2600–U+26FF
                "|\\p{InDingbats}" +                        // U+2700–U+27BF
                "|\\p{InVariation_Selectors}",              // U+FE00–U+FE0F
            Pattern.UNICODE_CASE | Pattern.UNICODE_CHARACTER_CLASS);
        return emojiPattern.matcher(inputText).replaceAll("");
    }

    /**
     * 去除微博昵称算子
     *
     * @param inputText 输入文本
     * @return {@link String }
     */
    @SchemaDefinition(zhName = "去除微博昵称", description = "去除微博昵称")
    public static String removeWeiBoNicknames(
        @SchemaProperty(name = "inputText", description = "输入文本") String inputText) {
        Pattern pattern = Pattern.compile("(//@)([^@]*?)(:)");
        Matcher m = pattern.matcher(inputText);
        while (m.find()) {
            if (m.group().length() <= 20) {
                inputText = inputText.replace(m.group(), "");
            }
        }
        return inputText;
    }

    /**
     * 提取微博转发原文
     *
     * @param inputText 输入文本
     * @param nickName  昵称
     * @return {@link ExtractOriginalTextResponse }
     */
    @SchemaDefinition(zhName = "提取微博转发原文", description = "提取微博转发原文")
    public static ExtractOriginalTextResponse extractOriginalText(
        @SchemaProperty(name = "inputText", description = "输入文本") String inputText,
        @SchemaProperty(name = "nickName", description = "发文作者昵称") String nickName) {

        String originalText = WeiboMsgUtils.getWeiboOriginalMessage(inputText);
        // 相等说明是原发，否则是转发的
        boolean isForward = !originalText.equals(inputText);
        // 如果是原发，添加昵称
        if (!isForward) {
            originalText = nickName + ":" + originalText;
        }
        String md5Hex = DigestUtils.md5Hex(originalText.getBytes());

        return new ExtractOriginalTextResponse(originalText, isForward, md5Hex);
    }

    /**
     * 截取字符串
     *
     * @param inputText  字符串
     * @param startIndex 开始索引
     * @param endIndex   结束索引
     * @return 截取结果
     */
    @SchemaDefinition(zhName = "截取字符串", description = "截取字符串")
    public static String substring(
        @SchemaProperty(name = "inputText", description = "输入文本") String inputText,
        @SchemaProperty(name = "startIndex", description = "开始索引") int startIndex,
        @SchemaProperty(name = "endIndex", description = "结束索引") int endIndex) {
        if (StringUtils.isBlank(inputText)) {
            return "";
        }
        if (startIndex < 0 || endIndex > inputText.length() || startIndex >= endIndex) {
            return "";
        }
        return inputText.substring(startIndex, endIndex);
    }

    /**
     * 往后拼接字符串
     *
     * @param inputText  输入字符串
     * @param appendText 拼接字符串
     * @return 拼接结果
     */
    @SchemaDefinition(zhName = "往后拼接字符串", description = "往后拼接字符串")
    public static String concat(
        @SchemaProperty(name = "inputText", description = "输入文本") String inputText,
        @SchemaProperty(name = "appendText", description = "拼接文本") String appendText) {
        if (StringUtils.isBlank(inputText)) {
            return appendText;
        }
        if (StringUtils.isBlank(appendText)) {
            return inputText;
        }
        return inputText.concat(appendText);
    }

    /**
     * 检查字符串是否相等
     *
     * @param inputText   输入文本
     * @param compareText 比较文本
     * @return 是否相等
     */
    @SchemaDefinition(zhName = "检查相等", description = "检查字符串是否相等")
    public static boolean checkTextEqual(
        @SchemaProperty(name = "inputText", description = "输入文本") String inputText,
        @SchemaProperty(name = "compareText", description = "比较文本") String compareText) {
        return StringUtils.equals(inputText, compareText);
    }


    /**
     * 文本md5 <br/>编码默认采用 utf-8
     *
     * @param inputText 输入文本
     * @return md5值
     */
    @SchemaDefinition(zhName = "生成md5", description = "生成md5")
    public static String getTextMd5(
        @SchemaProperty(name = "inputText", description = "输入文本") String inputText) {
        try {
            return DigestUtils.md5Hex(inputText.getBytes(StandardCharsets.UTF_8));
        } catch (Exception e) {
            log.error("execute do get md5 is error !!!", e);
            return null;
        }
    }

    /**
     * 根据分隔符将字符串拆分成数组
     *
     * @param inputText 输入文本
     * @param splitter  分隔符
     * @return 拆分后的数组
     */
    @SchemaDefinition(zhName = "拆分数组", description = "根据分隔符将字符串拆分成数组")
    public static String[] split(
        @SchemaProperty(name = "inputText", description = "输入文本") String inputText,
        @SchemaProperty(name = "splitter", description = "分隔符") String splitter) {
        return inputText.split(splitter);
    }

    /**
     * 根据正则匹配字符进行替换
     *
     * @param inputText   输入文本
     * @param regex       正则表达式
     * @param replacement 替换内容
     * @return 替换后的文本
     */
    @SchemaDefinition(zhName = "替换字符", description = "替换字符串中的指定内容")
    public static String replaceAll(
        @SchemaProperty(name = "inputText", description = "输入文本") String inputText,
        @SchemaProperty(name = "regex", description = "正则表达式") String regex,
        @SchemaProperty(name = "replacement", description = "替换文本") String replacement) {
        if (inputText == null || regex == null || replacement == null) {
            return inputText;
        }
        return inputText.replaceAll(regex, replacement);
    }

    /**
     * 去除字符串中的指定内容
     *
     * @param inputText 输入文本
     * @param regex     正则表达式
     * @return 去除后的文本
     */
    @SchemaDefinition(zhName = "去除字符", description = "去除字符串中的指定内容")
    public static String removeSpecifiedString(
        @SchemaProperty(name = "inputText", description = "输入文本") String inputText,
        @SchemaProperty(name = "regex", description = "正则表达式") String regex) {
        return replaceAll(inputText, regex, "");
    }
}
