package com.trs.moye.ability.invoker;

import com.fasterxml.jackson.databind.JsonNode;
import com.trs.moye.ability.annotation.SchemaProperty;
import com.trs.moye.ability.entity.InputBind;
import com.trs.moye.ability.entity.InputBind.Binding;
import com.trs.moye.ability.exception.AbilityInvokeException;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.lang.reflect.Parameter;
import java.util.Map;
import java.util.Objects;
import org.apache.commons.lang3.StringUtils;

/**
 * 本地算子调用
 *
 * <AUTHOR>
 * @since 2025/3/27 16:03
 */
public abstract class AbstractLocalAbilityInvoker {

    /**
     * 绑定输入数据到方法参数
     *
     * @param method    目标方法
     * @param input     输入数据（JSON 格式）
     * @param inputBind 数据绑定规则
     * @return 方法参数数组
     */
    protected static Object[] bindParameters(Method method, JsonNode input, InputBind inputBind) {
        Parameter[] parameters = method.getParameters();
        Object[] args = new Object[parameters.length];

        for (int i = 0; i < parameters.length; i++) {
            Parameter parameter = parameters[i];
            String paramName = parameter.getAnnotation(SchemaProperty.class).name();

            // 1.获取参数基础值（直接绑定或新建实例）
            Object paramValue = getBaseParameterValue(parameter, paramName, inputBind, input);

            // 2.处理子字段绑定
            applySubFieldBindings(paramValue, paramName, inputBind, input);

            args[i] = paramValue;
        }
        return args;
    }

    /**
     * 获取参数基础值（直接绑定或新建空实例）
     *
     * @param parameter 参数
     * @param paramName param name （参数名称）
     * @param inputBind 输入绑定
     * @param input     输入
     * @return {@link Object }
     */
    private static Object getBaseParameterValue(Parameter parameter, String paramName,
        InputBind inputBind, JsonNode input) {
        // 尝试直接绑定
        InputBind.Binding directBinding = inputBind.getBinding(paramName);
        if (directBinding != null) {
            return directBinding.parseValue(input, parameter.getType());
        }

        // 存在子字段绑定时创建空实例
        if (inputBind.hasSubFieldBindings(paramName)) {
            return createInstance(parameter.getType());
        }

        throw new IllegalArgumentException("No binding found for parameter: " + paramName);
    }

    /**
     * 应用子字段绑定到目标对象
     *
     * @param target        目标
     * @param baseParamName Base Param 名称
     * @param inputBind     输入绑定
     * @param input         输入
     */
    private static void applySubFieldBindings(Object target, String baseParamName,
        InputBind inputBind, JsonNode input) {
        Map<String, Binding> subBindings = inputBind.getSubFieldBindings(baseParamName);
        subBindings.forEach((subPath, binding) -> {
            String[] pathSegments = subPath.split("\\.", 2);
            String currentField = pathSegments[0];
            String remainingPath = (pathSegments.length > 1) ? pathSegments[1] : null;

            // 递归处理嵌套结构
            handleNestedField(target, baseParamName, currentField, remainingPath, inputBind, input);
        });
    }

    /**
     * 处理嵌套字段绑定（递归核心方法）
     *
     * @param parentObj     父对象
     * @param basePath      基本路径
     * @param currentField  当前字段
     * @param remainingPath 剩余路径
     * @param inputBind     输入绑定
     * @param input         输入
     */
    private static void handleNestedField(Object parentObj, String basePath,
        String currentField, String remainingPath,
        InputBind inputBind, JsonNode input) {
        try {
            // 获取字段定义
            Field field = parentObj.getClass().getDeclaredField(currentField);
            field.setAccessible(true);
            Class<?> fieldType = field.getType();

            // 当前层级字段处理
            if (StringUtils.isBlank(remainingPath)) {
                Object value = inputBind.getBinding(basePath + "." + currentField).parseValue(input, fieldType);
                field.set(parentObj, value);
                return;
            }

            // 递归处理子路径
            Object childObj = field.get(parentObj);
            if (Objects.isNull(childObj)) {
                childObj = createInstance(field.getType());
                field.set(parentObj, childObj);
            }
            applySubFieldBindings(childObj,
                buildNestedParamName(basePath, currentField),
                inputBind,
                input);
        } catch (Exception e) {
            throw new AbilityInvokeException(String.format("Field binding failed: %s.%s", basePath, currentField), e);
        }
    }

    /**
     * 安全创建实例（处理无参构造）
     *
     * @param type 类型
     * @return {@link Object }
     */
    private static Object createInstance(Class<?> type) {
        try {
            return type.getDeclaredConstructor().newInstance();
        } catch (Exception e) {
            throw new AbilityInvokeException("Cannot create instance of " + type.getName(), e);
        }
    }

    /**
     * 构建嵌套参数名
     *
     * @param base  父参数名
     * @param field 字段
     * @return {@link String }
     */
    private static String buildNestedParamName(String base, String field) {
        return base.isEmpty() ? field : base + "." + field;
    }
}
