package com.trs.moye.ability.entity.operator;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.trs.moye.ability.deserializer.InputBindDeserializer;
import com.trs.moye.ability.deserializer.OperatorRowTypeDeserializer;
import com.trs.moye.ability.deserializer.OutputBindDeserializer;
import com.trs.moye.ability.entity.Ability;
import com.trs.moye.ability.entity.InputBind;
import com.trs.moye.ability.entity.OperatorStyleInfo;
import com.trs.moye.ability.entity.OutputBind;
import com.trs.moye.ability.enums.ArrangeNodeType;
import com.trs.moye.ability.typehandler.InputBindTypeHandler;
import com.trs.moye.ability.typehandler.OperatorRowTypeHandler;
import com.trs.moye.ability.typehandler.OutputBindTypeHandler;
import com.trs.moye.base.common.entity.AuditBaseEntity;
import com.trs.moye.base.common.enums.ModelLayer;
import com.trs.moye.base.common.typehandler.CustomJacksonTypeHandler;
import com.trs.moye.base.data.service.entity.Condition;
import java.util.AbstractMap;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * BatchOperator 批处理算子
 *
 * <AUTHOR>
 * @since 2025/3/25 14:39
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
@SuperBuilder
@TableName(value = "batch_operator")
public class BatchOperator extends AuditBaseEntity {

    /**
     * 编排id
     */
    private Integer arrangementId;

    /**
     * 数据建模id
     */
    private Integer dataModelId;

    /**
     * 画布信息
     */
    @TableField(typeHandler = CustomJacksonTypeHandler.class)
    private OperatorStyleInfo canvas;

    /**
     * 算子id
     */
    private Long displayId;

    /**
     * 子节点算子id
     */
    @TableField(typeHandler = CustomJacksonTypeHandler.class)
    private List<Long> targetDisplayIds;

    /**
     * 父节点算子id
     */
    @TableField(typeHandler = CustomJacksonTypeHandler.class)
    private List<Long> parentDisplayIds;

    /**
     * 节点类型
     */
    private ArrangeNodeType type;

    /**
     * 算子名称
     */
    private String name;

    /**
     * 算子描述
     */
    @TableField(value = "`desc`")
    private String desc;

    /**
     * 是否启用
     */
    private Boolean enabled;

    /**
     * 条件
     */
    @TableField(typeHandler = CustomJacksonTypeHandler.class)
    private List<Condition> conditions;

    /**
     * 输入字段
     */
    @JsonDeserialize(using = OperatorRowTypeDeserializer.class)
    @TableField(typeHandler = OperatorRowTypeHandler.class)
    private OperatorRowType inputFields;

    /**
     * 输出字段
     */
    @JsonDeserialize(using = OperatorRowTypeDeserializer.class)
    @TableField(typeHandler = OperatorRowTypeHandler.class)
    private OperatorRowType outputFields;

    /**
     * 输入绑定
     */
    @JsonDeserialize(using = InputBindDeserializer.class)
    @TableField(typeHandler = InputBindTypeHandler.class)
    private InputBind inputBind;

    /**
     * 输出绑定
     */
    @JsonDeserialize(using = OutputBindDeserializer.class)
    @TableField(typeHandler = OutputBindTypeHandler.class)
    private OutputBind outputBind;

    /**
     * 能力id
     */
    private Integer abilityId;

    /**
     * 能力
     */
    @TableField(exist = false)
    private Ability ability;

    /**
     * 算子类型是 TABLE 时, 输入算子所选择的数据建模所处的层级
     */
    private ModelLayer tableType;

    /**
     * 算子类型是 TABLE 时, 输入算子所选择的数据建模
     */
    private Integer tableDataModelId;

    /**
     * 算子类型是 TABLE 时, 输入算子选择的数据建模中的存储点
     */
    private Integer tableStorageId;

    /**
     * 算子类型是 TABLE 时, 数据接入算子——是否启用增量
     */
    private Boolean tableIsIncrement;

    /**
     * 输出表名称
     */
    private String outputTableName;

    /**
     * outputTableName生成逻辑
     *
     * @param type      编排id
     * @param displayId 算子id
     * @param name      算子名称
     * @return outputTableName
     */
    public static String generateOutputTableName(ArrangeNodeType type, Long displayId, String name) {
        // 中文替换为 C
        String sanitized = name.replaceAll("[\\u4e00-\\u9fa5]", "C");
        // 移除无效字符，仅保留字母、数字、下划线
        sanitized = sanitized.replaceAll("\\W", "_");
        // 拼接 表名
        String tableName = type + "_" + displayId + "_" + sanitized;
        return tableName.length() <= 40 ? tableName : tableName.substring(0, 40);
    }

    /**
     * 设置输出表名称
     *
     * @param type 算子类型
     * @param displayId 算子id
     * @param name 算子名称
     */
    public void setOutputTableName(ArrangeNodeType type, Long displayId, String name) {
        this.outputTableName = generateOutputTableName(type, displayId, name);
    }

    /**
     * 处理父节点id
     *
     * @param operators 算子列表
     */
    public static void assignParentDisplayIds(List<BatchOperator> operators) {
        // Create a map from targetDisplayId to its parent displayIds
        Map<Long, List<Long>> parentMap = operators.stream()
            .filter(element -> element.getTargetDisplayIds() != null)
            .flatMap(element -> element.getTargetDisplayIds().stream()
                .map(targetId -> new AbstractMap.SimpleEntry<>(targetId, element.getDisplayId())))
            .collect(Collectors.groupingBy(Map.Entry::getKey,
                Collectors.mapping(Map.Entry::getValue, Collectors.toList())));

        operators.forEach(
            operator -> operator.setParentDisplayIds(parentMap.getOrDefault(operator.getDisplayId(), Collections.emptyList())));
    }
}
