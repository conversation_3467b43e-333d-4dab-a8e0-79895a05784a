package com.trs.moye.ability.utils;

import com.alibaba.fastjson.JSONObject;
import com.trs.ai.ty.base.constant.enums.KeyWordTypeEnum;
import com.trs.ai.ty.base.constant.enums.RuleErrorTypeEnum;
import com.trs.moye.ability.domain.KeywordMarkInfo;
import com.trs.moye.ability.domain.KeywordRuleMatchResult;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

/**
 * “（不）匹配”运算符实现
 *
 * <AUTHOR>
 * @date 2020/8/14 17:39
 */
@Slf4j
public class CkmEvaluator {

    private static final String INVALID_EXPRESSION = "invalid expression: {}";
    private static final char LEFT_BRACKET = '(';
    private static final char RIGHT_BRACKET = ')';


    /**
     * 替换表达式里面的关键词
     *
     * @param rule    规则
     * @param keyword 关键词
     * @param value   值
     * @return java.lang.String
     * <AUTHOR>
     * @since 2020/9/15
     */
    public static String replaceRuleKeywordWith(String rule, String keyword, String value) {
        // rule表达式支持的() 以及 | & - 等运算符
        Pattern pattern =
            Pattern.compile(
                String.format(
                    "(?<=(^|[&(|\\-]))(%s)(?=([&)|\\-]|$))", Pattern.quote(keyword)));
        Matcher matcher = pattern.matcher(rule);
        if (!matcher.find()) {
            log.error("can't find keyword [{}] in rule expression [{}]", keyword, rule);
            return rule;
        } else {
            return matcher.replaceAll(value);
        }
    }

    /**
     * 关键词回标 wiki http://wiki.devdemo.trs.net.cn/pages/viewpage.action?pageId=19312955
     *
     * @param text 文本
     * @param rule 规则
     * @return {@link KeywordRuleMatchResult}
     * <AUTHOR>
     * @since 2020/10/10 15:58
     */
    public static KeywordRuleMatchResult containsWord(String text, String rule) {
        if (StringUtils.isEmpty(rule)) {
            log.warn("rule is empty!");
            return KeywordRuleMatchResult.falseResult();
        }
        if (StringUtils.isEmpty(text)) {
//            log.info("match text is empty!");
            return KeywordRuleMatchResult.falseResult();
        }
        String textRuleCacheKey = buildTextRuleCacheKey(text, rule);
        //判断当前数据是否已经执行过当前算子，避免重复执行
        KeywordRuleMatchResult cached = ThreadLocalUtil.get(textRuleCacheKey);
        if (Objects.nonNull(cached)) {
            return cached;
        }
        // 如果表达式不合法，进入，返回构建的错误结果。
        if (!validateExpression(rule)) {
            log.error("invalid rule : {}", rule);
            ThreadLocalUtil.set(textRuleCacheKey, KeywordRuleMatchResult.falseResult());
            return KeywordRuleMatchResult.falseResult();
        }
        List<String> ruleKeywords = getRuleKeyWords(rule);
        if (ruleKeywords.isEmpty()) {
            // 缓存同一条数据同一个算子的的匹配结果，避免同一条数据重复执行同一个算子
            ThreadLocalUtil.set(textRuleCacheKey, KeywordRuleMatchResult.falseResult());
            return KeywordRuleMatchResult.falseResult();
        }
        ruleKeywords = ruleKeywords.stream()
            .filter(StringUtils::isNotEmpty)
            .collect(Collectors.toList());

        // 直接根据关键词trie去匹配文本
        List<KeywordMarkInfo> infoList =
            AhoCorasickUtil.getKeywordMarkInfos(AhoCorasickUtil.trie(ruleKeywords), text);

        String result = getResultBuyAc(rule, infoList);

        boolean isMatch = calcExpress(result, rule);

        KeywordRuleMatchResult matchResult = new KeywordRuleMatchResult();
        if (isMatch) {
            matchResult.setMatch(true);
            matchResult.setKeywords(infoList);
        } else {
            matchResult = KeywordRuleMatchResult.falseResult();
        }
        // 缓存同一条数据同一个算子的的匹配结果，避免同一条数据重复执行同一个算子
        ThreadLocalUtil.set(textRuleCacheKey, matchResult);
        return matchResult;
    }

    private static String buildTextRuleCacheKey(String text, String rule) {
        return "CKM-" + text + "$$$$" + rule;
    }

    private static boolean calcExpress(String expression, String rule) {
        if (StringUtils.isEmpty(expression)) {
            return false;
        }
        try {
            String[] results = expression.split("\\|\\|");
            for (String result : results) {
                if (result.equals("true")) {
                    return true;
                }
            }
        } catch (Exception e) {
            log.error("failed to exec expression {}, rule: {}", expression, rule, e);
        }
        return false;
    }

    /**
     * 在文本里面找到所有关键字内容
     *
     * @param text    原始文本
     * @param keyword 要查找的关键词
     * @return java.util.List
     */
    public static List<KeywordMarkInfo> findKeyword(String text, String keyword) {

        List<KeywordMarkInfo> keywordMarkInfoList = new ArrayList<>();
        if (StringUtils.isEmpty(keyword)) {
            return keywordMarkInfoList;
        }

        int index = text.indexOf(keyword);
        if (index < 0) {
            return keywordMarkInfoList;
        }

        KeywordMarkInfo keywordMarkInfo =
            new KeywordMarkInfo(
                keyword,
                index,
                index + keyword.length() - 1,
                KeyWordTypeEnum.FOCUS_PATTERN_KEYWORD.getValue());
        keywordMarkInfoList.add(keywordMarkInfo);
        while (index >= 0) {
            // 从字符串出现的位置的下一位置开始继续查找
            index = text.indexOf(keyword, index + keyword.length());
            if (index >= 0) {
                KeywordMarkInfo keywordMarkInfo1 =
                    new KeywordMarkInfo(
                        keyword,
                        index,
                        index + keyword.length() - 1,
                        KeyWordTypeEnum.FOCUS_PATTERN_KEYWORD.getValue());
                keywordMarkInfoList.add(keywordMarkInfo1);
            }
        }
        return keywordMarkInfoList;
    }

    /**
     * 判断条件表达式是否合法
     *
     * @param rule 规则表达式
     * @return boolean, true 表示合法， false表示不合法
     * <AUTHOR>
     * @since 2020/9/9 18:35
     */
    public static boolean validateExpression(String rule) {

        char[] arr = rule.toCharArray();
        int len = arr.length;
        // 前后括号计数，用来判断括号是否合法
        int checkNum = 0;

        // 循环判断
        for (int i = 0; i < len; i++) {
            if (i == 0) {
                // 首位是右括号或者运算符直接不合法
                if (isExpressOperator(arr[i]) || arr[i] == RIGHT_BRACKET) {
                    return false;
                } else if (arr[i] == LEFT_BRACKET) {
                    checkNum++;
                    if (!validLeftBracket(rule, arr, i)) {
                        return false;
                    }
                }
                // 加入对转义符的判断，如果有转义符就略过
            } else {
                if (isExpressOperator(arr[i])) {
                    if (!validExpressOperator(rule, arr, i)) {
                        return false;
                    }
                } else if (arr[i] == LEFT_BRACKET
                    && '\\' != arr[i - 1]) {
                    checkNum++;
                    if (!validLeftBracket(rule, arr, i)) {
                        return false;
                    }
                } else if (arr[i] == RIGHT_BRACKET
                    && '\\' != arr[i - 1]) {
                    checkNum--;
                    if (!validRightBracket(rule, arr, checkNum, i)) {
                        return false;
                    }
                }
            }
        }
        // 不为0,说明括号不对等，可能多前括号
        if (checkNum != 0) {
            log.error(INVALID_EXPRESSION, RuleErrorTypeEnum.WRONG_BRACKETS.getValue() + rule);
            return false;
        }
        return true;
    }

    /**
     * 判定运算符的合法性 判断规则(1.不能位于首位 2.不能位于末尾 3.后面不能有其他运算符 4.后面不能有后括号)
     *
     * @param rule 表达式
     * @param arr  表达式字符数组
     * @param i    运算符在表达式中的index
     * @return boolean true 表示合法
     * <AUTHOR>
     * @since 2021/1/15 7:34 上午
     */
    private static boolean validExpressOperator(String rule, char[] arr, int i) {
        int len = arr.length;
        if (i == 0
            || (i == (len - 1) && '\\' != arr[i - 1])) {
            log.error(
                INVALID_EXPRESSION,
                arr[i] + RuleErrorTypeEnum.WRONG_POSITION.getValue() + rule);
            return false;
        }
        // 最后一位不用判断
        if (len > i + 1) {
            if ((isExpressOperator(arr[i + 1]) && '\\' != arr[i - 1])
                || (arr[i + 1] == RIGHT_BRACKET && '\\' != arr[i - 1])) {
                log.error(
                    INVALID_EXPRESSION,
                    arr[i] + RuleErrorTypeEnum.WRONG_POSITION.getValue() + rule);
                return false;
            }
        }
        return true;
    }


    /**
     * 判定右括号的合法性 判定规则(1.不能位于首位 2.后面不能是前括号 3.括号计数不能小于0，小于0说明前面少了前括号)
     *
     * @param rule             表达式
     * @param arr              表达式字符数组
     * @param leftBracketCount 在index之前比，未匹配上右括号的左括号的个数
     * @param index            右括号在表达式中的index
     * @return boolean true 表示合法
     * <AUTHOR>
     * @since 2021/1/15 7:43 上午
     */
    private static boolean validRightBracket(String rule, char[] arr, int leftBracketCount, int index) {
        int len = arr.length;
        if (index == 0 || (index < (len - 1) && arr[index + 1] == LEFT_BRACKET) || leftBracketCount < 0) {
            log.error(
                INVALID_EXPRESSION,
                arr[index] + RuleErrorTypeEnum.NOT_AT_FIRST.getValue() + rule);
            return false;
        }
        return true;
    }

    /**
     * 判定左括号的合法性 判断规则(1.不能位于末尾 2.后面不能有-，|，&运算符和后括号 3.前面不能为数字)
     *
     * @param rule 表达式
     * @param arr  表达式字符数组
     * @param i    左括号在表达式中的index
     * @return boolean true 表示合法，false 表示不合法
     * <AUTHOR>
     * @since 2021/1/15 7:39 上午
     */
    private static boolean validLeftBracket(String rule, char[] arr, int i) {
        int len = arr.length;
        if (i == (len - 1)
            || isExpressOperator(arr[i + 1])
            || arr[i + 1] == RIGHT_BRACKET
            || (!(i == 0 || isExpressOperator(arr[i - 1]) || arr[i - 1] == LEFT_BRACKET))
        ) {
            log.error(
                INVALID_EXPRESSION,
                arr[i] + RuleErrorTypeEnum.NOT_AT_LAST.getValue() + rule);
            return false;
        }
        return true;
    }

    /**
     * 是否为表达式的运算符
     *
     * @param op 操作运算符
     * @return boolean
     * <AUTHOR>
     * @since 2021/1/15 7:17 上午
     */
    private static boolean isExpressOperator(char op) {
        return op == '-' || op == '&' || op == '|';
    }

    /**
     * 返回规则关键词集合。处理转义符
     *
     * @param rule 规则
     * @return {@link List String}
     * <AUTHOR>
     * @since 2020/10/10 15:52
     */
    public static List<String> getRuleKeyWords(String rule) {
        Set<String> set = new HashSet<>();
        List<Character> characters = Arrays.asList('(', ')', '&', '|', '-', ' ', '?', '!', '.');
        StringBuilder temp = new StringBuilder();
        boolean addSlash = false;
        if ('\\' == rule.charAt(0)) {
            addSlash = true;
            temp.append(rule.charAt(0));
        } else if (!characters.contains(rule.charAt(0))) {
            temp.append(rule.charAt(0));
        }
        for (int i = 1; i < rule.length(); i++) {
            if ('\\' == rule.charAt(i)) {
                addSlash = true;
                temp.append(rule.charAt(i));
            } else if (characters.contains(rule.charAt(i))
                && '\\' != rule.charAt(i - 1)) {
                set.add(temp.toString());
                temp.setLength(0);
            } else {
                if (addSlash) {
                    temp.deleteCharAt(temp.length() - 1);
                    addSlash = false;
                }
                temp.append(rule.charAt(i));
            }
        }
        set.add(temp.toString());
        return new ArrayList<>(set);
    }

    /**
     * 去重并替换表达式里面的关键词
     *
     * @param rule     规则
     * @param infoList AC匹配后的关键词位置集合
     * @return {@link String} 替换后的规则表达式
     * <AUTHOR>
     * @since 2020/10/10 15:53
     */
    protected static String getResultBuyAc(String rule, List<KeywordMarkInfo> infoList) {
        HashSet<String> set = new HashSet<>();
        for (KeywordMarkInfo keywordMarkInfo : infoList) {
            set.add(keywordMarkInfo.getKeyword());
        }
        return replaceRuleKeywordWithByChars(rule, set.toString());
    }

    /**
     * @param rule     规则
     * @param keyWords 去重后的关键词集合
     * @return 替换后的规则表达式
     * <AUTHOR>
     * @since 2020/10/10 15:54
     */
    private static String replaceRuleKeywordWithByChars(String rule, String keyWords) {
        List<Character> symbolList = Arrays.asList('(', ')', '[', ']', '|', '&', ' ', '!');
        StringBuilder ruleBuilder = new StringBuilder();
        StringBuilder keyBuilder = new StringBuilder();
        rule = replaceSymbol(rule);

        boolean addSlash = false;
        if ('\\' == rule.charAt(0)) {
            addSlash = true;
            keyBuilder.append(rule.charAt(0));
        } else if (!symbolList.contains(rule.charAt(0))) {
            keyBuilder.append(rule.charAt(0));
        } else {
            ruleBuilder.append(rule.charAt(0));
        }

        for (int i = 1; i < rule.length(); i++) {
            if ('\\' == rule.charAt(i)) {
                addSlash = true;
                keyBuilder.append(rule.charAt(i));
            } else if (symbolList.contains(rule.charAt(i))
                && '\\' != rule.charAt(i - 1)) {
                if (keyBuilder.length() > 0) {
                    ruleBuilder.append(keyWords.contains(keyBuilder.toString()));
                    keyBuilder.delete(0, keyBuilder.length());
                }
                ruleBuilder.append(rule.charAt(i));
            } else {
                if (addSlash) {
                    keyBuilder.deleteCharAt(keyBuilder.length() - 1);
                    addSlash = false;
                }
                keyBuilder.append(rule.charAt(i));
            }
        }

        if (keyBuilder.toString().length() > 0) {
            ruleBuilder.append(keyWords.contains(keyBuilder.toString()));
        }
        return ruleBuilder.toString();
    }

    /**
     * 替换特殊符号|&-，但是有转义符的保留
     *
     * @param rule 匹配表达式
     * @return java.lang.String
     * <AUTHOR>
     * @since 2022/8/2 17:15
     */
    private static String replaceSymbol(String rule) {
        StringBuilder temp = new StringBuilder();
        temp.append(rule.charAt(0));
        for (int i = 1; i < rule.length(); i++) {
            if ('|' == rule.charAt(i)
                && '\\' != rule.charAt(i - 1)) {
                temp.append("||");
            } else if ('&' == rule.charAt(i)
                && '\\' != rule.charAt(i - 1)) {
                temp.append("&&");
            } else if ('-' == rule.charAt(i)
                && '\\' != rule.charAt(i - 1)) {
                temp.append("&& !");
            } else {
                temp.append(rule.charAt(i));
            }
        }
        return temp.toString();
    }

    /**
     * 排除不符合条件的实体，匹配出最终的结果
     *
     * @param text     排除词
     * @param entities 实体列表
     * @return result
     * @since 2021/2/25 17:06
     */
    public static List<JSONObject> filterEntityOfExcludeWord(String text, List<JSONObject> entities) {
        return entities.stream()
            .filter(Objects::nonNull)
            .filter(entity -> !isExcluded(entity, text))
            .filter(entity -> isContextRelated(entity, text))
            .collect(Collectors.toList());
    }

    /**
     * 判定是否被排除词排除
     *
     * @param entity 实体对象，包含排除词 excludeWord 字段
     * @param text   匹配的文本
     * @return {@link boolean} true 表示被排除
     * <AUTHOR>
     * @date 2022/8/5 14:41
     **/
    public static boolean isExcluded(JSONObject entity, String text) {
        String excludeWords = entity.getString("excludeWord");
        if (StringUtils.isNotEmpty(excludeWords)) {
            return containsWord(text, excludeWords).isMatch();
        } else {
            return false;
        }
    }

    /**
     * 判断是否相关上下文
     *
     * @param entity 实体对象，包含排除词 context 字段
     * @param text   匹配的文本
     * @return {@link boolean} true 表示相关
     * <AUTHOR>
     * @date 2022/8/5 14:41
     **/
    public static boolean isContextRelated(JSONObject entity, String text) {
        String contextWord = entity.getString("context");
        if (StringUtils.isNotEmpty(contextWord)) {
            return containsWord(text, contextWord).isMatch();
        } else {
            return true;
        }
    }

    @Override
    public int hashCode() {
        return super.hashCode();
    }

    @Override
    public boolean equals(Object object) {
        if (this == object) {
            return true;
        }
        if (object == null) {
            return false;
        }

        return object instanceof CkmEvaluator;
    }
}
