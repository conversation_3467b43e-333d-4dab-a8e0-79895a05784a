package com.trs.moye.ability.base.nlp.domain;

import com.trs.moye.ability.annotation.SchemaProperty;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * cws simple
 */
@Data
public class SimpleCwsResult {

    @SchemaProperty(name = "person", description = "人物")
    private List<String> person = new ArrayList<>();
    /**
     * 地域
     */
    @SchemaProperty(name = "place", description = "地域")
    private List<String> place = new ArrayList<>();
    /**
     * 机构
     */
    @SchemaProperty(name = "org", description = "机构")
    private List<String> org = new ArrayList<>();
    /**
     * 分词结果
     */
    @SchemaProperty(name = "tokenizerResult", description = "分词结果")
    private String tokenizerResult;

}
