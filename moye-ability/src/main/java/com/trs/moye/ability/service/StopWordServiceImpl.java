package com.trs.moye.ability.service;

import com.huaban.analysis.jieba.JiebaSegmenter;
import com.huaban.analysis.jieba.SegToken;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.util.HashSet;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.ClassPathResource;

/**
 * 停用词的服务
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2022/3/9 5:30 下午
 **/
@Slf4j
public class StopWordServiceImpl {

    JiebaSegmenter segmenter = new JiebaSegmenter();

    // 停用词集合
    private Set<String> stopWords = new HashSet<>();

    public StopWordServiceImpl() {
        ClassPathResource classPathResource = new ClassPathResource("stop_words.txt");
        InputStream inputStream = null;
        try {
            inputStream = classPathResource.getInputStream();
            BufferedReader br = new BufferedReader(new InputStreamReader(inputStream));
            for (String line; (line = br.readLine()) != null; ) {
                stopWords.add(line);
            }
        } catch (IOException ex) {
            log.error("faild to load stop words!", ex);
        } finally {
            if (Objects.nonNull(inputStream)) {
                try {
                    inputStream.close();
                } catch (IOException e) {
                    // ignore;
                }
            }
        }
    }

    /**
     * 去除文本中的停用词
     *
     * @param text 待处理的文本
     * @return java.lang.String 返回清洗后的结果
     * @creator linwei
     * @since 2022/3/9 5:39 下午
     */
    public String removeStopWords(String text) {
        List<SegToken> segments = segmenter.process(text, JiebaSegmenter.SegMode.INDEX);
        return segments.stream().filter(seg -> !stopWords.contains(seg.word))
            .map(seg -> seg.word)
            .collect(Collectors.joining());
    }

}
