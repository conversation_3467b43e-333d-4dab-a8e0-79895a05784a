package com.trs.moye.ability.entity;

import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * 算子位置信息
 *
 * <AUTHOR>
 * @since 2021/5/7 22:50
 */
@Setter
@Getter
@AllArgsConstructor
@NoArgsConstructor
@ToString
public class OperatorStyleInfo implements Serializable {

    private Integer top;

    private Integer left;

    private Integer width;

    private Integer height;

    private String color;
}
