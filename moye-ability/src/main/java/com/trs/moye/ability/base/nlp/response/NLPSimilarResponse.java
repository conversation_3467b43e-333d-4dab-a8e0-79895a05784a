package com.trs.moye.ability.base.nlp.response;

import com.trs.moye.ability.annotation.SchemaProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 相似文识别响应
 *
 * <AUTHOR>
 * @since 2025/03/28 18:18:57
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class NLPSimilarResponse {

    /**
     * 是否原创
     */
    @SchemaProperty(name = "isOriginal", description = "是否原创")
    private Boolean isOriginal;
    /**
     * 相似文的recordId
     */
    @SchemaProperty(name = "similarRecord", description = "相似文recordId")
    private String similarRecordId;
    /**
     * 相似文个数
     */
    @SchemaProperty(name = "similarCount", description = "相似文个数")
    private Long similarCount;

}
