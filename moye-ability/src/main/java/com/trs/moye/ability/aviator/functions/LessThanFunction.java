package com.trs.moye.ability.aviator.functions;

import com.googlecode.aviator.runtime.function.AbstractFunction;
import com.googlecode.aviator.runtime.function.FunctionUtils;
import com.googlecode.aviator.runtime.type.AviatorObject;
import com.trs.moye.ability.aviator.enums.EvaluateOperatorEnum;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * functions包下的类为了避免多线程问题，类本身不能持有变量！！ AviatorEngine会被多线程调用，所以这些function也在多个线程里执行。
 */
@Slf4j
@Component("$lessThan$")
public class LessThanFunction extends AbstractFunction {

	@Override
	public String getName() {
		return "$lessThan$";
	}

	@Override
	public AviatorObject call(Map<String, Object> env, AviatorObject arg1, AviatorObject arg2) {
		return FunctionUtils.wrapReturn(AviatorFunctions.compare(arg1.getValue(env), arg2.getValue(env),
			EvaluateOperatorEnum.LessThan));
	}
}
