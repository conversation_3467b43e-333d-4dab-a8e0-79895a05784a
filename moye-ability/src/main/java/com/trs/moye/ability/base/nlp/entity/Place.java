package com.trs.moye.ability.base.nlp.entity;

import com.trs.moye.ability.annotation.SchemaEntityType;
import com.trs.moye.ability.annotation.SchemaProperty;
import com.trs.moye.base.knowledgebase.enums.KnowledgeBaseType;
import lombok.Data;

/**
 * 地域实体
 *
 * <AUTHOR>
 * @since 2025/5/12 16:31
 */
@Data
@SchemaEntityType(
    enName = "aity_place",
    zhName = "地域",
    type = KnowledgeBaseType.ENTITY
)
public class Place {
    @SchemaProperty(name = "id", description = "id")
    private Integer id;
    @SchemaProperty(name = "area", description = "地名")
    private String area;
    @SchemaProperty(name = "areaCode", description = "地区代码")
    private String areaCode;
    @SchemaProperty(name = "level", description = "级别")
    private String level;
    @SchemaProperty(name = "parentArea", description = "上级区划")
    private String parentArea;
    @SchemaProperty(name = "alias", description = "别名")
    private String alias;
    @SchemaProperty(name = "count", description = "数量")
    private Integer count;
}
