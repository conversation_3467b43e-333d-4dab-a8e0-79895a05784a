package com.trs.moye.ability.base.nlp.request;

import com.alibaba.fastjson.annotation.JSONField;
import java.util.List;
import java.util.Objects;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @version 1.0
 * @description 网信多分类
 * @since 2020/9/17 13:36
 */
@Getter
@Setter
public class MultiClassifyRequest {

    /**
     * title和content合并后的文本内容，如果只有content，则取content。
     */
    private String text;

    /**
     * 文本分词后词组，单个元素格式为词/词性
     */
    private List<String> words;

    /**
     * true则为返回特征词，false则不返回特征词，默认不返回特征词
     */
    @JSONField(name = "return_feature_word")
    private Boolean returnFeatureWord = true;

    /**
     * 返回特征词数量占原文词数的比例，取值[0,1]，默认返回0.3
     */
    @JSONField(name = "feature_word_ratio")
    private float featureWordRatio = 0.3f;

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (!(o instanceof MultiClassifyRequest)) {
            return false;
        }
        MultiClassifyRequest that = (MultiClassifyRequest) o;
        return Float.compare(that.featureWordRatio, featureWordRatio) == 0
            && text.equals(that.text)
            && words.equals(that.words)
            && Objects.equals(returnFeatureWord, that.returnFeatureWord);
    }

    @Override
    public int hashCode() {
        return Objects.hash(text, words, returnFeatureWord, featureWordRatio);
    }
}
