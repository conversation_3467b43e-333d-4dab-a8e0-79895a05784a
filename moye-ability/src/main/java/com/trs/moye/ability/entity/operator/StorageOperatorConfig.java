package com.trs.moye.ability.entity.operator;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.trs.moye.ability.deserializer.OperatorRowTypeDeserializer;
import com.trs.moye.base.data.service.entity.Condition;
import java.util.List;
import lombok.Data;

/**
 * 存储算子配置
 *
 * <AUTHOR>
 * @since 2025/03/19 16:50:00
 */
@Data
public class StorageOperatorConfig {

    /**
     * 存储id
     */
    private Integer storageId;

    /**
     * 条件
     */
    Condition[] conditions;

    /**
     * 输入字段
     */
    @JsonDeserialize(using = OperatorRowTypeDeserializer.class)
    private OperatorRowType storageFields;

    /**
     * 数据拆分开关(用于es的向量字段)
     */
    private Boolean flatMapSwitch = false;

    /**
     * 数据拆分字段(用于es的向量字段)
     */
    private List<String> flatMapFields;
}
