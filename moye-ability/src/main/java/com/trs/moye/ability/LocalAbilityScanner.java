package com.trs.moye.ability;

import com.trs.moye.ability.annotation.SchemaCategory;
import com.trs.moye.ability.annotation.SchemaDefinition;
import com.trs.moye.ability.annotation.SchemaEntityType;
import com.trs.moye.ability.annotation.SchemaProperty;
import com.trs.moye.ability.entity.Ability;
import com.trs.moye.ability.entity.AbilityFieldObjectType;
import com.trs.moye.ability.entity.Schema;
import com.trs.moye.ability.entity.Schema.ArrayTypeSchema;
import com.trs.moye.ability.entity.Schema.ObjectTypeSchema;
import com.trs.moye.ability.enums.AbilityCategory;
import com.trs.moye.ability.enums.AbilityFieldType;
import com.trs.moye.ability.enums.AbilityType;
import com.trs.moye.ability.exception.AbilityScanException;
import com.trs.moye.base.common.utils.ReflectUtils;
import java.io.File;
import java.lang.reflect.AnnotatedType;
import java.lang.reflect.Method;
import java.lang.reflect.Parameter;
import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.ClassPathScanningCandidateComponentProvider;

/**
 * 能力扫描器 - 用于解析类中定义的能力方法及其参数结构
 *
 * <p>该类通过反射机制扫描指定类中标记了{@link SchemaDefinition}注解的方法，
 * 解析方法的输入参数和返回值类型，生成对应的{@link Ability}结构描述。</p>
 *
 * <h3>主要功能：</h3>
 * <ul>
 *   <li>识别方法级别的能力定义</li>
 *   <li>解析基础类型、数组、集合和嵌套对象的结构</li>
 *   <li>生成标准化的模式(Schema)描述</li>
 * </ul>
 *
 * @see SchemaDefinition
 * @see SchemaProperty
 * @see Ability
 * @since 1.0
 */
public class LocalAbilityScanner {

    private LocalAbilityScanner() {
    }

    /**
     * 获取指定类中的指定方法的能力描述
     *
     * @param clazz      类
     * @param methodName 方法名
     * @return 能力描述
     */
    public static Ability getAbility(Class<?> clazz, String methodName) {
        return scanClass(clazz).stream()
            .filter(ability -> ability.getEnName().equals(methodName))
            .findFirst()
            .orElseThrow(() -> new AbilityScanException("Method not found: " + methodName));
    }

    /**
     * 扫描指定包下的所有类，生成 Ability 对象列表
     * <p>该方法会递归扫描指定包下的所有类，解析类中的能力方法并生成对应的 Ability 对象。</p>
     *
     * @param packageName 包名
     * @return Ability 对象列表
     */
    public static List<Ability> scanPackage(String packageName) {
        List<Ability> abilities = new ArrayList<>();

        // 获取包下的所有类
        List<Class<?>> classes = getClasses(packageName);
        for (Class<?> clazz : classes) {
            // 扫描类中的方法并生成 Ability 对象
            abilities.addAll(scanClass(clazz));
        }

        return abilities;
    }

    /**
     * 获取指定包下的所有类
     *
     * @param packageName 包名
     * @return 类列表
     */
    private static List<Class<?>> getClasses(String packageName) {
        List<Class<?>> classes = new ArrayList<>();
        try {
            ClassPathScanningCandidateComponentProvider scanner =
                new ClassPathScanningCandidateComponentProvider(false);
            // 扫描所有类
            scanner.addIncludeFilter((metadataReader, metadataReaderFactory) -> true);

            for (BeanDefinition bd : scanner.findCandidateComponents(packageName)) {
                classes.add(Class.forName(bd.getBeanClassName()));
            }
        } catch (Exception e) {
            throw new AbilityScanException("Failed to scan package: " + packageName, e);
        }

        return classes;
    }

    /**
     * 递归查找包下的所有类
     *
     * @param directory   目录
     * @param packageName 包名
     * @return 类列表
     */
    private static List<Class<?>> findClasses(File directory, String packageName) {
        if (!directory.exists()) {
            return Collections.emptyList();
        }

        // 遍历目录下的所有文件
        File[] files = directory.listFiles();
        if (files == null) {
            return Collections.emptyList();
        }
        List<Class<?>> classes = new ArrayList<>();
        for (File file : files) {
            if (file.isDirectory()) {
                // 递归查找子目录
                classes.addAll(findClasses(file, packageName + "." + file.getName()));
            } else if (file.getName().endsWith(".class")) {
                // 加载类
                String className = packageName + '.' + file.getName().substring(0, file.getName().length() - 6);
                try {
                    Class<?> clazz = Class.forName(className);
                    classes.add(clazz);
                } catch (ClassNotFoundException e) {
                    throw new AbilityScanException("Failed to load class: " + className, e);
                }
            }
        }

        return classes;
    }

    /**
     * 扫描目标类并解析所有能力方法
     * <h4>处理流程：</h4>
     * <ol>
     *   <li>遍历类中所有声明方法</li>
     *   <li>筛选带有{@link SchemaDefinition}注解的方法</li>
     *   <li>解析方法参数构建输入模式</li>
     *   <li>解析返回类型构建输出模式</li>
     * </ol>
     *
     * @param clazz 要扫描的目标类（通常为接口类）
     * @return 解析得到的能力描述列表，按方法声明顺序排列
     * @throws IllegalArgumentException 当遇到不支持的类型结构时抛出
     */
    public static List<Ability> scanClass(Class<?> clazz) {
        List<Ability> abilities = new ArrayList<>();

        // 获取类上的SchemaCategory注解
        SchemaCategory categoryAnnotation = clazz.getAnnotation(SchemaCategory.class);

        for (Method method : clazz.getDeclaredMethods()) {
            SchemaDefinition schemaDef = method.getAnnotation(SchemaDefinition.class);
            if (schemaDef == null) {
                continue;
            }

            Ability ability = new Ability();
            ability.setEnName(method.getName());
            ability.setZhName(schemaDef.zhName());
            ability.setDescription(schemaDef.description());
            ability.setType(schemaDef.type());
            ability.setPath(clazz.getName() + "." + method.getName());

            // 设置分类信息
            if (categoryAnnotation != null) {
                ability.setCategoryEnAbbr(categoryAnnotation.enAbbr());
                ability.setCategoryName(categoryAnnotation.name());
                ability.setCategoryDescription(categoryAnnotation.description());
            } else if (ability.getType().equals(AbilityType.NLP)) {
                //nlp算子
                ability.setOperatorCategoryId(AbilityCategory.NLP.getId());
                ability.setIconName(AbilityCategory.NLP.getIconName());
            } else {
                // 默认为1（通用算子）
                ability.setOperatorCategoryId(AbilityCategory.GENERAL.getId());
            }

            // 处理输入参数
            if (method.getParameterCount() == 0) {
                ability.setInputSchema(Schema.voidSchema());
            } else {
                Schema.ObjectTypeSchema inputSchema = new Schema.ObjectTypeSchema();
                for (Parameter param : method.getParameters()) {
                    SchemaProperty schemaProperty = param.getAnnotation(SchemaProperty.class);
                    if (Objects.nonNull(schemaProperty)) {
                        Schema schema = createSchemaForType(param.getParameterizedType(), schemaProperty);
                        inputSchema.addProperty(schemaProperty.name(), schema);
                    }
                }
                ability.setInputSchema(inputSchema);
            }

            // 处理返回类型
            AnnotatedType annotatedType = method.getAnnotatedReturnType();
            SchemaProperty schemaProperty = annotatedType.getAnnotation(SchemaProperty.class);
            Type type = annotatedType.getType();
            ability.setOutputSchema(createSchemaForType(type, schemaProperty));

            abilities.add(ability);
        }

        return abilities;
    }

    /**
     * 递归类型解析核心方法
     * <h4>解析优先级：</h4>
     * <pre>
     * 1. 泛型集合类型（如List<String/>）
     * 2. Java原生数组类型（如String[]）
     * 3. 自定义对象类型（如UserDTO）
     * 4. 基础类型（String/Integer等）
     * </pre>
     *
     * @param type           需要解析的Java类型
     * @param schemaProperty schema 属性
     * @return 对应的模式结构对象
     * @throws IllegalArgumentException 当遇到无法解析的类型时抛出
     */
    private static Schema createSchemaForType(Type type, SchemaProperty schemaProperty) {
        // 优先处理泛型集合类型
        if (isCollection(type)) {
            return processCollectionType(type, schemaProperty);
        }

        // 处理Java原生数组
        if (type instanceof Class<?>) {
            Class<?> clazz = (Class<?>) type;
            if (clazz.isArray()) {
                return processArrayType(schemaProperty, clazz);
            }
        }
        // 处理枚举类型
        if (type instanceof Class<?>) {
            Class<?> clazz = (Class<?>) type;
            if (clazz.isEnum()) {
                return processEnumType(schemaProperty, clazz);
            }
        }

        // 处理自定义对象类型
        if (type instanceof Class<?>) {
            Class<?> clazz = (Class<?>) type;
            if (!isBasicType(clazz)) {
                return processObjectType(schemaProperty, clazz);
            }
        }

        // 基础类型处理
        if (type instanceof Class<?>) {
            Class<?> clazz = (Class<?>) type;
            return createSchema(clazz, schemaProperty);
        }

        throw new IllegalArgumentException("不支持的类型: " + type);
    }

    @NotNull
    private static ObjectTypeSchema processObjectType(SchemaProperty schemaProperty, Class<?> clazz) {
        ObjectTypeSchema objectSchema = new ObjectTypeSchema();
        buildNameAndDesc(schemaProperty, objectSchema);
        SchemaEntityType schemaEntityType = clazz.getAnnotation(SchemaEntityType.class);
        if (schemaEntityType != null) {
            AbilityFieldObjectType entityType = new AbilityFieldObjectType(null,
                schemaEntityType.enName(), schemaEntityType.zhName(), schemaEntityType.type());
            objectSchema.setObjectType(entityType);
        }
        try {
            for (java.lang.reflect.Field field : clazz.getDeclaredFields()) {
                AnnotatedType annotatedType = field.getAnnotatedType();
                Schema fieldSchema = createSchemaForType(annotatedType.getType(),
                    annotatedType.getAnnotation(SchemaProperty.class));
                if (fieldSchema.getEnName() == null || fieldSchema.getEnName().isEmpty()) {
                    fieldSchema.setEnName(field.getName());
                }
                objectSchema.addProperty(field.getName(), fieldSchema);
            }
        } catch (Exception e) {
            throw new AbilityScanException("Error parsing object schema for " + clazz.getName(), e);
        }
        return objectSchema;
    }

    @NotNull
    private static ArrayTypeSchema processArrayType(SchemaProperty schemaProperty, Class<?> clazz) {
        ArrayTypeSchema arraySchema = new ArrayTypeSchema();
        buildNameAndDesc(schemaProperty, arraySchema);
        arraySchema.setItems(createSchemaForType(clazz.getComponentType(), null));
        return arraySchema;
    }

    @NotNull
    private static ArrayTypeSchema processCollectionType(Type type, SchemaProperty schemaProperty) {
        ArrayTypeSchema arraySchema = new ArrayTypeSchema();
        buildNameAndDesc(schemaProperty, arraySchema);
        // 默认元素类型
        Type elementType = Object.class;
        if (type instanceof ParameterizedType) {
            ParameterizedType parameterizedType = (ParameterizedType) type;
            Type[] actualArgs = parameterizedType.getActualTypeArguments();
            if (actualArgs.length > 0) {
                elementType = actualArgs[0];
            }
        }
        arraySchema.setItems(createSchemaForType(elementType, null));
        return arraySchema;
    }

    private static boolean isCollection(Type type) {
        if (type instanceof ParameterizedType) {
            ParameterizedType parameterizedType = (ParameterizedType) type;
            return Collection.class.isAssignableFrom((Class<?>) parameterizedType.getRawType());
        } else if (type instanceof Class<?>) {
            Class<?> clazz = (Class<?>) type;
            return Collection.class.isAssignableFrom(clazz);
        }
        return false;
    }

    private static Schema processEnumType(SchemaProperty schemaProperty, Class<?> clazz) {
        if (!clazz.isEnum()) {
            throw new IllegalArgumentException(String.format("[%s]不是枚举类型", clazz.getName()));
        }
        AbilityFieldType fieldType = AbilityFieldType.STRING;
        // 基础类型创建 BaseTypeSchema
        Schema.BaseTypeSchema schema = new Schema.BaseTypeSchema();
        List<Object> enumValues = ReflectUtils.getEnumValues(clazz).stream().map(s -> (Object) s).collect(
            Collectors.toList());
        schema.setEnumValues(enumValues);
        buildNameAndDesc(schemaProperty, schema);
        schema.setType(fieldType);
        return schema;
    }

    private static Schema createSchema(Class<?> clazz, SchemaProperty schemaProperty) {
        AbilityFieldType fieldType = resolveFieldType(clazz);
        Schema schema;
        switch (fieldType) {
            case OBJECT:
                // 如果是对象类型，创建 ObjectTypeSchema
                schema = new Schema.ObjectTypeSchema();
                break;
            case ARRAY:
                // 如果是数组类型，创建 ArrayTypeSchema
                Schema.ArrayTypeSchema arraySchema = new Schema.ArrayTypeSchema();
                // 解析数组元素类型
                Class<?> componentType = clazz.isArray() ? clazz.getComponentType() : Object.class;
                arraySchema.setItems(createSchema(componentType, null));
                schema = arraySchema;
                break;
            default:
                // 基础类型创建 BaseTypeSchema
                schema = new Schema.BaseTypeSchema();
        }
        buildNameAndDesc(schemaProperty, schema);
        schema.setType(fieldType);
        return schema;
    }

    private static void buildNameAndDesc(SchemaProperty schemaProperty, Schema schema) {
        if (Objects.nonNull(schemaProperty)) {
            schema.setEnName(schemaProperty.name());
            schema.setZhName(schemaProperty.description());
            schema.setDescription(schemaProperty.description());
            schema.setRequired(schemaProperty.required());
        }
    }


    /**
     * 类型映射解析器 - 将Java类型映射为系统字段类型
     *
     * <p>特殊处理规则：</p>
     * <ul>
     *   <li>int.class 和 Integer.class 统一映射为INT</li>
     *   <li>LocalDate和LocalDateTime分别映射为DATE/DATETIME</li>
     *   <li>其他复杂类型映射为OBJECT</li>
     * </ul>
     *
     * @param clazz 需要映射的Java类型
     * @return 对应的字段类型枚举
     */
    private static AbilityFieldType resolveFieldType(Class<?> clazz) {
        if (clazz == String.class) {
            return AbilityFieldType.STRING;
        }
        if (isNumericType(clazz)) {
            return getNumericFieldType(clazz);
        }
        if (clazz == Boolean.class || clazz == boolean.class) {
            return AbilityFieldType.BOOLEAN;
        }
        if (clazz == LocalDate.class) {
            return AbilityFieldType.DATE;
        }
        if (clazz == LocalDateTime.class) {
            return AbilityFieldType.DATETIME;
        }
        if (clazz.isArray() || List.class.isAssignableFrom(clazz)) {
            return AbilityFieldType.ARRAY;
        }
        if (clazz == void.class) {
            return AbilityFieldType.VOID;
        }
        return AbilityFieldType.OBJECT;
    }

    /**
     * 基础类型判断器
     * <p>基础类型范围：</p>
     * <ul>
     *   <li>Java原生类型及其包装类</li>
     *   <li>字符串类型（String）</li>
     *   <li>日期时间类型（LocalDate/LocalDateTime）</li>
     * </ul>
     *
     * @param clazz 需要判断的类型
     * @return true表示是系统定义的基础类型，false表示需要结构解析的对象类型
     */
    private static boolean isBasicType(Class<?> clazz) {
        return clazz.isPrimitive()
            || clazz == String.class
            || clazz == Integer.class
            || clazz == Boolean.class
            || clazz == Long.class
            || clazz == Float.class
            || clazz == Double.class
            || clazz == Byte.class
            || clazz == Short.class
            || clazz == Character.class
            || clazz == LocalDate.class
            || clazz == LocalDateTime.class;
    }

    private static boolean isNumericType(Class<?> clazz) {
        return clazz == Integer.class || clazz == int.class
            || clazz == Long.class || clazz == long.class
            || clazz == Double.class || clazz == double.class
            || clazz == Float.class || clazz == float.class;
    }

    private static AbilityFieldType getNumericFieldType(Class<?> clazz) {
        if (clazz == Integer.class || clazz == int.class) {
            return AbilityFieldType.INT;
        }
        if (clazz == Long.class || clazz == long.class) {
            return AbilityFieldType.LONG;
        }
        if (clazz == Double.class || clazz == double.class) {
            return AbilityFieldType.DOUBLE;
        }
        return AbilityFieldType.FLOAT;
    }
}