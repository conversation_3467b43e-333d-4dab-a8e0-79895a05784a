package com.trs.moye.ability.aviator;

import com.fasterxml.jackson.databind.JsonNode;
import com.googlecode.aviator.AviatorEvaluator;
import com.googlecode.aviator.Expression;
import com.trs.moye.ability.constants.Constants;
import com.trs.moye.ability.entity.operator.Operator;
import com.trs.moye.base.data.service.entity.Condition;
import com.trs.moye.base.data.service.entity.ValueObject;
import com.trs.moye.base.data.service.enums.DataServiceConditionType;
import com.trs.moye.base.data.service.enums.EvaluateOperator;
import java.util.List;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;

/**
 * 使用aviator执行算子过滤条件判断
 *
 * <AUTHOR>
 * @since 2025/3/3 11:16
 */
@Slf4j
public class AviatorService {

    private AviatorService() {

    }

    /**
     * aviator执行过滤条件，过滤处理算子 过滤条件：enabled=true；aviator执行结果为true
     *
     * @param operators 算子列表
     * @return 过滤后的算子列表
     */
    public static List<ExecuteOperator> filterProcessingOperator(List<Operator> operators) {
        return operators.stream()
            .filter(Operator::getEnabled)
            .map(ExecuteOperator::new)
            .collect(Collectors.toList());
    }

    /**
     * aviator执行过滤条件
     *
     * @param message 消息
     * @param script  条件脚本
     * @return 是否满足条件
     */
    public static boolean executeAviatorScript(JsonNode message, String script) {
        try {
            //将脚本作为缓存key，相同脚本时从缓存中读取
            Expression expression = AviatorEvaluator.compile(script, true);
            Object result = expression.execute(expression.newEnv(Constants.MESSAGE, message));
            if (result instanceof Boolean) {
                return (Boolean) result;
            } else {
                throw new IllegalArgumentException("aviator execute result error! result:" + result);
            }
        } catch (Exception e) {
            log.error("AviatorEngine: 算子过滤条件处理出错！msg ==> {}, script ==> {}", message, script, e);
            return false;
        }
    }

    /**
     * 生成脚本
     *
     * @param conditions 条件列表
     * @return 脚本
     */
    public static String createScript(Condition[] conditions) {
        try {
            // 根据conditions生成表达式
            if (conditions == null || conditions.length == 0) {
                return "1==1";
            }
            StringBuilder scriptBuilder = new StringBuilder();
            for (int i = 0; i < conditions.length; i++) {
                Condition condition = conditions[i];
                if (condition.getType().equals(DataServiceConditionType.EXPRESSION)) {
                    scriptBuilder.append(createOperatorScript(condition));
                } else {
                    EvaluateOperator logicOperator = condition.getOperator();
                    //not 额外处理，如果前面没有and/or则自动拼接and
                    if (logicOperator.equals(EvaluateOperator.NOT) && i > 0) {
                        Condition lastCondition = conditions[i - 1];
                        if (lastCondition.getOperator().equals(EvaluateOperator.AND)
                            || lastCondition.getOperator().equals(EvaluateOperator.OR)) {
                            scriptBuilder.append(logicOperator.getEngineOperator());
                        } else {
                            scriptBuilder.append(EvaluateOperator.AND.getEngineOperator())
                                .append(" ")
                                .append(logicOperator.getEngineOperator());
                        }
                    } else {
                        scriptBuilder.append(logicOperator.getEngineOperator());
                    }
                }
                scriptBuilder.append(" ");
            }
            return scriptBuilder.toString().trim();

        } catch (Exception e) {
            log.error("AviatorEngine: 算子过滤条件编译出错！conditions ==> {}", conditions, e);
            return "1==1";
        }
    }

    private static String createOperatorScript(Condition condition) {
        EvaluateOperator operator = condition.getOperator();
        List<String> values = condition.getValues().stream().map(ValueObject::getValue).collect(Collectors.toList());
        return operator.createAviatorScript(condition.getKey().getEnName(), values);
    }
}
