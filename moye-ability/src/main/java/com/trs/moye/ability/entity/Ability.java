package com.trs.moye.ability.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.trs.moye.ability.deserializer.SchemaDeserializer;
import com.trs.moye.ability.entity.operator.AbilityCenterCallParams;
import com.trs.moye.ability.enums.AbilityTestStatus;
import com.trs.moye.ability.enums.AbilityType;
import com.trs.moye.ability.enums.AbilityUpdateStatus;
import com.trs.moye.ability.enums.FieldRetentionMode;
import com.trs.moye.ability.typehandler.HttpRequestConfigTypeHandler;
import com.trs.moye.ability.typehandler.SchemaTypeHandler;
import com.trs.moye.base.common.entity.AuditBaseEntity;
import com.trs.moye.base.common.typehandler.CustomJacksonTypeHandler;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 能力定义
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName("ability")
public class Ability extends AuditBaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 英文名
     */
    private String enName;

    /**
     * 中文名
     */
    private String zhName;

    /**
     * 能力描述
     */
    private String description;

    /**
     * 能力类型
     */
    private AbilityType type;

    /**
     * 能力路径
     */
    private String path;

    /**
     * 算子业务分类id
     */
    private Integer operatorCategoryId;

    /**
     * 图标
     */
    private String iconName;

    /**
     * 更新状态
     */
    private AbilityUpdateStatus updateStatus;

    /**
     * 测试状态
     */
    private AbilityTestStatus testStatus;

    /**
     * 能力请求配置·
     */
    @TableField(typeHandler = HttpRequestConfigTypeHandler.class)
    HttpRequestConfig httpRequestConfig;

    /**
     * 输入参数
     */
    @JsonDeserialize(using = SchemaDeserializer.class)
    @TableField(typeHandler = SchemaTypeHandler.class)
    private Schema inputSchema;

    /**
     * 输出参数
     */
    @JsonDeserialize(using = SchemaDeserializer.class)
    @TableField(typeHandler = SchemaTypeHandler.class)
    private Schema outputSchema;

    /**
     * 能力中心配置
     */
    @TableField(exist = false)
    private AbilityCenterProperties abilityCenterProperties;
    /**
     * 能力中心参数
     */
    @TableField(typeHandler = CustomJacksonTypeHandler.class)
    private AbilityCenterCallParams abilityCenterParams;

    /**
     * 输入节点个数限制
     */
    private Integer inputSize;

    /**
     * 输出节点个数限制
     */
    private Integer outputSize;

    /**
     * 是否支持批处理中使用
     */
    private Boolean isBatchSupported = Boolean.TRUE;

    /**
     * 是否支持批处理执行条件
     */
    private Boolean isBatchConditionSupported = Boolean.TRUE;

    /**
     * 向前端说明输出字段的保留方式
     */
    private FieldRetentionMode fieldRetentionMode;

    /**
     * 算子能力详细描述(md文本)
     */
    private String details;

    /**
     * 分类英文缩写 (非数据库字段，用于临时存储)
     */
    @TableField(exist = false)
    private String categoryEnAbbr;

    /**
     * 分类名称 (非数据库字段，用于临时存储)
     */
    @TableField(exist = false)
    private String categoryName;

    /**
     * 分类描述 (非数据库字段，用于临时存储)
     */
    @TableField(exist = false)
    private String categoryDescription;
}
