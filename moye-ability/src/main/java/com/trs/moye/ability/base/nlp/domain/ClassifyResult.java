package com.trs.moye.ability.base.nlp.domain;

import com.trs.moye.ability.annotation.SchemaProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 情感分类结果
 *
 * <AUTHOR>
 * @since 2025/3/27 16:11
 */
@Data
@NoArgsConstructor
public class ClassifyResult {

    @SchemaProperty(name = "probability", description = "概率")
    private Double probability;

    @SchemaProperty(name = "labelName", description = "标签名称")
    private String labelName;


    public ClassifyResult(NlpLabel label) {
        this.probability = (double) label.getProbability();
        this.labelName = label.getLabelName();
    }
}
