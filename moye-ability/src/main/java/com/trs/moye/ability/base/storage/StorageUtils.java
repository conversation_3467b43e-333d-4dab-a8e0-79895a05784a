package com.trs.moye.ability.base.storage;

import com.trs.moye.ability.base.storage.entity.MqStorageConnectionInfo;
import com.trs.moye.ability.utils.BeanUtil;

/**
 * <AUTHOR>
 * @since 2025-05-20 17:05
 */
public class StorageUtils {

    private StorageUtils() {
    }

    public static final String INTERNAL_KAFKA_CONFIG_PROPERTIES_BEAN_NAME = "internalKafkaConfigProperties";

    private static volatile MqStorageConnectionInfo internalKafkaConnectionInfo = null;

    /**
     * 获取内部kafka连接信息
     *
     * @return {@link MqStorageConnectionInfo}
     */
    public static MqStorageConnectionInfo getInternalKafkaConnectionInfo() {
        if (internalKafkaConnectionInfo != null) {
            return internalKafkaConnectionInfo;
        }
        return initInternalKafkaConnectionInfo();
    }

    private static synchronized MqStorageConnectionInfo initInternalKafkaConnectionInfo() {
        if (internalKafkaConnectionInfo != null) {
            return internalKafkaConnectionInfo;
        }
        InternalKafkaConnectionInfoProvider provider = BeanUtil.getBean(InternalKafkaConnectionInfoProvider.class);
        internalKafkaConnectionInfo = provider.getConnectionInfo();
        return internalKafkaConnectionInfo;
    }

}
