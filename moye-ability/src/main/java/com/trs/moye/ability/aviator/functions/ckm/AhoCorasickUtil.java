package com.trs.moye.ability.aviator.functions.ckm;

import com.trs.ai.ty.base.constant.enums.KeyWordTypeEnum;
import com.trs.moye.ability.domain.KeywordMarkInfo;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import org.ahocorasick.trie.Emit;
import org.ahocorasick.trie.Trie;

/**
 * AC算法工具类
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2020/9/28 17:23
 */
public class AhoCorasickUtil {

    /**
     * 将Emit 转换为 List KeywordMarkInfo 命中关键字数据集
     *
     * @param trie 外部构建规则关键词树
     * @param text 文本
     * @return {@link KeywordMarkInfo}
     * <AUTHOR>
     * @since 2020/10/10 11:47
     */
    public static List<KeywordMarkInfo> getKeywordMarkInfos(Trie trie, String text) {

        Collection<Emit> emits = trie.parseText(text);
        List<KeywordMarkInfo> keywordMarkInfos = new ArrayList<>();
        for (Emit emit : emits) {
            keywordMarkInfos.add(
                new KeywordMarkInfo().formatEmit(emit, KeyWordTypeEnum.FOCUS_PATTERN_KEYWORD.getValue()));
        }
        return keywordMarkInfos;
    }

    /**
     * 消息处理需要外部缓存的关键词树
     *
     * @param keys 规则中关键词数组
     * @return {@link Trie}
     * <AUTHOR>
     * @since 2020/10/10 15:33
     */
    public static Trie trie(List<String> keys) {
        return Trie.builder().addKeywords(keys).build();
    }

    private AhoCorasickUtil() {
    }
}
