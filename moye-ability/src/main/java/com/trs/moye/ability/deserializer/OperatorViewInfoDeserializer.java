package com.trs.moye.ability.deserializer;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import com.fasterxml.jackson.databind.JsonNode;
import com.trs.moye.ability.entity.operator.OperatorViewInfo;
import java.io.IOException;

/**
 * Operator View Info 反序列化器
 *
 * <AUTHOR>
 * @since 2025/03/11 11:48:42
 */
public class OperatorViewInfoDeserializer extends JsonDeserializer<OperatorViewInfo> {

    @Override
    public OperatorViewInfo deserialize(JsonParser p, DeserializationContext ctx) throws IOException {
        JsonNode json = p.getCodec().readTree(p);
        return OperatorViewInfo.fromJson(json);
    }
}