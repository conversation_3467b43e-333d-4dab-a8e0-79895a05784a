package com.trs.moye.ability.deserializer;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import com.trs.moye.ability.entity.Schema;
import com.fasterxml.jackson.databind.JsonNode;
import java.io.IOException;

/**
 * schema反序列化
 */
public class SchemaDeserializer extends JsonDeserializer<Schema>{

    @Override
    public Schema deserialize(JsonParser p, DeserializationContext context)
        throws IOException {
        JsonNode node = p.getCodec().readTree(p);
        return Schema.fromJson(node);
    }

}
