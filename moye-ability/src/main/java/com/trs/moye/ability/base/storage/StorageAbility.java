package com.trs.moye.ability.base.storage;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.JsonNodeFactory;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.trs.moye.ability.annotation.SchemaDefinition;
import com.trs.moye.ability.annotation.SchemaProperty;
import com.trs.moye.ability.base.storage.entity.MqStorageConnectionInfo;
import com.trs.moye.ability.base.storage.entity.StoragePointConfig;
import com.trs.moye.ability.base.storage.mq.MessageSender;
import com.trs.moye.ability.base.storage.mq.MessageSenderFactory;
import com.trs.moye.ability.enums.AbilityType;
import com.trs.moye.ability.exception.AbilityInvokeException;
import com.trs.moye.base.common.utils.JsonUtils;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.atomic.AtomicInteger;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

/**
 * 存储能力
 */
@Slf4j
@Component
public class StorageAbility {

    public static final String SEATUNNEL_STORAGE_ABILITY = "doSeatunnelStorage";

    public static final String MQ_STORAGE_ABILITY = "sendDataToMqStoragePoint";
    public static final String TRS_MOYE_TASK_ID = "trs_moye_task_id";

    /**
     * 存储数据(推送数据到指定kafka主题)
     *
     * @param topic         主题
     * @param fields        字段列表
     * @param flatMapSwitch 数据拆分开关
     * @param flatMapFields 数据拆分字段
     * @param hasPrimaryKey 是否有主键
     * @param data          数据
     * @return 推送数据
     */
    @SchemaDefinition(zhName = "存储数据", description = "存储数据；推送数据到kafka，由seatunnel消费并存储", type = AbilityType.STORAGE)
    public JsonNode doSeatunnelStorage(@SchemaProperty(name = "topic", description = "主题") String topic,
        @SchemaProperty(name = "fields", description = "字段列表") List<String> fields,
        @SchemaProperty(name = "flatMapSwitch", description = "数据拆分开关") Boolean flatMapSwitch,
        @SchemaProperty(name = "flatMapFields", description = "数据拆分字段") List<String> flatMapFields,
        @SchemaProperty(name = "hasPrimaryKey", description = "是否有主键") Boolean hasPrimaryKey,
        @SchemaProperty(name = "data", description = "json数据") JsonNode data) {
        if (StringUtils.isBlank(topic)) {
            throw new AbilityInvokeException("doSeatunnelStorage: Topic is blank");
        }
        if (data == null) {
            log.error("存储数据为空！");
            return null;
        }
        log.info("存储算子是否有主键: {}", hasPrimaryKey);
        return sendDataToMq(
            MessageSenderFactory.getOrCreateMessageSender(StorageUtils.getInternalKafkaConnectionInfo()), topic, fields,
            flatMapSwitch, flatMapFields, data, hasPrimaryKey);
    }

    private JsonNode sendDataToMq(MessageSender sender, String topic, List<String> fields, Boolean flatMapSwitch,
        List<String> flatMapFields, JsonNode data, Boolean hasPrimaryKey) {
        try {
            //过滤存储字段
            if (fields != null && !fields.isEmpty()) {
                ObjectNode saveFieldsData = new ObjectNode(JsonNodeFactory.instance);
                data.fields().forEachRemaining(entry -> {
                    if (fields.contains(entry.getKey())) {
                        saveFieldsData.set(entry.getKey(), entry.getValue());
                    }
                });
                data = saveFieldsData;
                log.debug("save fields filter result: {}", saveFieldsData);
            }

            //若开启向量分段
            if (flatMapSwitch) {
                data = sendFlatMapToEs(sender, topic, data, flatMapFields, hasPrimaryKey);
            } else {
                sender.sendOne(topic, JsonUtils.toJsonString(data));
            }
            log.debug("doSeatunnelStorage: Data stored to kafka, topic: {}, data: {}", topic, data);
            return data;
        } catch (Exception e) {
            throw new AbilityInvokeException(
                "doSeatunnelStorage: Failed to store data to kafka, topic: " + topic + ", data: " + data, e);
        }
    }

    private JsonNode sendFlatMapToEs(MessageSender sender, String topic, JsonNode data, List<String> flatMapFields,
        Boolean hasPrimaryKey) {
        log.info("进入es向量分段处理");

        //将所选的字段的二维数组, 有几个元素,就改成几条数据
        List<JsonNode> flatMapResults = processingFlatMap(data, flatMapFields, hasPrimaryKey);

        log.info("es向量处理后的数据条数: {}", flatMapResults.size());
        for (JsonNode flatMapData : flatMapResults) {
            log.info("发送es向量处理后的数据到kafka : {}", flatMapData);
            sender.sendOne(topic, JsonUtils.toJsonString(flatMapData));
        }
        return JsonUtils.toJsonNode(JsonUtils.toJsonString(flatMapResults));
    }

    private static List<JsonNode> processingFlatMap(JsonNode data, List<String> flatMapFields,
        boolean hasPrimaryKey) {
        List<JsonNode> flatMapResults = new ArrayList<>();
        for (String field : flatMapFields) {
            //处理单个字段的向量分段
            processingSingleField(data, hasPrimaryKey, field, flatMapResults);
        }
        return flatMapResults;
    }

    private static void processingSingleField(JsonNode data, boolean hasPrimaryKey, String field,
        List<JsonNode> flatMapResults) {
        log.info("进入es向量处理, 字段: {}", field);
        JsonNode vectorField = data.get(field);
        if (!(vectorField instanceof ArrayNode)) {
            log.error("[{}]字段值不为数组，无法分割！value: {}", field, vectorField);
            return;
        }
        log.info("是否有主键: {}", hasPrimaryKey);
        ArrayNode arrayVectorNode = (ArrayNode) vectorField;
        AtomicInteger index = new AtomicInteger(1);
        arrayVectorNode.forEach(vector -> {
            ObjectNode flatMapData = data.deepCopy();
            flatMapData.set(field, vector);
            if (!hasPrimaryKey) {
                //如果不包含主键,   则每一个trs_moye_record_id都要带上从1开始的自增数字后缀, 例如 123333-1, 123333_2
                addIndexSuffix(flatMapData, index.getAndIncrement());
            }
            flatMapResults.add(flatMapData);
        });
    }

    private static void addIndexSuffix(ObjectNode flatMapData, int index) {
        if (Objects.isNull(flatMapData.get("trs_moye_record_id"))) {
            return;
        }
        String originalId = flatMapData.get("trs_moye_record_id").asText();
        String newId = String.format("%s-%d", originalId, index);
        flatMapData.put("trs_moye_record_id", newId);
    }

    /**
     * 获取存储数据方法名
     *
     * @return 存储数据方法名
     */
    public static String getStoreDataFunctionName() {
        return SEATUNNEL_STORAGE_ABILITY;
    }


    /**
     * 发送数据到MQ存储点
     *
     * @param connectionInfo 存储点参数
     * @param config         存储点配置
     * @param data           数据
     * @return 推送数据
     */
    @SchemaDefinition(zhName = "发送数据到MQ存储点", description = "存储数据：将处理后的数据发送到MQ类型存储点", type = AbilityType.STORAGE)
    public JsonNode sendDataToMqStoragePoint(
        @SchemaProperty(name = "connectionInfo", description = "MQ存储点连接信息") MqStorageConnectionInfo connectionInfo,
        @SchemaProperty(name = "config", description = "存储点配置信息") StoragePointConfig config,
        @SchemaProperty(name = "data", description = "json数据") JsonNode data) {
        if (StringUtils.isBlank(config.getTopic())) {
            throw new AbilityInvokeException("doSeatunnelStorage: Topic is blank");
        }
        if (data == null) {
            log.error("存储数据为空！");
            return null;
        }
        return sendDataToMq(MessageSenderFactory.getOrCreateMessageSender(connectionInfo),
            config.getTopic(), config.getFields(), config.getFlatMapSwitch(), config.getFlatMapFields(), data, false);
    }
}
