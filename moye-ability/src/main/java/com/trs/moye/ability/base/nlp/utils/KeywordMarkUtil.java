package com.trs.moye.ability.base.nlp.utils;

import com.alibaba.fastjson.JSONObject;
import com.trs.ai.ty.base.constant.enums.KeyWordTypeEnum;
import com.trs.moye.ability.domain.KeywordMarkInfo;
import java.util.List;
import java.util.Map;

/**
 * 关键词回标工具类
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2021/1/5 9:39 下午
 **/
public class KeywordMarkUtil {

    private KeywordMarkUtil() {
    }

    /**
     * 关键词回标
     *
     * @param tokenText       关键词
     * @param tokenStartIndex 关键词开始索引
     * @param entityTypeEnum  关键词类型
     * @return KeywordMarkInfo 回标结果
     * <AUTHOR>
     * @since 2020/12/28 16:48
     */
    public static KeywordMarkInfo newKeywordMarkInfo(
        String tokenText, Integer tokenStartIndex, KeyWordTypeEnum entityTypeEnum) {
        return new KeywordMarkInfo(
            tokenText,
            tokenStartIndex,
            tokenStartIndex + tokenText.length() - 1,
            entityTypeEnum.getValue());
    }

    /**
     * 从传入的原文、实体、回标词获取回标的List信息
     *
     * @param keywordMarkInfoList 获取的回标信息
     * @param tokenText           删去标签后的回标词语
     * @param tokenIndex          回标词语的下标
     * @param type                回标词语的类型
     * @param entityCache         cache
     * @return 新下标
     * <AUTHOR>
     * @since 2020/12/9 14:54
     */
    public static int getKeywordMarkInfoNew(
        List<KeywordMarkInfo> keywordMarkInfoList,
        String tokenText,
        int tokenIndex,
        KeyWordTypeEnum type,
        Map<String, List<JSONObject>> entityCache) {

        if (entityCache.containsKey(tokenText)) {
            keywordMarkInfoList.add(newKeywordMarkInfo(tokenText, tokenIndex, type));
        }
        tokenIndex += tokenText.length();
        return tokenIndex;
    }

}
