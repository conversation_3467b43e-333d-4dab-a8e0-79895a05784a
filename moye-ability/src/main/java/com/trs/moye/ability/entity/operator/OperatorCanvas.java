package com.trs.moye.ability.entity.operator;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.trs.moye.ability.deserializer.OperatorViewInfoDeserializer;
import com.trs.moye.ability.typehandler.OperatorViewInfoTypeHandler;
import lombok.Data;

/**
 * 算子编排画布
 *
 * <AUTHOR>
 * @since 2025/03/10 15:37:20
 */
@Data
public class OperatorCanvas {

    private Integer width;

    private Integer height;

    private Integer top;

    private Integer left;

    /**
     * 画布显示信息
     */
    @JsonDeserialize(contentUsing = OperatorViewInfoDeserializer.class)
    @TableField(typeHandler = OperatorViewInfoTypeHandler.class)
    private OperatorViewInfo[] operatorViewInfo;
}
