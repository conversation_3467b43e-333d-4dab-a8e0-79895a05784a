package com.trs.moye.ability.typehandler;

import com.trs.moye.ability.entity.operator.OperatorViewInfo;
import com.trs.moye.base.common.utils.JsonUtils;
import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;
import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;

/**
 * 算子显示信息类型处理程序
 *
 * <AUTHOR>
 * @since 2025/03/11 12:32:09
 */
public class OperatorViewInfoTypeHandler extends BaseTypeHandler<OperatorViewInfo> {

    @Override
    public void setNonNullParameter(PreparedStatement ps, int i, OperatorViewInfo parameter, JdbcType jdbcType) 
        throws SQLException {
        ps.setString(i, JsonUtils.toJsonString(parameter));
    }

    @Override
    public OperatorViewInfo getNullableResult(ResultSet rs, String columnName) throws SQLException {
        return parseJson(rs.getString(columnName));
    }

    @Override
    public OperatorViewInfo getNullableResult(ResultSet rs, int columnIndex) throws SQLException {
        return parseJson(rs.getString(columnIndex));
    }

    @Override
    public OperatorViewInfo getNullableResult(CallableStatement cs, int columnIndex) throws SQLException {
        return parseJson(cs.getString(columnIndex));
    }

    private OperatorViewInfo parseJson(String json) {
        return JsonUtils.parseObject(json, OperatorViewInfo.class);
    }
}
