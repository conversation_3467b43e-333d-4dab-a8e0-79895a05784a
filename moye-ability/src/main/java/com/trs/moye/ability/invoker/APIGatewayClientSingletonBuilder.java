package com.trs.moye.ability.invoker;

import com.trs.midend.sdk.upgrade.APIGatewayClient;
import com.trs.moye.ability.entity.AbilityCenterProperties;


/**
 * API Gateway Client Singleton
 *
 * <AUTHOR>
 * @since 2025/3/7
 */
public class APIGatewayClientSingletonBuilder {

    private static APIGatewayClient instance;

    private APIGatewayClientSingletonBuilder() {
        // Private constructor to prevent instantiation
    }

    /**
     * Get API Gateway Client instance
     *
     * @param abilityCenterProperties Ability Center Properties
     * @return API Gateway Client instance
     */
    public static synchronized APIGatewayClient getInstance(AbilityCenterProperties abilityCenterProperties) {
        if (instance == null) {
            instance = new APIGatewayClient(abilityCenterProperties.getClientKey(),
                abilityCenterProperties.getClientName(), abilityCenterProperties.getPublicKey());
        }
        return instance;
    }
}