package com.trs.moye.ability.base.storage.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.trs.moye.base.data.connection.enums.ConnectionType;
import com.trs.moye.base.data.connection.enums.KafkaSaslMechanism;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.ObjectUtils;

/**
 * <AUTHOR>
 * @since 2025-05-20 15:45
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class MqStorageConnectionInfo {

    /**
     * 连接类型
     */
    private ConnectionType connectionType;

    /**
     * 地址
     */
    private String host;

    /**
     * 端口
     */
    private Integer port;

    /**
     * 用户名
     */
    private String username;

    /**
     * 密码
     */
    private String password;

    /**
     * kafka sasl 认证机制
     */
    private KafkaSaslMechanism saslMechanism;

    /**
     * 获取服务地址
     *
     * @return 服务地址
     */
    @JsonIgnore
    public String getServerAddress() {
        return host + ":" + port;
    }

    /**
     * 获取身份标识
     *
     * @return 身份标识
     */
    @JsonIgnore
    public String getIdentity() {
        return connectionType.name().toLowerCase() + "://" + host + ":" + port
            + "/username=" + (ObjectUtils.isEmpty(username) ? "" : username)
            + "&password=" + (ObjectUtils.isEmpty(password) ? "" : password)
            + "&saslMechanism=" + (ObjectUtils.isEmpty(saslMechanism) ? "" : saslMechanism);
    }
}
