package com.trs.moye.ability.exception;

import com.trs.moye.ability.enums.AbilityType;

/**
 * 能力执行异常
 */
public class AbilityInvokeException extends RuntimeException {

    public AbilityInvokeException(String message) {
        super(message);
    }

    public AbilityInvokeException(String message, Throwable cause) {
        super(message + "\n[cause]:" + createExceptionMessage(cause), cause);
    }

    public AbilityInvokeException(AbilityType abilityType, String abilityName, String desc, Throwable cause) {
        super(String.format("调用%s算子失败：%s\n[desc]:%s\n[cause]:%s",
            abilityType.getLabel(), abilityName, desc, createExceptionMessage(cause)), cause);
    }

    private static String createExceptionMessage(Throwable cause) {
        return cause.getClass().getName() + ": " + cause.getMessage();
    }
}
