package com.trs.moye.ability.entity.operator;

import java.io.Serializable;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 能力中心算子参数
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
public class AbilityCenterCallParams implements Serializable {

    /**
     * 应用id
     */
    private Integer appId;

    /**
     * 应用名称
     */
    private String appName;

    /**
     * 能力id
     */
    private Integer abilityId;

    /**
     * 版本号
     */
    private String version;

    /**
     * 过期时间
     */
    private String expirationTime;


    public AbilityCenterCallParams(Integer appId, String appName, Integer abilityId, String version) {
        this.appId = appId;
        this.appName = appName;
        this.abilityId = abilityId;
        this.version = version;
    }
}
