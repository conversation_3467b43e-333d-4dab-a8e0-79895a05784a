package com.trs.moye.ability.entity.operator;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.trs.moye.ability.typehandler.OperatorCanvasTypeHandler;
import com.trs.moye.ability.typehandler.OperatorRowTypeHandler;
import com.trs.moye.base.common.typehandler.CustomJacksonTypeHandler;
import java.util.List;
import java.util.Map;
import lombok.Data;

/**
 * 算子编排
 */
@Data
@TableName("operator_pipeline")
public class OperatorPipeline {

    /**
     * id
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 数据建模id
     */
    private Long dataModelId;

    /**
     * 算子
     */
    @TableField(exist = false)
    private List<Operator> operators;

    /**
     * 画布
     */
    @TableField(typeHandler = OperatorCanvasTypeHandler.class)
    private OperatorCanvas canvas;

    /**
     * 处理流程的输入字段，即进入算子编排的数据的字段。由来源表配置字段映射时勾选的字段组成。
     */
    @TableField(typeHandler = OperatorRowTypeHandler.class)
    private OperatorRowType inputFields;

    /**
     * 字段映射
     */
    @TableField(typeHandler = CustomJacksonTypeHandler.class)
    private Map<String, List<String>> fieldMapping;

    /**
     * 数据源连接id：选择做流处理数据来源的贴源库存储点的连接id
     */
    private Integer dataSourceConnectionId;
}