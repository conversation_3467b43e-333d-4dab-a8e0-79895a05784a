package com.trs.moye.ability.base.nlp.domain;

import com.trs.moye.ability.annotation.SchemaProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2025/3/27 19:54
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class NlpLabel {

    @SchemaProperty(name = "labelName", description = "标签名称")
    private String labelName;

    @SchemaProperty(name = "probability", description = "概率")
    private float probability;
}
