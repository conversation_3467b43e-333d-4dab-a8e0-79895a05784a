package com.trs.moye.ability.enums;

import java.util.HashMap;
import java.util.Map;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/6/25
 */

@Getter
@AllArgsConstructor
public enum AbilityCenterDataType {
    STRING(1, "String"),
    INTEGER(2, "Integer"),
    LONG(3, "Long"),
    FLOAT(4, "Float"),
    DOUBLE(5, "Double"),
    BOOLEAN(6, "Boolean"),
    OBJECT(7, "Object"),
    ARRAY(8, "Array"),
    TIMESTAMP(9,"Timestamp"),
    BIGINT(10,"Bigint"),
    TEXT(11,"Text"),
    NUMBER(12,"Number"),
    FILE(13,"File"),
    ;

    private final int code;
    private final String name;

    private static final Map<Integer, AbilityCenterDataType> CODE_TO_ENUM_MAP = new HashMap<>();
    static {
        for (AbilityCenterDataType e : values()) {
            CODE_TO_ENUM_MAP.put(e.code, e);
        }
    }

    /**
     * 根据id获取枚举
     *
     * @param id id
     * @return 枚举
     */
    public static AbilityCenterDataType findById(Integer id) {
        return id == null ? null : CODE_TO_ENUM_MAP.get(id);
    }

    /**
     * 根据名称获取枚举
     *
     * @param name 名称
     * @return 枚举
     */
    public static AbilityCenterDataType valueOfName(String name) {
        for (AbilityCenterDataType e : values()) {
            if (e.name.equalsIgnoreCase(name)) {
                return e;
            }
        }
        return null;
    }


    /**
     * 转换为能力中心字段类型
     *
     * @return 能力中心字段类型
     */
    public AbilityFieldType toAbilityFieldType() {
        // 特殊处理 BIGINT → LONG
        // NUMBER → DOUBLE
        switch (this) {
            case STRING:
            case TIMESTAMP:
                return AbilityFieldType.STRING;
            case INTEGER:
                return AbilityFieldType.INT;
            case LONG:
            case BIGINT:
                return AbilityFieldType.LONG;
            case FLOAT:
                return AbilityFieldType.FLOAT;
            case DOUBLE:
            case NUMBER:
                return AbilityFieldType.DOUBLE;
            case BOOLEAN:
                return AbilityFieldType.BOOLEAN;
            case OBJECT:
                return AbilityFieldType.OBJECT;
            case ARRAY:
                return AbilityFieldType.ARRAY;
            default:
                return AbilityFieldType.VOID;
        }
    }
}
