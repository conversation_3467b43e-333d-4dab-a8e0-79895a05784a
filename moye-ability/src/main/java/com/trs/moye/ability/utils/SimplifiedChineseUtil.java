package com.trs.moye.ability.utils;

import com.github.houbb.opencc4j.util.ZhConverterUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * 判断是否是简体中文，并转换的工具
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2020/9/14 13:43
 **/
@Slf4j
public class SimplifiedChineseUtil {

    private static final String ENCODE = "GB2312";

    private SimplifiedChineseUtil() {
    }

    /**
     * 如果是繁体，则转为简体，不是则返回原文。
     *
     * @param str str
     * @return java.lang.String
     * <AUTHOR>
     * @since 2020/9/14 17:54
     */
    public static String isSimpleOrComplex(String str) {
        try {
            if (str.equals(new String(str.getBytes(ENCODE), ENCODE))) {
                return str;
            } else {
                return ZhConverterUtil.toSimple(str);
            }
        } catch (Exception e) {
            log.error("繁体转换简体错误！错误信息 : ", e);
        }
        return str;
    }

}
