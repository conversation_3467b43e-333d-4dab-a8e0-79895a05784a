package com.trs.moye.ability.entity.operator;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.trs.moye.ability.deserializer.OperatorRowTypeDeserializer;
import com.trs.moye.ability.entity.AbilityFieldObjectType;
import com.trs.moye.ability.entity.Schema;
import com.trs.moye.ability.entity.Schema.ArrayTypeSchema;
import com.trs.moye.ability.entity.Schema.ObjectTypeSchema;
import com.trs.moye.ability.enums.AbilityFieldType;
import com.trs.moye.base.common.enums.FieldType;
import com.trs.moye.base.data.model.entity.DataModelField;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Optional;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 算子数据结构
 */

@EqualsAndHashCode(callSuper = true)
@Data
@JsonDeserialize(using = OperatorRowTypeDeserializer.class)
public class OperatorRowType extends HashMap<String, Schema> {

    /**
     * 生成dataModelField
     *
     * @return {@link DataModelField}
     */
    public List<DataModelField> toFields() {
        List<DataModelField> fields = new ArrayList<>(this.size());
        this.forEach((fieldName, schema) -> {
            DataModelField field = createAdaptiveField(fieldName, schema);
            fields.add(field);
        });
        return fields;
    }

    private DataModelField createAdaptiveField(String fieldName, Schema schema) {
        DataModelField field = new DataModelField();
        field.setEnName(fieldName);
        field.setZhName(Optional.ofNullable(schema.getZhName()).orElse(fieldName));
        //数组类型转换为数组item类型的多值字段
        if (schema instanceof ArrayTypeSchema) {
            ArrayTypeSchema arrayTypeSchema = (ArrayTypeSchema) schema;
            AbilityFieldType itemType = arrayTypeSchema.getItems().getType();
            field.setType(itemType.toFieldType());
            field.setTypeName(itemType.getLabel());
            field.setMultiValue(true);
            // 设置数组元素对象类型转换为具体知识库类型
            if (AbilityFieldType.OBJECT.equals(itemType)
                && arrayTypeSchema.getItems() instanceof ObjectTypeSchema) {
                ObjectTypeSchema objectTypeSchema = (ObjectTypeSchema) arrayTypeSchema.getItems();
                if (AbilityFieldObjectType.isNotEmpty(objectTypeSchema.getObjectType())) {
                    setKnowledgeBaseType(objectTypeSchema, field);
                }
            }
        } else if (schema instanceof ObjectTypeSchema
                   && AbilityFieldObjectType.isNotEmpty(((ObjectTypeSchema) schema).getObjectType())) {
            ObjectTypeSchema objectTypeSchema = (ObjectTypeSchema) schema;
            // 对象类型转换为具体知识库类型
            setKnowledgeBaseType(objectTypeSchema, field);
        } else {
            AbilityFieldType abilityFieldType = Optional.ofNullable(schema.getType()).orElse(AbilityFieldType.STRING);
            FieldType fieldType = abilityFieldType.toFieldType();
            field.setType(fieldType);
            field.setTypeName(fieldType.getLabel());
        }
        return field;
    }

    private void setKnowledgeBaseType(ObjectTypeSchema objectTypeSchema, DataModelField field) {
        AbilityFieldObjectType objectType = objectTypeSchema.getObjectType();
        field.setType(FieldType.valueOf(objectType.getKnowledgeBaseType().name()));
        field.setTypeName(objectType.getKnowledgeBaseZhName());
    }
}
