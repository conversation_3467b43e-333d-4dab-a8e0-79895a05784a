package com.trs.moye.ability.domain;

import com.fasterxml.jackson.databind.JsonNode;
import com.trs.moye.ability.entity.HttpRequestConfig;
import com.trs.moye.ability.entity.HttpRequestConfig.HttpContentType;
import java.util.Map;
import lombok.AllArgsConstructor;
import lombok.Data;

/**
 * http算子调用参数
 *
 * <AUTHOR>
 * @since 2025/6/12 10:03
 */
@Data
@AllArgsConstructor
public class HttpAbilityInfo {

    /**
     * url(包括query参数)
     */
    private String url;
    /**
     * POST/GET
     */
    private String method;
    /**
     * JSON/FORM
     */
    private HttpContentType contentType;
    /**
     * headers
     */
    private Map<String, String> headers;
    /**
     * form参数
     */
    private Map<String, String> formParams;
    /**
     * json参数
     */
    private JsonNode jsonParams;


    public HttpAbilityInfo(HttpRequestConfig config) {
        this.url = config.getUrl();
        this.method = config.getMethod();
        this.contentType = config.getContentType();
    }
}
