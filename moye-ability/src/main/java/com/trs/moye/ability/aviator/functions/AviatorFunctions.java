package com.trs.moye.ability.aviator.functions;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.trs.moye.ability.aviator.enums.EvaluateOperatorEnum;
import com.trs.moye.ability.aviator.utils.DateTimeHelper;
import com.trs.moye.base.common.utils.JsonUtils;
import java.nio.charset.StandardCharsets;
import java.util.Base64;
import java.util.Objects;
import java.util.regex.Pattern;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

/**
 * aviator函数实现
 *
 * <AUTHOR>
 * @since 2025/3/3 16:42
 */
@Slf4j
public class AviatorFunctions {

    /**
     * 判断是否为空
     *
     * @param value object
     * @return 结果
     */
    public static Boolean isEmpty(Object value) {
        try {
            //统一处理成JsonNode
            JsonNode jsonValue = toJsonNode(value);
            if (Objects.isNull(jsonValue) || jsonValue.isNull()) {
                return true;
            }
            // 多值数组情况
            if (jsonValue instanceof ArrayNode) {
                ArrayNode a = (ArrayNode) jsonValue;
                return a.isEmpty();
            } else {
                return StringUtils.isBlank(JsonUtils.nodeToString(jsonValue));
            }
        } catch (Exception e) {
            log.error("执行自定义表达式 [$empty$] error !!!", e);
            return false;
        }
    }

    private static JsonNode toJsonNode(Object value) {
        if (value == null) {
            return null;
        }
        if (value instanceof JsonNode) {
            return (JsonNode) value;
        } else {
            return JsonUtils.toJsonNode(value.toString());
        }
    }

    /**
     * 值比较 大于小于等 只支持数字类型和时间字符串类型比较
     *
     * @param messageValue 消息值
     * @param filterValue  过滤值
     * @param operator     操作符
     * @return 结果
     */
    public static Boolean compare(Object messageValue, Object filterValue, EvaluateOperatorEnum operator) {
        JsonNode messageNode = toJsonNode(messageValue);
        if (messageNode == null || filterValue == null) {
            return false;
        }
        String filterStr = filterValue.toString();
        if (messageNode.isNumber() && StringUtils.isNumeric(filterStr)) {
            return compareNumber(messageNode, filterStr, operator);
        } else {
            return compareString(messageNode, filterStr, operator);
        }
    }

    /**
     * 数值比较
     *
     * @param messageNode 消息值
     * @param filterNode  过滤值
     * @param operator    操作符
     * @return 结果
     */
    private static Boolean compareNumber(JsonNode messageNode, String filterNode, EvaluateOperatorEnum operator) {
        try {
            double filterValue = Double.parseDouble(filterNode);
            switch (operator) {
                case GreaterThan:
                    return messageNode.asDouble() > filterValue;
                case GreaterThanOrEqual:
                    return messageNode.asDouble() >= filterValue;
                case LessThan:
                    return messageNode.asDouble() < filterValue;
                case LessThanOrEqual:
                    return messageNode.asDouble() <= filterValue;
                default:
                    return false;
            }
        } catch (Exception e) {
            log.error("数值比较异常，messageNode: {}, filterNode: {}", messageNode, filterNode);
            return false;
        }

    }

    /**
     * 时间字符串比较
     *
     * @param messageNode 消息值
     * @param filterStr   过滤值
     * @param operator    操作符
     * @return 结果
     */
    private static Boolean compareString(JsonNode messageNode, String filterStr, EvaluateOperatorEnum operator) {
        String message = JsonUtils.nodeToString(messageNode);
        if (DateTimeHelper.isDateType(message) && DateTimeHelper.isDateType(filterStr)) {
            return DateTimeHelper.evaluate(message, filterStr, operator);
        } else {
            log.error("时间格式不正确，无法进行比较，messageNode: {}, filterNode: {}", messageNode, filterStr);
            return false;
        }
    }

    /**
     * 判断是否相等
     *
     * @param messageValue 消息值 从消息json中提取的值，一般情况下类型为JsonNode
     * @param filterValue  过滤值 过滤条件中提供的比较值
     * @return 结果
     */
    public static Boolean equals(Object messageValue, Object filterValue) {
        JsonNode messageNode = toJsonNode(messageValue);
        if (messageNode == null || filterValue == null) {
            return false;
        }
        String filterStr = filterValue.toString();
        // 时间字符串比较
        if (DateTimeHelper.isDateType(filterStr)) {
            return DateTimeHelper.evaluate(JsonUtils.nodeToString(messageNode), filterStr, EvaluateOperatorEnum.Equal);
        } else {
            return filterStr.equals(JsonUtils.nodeToString(messageNode));
        }
    }

    /**
     * 正则匹配
     *
     * @param messageValue 消息值
     * @param filterValue  过滤值
     * @return 结果
     */
    public static Boolean regexPattern(Object messageValue, Object filterValue) {
        JsonNode messageNode = toJsonNode(messageValue);
        if (messageNode == null || filterValue == null) {
            return false;
        }
        // 为防止转义符报错，正则表达式进行了base64编码
        String regex = new String(Base64.getDecoder().decode(filterValue.toString().getBytes(StandardCharsets.UTF_8)));
        if (StringUtils.isBlank(regex)) {
            log.error("正则表达式为空！filterValue = {}", filterValue);
            return false;
        }
        try {
            // 只要匹配到一个就返回true
            Pattern compile = Pattern.compile(regex);
            return compile.matcher(JsonUtils.nodeToString(messageNode)).find();
        } catch (Exception e) {
            log.error("正则匹配异常，messageNode: {}, filterNode: {}", messageValue, filterValue);
            return false;
        }

    }

    /**
     * 判断是否包含
     *
     * @param messageValue 消息值
     * @param filterValue  过滤值
     * @return 结果
     */
    public static Boolean in(Object messageValue, Object filterValue) {
        JsonNode messageNode = toJsonNode(messageValue);
        JsonNode filterNode = toJsonNode(filterValue);
        if (messageNode == null || filterNode == null) {
            return false;
        }
        // filterValue为数组
        if (filterNode.isArray()) {
            for (JsonNode node : filterNode) {
                // messageValue为数组, 则遍历数组匹配
                if (messageNode.isArray()) {
                    for (JsonNode message : messageNode) {
                        if (JsonUtils.equals(message, node)) {
                            return true;
                        }
                    }
                } else {
                    if (JsonUtils.equals(messageNode, node)) {
                        return true;
                    }
                }
            }
        } else {
            log.error("filterValue类型不为数组，无法进行包含匹配！filterValue = {}", filterValue);
            return false;
        }
        return false;
    }

    /**
     * 模糊匹配
     *
     * @param messageValue 消息值
     * @param filterValue  过滤值
     * @return 结果
     */
    public static Boolean fuzzyIn(Object messageValue, Object filterValue) {
        JsonNode messageNode = toJsonNode(messageValue);
        JsonNode filterNode = toJsonNode(filterValue);
        if (messageNode == null || filterNode == null) {
            return false;
        }
        // filterValue为数组
        if (filterNode.isArray()) {
            for (JsonNode node : filterNode) {
                if (messageNode.toString().contains(node.asText())) {
                    return true;
                }
            }
        } else {
            log.error("filterValue类型不为数组，无法进行模糊匹配！filterValue = {}", filterValue);
            return false;
        }
        return false;
    }
}
