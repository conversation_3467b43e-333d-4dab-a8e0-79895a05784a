package com.trs.moye.ability.typehandler;

import com.trs.moye.ability.entity.operator.OperatorRowType;
import com.trs.moye.base.common.utils.JsonUtils;
import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;

/**
 * OperatorRowTypeHandler
 */
public class OperatorRowTypeHandler extends BaseTypeHandler<OperatorRowType> {

    @Override
    public void setNonNullParameter(PreparedStatement ps, int i, OperatorRowType parameter, JdbcType jdbcType)
        throws SQLException {
        ps.setString(i, JsonUtils.toJsonString(parameter));
    }

    @Override
    public OperatorRowType getNullableResult(ResultSet rs, String columnName) throws SQLException {
        String json = rs.getString(columnName);
        return JsonUtils.parseObject(json, OperatorRowType.class);
    }

    @Override
    public OperatorRowType getNullableResult(ResultSet rs, int columnIndex) throws SQLException {
        String json = rs.getString(columnIndex);
        return JsonUtils.parseObject(json, OperatorRowType.class);
    }

    @Override
    public OperatorRowType getNullableResult(CallableStatement cs, int columnIndex) throws SQLException {
        String json = cs.getString(columnIndex);
        return JsonUtils.parseObject(json, OperatorRowType.class);
    }
}
