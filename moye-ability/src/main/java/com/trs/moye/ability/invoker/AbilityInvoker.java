package com.trs.moye.ability.invoker;

import com.fasterxml.jackson.databind.JsonNode;
import com.trs.moye.ability.entity.InputBind;
import com.trs.moye.ability.entity.Ability;

/**
 * 能力调用器
 */
public interface AbilityInvoker {

    /**
     * 能力调用
     *
     * @param ability  能力
     * @param input    输入参数
     * @param inputBind 数据绑定
     * @return 输出参数
     * @throws Throwable Throwable
     */
    Object invoke(Ability ability, JsonNode input, InputBind inputBind) throws Throwable;
}
