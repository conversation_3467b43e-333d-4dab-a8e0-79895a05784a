package com.trs.moye.ability.entity;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.trs.moye.ability.deserializer.SchemaDeserializer;
import com.trs.moye.ability.enums.AbilityFieldBindType;
import com.trs.moye.ability.enums.AbilityFieldType;
import com.trs.moye.base.common.utils.JsonUtils;
import java.io.Serializable;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

/**
 * 能力参数定义
 */
@Data
@Slf4j
@JsonDeserialize(using = SchemaDeserializer.class)
public abstract class Schema implements Serializable {

    private static final long serialVersionUID = 1L;

    protected String enName;
    protected String zhName;
    protected AbilityFieldType type;
    protected String description;
    protected boolean required = true;
    /**
     * 绑定类型, null表示不限制绑定方式
     */
    protected AbilityFieldBindType bindType;

    /**
     * 基础类型
     */
    @EqualsAndHashCode(callSuper = true)
    @Data
    public static class BaseTypeSchema extends Schema {

        /**
         * 枚举值, 如果是枚举类型, 则该字段不为null
         */
        private List<Object> enumValues;

        /**
         * 默认值
         */
        private Object defaultValue;

    }

    /**
     * 获取void类型的Schema
     *
     * @return void类型的Schema
     */
    public static Schema voidSchema() {
        return new BaseTypeSchema() {{
            setType(AbilityFieldType.VOID);
        }};
    }

    /**
     * 获取默认string类型的Schema
     *
     * @return Schema
     */
    public static Schema defaultStringSchema() {
        return new BaseTypeSchema() {{
            setType(AbilityFieldType.STRING);
        }};
    }

    /**
     * 数组
     */
    @EqualsAndHashCode(callSuper = true)
    @Data
    public static class ArrayTypeSchema extends Schema {

        private Schema items;

        public ArrayTypeSchema() {
            this.type = AbilityFieldType.ARRAY;
        }

        public ArrayTypeSchema(Schema items) {
            this.type = AbilityFieldType.ARRAY;
            this.items = items;
        }
    }

    /**
     * 对象
     */
    @EqualsAndHashCode(callSuper = true)
    @Data
    public static class ObjectTypeSchema extends Schema {

        private static final long serialVersionUID = 1L;

        private Map<String, Schema> properties = new HashMap<>();

        private String propertiesFromKey;

        private AbilityFieldObjectType objectType;

        public ObjectTypeSchema() {
            this.type = AbilityFieldType.OBJECT;
        }

        public ObjectTypeSchema(Map<String, Schema> properties) {
            this.type = AbilityFieldType.OBJECT;
            this.properties.putAll(properties);
        }

        /**
         * 添加属性
         *
         * @param name        属性名称
         * @param fieldSchema 属性定义
         */
        public void addProperty(String name, Schema fieldSchema) {
            properties.put(name, fieldSchema);
        }

        /**
         * 将属性转换为Map
         *
         * @param paramBindMap 参数绑定Map
         * @return 属性Map
         */
        public Map<String, Object> toMap(Map<String, Object> paramBindMap) {
            Map<String, Object> bodyMap = new HashMap<>();
            properties.forEach(
                (key, value) -> {
                    if (paramBindMap.containsKey(key)) {
                        bodyMap.put(key, paramBindMap.get(key));
                    }
                }
            );
            return bodyMap;
        }
    }

    /**
     * 解析节点
     *
     * @param json json
     * @return schema
     */
    public static Schema fromJson(JsonNode json) {
        if (Objects.isNull(json)) {
            return new BaseTypeSchema();
        }
        // 公共字段解析
        String typeName = Optional.ofNullable(json.get("type"))
            .map(JsonNode::asText)
            .filter(StringUtils::isNotBlank)
            .orElse("STRING");

        AbilityFieldType type;
        try {
            type = AbilityFieldType.valueOf(typeName.toUpperCase());
        } catch (IllegalArgumentException e) {
            log.error("Schema type {} not support, use STRING instead", typeName);
            type = AbilityFieldType.STRING;
        }

        Schema schema;
        switch (type) {
            case OBJECT:
                ObjectTypeSchema objectTypeSchema = new ObjectTypeSchema();
                Map<String, Schema> properties = Optional.ofNullable(json.get("properties"))
                    .map(JsonNode::toString)
                    .map(p -> JsonUtils.toMap(p, String.class, Schema.class))
                    .orElseGet(Collections::emptyMap);
                properties.remove("@class");
                objectTypeSchema.setProperties(properties);
                AbilityFieldObjectType objectType = json.has("objectType")
                    ? JsonUtils.parseObject(json.get("objectType").toString(), AbilityFieldObjectType.class)
                    : new AbilityFieldObjectType();
                objectTypeSchema.setObjectType(objectType);
                objectTypeSchema.setPropertiesFromKey(
                    json.has("propertiesFromKey") ? json.get("propertiesFromKey").asText() : null);
                schema = objectTypeSchema;
                break;
            case ARRAY:
                ArrayTypeSchema arrayTypeSchema = new ArrayTypeSchema();
                arrayTypeSchema.setItems(Schema.fromJson(json.get("items")));
                schema = arrayTypeSchema;
                break;
            default:
                BaseTypeSchema baseTypeSchema = new BaseTypeSchema();
                List<Object> enumValues =
                    json.has("enumValues") ? JsonUtils.toList(json.get("enumValues").toString(), Object.class) : null;
                baseTypeSchema.setEnumValues(enumValues);
                schema = baseTypeSchema;
        }

        String enName = json.has("enName") ? json.get("enName").asText() : null;
        String zhName = json.has("zhName") ? json.get("zhName").asText() : null;
        String description = json.has("description") ? json.get("description").asText() : null;
        boolean required = json.has("required") && json.get("required").asBoolean();
        String bindType = json.has("bindType") ? json.get("bindType").asText() : null;

        // 设置公共字段
        schema.setType(type);
        schema.setEnName(enName);
        schema.setZhName(zhName);
        schema.setDescription(description);
        schema.setRequired(required);
        schema.setBindType(StringUtils.isNotBlank(bindType) ? AbilityFieldBindType.valueOf(bindType) : null);

        return schema;
    }

}


