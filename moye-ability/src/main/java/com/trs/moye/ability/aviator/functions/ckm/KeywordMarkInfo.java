package com.trs.moye.ability.aviator.functions.ckm;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import org.ahocorasick.trie.Emit;

/**
 * 关键词打标
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class KeywordMarkInfo {

  private String keyword;

  private int from;

  private int end;

  private int type;

  /**
   * 将Emit转换成现有关键词命中实体
   *
   * @param emit {@link Emit}
   * @param type 类型
   * @return {@link KeywordMarkInfo}
   * <AUTHOR>
   * @since 2020/9/28 18:46
   */
  public KeywordMarkInfo formatEmit(Emit emit, int type) {
    KeywordMarkInfo keywordMarkInfo = new KeywordMarkInfo();
    keywordMarkInfo.setEnd(emit.getEnd());
    keywordMarkInfo.setFrom(emit.getStart());
    keywordMarkInfo.setKeyword(emit.getKeyword());
    keywordMarkInfo.setType(type);
    return keywordMarkInfo;
  }
}
