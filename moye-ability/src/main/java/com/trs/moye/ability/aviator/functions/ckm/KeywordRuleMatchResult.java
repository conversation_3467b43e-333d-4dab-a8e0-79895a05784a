package com.trs.moye.ability.aviator.functions.ckm;

import java.util.List;
import lombok.Data;

/**
 * 态势一期表达式匹配结果
 */
@Data
public class KeywordRuleMatchResult {

    private boolean isMatch;
    private List<KeywordMarkInfo> keywords;

    /**
     * 构建一个不匹配的结果
     *
     * @return {@link KeywordRuleMatchResult}
     */
    public static KeywordRuleMatchResult falseResult() {
        KeywordRuleMatchResult result = new KeywordRuleMatchResult();
        result.setMatch(false);
        return result;
    }
}
