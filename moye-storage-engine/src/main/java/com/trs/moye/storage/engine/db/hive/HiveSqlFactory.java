package com.trs.moye.storage.engine.db.hive;

import com.trs.moye.base.data.model.entity.DataModel;
import com.trs.moye.base.data.model.entity.DataModelField;
import com.trs.moye.base.data.storage.setting.HiveDataStorageSettings;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;

/**
 * Hive SQL工厂 创建Hive SQL语句
 */
public class HiveSqlFactory {

    // 私有构造函数，防止实例化
    private HiveSqlFactory() {
        throw new UnsupportedOperationException("This is a utility class and cannot be instantiated");
    }

    /**
     * 构造检查表是否存在sql
     *
     * @param tableName 表名
     * @return 检查表是否存储在sql
     */
    public static String tableExistsSql(String tableName) {
        return String.format("show tables like '%s'", tableName);
    }

    /**
     * 构造建表语句
     *
     * @param table                   表结构信息
     * @param hiveDataStorageSettings hive存储设置
     * @return 建表语句
     */
    public static String ddlSql(DataModel table, HiveDataStorageSettings hiveDataStorageSettings) {
        StringBuilder createTableSql = new StringBuilder();
        createTableSql.append("CREATE TABLE `").append(table.getEnName()).append("` (\n");
        List<DataModelField> fields = table.getFields();
        // 分区字段在最后创建
        List<String> columnSql = fields.stream().filter(field -> !field.isPartition())
            .map(field -> buildColumnSql(field, hiveDataStorageSettings)).toList();
        // 添加列定义
        createTableSql.append(String.join(",\n", columnSql)).append("\n");
        createTableSql.append(")");
        // 添加表注释
        String tableComment = hiveDataStorageSettings.getTableComment();
        if (tableComment != null && !tableComment.isEmpty()) {
            createTableSql.append(tableCommentSql(tableComment));
        } else {
            createTableSql.append(tableCommentSql(table.getZhName())); // 使用 DataModel 的中文名称作为注释
        }
        String partitionDefinition = partitionSql(hiveDataStorageSettings);
        if (StringUtils.isNotBlank(partitionDefinition)) {
            createTableSql.append(partitionDefinition).append("\n");
        }
        String storedAs = hiveDataStorageSettings.getStoredAs();
        if (StringUtils.isNotBlank(storedAs)) {
            createTableSql.append("STORED AS ").append(storedAs).append("\n");
        }
        return createTableSql.toString();
    }

    @NotNull
    private static String tableCommentSql(String tableComment) {
        return String.format("COMMENT '%s'%n", tableComment);
    }


    private static String partitionSql(HiveDataStorageSettings settings) {
        if (StringUtils.isNotBlank(settings.getPartitionField())) {
            return String.format("PARTITIONED BY (`%s` string)", settings.getPartitionField());
        }
        return "";
    }

    private static String buildColumnSql(DataModelField dataModelField, HiveDataStorageSettings settings) {
        StringBuilder columnSql = new StringBuilder();
        columnSql.append("`").append(dataModelField.getEnName()).append("` ");

        // 类型
        HiveTypeConverter converter = new HiveTypeConverter();
        String dataType = converter.reconvert(dataModelField).getDataType();
        if (Objects.nonNull(settings)) {
            // 检查数据类型是否为集合或映射
            if (dataType.startsWith("ARRAY<")) {
                // 对于数组，使用集合分割方式
                dataType = dataType.replace("ARRAY<", "ARRAY<STRING" + settings.getCollectionTerminal());
            } else if (dataType.startsWith("MAP<")) {
                // 对于映射，使用 map 元素分割方式
                dataType = dataType.replace("MAP<", "MAP<STRING" + settings.getMapTerminal() + "STRING>"); // 示例处理
            }
        }
        columnSql.append(dataType);

        // 注释
        if (dataModelField.getZhName() != null && !dataModelField.getZhName().isEmpty()) {
            columnSql.append(" COMMENT '").append(dataModelField.getZhName()).append("'");
        }
        return columnSql.toString();
    }

    /**
     * 构造添加字段的sql语句
     *
     * @param tableName   表名
     * @param fieldsToAdd 字段
     * @return 添加字段的sql语句
     */
    public static String buildAddFieldSql(String tableName, List<DataModelField> fieldsToAdd) {
        if (fieldsToAdd.isEmpty()) {
            throw new IllegalArgumentException("fieldsToAdd不能为空");
        }
        String fieldSql = fieldsToAdd.stream().map(field -> buildColumnSql(field, null))
            .collect(Collectors.joining("\n"));
        return String.format("alter table %s add columns %s", tableName, fieldSql);
    }

    private static final String SELECT_COLUMNS_SQL_TEMPLATE = "DESCRIBE %s.%s";

    /**
     * 构造获取表信息sql
     *
     * @param dbName    schema库名
     * @param tableName 表名
     * @return 构造好的sql
     */
    public static String createTableInfoSql(String dbName, String tableName) {
        return String.format(SELECT_COLUMNS_SQL_TEMPLATE, dbName, tableName);
    }

    /**
     * 获取该表中该字段的最大值
     *
     * @param tableName 库名.表名
     * @param fieldName 字段名
     * @return 最大值
     */
    public static String buildMaxValueSql(String tableName, String fieldName) {
        return "select max("
            + fieldName
            + ") as `maxValue` from "
            + tableName;
    }
}