package com.trs.moye.storage.engine.seatunnel;

import com.trs.moye.storage.engine.seatunnel.enums.SeaTunnelDataType;
import javax.validation.constraints.NotNull;
import lombok.Data;
import org.springframework.validation.annotation.Validated;

/**
 * 字段描述
 *
 * <AUTHOR>
 */
@Data
@Validated
public class FieldDefinition {

    /**
     * 数据标准id
     */
    private Integer dataSourcePropertyId;

    /**
     * 中文名
     */
    @NotNull(message = "缺少中文名")
    private String zhName;

    /**
     * 英文名
     */
    @NotNull(message = "缺少英文名")
    private String enName;

    /**
     * 数据类型
     */
    @NotNull(message = "缺少数据类型")
    private SeaTunnelDataType type;

    /**
     * 是否为主键
     */
    private boolean primaryKey = false;

    /**
     * 是否允许为空值
     */
    private boolean nullable = true;

    /**
     * 标度（小数点后位数） 暂用于浮点型
     */
    private Integer scale;

    /**
     * 是否增量字段
     */
    private boolean incrementColumn = false;

    /**
     * 是否为分区字段
     */
    private boolean partitionColumn = false;

    /**
     * 分区字段信息
     */
    private PartitionInfo partitionInfo;

    /**
     * 分区信息
     */
    @Data
    public static class PartitionInfo {

        private String source = "trs_moye_input_time";

        private String format = "yyyyMMdd";
    }

    /**
     * 构建 seatunnel 字段定义
     *
     * @return seatunnel 字段定义
     */
    public String toSeatunnelPartitionQuery() {
        return String.format("FORMATDATETIME(%s,'yyyyMMdd') as %s", partitionInfo.getSource(), enName);
    }
}
