package com.trs.moye.storage.engine.pojo.entity;


import java.time.LocalDateTime;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2024/1/19
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class DataAccessTrace {

    /**
     * 数据id
     */
    private Long recordId;

    /**
     * 建模id
     */
    private Integer dataModelId;

    /**
     * 批次号
     */
    private String batchNo;

    /**
     * 开始时间
     */
    private LocalDateTime accessTime;

    /**
     * 是否异常 false-正常 true-异常
     */
    private Boolean isError;

    /**
     * 异常信息
     */
    private String errorMessage;

    /**
     * 数据信息
     */
    private String data;

    /**
     * 存储点名称
     */
    private String storageName;

}
