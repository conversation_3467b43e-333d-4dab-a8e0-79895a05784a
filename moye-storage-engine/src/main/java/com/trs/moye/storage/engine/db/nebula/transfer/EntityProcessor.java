package com.trs.moye.storage.engine.db.nebula.transfer;

import com.trs.moye.storage.engine.pojo.constant.NebulaEntity;
import com.vesoft.nebula.client.graph.SessionPool;

/**
 * 统一接口，抽象出标签和边的处理
 *
 * <AUTHOR>
 * @since 2024/12/26 19:20:36
 */
public interface EntityProcessor {

    /**
     * 是否支持
     *
     * @param entityType 实体类型
     * @return boolean
     * <AUTHOR>
     * @since 2024/12/16 10:34:14
     */
    boolean isSupport(NebulaEntity entityType);

    /**
     * 处理
     *
     * @param entityName        实体名称
     * @param sourceSessionPool 源会话池
     * @param targetSessionPool 目标会话池
     * <AUTHOR>
     * @since 2024/12/27 10:34:54
     */
    void process(String entityName, SessionPool sourceSessionPool, SessionPool targetSessionPool);
}