package com.trs.moye.storage.engine.pool;

import com.trs.moye.storage.engine.common.MoyeConnection;
import java.time.Duration;
import lombok.Data;
import org.apache.commons.pool2.impl.GenericKeyedObjectPoolConfig;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 连接池配置
 *
 * <AUTHOR>
 * @since 2025-02-12 10:38
 */
@Data
@Component
@ConfigurationProperties(prefix = "connection.pool")
public class PoolConfig {

    /**
     * 最小空闲连接数：推荐设置为0，因为有些连接只需要做测试连接，然后很长时间内将不会被使用，设置为0可以节省资源。
     */
    private int minIdleCount = 0;

    /**
     * 最大连接数
     */
    private int maxCount = 20;

    /**
     * 借用连接对象的最大等待时间（从连接池获取连接的超时时间），单位：毫秒
     */
    private long maxWaitTime = 10000;

    /**
     * 最大空闲时间，默认30分钟，单位：毫秒
     */
    private long maxIdleTime = 1800000;

    /**
     * 最大空闲时间，默认1分钟，单位：毫秒
     */
    private long idleCheckTime = 60000;

    /**
     * 转为对象池配置
     *
     * @return 对象池配置
     */
    public GenericKeyedObjectPoolConfig<MoyeConnection> toObjectPoolConfig() {
        GenericKeyedObjectPoolConfig<MoyeConnection> config = new GenericKeyedObjectPoolConfig<>();
        // 设置最小连接数（每个键的最小空闲对象数）
        config.setMinIdlePerKey(minIdleCount);
        // 设置最大连接数（每个键的最大对象数）
        config.setMaxTotalPerKey(maxCount);
        config.setMaxIdlePerKey(maxCount);
        // 设置最大等待时间（获取对象时的最大等待时间）
        config.setMaxWait(Duration.ofMillis(maxWaitTime));
        // 设置最大空闲时间（对象在池中的最大空闲时间）
        config.setMinEvictableIdleTime(Duration.ofMillis(maxIdleTime));
        // 设置空闲对象驱逐器运行间隔时间（空闲对象的检查周期）
        config.setTimeBetweenEvictionRuns(Duration.ofMillis(idleCheckTime));
        // 借出对象时是否做有效性检查
        config.setTestOnBorrow(true);
        return config;
    }
}
