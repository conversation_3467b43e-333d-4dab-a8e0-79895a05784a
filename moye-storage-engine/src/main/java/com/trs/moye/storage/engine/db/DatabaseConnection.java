package com.trs.moye.storage.engine.db;

import com.trs.moye.base.data.connection.entity.DataConnection;
import com.trs.moye.base.data.connection.enums.ConnectionType;
import com.trs.moye.base.data.explore.TableExplorationRequest;
import com.trs.moye.base.data.explore.TableExplorationResult;
import com.trs.moye.base.data.model.entity.DataModel;
import com.trs.moye.base.data.model.entity.DataModelField;
import com.trs.moye.base.data.storage.setting.DataStorageSettings;
import com.trs.moye.base.mcp.TableInfo;
import com.trs.moye.storage.engine.common.MoyeConnection;
import com.trs.moye.storage.engine.pojo.dto.PageParams;
import com.trs.moye.storage.engine.pojo.request.search.CodeSearchParams;
import com.trs.moye.storage.engine.pojo.request.search.Condition;
import com.trs.moye.storage.engine.pojo.request.search.ConditionSearchParams;
import com.trs.moye.storage.engine.pojo.response.PageResult;
import com.trs.moye.storage.engine.pojo.response.StorageEngineResponse;
import com.trs.moye.storage.engine.pojo.response.alltable.TableResponse;
import java.io.IOException;
import java.util.List;
import java.util.Map;
import javax.validation.constraints.NotBlank;
import lombok.extern.slf4j.Slf4j;

/**
 * 数据库连接接口类，用于执行数据库相关操作
 */
@Slf4j
public abstract class DatabaseConnection implements MoyeConnection {

    /**
     * 创建表
     *
     * @param table    表信息
     * @param settings 建表参数
     * @return 是否创建成功
     */
    public abstract StorageEngineResponse createTable(DataModel table, DataStorageSettings settings);

    /**
     * 获取 连接下的所有 库,表,字段 信息
     *
     * @return 数据库元数据信息
     */
    public DatabaseMetadata getMetadata() {
        throw new UnsupportedOperationException(String.format("%s 不支持!", this.getClass().getName()));
    }

    /**
     * 获取表信息
     *
     * @param tableName 表名
     * @return 表信息
     */
    public abstract DataModel getTableInfo(String tableName);

    /**
     * 获取表字段定义
     *
     * @param tableName 表名
     * @return 基本类型定义列表
     */
    public abstract List<BasicTypeDefine> getTableFieldDefines(String tableName);

    /**
     * 查询表是否存在
     *
     * @param tableName 表名
     * @return 表是否存在
     */
    public boolean tableExist(String tableName) {
        List<TableResponse> tables = getAllTables();
        log.info("查询表是否存在: {}, 当前连接下的所有表: {}", tableName, tables);
        return tables.stream()
            .map(TableResponse::getTableName)
            .anyMatch(tableName::equals);
    }

    /**
     * 删除表
     *
     * @param tableName 表名
     * @return 是否删除成功
     */
    public abstract boolean dropTable(String tableName);

    /**
     * 获取所有表
     *
     * @return 所有表信息
     */
    public abstract List<TableResponse> getAllTables();

    /**
     * 添加字段
     *
     * @param tableName 表名
     * @param fields    字段信息
     * @return 是否添加成功
     */
    public abstract StorageEngineResponse addFields(String tableName, List<DataModelField> fields);


    /**
     * 执行列表查询 并返回结果
     *
     * @param sql 查询语句
     * @return 结果
     */
    public List<Map<String, Object>> executeQuery(@NotBlank(message = "检索语句不能为空") String sql)
        throws IOException {
        throw new UnsupportedOperationException("不支持列表查询！");
    }

    /**
     * 执行语句查询 表级，带分页
     *
     * @param tableName      表名称
     * @param pageParams     页面参数
     * @param sql            SQL
     * @param dataConnection 连接
     * @return {@link List }<{@link Map }<{@link String }, {@link Object }>>
     * <AUTHOR>
     * @since 2024/10/17 15:14:46
     */
    public List<Map<String, Object>> executeQuery(@NotBlank String tableName, PageParams pageParams, String sql,
        DataConnection dataConnection) {
        throw new UnsupportedOperationException("不支持sql查询！");
    }

    /**
     * 按参数执行查询
     *
     * @param tableName      表名称
     * @param searchParams   搜索参数
     * @param dataConnection 连接信息
     * @return {@link List }<{@link Map }<{@link String }, {@link Object }>>
     * <AUTHOR>
     * @since 2024/10/18 14:08:54
     */
    public List<Map<String, Object>> executeQueryByParams(String tableName, ConditionSearchParams searchParams,
        DataConnection dataConnection) {
        throw new UnsupportedOperationException("不支持条件查询！");
    }

    /**
     * 对sql 执行条件查询
     *
     * @param queryStatement 原sql
     * @param searchParams   条件查询的条件信息
     * @param connectionType 连接类型
     * @return 结果
     */
    public PageResult<Map<String, Object>> executeQueryByParamsAndSql(String queryStatement,
        ConditionSearchParams searchParams,
        ConnectionType connectionType) {
        throw new UnsupportedOperationException("不支持视图查询！");
    }

    /**
     * 查询总数
     *
     * @param conditions 查询参数
     * @param tablePath  表路径
     * @return 总数
     */
    public abstract long count(List<Condition> conditions, String tablePath);


    /**
     * 查询总数
     *
     * @param conditions     查询参数
     * @param tablePath      表路径
     * @param dataConnection 连接信息
     * @return 总数
     */
    public abstract long count(List<Condition> conditions, String tablePath, DataConnection dataConnection);

    /**
     * 计数
     *
     * @param code      代码
     * @param tablePath 表路径
     * @return long
     */
    public long count(String code, String tablePath) {
        return 0;
    }

    /**
     * 通过代码 查询总数
     *
     * @param code           代码
     * @param tablePath      表路径
     * @param dataConnection 数据连接
     * @return 总数
     */
    public long count(String code, String tablePath, DataConnection dataConnection) {
        return 0;
    }

    /**
     * 获取最大值,用于更新增量信息
     *
     * @param tableName 表名
     * @param filedName 字段名
     * @return 最大值
     */
    public abstract Object getMaxValue(String tableName, String filedName);

    /**
     * 构造默认的分页参数
     *
     * @param request 代码模式查询参数
     * @return 分页完成的sql
     */
    public abstract String buildDefaultPageParam(CodeSearchParams request);

    /**
     * 获取非普通查询的数量
     *
     * @param searchParams   查询参数
     * @param tableName      表名
     * @param dataConnection 连接信息
     * @return 数量
     */
    public abstract long countNotNormalQuery(ConditionSearchParams searchParams, String tableName,
        DataConnection dataConnection);

    /**
     * 获取类型转换器
     *
     * @return 类型转换器
     */
    public abstract TypeConverter<BasicTypeDefine> getTypeConverter();

    /**
     * 获取表的DDL语句
     *
     * @param tableName 表名
     * @return DDL语句
     */
    public String getDdl(String tableName) {
        throw new UnsupportedOperationException("不支持DDL语句查询！");
    }

    /**
     * 判断是否为查询sql，避免修改库表及数据的操作
     *
     * @param sql sql语句
     * @return 是否查询sql
     */
    public boolean isSelectSql(String sql) {
        return true;
    }

    /**
     * 加载数据到存储引擎
     *
     * @param tableInfo 表信息，包括表中英文名、字段信息、连接信息、minioPath等
     * @return 是否保存成功
     */
    public boolean loadDataToStorage(TableInfo tableInfo) {
        throw new UnsupportedOperationException("不支持加载文件数据到数据库！");
    }

    /**
     * 数据探查
     *
     * @param request 表信息
     * @return 数据探查结果
     */
    public TableExplorationResult dataExplore(TableExplorationRequest request) {
        throw new UnsupportedOperationException("不支持数据探查！");
    }
}
