/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.trs.moye.storage.engine.db.oracle;

import static com.trs.moye.storage.engine.db.postgresql.PostgresTypeConverter.PG_NUMERIC;

import com.trs.moye.base.common.entity.field.DecimalAdvanceConfig;
import com.trs.moye.base.common.entity.field.FieldAdvanceConfig;
import com.trs.moye.base.common.entity.field.StringAdvanceConfig;
import com.trs.moye.base.common.enums.FieldType;
import com.trs.moye.base.data.model.entity.DataModelField;
import com.trs.moye.storage.engine.db.BasicTypeDefine;
import com.trs.moye.storage.engine.db.BasicTypeDefine.BasicTypeDefineBuilder;
import com.trs.moye.storage.engine.db.TypeConverter;
import com.trs.moye.storage.engine.exception.DataStorageEngineException;
import com.trs.moye.storage.engine.utils.BizUtils;
import java.util.Objects;
import lombok.extern.slf4j.Slf4j;

/**
 * Oracle data type converter
 *
 * <AUTHOR>
 */
@Slf4j
public class OracleTypeConverter implements TypeConverter<BasicTypeDefine> {

    // ============================data types=====================
    // -------------------------number----------------------------
    public static final String ORACLE_BINARY_DOUBLE = "BINARY_DOUBLE";
    public static final String ORACLE_BINARY_FLOAT = "BINARY_FLOAT";
    public static final String ORACLE_NUMBER = "NUMBER";
    public static final String ORACLE_FLOAT = "FLOAT";
    public static final String ORACLE_REAL = "REAL";
    public static final String ORACLE_INTEGER = "INTEGER";

    // -------------------------string----------------------------
    public static final String ORACLE_CHAR = "CHAR";
    public static final String ORACLE_NCHAR = "NCHAR";
    public static final String ORACLE_VARCHAR = "VARCHAR";
    public static final String ORACLE_VARCHAR2 = "VARCHAR2";
    public static final String ORACLE_NVARCHAR2 = "NVARCHAR2";
    public static final String ORACLE_LONG = "LONG";
    public static final String ORACLE_ROWID = "ROWID";
    public static final String ORACLE_CLOB = "CLOB";
    public static final String ORACLE_NCLOB = "NCLOB";
    public static final String ORACLE_XML = "XMLTYPE";
    public static final String ORACLE_SYS_XML = "SYS.XMLTYPE";

    // ------------------------------time-------------------------
    public static final String ORACLE_DATE = "DATE";
    public static final String ORACLE_TIMESTAMP = "TIMESTAMP";
    public static final String ORACLE_TIMESTAMP_WITH_TIME_ZONE =
        ORACLE_TIMESTAMP + " WITH TIME ZONE";
    public static final String ORACLE_TIMESTAMP_WITH_LOCAL_TIME_ZONE =
        ORACLE_TIMESTAMP + " WITH LOCAL TIME ZONE";

    // ------------------------------blob-------------------------
    public static final String ORACLE_BLOB = "BLOB";
    public static final String ORACLE_RAW = "RAW";
    public static final String ORACLE_LONG_RAW = "LONG RAW";

    public static final int MAX_PRECISION = 38;
    public static final int DEFAULT_PRECISION = MAX_PRECISION;
    public static final int MAX_SCALE = 127;
    public static final int DEFAULT_SCALE = 18;
    public static final int TIMESTAMP_DEFAULT_SCALE = 6;
    public static final int MAX_TIMESTAMP_SCALE = 9;
    public static final long MAX_RAW_LENGTH = 2000;
    public static final long MAX_ROWID_LENGTH = 18;
    public static final long MAX_CHAR_LENGTH = 2000;
    public static final long MAX_VARCHAR_LENGTH = 4000;

    public static final long BYTES_2GB = (long) Math.pow(2, 31);
    public static final long BYTES_4GB = (long) Math.pow(2, 32);
    public static final String FORMAT = "%s(%s)";

    @Override
    public DataModelField convert(BasicTypeDefine typeDefine) {
        DataModelField.DataModelFieldBuilder<?, ?> builder =
            DataModelField.builder()
                .enName(typeDefine.getName())
                .zhName(typeDefine.getComment())
                .isNullable(typeDefine.isNullable())
                .description(typeDefine.getComment());

        String oracleType = typeDefine.getDataType().toUpperCase();
        switch (oracleType) {
            case ORACLE_INTEGER:
                builder.type(FieldType.INT);
                break;
            case ORACLE_NUMBER:
                buildNumber(typeDefine, builder, new DecimalAdvanceConfig());
                break;
            case ORACLE_FLOAT, ORACLE_BINARY_FLOAT, ORACLE_REAL:
                builder.type(FieldType.FLOAT);
                break;
            case ORACLE_BINARY_DOUBLE:
                builder.type(FieldType.DOUBLE);
                break;
            case ORACLE_XML, ORACLE_SYS_XML, ORACLE_CHAR, ORACLE_VARCHAR, ORACLE_VARCHAR2:
                builder.type(FieldType.STRING);
                StringAdvanceConfig stringConfig = new StringAdvanceConfig();
                stringConfig.setMaxLength(typeDefine.getLength());
                builder.advanceConfig(stringConfig);
                break;
            case ORACLE_NCHAR, ORACLE_NVARCHAR2:
                builder.type(FieldType.STRING);
                StringAdvanceConfig charConfig = new StringAdvanceConfig();
                charConfig.setMaxLength(typeDefine.getLength() * 2);
                builder.advanceConfig(charConfig);
                break;
            case ORACLE_ROWID:
                builder.type(FieldType.STRING);
                StringAdvanceConfig rowConfig = new StringAdvanceConfig();
                rowConfig.setMaxLength(MAX_ROWID_LENGTH);
                builder.advanceConfig(rowConfig);
                break;
            case ORACLE_LONG:
                builder.type(FieldType.STRING);
                StringAdvanceConfig longConfig = new StringAdvanceConfig();
                // The maximum length of the column is 2GB-1
                longConfig.setMaxLength(BYTES_2GB - 1);
                builder.advanceConfig(longConfig);
                break;
            case ORACLE_CLOB, ORACLE_NCLOB:
                builder.type(FieldType.STRING);
                StringAdvanceConfig clobConfig = new StringAdvanceConfig();
                // The maximum length of the column is 2GB-1
                clobConfig.setMaxLength(BYTES_4GB - 1);
                builder.advanceConfig(clobConfig);
                break;
            case ORACLE_DATE:
                builder.type(FieldType.DATE);
                break;
            case ORACLE_TIMESTAMP:
                builder.type(FieldType.DATETIME);
                break;
            case ORACLE_BLOB:
                builder.type(FieldType.STRING);
                break;
            default:
                throw new DataStorageEngineException(
                    String.format("不支持的数据类型: %s", oracleType));
        }
        return BizUtils.buildAndSupplementAdvanceConfig(builder);
    }

    private static void buildNumber(BasicTypeDefine typeDefine, DataModelField.DataModelFieldBuilder<?, ?> builder,
        DecimalAdvanceConfig config) {
        Integer precision = typeDefine.getPrecision();
        if (precision == null || precision == 0 || precision > DEFAULT_PRECISION) {
            precision = DEFAULT_PRECISION;
        }
        Integer scale = typeDefine.getScale();
        if (scale == null) {
            scale = 127;
        }
        if (scale <= 0) {
            int newPrecision = precision - scale;
            if (newPrecision == 1) {
                builder.type(FieldType.BOOLEAN);
            } else if (newPrecision <= 9) {
                builder.type(FieldType.INT);
            } else if (newPrecision <= 18) {
                builder.type(FieldType.LONG);
            } else if (newPrecision < 38) {
                builder.type(FieldType.DECIMAL);
                config.setAccuracy(newPrecision);
            } else {
                builder.type(FieldType.DECIMAL);
                config.setAccuracy(DEFAULT_PRECISION);
            }
        } else if (scale <= DEFAULT_SCALE) {
            builder.type(FieldType.DECIMAL);
            config.setAccuracy(precision);
            config.setScale(scale);
        } else {
            builder.type(FieldType.DECIMAL);
            config.setAccuracy(precision);
            config.setScale(DEFAULT_SCALE);
        }
        builder.advanceConfig(config);
    }

    @Override
    public BasicTypeDefine reconvert(DataModelField column) {
        BasicTypeDefine.BasicTypeDefineBuilder builder =
            BasicTypeDefine.builder()
                .name(column.getEnName())
                .nullable(column.isNullable())
                .comment(column.getZhName());
        FieldAdvanceConfig config = column.getAdvanceConfig();
        switch (column.getType()) {
            case BOOLEAN:
                builder.columnType(String.format(FORMAT, ORACLE_NUMBER, 1));
                builder.dataType(ORACLE_NUMBER);
                builder.length(1L);
                break;
            case SHORT, INT, LONG:
                builder.columnType(ORACLE_INTEGER);
                builder.dataType(ORACLE_INTEGER);
                break;
            case FLOAT:
                builder.columnType(ORACLE_BINARY_FLOAT);
                builder.dataType(ORACLE_BINARY_FLOAT);
                break;
            case DOUBLE:
                builder.columnType(ORACLE_BINARY_DOUBLE);
                builder.dataType(ORACLE_BINARY_DOUBLE);
                break;
            case DECIMAL:
                buildDecimal(config, builder);
                break;
            case STRING, TEXT:
                builder.columnType(String.format(FORMAT, ORACLE_VARCHAR2, MAX_VARCHAR_LENGTH));
                builder.dataType(String.format(FORMAT, ORACLE_VARCHAR2, MAX_VARCHAR_LENGTH));
                buildString(config, builder);
                break;
            case DATE, DATETIME:
                builder.columnType(ORACLE_DATE);
                builder.dataType(ORACLE_DATE);
                break;
            default:
                throw new DataStorageEngineException(
                    String.format("不支持的数据类型: %s", column.getType()));
        }
        return builder.build();
    }

    private static void buildString(FieldAdvanceConfig config, BasicTypeDefineBuilder builder) {
        if (config == null) {
            return;
        }
        if (Objects.isNull(config.getType())) {
            return;
        }
        if (!(config instanceof StringAdvanceConfig stringConfig)) {
            return;
        }
        if (stringConfig.getMaxLength() == null || stringConfig.getMaxLength() <= 0) {
            builder.columnType(
                String.format(FORMAT, ORACLE_VARCHAR2, MAX_VARCHAR_LENGTH));
            builder.dataType(ORACLE_VARCHAR2);
        } else if (stringConfig.getMaxLength() <= MAX_VARCHAR_LENGTH) {
            builder.columnType(
                String.format(FORMAT, ORACLE_VARCHAR2, stringConfig.getMaxLength()));
            builder.dataType(ORACLE_VARCHAR2);
        } else {
            builder.columnType(ORACLE_CLOB);
            builder.dataType(ORACLE_CLOB);
        }
    }

    private static void buildDecimal(FieldAdvanceConfig config, BasicTypeDefineBuilder builder) {
        if (Objects.isNull(config)) {
            return;
        }
        if (!(config instanceof DecimalAdvanceConfig decimalConfig)) {
            return;
        }
        int precision = decimalConfig.getAccuracy();
        int scale = decimalConfig.getScale();
        if (precision <= 0) {
            precision = DEFAULT_PRECISION;
            scale = DEFAULT_SCALE;
        } else if (precision > MAX_PRECISION) {
            scale = Math.max(0, scale - (precision - MAX_PRECISION));
            precision = MAX_PRECISION;
        }
        if (scale < 0) {
            scale = 0;
        } else if (scale > MAX_SCALE) {
            scale = MAX_SCALE;
        }
        builder.columnType(String.format("%s(%s,%s)", PG_NUMERIC, precision, scale));
        builder.precision(precision);
        builder.scale(scale);
        builder.dataType(ORACLE_NUMBER);
    }
}
