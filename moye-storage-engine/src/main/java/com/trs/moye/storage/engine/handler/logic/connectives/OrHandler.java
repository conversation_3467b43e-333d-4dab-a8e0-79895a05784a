package com.trs.moye.storage.engine.handler.logic.connectives;

import com.trs.moye.storage.engine.pojo.constant.EnumCondOperator;
import com.trs.moye.storage.engine.pojo.entity.EsConditionState;
import java.util.Deque;

/**
 * 处理OR
 *
 * <AUTHOR>
 * @since 2024/10/14
 */
public class OrHandler implements LogicConnectivesHandler {

    @Override
    public void handleNormalJdbc(StringBuilder whereClause) {
        whereClause.append(EnumCondOperator.OR.getHbName()); // 添加 OR
    }

    @Override
    public void handle(Deque<EsConditionState> esConditionStates) {
        EsConditionState currentState = esConditionStates.peek();
        assert currentState != null;
        currentState.setOrActive(true);
        currentState.setAndActive(currentState.isAndActive());
        currentState.setNotActive(currentState.isNotActive());
    }

    @Override
    public void handleHyBase(StringBuilder whereClause) {
        handleNormalJdbc(whereClause);
    }


}
