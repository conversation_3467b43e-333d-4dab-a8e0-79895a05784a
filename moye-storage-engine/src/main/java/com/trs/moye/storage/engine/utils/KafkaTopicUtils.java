package com.trs.moye.storage.engine.utils;

import com.trs.moye.base.common.entity.mq.MqConsumeInfoResponse;
import com.trs.moye.base.common.exception.BizException;
import com.trs.moye.base.common.utils.DateTimeUtils;
import com.trs.moye.storage.engine.exception.DataStorageEngineException;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Set;
import java.util.concurrent.ExecutionException;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.admin.AdminClient;
import org.apache.kafka.clients.admin.DescribeTopicsResult;
import org.apache.kafka.clients.admin.ListOffsetsResult.ListOffsetsResultInfo;
import org.apache.kafka.clients.admin.ListTopicsOptions;
import org.apache.kafka.clients.admin.OffsetSpec;
import org.apache.kafka.clients.admin.TopicDescription;
import org.apache.kafka.clients.consumer.OffsetAndMetadata;
import org.apache.kafka.common.TopicPartition;
import org.apache.kafka.common.TopicPartitionInfo;
import org.apache.kafka.common.errors.UnknownTopicOrPartitionException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 拼接topic和group
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025/2/26 17:31
 **/
@Slf4j
public class KafkaTopicUtils {

    private KafkaTopicUtils() {
    }

    private static final Logger LOGGER = LoggerFactory.getLogger(KafkaTopicUtils.class);

    /**
     * 拼接默认的topic
     *
     * @param storageId   存储点ID
     * @param dataModelId 数据建模ID
     * @return {@link String}
     * <AUTHOR>
     * @since 2025/2/26 17:33
     */
    public static String buildTopicName(Integer storageId, Integer dataModelId) {
        return "moye_storage_" + dataModelId + "_" + storageId;
    }

    /**
     * 拼接group
     *
     * @param topicName topic
     * @return {@link String}
     * <AUTHOR>
     * @since 2025/2/26 17:35
     */
    public static String buildGroupName(String topicName) {
        return "moye_dwd_seatunnel_" + topicName;
    }

    /**
     * 检查单个Topic是否存在
     *
     * @param adminClient Kafka管理客户端
     * @param topic       需要检查的Topic名称
     * @return 当Topic存在时返回true，否则返回false
     */
    public static boolean checkTopicExists(AdminClient adminClient, String topic) {
        return checkTopicExists(adminClient, Collections.singleton(topic));
    }

    /**
     * 检查多个Topic是否全部存在
     *
     * @param adminClient Kafka管理客户端
     * @param topics      需要检查的Topic名称集合
     * @return 当所有Topic都存在时返回true，否则返回false
     */
    public static boolean checkTopicExists(AdminClient adminClient, Set<String> topics) {
        try {
            // 获取集群中所有Topic的名称集合
            Set<String> remoteTopics = adminClient.listTopics(new ListTopicsOptions().listInternal(false)).names()
                .get();
            // 判断Topic是否在集合中
            return remoteTopics.containsAll(topics);
        } catch (ExecutionException e) {
            LOGGER.warn("Error while checking if topic exists: {}", e.getMessage());
            return false;
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            throw new BizException(e.getMessage());
        }
    }

    /**
     * 获取Topic的最新偏移量信息
     *
     * @param adminClient Kafka管理客户端
     * @param topicDesc   Topic描述信息
     * @return topic分区偏移量信息
     */
    public static Map<TopicPartition, ListOffsetsResultInfo> getTopicLatestOffsetMap(AdminClient adminClient,
        TopicDescription topicDesc) {
        return getTopicOffsetMap(adminClient, topicDesc, OffsetSpec.latest());
    }

    /**
     * 获取Topic的最老且有数据的偏移量信息
     *
     * @param adminClient Kafka管理客户端
     * @param topicDesc   Topic描述信息
     * @return topic分区偏移量信息
     */
    public static Map<TopicPartition, ListOffsetsResultInfo> getTopicEarliestOffsetMap(AdminClient adminClient,
        TopicDescription topicDesc) {
        return getTopicOffsetMap(adminClient, topicDesc, OffsetSpec.earliest());
    }

    /**
     * 根据时间戳获取数据偏移量
     *
     * @param adminClient Kafka管理客户端
     * @param topicDesc   Topic描述信息
     * @param timestamp   时间戳
     * @return topic分区偏移量信息
     */
    public static Map<TopicPartition, ListOffsetsResultInfo> getTopicTimesTampOffsetMap(AdminClient adminClient,
        TopicDescription topicDesc, long timestamp) {
        return getTopicOffsetMap(adminClient, topicDesc, OffsetSpec.forTimestamp(timestamp));
    }

    /**
     * 获取时间戳偏移量
     *
     * @param admin     Kafka管理客户端
     * @param topic     topicName
     * @param timestamp 时间戳
     * @return 偏移量
     */
    public static long getTopicTimestampOffset(AdminClient admin, String topic, long timestamp) {
        TopicDescription topicDescription = getTopicDescription(admin, topic);
        if (topicDescription == null) {
            return 0L;
        }
        return getTopicTimestampOffset(admin, topicDescription, timestamp);
    }

    /**
     * 获取时间戳偏移量
     *
     * @param admin            Kafka管理客户端
     * @param topicDescription topic描述信息
     * @param timestamp        时间戳
     * @return 偏移量
     */
    public static long getTopicTimestampOffset(AdminClient admin, TopicDescription topicDescription, long timestamp) {
        Map<TopicPartition, ListOffsetsResultInfo> offsetMap = getTopicTimesTampOffsetMap(
            admin, topicDescription, timestamp);
        long total = 0;
        Map<TopicPartition, ListOffsetsResultInfo> latestOffsetMap = null;
        for (Entry<TopicPartition, ListOffsetsResultInfo> entry : offsetMap.entrySet()) {
            if (entry.getValue().offset() == -1L) {
                if (latestOffsetMap == null) {
                    latestOffsetMap = getTopicLatestOffsetMap(admin,
                        topicDescription);
                }
                long latestOffset = latestOffsetMap.get(entry.getKey()).offset();
                total += latestOffset;
            } else {
                total += entry.getValue().offset();
            }
        }
        return total;
    }

    /**
     * 获取Topic的偏移量信息
     *
     * @param adminClient Kafka管理客户端
     * @param topicDesc   Topic描述信息
     * @param offsetSpec  偏移量规格
     * @return topic分区偏移量信息
     */
    public static Map<TopicPartition, ListOffsetsResultInfo> getTopicOffsetMap(AdminClient adminClient,
        TopicDescription topicDesc, OffsetSpec offsetSpec) {
        if (topicDesc == null) {
            return new HashMap<>();
        }
        List<TopicPartitionInfo> partitions = topicDesc.partitions();
        Map<TopicPartition, OffsetSpec> offsetSpecMap = new HashMap<>();
        for (TopicPartitionInfo partition : partitions) {
            TopicPartition tp = new TopicPartition(topicDesc.name(), partition.partition());
            offsetSpecMap.put(tp, offsetSpec); // 指定查询最新偏移量
        }
        Map<TopicPartition, ListOffsetsResultInfo> offsets;
        try {
            offsets = adminClient.listOffsets(offsetSpecMap).all().get();
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            throw new BizException(e, "获取【%s】topic的消息总量发生中断异常", topicDesc.name());
        } catch (Exception e) {
            throw new BizException(e, "获取【%s】topic的消息总量发生异常", topicDesc.name());
        }
        return offsets;
    }

    /**
     * 获取topic分区信息
     *
     * @param adminClient kafka管理客户端
     * @param topic       topic名称
     * @return 主题分区列表
     */
    public static List<TopicPartition> getTopicPartitions(AdminClient adminClient, String topic) {
        try {
            DescribeTopicsResult deleteTopicsResult = adminClient.describeTopics(Collections.singleton(topic));
            TopicDescription topicDescription = deleteTopicsResult.allTopicNames().get().get(topic);
            if (topicDescription == null) {
                throw new DataStorageEngineException(topic + " topic 不存在");
            }
            return topicDescription.partitions().stream().map(p -> new TopicPartition(topic, p.partition())).toList();
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            throw new DataStorageEngineException("获取topic分区信息发生中断异常，topic: " + topic, e);
        } catch (DataStorageEngineException e) {
            throw e;
        } catch (Exception e) {
            throw new DataStorageEngineException("获取topic分区信息发生异常，topic: " + topic, e);
        }
    }

    /**
     * 获取topic描述信息
     *
     * @param adminClient kafka管理客户端
     * @param topic       topic名称
     * @return topic描述信息
     */
    public static TopicDescription getTopicDescription(AdminClient adminClient, String topic) {
        try {
            return adminClient.describeTopics(
                Collections.singleton(topic)).allTopicNames().get().get(topic);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            throw new BizException(e, "获取【%s】主题信息发生中断异常", topic);
        } catch (ExecutionException e) {
            // 捕获ExecutionException，检查是否是UnknownTopicOrPartitionException（topic不存在）
            if (e.getCause() instanceof UnknownTopicOrPartitionException ee) {
                log.warn("Topic {} 不存在，错误信息：{}", topic, ee.getMessage());
                return null;  // 主题不存在时返回null
            }
            throw new BizException(e, "获取【%s】主题信息发生异常", topic);
        } catch (Exception e) {
            throw new BizException(e, "获取【%s】主题信息发生异常", topic);
        }
    }

    /**
     * 获取topic消费信息
     *
     * @param adminClient kafka管理客户端
     * @param topicDesc   topic描述信息
     * @param group       消费者组名称
     * @return 消费信息
     */
    public static MqConsumeInfoResponse getTopicConsumeInfo(AdminClient adminClient, TopicDescription topicDesc,
        String group) {
        long totalMessages = getTopicTotalOffset(adminClient, topicDesc);
        long consumedMessages = getGroupConsumeCount(adminClient, topicDesc, group);
        MqConsumeInfoResponse consumeInfo = new MqConsumeInfoResponse();
        consumeInfo.setEndOffset(totalMessages);
        consumeInfo.setCurrentOffset(consumedMessages);
        consumeInfo.setLag(totalMessages - consumedMessages);
        return consumeInfo;
    }

    /**
     * 获取topic总偏移量
     *
     * @param adminClient kafka管理客户端
     * @param topicDesc   topic描述信息
     * @return 总偏移量
     */
    public static long getTopicTotalOffset(AdminClient adminClient, TopicDescription topicDesc) {
        Map<TopicPartition, ListOffsetsResultInfo> offsets = KafkaTopicUtils.getTopicLatestOffsetMap(adminClient,
            topicDesc);
        long totalMessages = 0;
        for (ListOffsetsResultInfo info : offsets.values()) {
            totalMessages += info.offset();
        }
        return totalMessages;
    }

    private static long getGroupConsumeCount(AdminClient adminClient, TopicDescription topicDesc, String group) {
        if (topicDesc == null) {
            return 0L;
        }
        Map<TopicPartition, OffsetAndMetadata> consumerOffsets;
        try {
            consumerOffsets = adminClient.listConsumerGroupOffsets(group).partitionsToOffsetAndMetadata().get();
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            throw new BizException(e, "获取【%s】topic的【%s】消费者组消费总量发生中断异常", topicDesc.name(), group);
        } catch (Exception e) {
            throw new BizException(e, "获取【%s】topic的【%s】消费者组消费总量发生异常", topicDesc.name(), group);
        }
        long consumedMessages = 0;
        for (Entry<TopicPartition, OffsetAndMetadata> entry : consumerOffsets.entrySet()) {
            TopicPartition tp = entry.getKey();
            if (tp.topic().equals(topicDesc.name())) {
                consumedMessages += entry.getValue().offset();
            }
        }
        return consumedMessages;
    }

    /**
     * 获取当天偏移量
     *
     * @param admin kafka管理客户端
     * @param topic topic名称
     * @return 当天偏移量
     */
    public static long getTopicTodayOffset(AdminClient admin, String topic) {
        TopicDescription topicDescription = getTopicDescription(admin, topic);
        if (topicDescription == null) {
            return 0L;
        }
        LocalDateTime startOfDay = LocalDate.now().atStartOfDay();
        long timestamp = DateTimeUtils.toTimestamp(startOfDay);
        Map<TopicPartition, ListOffsetsResultInfo> offsetMap = getTopicTimesTampOffsetMap(
            admin, topicDescription, timestamp);
        Map<TopicPartition, ListOffsetsResultInfo> latestOffsetMap = null;
        long total = 0;
        for (Entry<TopicPartition, ListOffsetsResultInfo> entry : offsetMap.entrySet()) {
            if (entry.getValue().offset() == -1L) {
                if (latestOffsetMap == null) {
                    latestOffsetMap = getTopicLatestOffsetMap(admin,
                        topicDescription);
                }
                long latestOffset = latestOffsetMap.get(entry.getKey()).offset();
                total += latestOffset;
            } else {
                total += entry.getValue().offset();
            }
        }
        return total;
    }
}
