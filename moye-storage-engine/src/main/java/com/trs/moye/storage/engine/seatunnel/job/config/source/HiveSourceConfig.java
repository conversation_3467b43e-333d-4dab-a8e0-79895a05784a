package com.trs.moye.storage.engine.seatunnel.job.config.source;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.SuperBuilder;

/**
 * Hive source config
 *
 * <p>Hive source config is used to specify the configuration of the Hive source</p>
 *
 * <AUTHOR>

 */
@EqualsAndHashCode(callSuper = true)
@Data
@SuperBuilder
public class HiveSourceConfig extends SeatunnelSourceConfig {

    /**
     * 表名
     */
    @JsonProperty("table_name")
    private String tableName;
    /**
     * 元数据存储地址
     */
    @JsonProperty("metastore_uri")
    private String metastoreUri;
    /**
     * HDFS配置文件路径
     */
    @JsonProperty("hdfs_site_path")
    private String hdfsSitePath;
    /**
     * Hive配置文件路径
     */
    @JsonProperty("hive_site_path")
    private String hiveSitePath;
    /**
     * 是否使用Kerberos认证
     */
    @JsonProperty("use_kerberos")
    @Builder.Default
    private boolean useKerberos = false;
    /**
     * Kerberos principal
     */
    @JsonProperty("kerberos_principal")
    private String kerberosPrincipal;
    /**
     * Kerberos keytab路径
     */
    @JsonProperty("kerberos_keytab_path")
    private String kerberosKeytabPath;
    /**
     * Kerberos配置文件路径
     */
    @JsonProperty("krb5_path")
    private String krb5Path;
    /**
     * 读取的分区
     */
    @JsonProperty("read_partitions")
    private String[] readPartitions;
    /**
     * 读取的列
     */
    @JsonProperty("read_columns")
    private String readColumns;
    /**
     * Hive
     */
    @JsonProperty("plugin_name")
    @Builder.Default
    private String pluginName = "Hive";
}
