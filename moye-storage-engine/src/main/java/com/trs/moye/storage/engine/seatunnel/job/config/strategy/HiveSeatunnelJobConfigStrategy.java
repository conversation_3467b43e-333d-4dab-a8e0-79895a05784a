package com.trs.moye.storage.engine.seatunnel.job.config.strategy;

import com.trs.moye.base.data.connection.entity.DataConnection;
import com.trs.moye.base.data.connection.entity.KerberosCertificate;
import com.trs.moye.base.data.connection.entity.params.HiveConnectionParams;
import com.trs.moye.base.data.model.entity.DataModelField;
import com.trs.moye.base.data.model.entity.DataSourceConfig;
import com.trs.moye.base.data.storage.DataStorage;
import com.trs.moye.base.data.storage.IncrementInfo;
import com.trs.moye.storage.engine.pojo.request.task.TaskStartParam;
import com.trs.moye.storage.engine.seatunnel.job.config.sink.HiveSinkConfig;
import com.trs.moye.storage.engine.seatunnel.job.config.sink.HiveSinkConfig.HiveSinkConfigBuilder;
import com.trs.moye.storage.engine.seatunnel.job.config.source.HiveSourceConfig;
import com.trs.moye.storage.engine.seatunnel.job.config.source.HiveSourceConfig.HiveSourceConfigBuilder;
import java.util.List;
import java.util.Objects;

/**
 * Hive 数据源配置
 */
public class HiveSeatunnelJobConfigStrategy implements SeatunnelJobConfigStrategy {

    @Override
    public HiveSourceConfig createSourceConfig(DataSourceConfig dataSourceConfig, String tableName,
        List<DataModelField> fields, IncrementInfo incrementInfo, TaskStartParam taskStartParam) {
        HiveConnectionParams connectionParams = (HiveConnectionParams) dataSourceConfig.getConnection()
            .getConnectionParams();
        HiveSourceConfigBuilder<?, ?> builder = HiveSourceConfig.builder()
            .tableName(String.join(".", connectionParams.getDatabase(), tableName))
            .metastoreUri(connectionParams.getMetastoreUri())
            .hiveSitePath(connectionParams.getHiveSitePath())
            .hdfsSitePath(connectionParams.getHdfsSitePath());
        KerberosCertificate kerberosCertificate = dataSourceConfig.getConnection().getKerberosCertificate();
        boolean useKerberos = Objects.nonNull(kerberosCertificate);
        if (useKerberos) {
            builder.useKerberos(true).kerberosPrincipal(kerberosCertificate.getPrincipal())
                .kerberosKeytabPath(kerberosCertificate.getKeytabPath());
        }
        return builder.build();
    }

    @Override
    public HiveSinkConfig createSinkConfig(DataConnection connection, DataStorage storage,
        IncrementInfo incrementInfo, List<DataModelField> fields) {
        HiveConnectionParams connectionParams = (HiveConnectionParams) connection.getConnectionParams();
        HiveSinkConfigBuilder<?, ?> builder = HiveSinkConfig.builder()
            .metastoreUri(connectionParams.getMetastoreUri())
            .hiveSitePath(connectionParams.getHiveSitePath()).hdfsSitePath(connectionParams.getHdfsSitePath())
            .tableName(String.join(".", connectionParams.getDatabase(), storage.getEnName()))
            .storageName(storage.getConnection().getName());
        KerberosCertificate kerberosCertificate = connection.getKerberosCertificate();
        boolean useKerberos = Objects.nonNull(kerberosCertificate);
        if (useKerberos) {
            builder.useKerberos(true).kerberosPrincipal(kerberosCertificate.getPrincipal())
                .kerberosKeytabPath(kerberosCertificate.getKeytabPath());
        }
        return builder.build();
    }
}
