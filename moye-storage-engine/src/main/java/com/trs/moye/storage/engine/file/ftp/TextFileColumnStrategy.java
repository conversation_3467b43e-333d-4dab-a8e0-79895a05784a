package com.trs.moye.storage.engine.file.ftp;

import com.trs.moye.base.data.model.field.mapping.ColumnResponse;
import com.trs.moye.base.data.source.setting.file.FileTypeConfig;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;
import lombok.extern.slf4j.Slf4j;

/**
 * Txt文件列策略
 */
@Slf4j
public class TextFileColumnStrategy implements FileColumnStrategy {

    @Override
    public List<ColumnResponse> getFtpFileColumns(FileTypeConfig fileTypeConfig, InputStream inputStream) {
        log.info("目前txt只支持已有表!");
        return new ArrayList<>();
    }


}
