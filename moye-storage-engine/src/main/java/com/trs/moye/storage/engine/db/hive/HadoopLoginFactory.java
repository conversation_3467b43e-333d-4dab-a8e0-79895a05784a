/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.trs.moye.storage.engine.db.hive;

import java.io.IOException;
import java.security.PrivilegedExceptionAction;
import org.apache.hadoop.conf.Configuration;
import org.apache.hadoop.security.UserGroupInformation;

/**
 * hadoop 进行 kerberos认证工厂类
 */
public class HadoopLoginFactory {

    /**
     * 使用kerberos认证执行回调
     *
     * @param <T>                回调的返回值类型
     * @param kerberosKeytabPath kerberos keytab path
     * @param kerberosPrincipal  kerberos principal
     * @param krb5FilePath       krb5 file path
     * @param configuration      hadoop 配置
     * @param action             kerberos认证后执行的回调
     * @return the result of the action
     */
    public static <T> T loginWithKerberos(
        Configuration configuration,
        String krb5FilePath,
        String kerberosPrincipal,
        String kerberosKeytabPath,
        LoginFunction<T> action)
        throws IOException, InterruptedException {
        if (!configuration.get("hadoop.security.authentication").equals("kerberos")) {
            throw new IllegalArgumentException("hadoop.security.authentication must be kerberos");
        }
        // Use global lock to avoid multiple threads to execute setConfiguration at the same time
        synchronized (UserGroupInformation.class) {
            // 设置客户端的keytab和zookeeper认证principal
            System.setProperty("java.security.krb5.conf", krb5FilePath);
            System.setProperty("zookeeper.server.principal", "zookeeper/hadoop.hadoop.com");
            // init configuration
            UserGroupInformation.setConfiguration(configuration);
            UserGroupInformation userGroupInformation =
                UserGroupInformation.loginUserFromKeytabAndReturnUGI(
                    kerberosPrincipal, kerberosKeytabPath);
            return userGroupInformation.doAs(
                (PrivilegedExceptionAction<T>)
                    () -> action.run(configuration, userGroupInformation));
        }
    }

    /**
     * Login function 执行kerberos认证后的回调函数
     *
     * @param <T> 返回值类型
     */
    public interface LoginFunction<T> {

        /**
         * Run function
         *
         * @param configuration        hadoop 配置
         * @param userGroupInformation kerberos认证后的用户信息
         * @return the result of the action
         * @throws Exception if an error occurs
         */
        T run(Configuration configuration, UserGroupInformation userGroupInformation)
            throws Exception;
    }
}
