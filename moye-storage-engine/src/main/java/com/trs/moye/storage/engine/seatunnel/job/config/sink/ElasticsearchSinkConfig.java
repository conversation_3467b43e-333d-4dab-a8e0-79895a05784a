package com.trs.moye.storage.engine.seatunnel.job.config.sink;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.trs.moye.base.data.model.enums.DataSaveMode;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.SuperBuilder;

/**
 * Elasticsearch sink config
 *
 * <p>Elasticsearch sink config is used to specify the configuration of the Elasticsearch sink</p>
 *
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
@SuperBuilder
public class ElasticsearchSinkConfig extends SeatunnelSinkConfig {

    @JsonProperty("hosts")
    private String[] hosts;
    @JsonProperty("primary_keys")
    private String[] primaryKeys;
    @JsonProperty("index")
    private String index;
    @JsonProperty("index_type")
    private String indexType;
    @JsonProperty("username")
    private String username;
    @JsonProperty("password")
    private String password;
    @JsonProperty("storage_name")
    private String storageName;
    @JsonProperty("plugin_name")
    @Builder.Default
    private String pluginName = "Elasticsearch";
    @JsonProperty("data_save_mode")
    @Builder.Default
    private DataSaveMode dataSaveMode = DataSaveMode.APPEND_DATA;
}
