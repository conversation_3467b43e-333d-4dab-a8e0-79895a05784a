package com.trs.moye.storage.engine.db.hive;

import com.trs.moye.base.data.model.entity.DataModelField;
import com.trs.moye.base.common.entity.field.DecimalAdvanceConfig;
import com.trs.moye.base.common.entity.field.FieldAdvanceConfig;
import com.trs.moye.base.common.enums.FieldType;
import com.trs.moye.storage.engine.db.BasicTypeDefine;
import com.trs.moye.storage.engine.db.TypeConverter;
import com.trs.moye.storage.engine.exception.DataStorageEngineException;
import com.trs.moye.storage.engine.utils.BizUtils;
import java.util.Objects;
import lombok.extern.slf4j.Slf4j;

/**
 * Hive 数据类型转换器
 */
@Slf4j
public class HiveTypeConverter implements TypeConverter<BasicTypeDefine> {

    // Numeric Types
    private static final String HIVE_TINYINT = "TINYINT";
    private static final String HIVE_SMALLINT = "SMALLINT";
    private static final String HIVE_INT = "INT";
    private static final String HIVE_INTEGER = "INTEGER";
    private static final String HIVE_BIGINT = "BIGINT";
    private static final String HIVE_FLOAT = "FLOAT";
    private static final String HIVE_DOUBLE = "DOUBLE";
    private static final String HIVE_DOUBLE_PRECISION = "DOUBLE PRECISION";
    private static final String HIVE_DECIMAL = "DECIMAL";
    private static final String HIVE_NUMERIC = "NUMERIC";
    // Date/Time Types
    private static final String HIVE_TIMESTAMP = "TIMESTAMP";
    private static final String HIVE_DATE = "DATE";
    private static final String HIVE_INTERVAL = "INTERVAL";
    // String Types
    private static final String HIVE_STRING = "STRING";
    private static final String HIVE_VARCHAR = "VARCHAR";
    private static final String HIVE_CHAR = "CHAR";
    // Misc Types
    private static final String HIVE_BOOLEAN = "BOOLEAN";
    private static final String HIVE_BINARY = "BINARY";
    // Complex Types
    private static final String HIVE_ARRAY = "ARRAY";
    private static final String HIVE_MAP = "MAP";
    private static final String HIVE_STRUCT = "STRUCT";
    private static final String HIVE_UNIONTYPE = "UNIONTYPE";

    @Override
    public DataModelField convert(BasicTypeDefine typeDefine) {
        String columnType = typeDefine.getColumnType();
        Integer precision = typeDefine.getPrecision();
        DataModelField.DataModelFieldBuilder<?, ?> builder = DataModelField.builder()
            .enName(typeDefine.getName())
            .zhName(typeDefine.getComment())
            .isNullable(typeDefine.isNullable());
        switch (columnType.toUpperCase()) {
            case HIVE_TINYINT, HIVE_BOOLEAN:
                builder.type(FieldType.BOOLEAN);
                break;
            case HIVE_SMALLINT:
                builder.type(FieldType.SHORT);
                break;
            case HIVE_INT, HIVE_INTEGER:
                builder.type(FieldType.INT);
                break;
            case HIVE_BIGINT:
                builder.type(FieldType.LONG);
                break;
            case HIVE_FLOAT:
                builder.type(FieldType.FLOAT);
                break;
            case HIVE_DOUBLE, HIVE_DOUBLE_PRECISION:
                builder.type(FieldType.DOUBLE);
                break;
            case HIVE_DECIMAL, HIVE_NUMERIC:
                DecimalAdvanceConfig config = new DecimalAdvanceConfig();
                if (Objects.nonNull(precision) && precision > 0) {
                    builder.type(FieldType.DECIMAL);
                    config.setAccuracy(precision);
                    config.setScale(typeDefine.getScale());
                }
                builder.type(FieldType.DECIMAL);
                builder.advanceConfig(config);
                break;
            case HIVE_TIMESTAMP:
                builder.type(FieldType.DATETIME);
                break;
            case HIVE_DATE:
                builder.type(FieldType.DATE);
                break;
            case HIVE_STRING, HIVE_VARCHAR, HIVE_CHAR:
                builder.type(FieldType.STRING);
                break;
            default:
                builder.type(FieldType.STRING);
                break;
        }
        return BizUtils.buildAndSupplementAdvanceConfig(builder);
    }

    @Override
    public BasicTypeDefine reconvert(DataModelField column) {
        FieldAdvanceConfig config = column.getAdvanceConfig();
        BasicTypeDefine.BasicTypeDefineBuilder builder =
            BasicTypeDefine.builder()
                .name(column.getEnName())
                .nullable(column.isNullable())
                .comment(column.getZhName());
        switch (column.getType()) {
            case BOOLEAN:
                return builder.columnType(HIVE_TINYINT).dataType(HIVE_TINYINT).build();
            case SHORT:
                return builder.columnType(HIVE_SMALLINT).dataType(HIVE_SMALLINT).build();
            case INT:
                return builder.columnType(HIVE_INT).dataType(HIVE_INT).build();
            case LONG:
                return builder.columnType(HIVE_BIGINT).dataType(HIVE_BIGINT).build();
            case FLOAT:
                return builder.columnType(HIVE_FLOAT).dataType(HIVE_FLOAT).build();
            case DOUBLE:
                return builder.columnType(HIVE_DOUBLE).dataType(HIVE_DOUBLE).build();
            case DECIMAL:
                builder.columnType(HIVE_DECIMAL).dataType(HIVE_DECIMAL);
                if (config instanceof DecimalAdvanceConfig decimalConfig) {
                    builder.precision(decimalConfig.getAccuracy()).scale(decimalConfig.getScale()).build();
                }
                return builder.build();
            case DATETIME:
                return builder.columnType(HIVE_TIMESTAMP).dataType(HIVE_TIMESTAMP).build();
            case DATE:
                return builder.columnType(HIVE_DATE).dataType(HIVE_DATE).build();
            case STRING, TEXT:
                return builder.columnType(HIVE_STRING).dataType(HIVE_STRING).build();
            default:
                throw new DataStorageEngineException(
                    String.format("不支持的数据类型: %s", column.getType()));
        }
    }
}
