package com.trs.moye.storage.engine.pool;

import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 连接池信息
 *
 * <AUTHOR>
 * @since 2025-02-12 14:49
 */
@Data
@NoArgsConstructor
public class PoolInfo {

    /**
     * 总活跃连接数
     */
    private int activeTotalCount;

    /**
     * 总空闲连接数
     */
    private int idleTotalCount;

    /**
     * 等待线程总数
     */
    private int waitThreadTotalCount;

    /**
     * 创建的对象总数
     */
    private long createdTotalCount;

    /**
     * 销毁的对象总数
     */
    private long destroyedTotalCount;

    /**
     * 连接池原始信息
     */
    private String originalInfo;

    /**
     * 键信息列表
     */
    private List<PoolKeyInfo> keyInfoList;

    public PoolInfo(ConnectionPool pool) {
        setActiveTotalCount(pool.getNumActive());
        setIdleTotalCount(pool.getNumIdle());
        setWaitThreadTotalCount(pool.getNumWaiters());
        setCreatedTotalCount(pool.getCreatedCount());
        setDestroyedTotalCount(pool.getDestroyedCount());
        setKeyInfoList(getPoolKeyInfoList(pool));
        setOriginalInfo(pool.toString());
    }

    private static List<PoolKeyInfo> getPoolKeyInfoList(ConnectionPool pool) {
        // 获取每个键的活跃连接数和空闲连接数
        Map<String, Integer> activeCountPerKey = pool.getNumActivePerKey();
        Map<String, Integer> numWaitersByKey = pool.getNumWaitersByKey();
        Set<String> keySet = new HashSet<>(activeCountPerKey.keySet());
        keySet.addAll(numWaitersByKey.keySet());
        return keySet.stream().map(key -> {
            PoolKeyInfo poolKeyInfo = new PoolKeyInfo();
            poolKeyInfo.setKey(key);
            poolKeyInfo.setActiveCount(activeCountPerKey.getOrDefault(key, 0));
            poolKeyInfo.setIdleCount(pool.getNumIdle(key));
            poolKeyInfo.setWaitThreadCount(numWaitersByKey.getOrDefault(key, 0));
            return poolKeyInfo;
        }).toList();
    }
}
