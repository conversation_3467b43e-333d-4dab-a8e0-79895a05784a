package com.trs.moye.storage.engine.seatunnel.job.config.strategy;

import com.trs.moye.base.data.connection.entity.DataConnection;
import com.trs.moye.base.data.connection.entity.params.DorisConnectionParams;
import com.trs.moye.base.data.model.entity.DataModelField;
import com.trs.moye.base.data.model.entity.DataSourceConfig;
import com.trs.moye.base.data.storage.DataStorage;
import com.trs.moye.base.data.storage.IncrementInfo;
import com.trs.moye.storage.engine.pojo.request.task.TaskStartParam;
import com.trs.moye.storage.engine.seatunnel.job.config.schema.Schema;
import com.trs.moye.storage.engine.seatunnel.job.config.sink.DorisSinkConfig;
import com.trs.moye.storage.engine.seatunnel.job.config.source.DorisSourceConfig;
import com.trs.moye.storage.engine.seatunnel.job.config.source.DorisSourceConfig.DorisTableConfig;
import java.util.List;

/**
 * apache doris Seatunnel 数据源配置
 */
public class DorisSeatunnelJobConfigStrategy implements SeatunnelJobConfigStrategy {

    @Override
    public DorisSourceConfig createSourceConfig(DataSourceConfig dataSourceConfig, String tableName,
        List<DataModelField> fields, IncrementInfo incrementInfo, TaskStartParam taskStartParam) {
        DorisConnectionParams dorisConnectionParams = (DorisConnectionParams) dataSourceConfig.getConnection()
            .getConnectionParams();
        String frontends = String.format("%s:%s", dorisConnectionParams.getHost(), dorisConnectionParams.getFePort());
        String query = buildQuery(incrementInfo);
        Schema schema = Schema.of(fields);
        List<DorisTableConfig> tableConfig = List.of(
            DorisTableConfig.builder()
                .database(dorisConnectionParams.getDatabase())
                .table(tableName)
                .filterQuery(query)
                .build());
        return DorisSourceConfig.builder().frontends(frontends)
            .schema(schema)
            .password(dorisConnectionParams.getDecryptedPassword())
            .username(dorisConnectionParams.getUsername())
            .tableConfigList(tableConfig)
            .build();
    }

    private String buildQuery(IncrementInfo incrementInfo) {
        return incrementInfo.getFieldName() + ">"
            + incrementInfo.getIncrementValue();
    }

    @Override
    public DorisSinkConfig createSinkConfig(DataConnection connection, DataStorage storage,
        IncrementInfo incrementInfo, List<DataModelField> fields) {
        DorisConnectionParams connectionParams = (DorisConnectionParams) connection.getConnectionParams();
        String frontends = String.format("%s:%s", connectionParams.getHost(), connectionParams.getFePort());
        return DorisSinkConfig.builder()
            .frontends(frontends)
            .username(connectionParams.getUsername())
            .password(connectionParams.getDecryptedPassword())
            .database(connectionParams.getDatabase())
            .table(storage.getEnName())
            .build();
    }
}
