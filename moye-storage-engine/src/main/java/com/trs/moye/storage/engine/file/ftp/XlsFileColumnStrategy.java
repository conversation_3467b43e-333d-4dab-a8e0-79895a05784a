package com.trs.moye.storage.engine.file.ftp;

import com.trs.moye.base.data.model.field.mapping.ColumnResponse;
import com.trs.moye.base.data.source.setting.file.FileTypeConfig;
import com.trs.moye.base.common.enums.FieldType;
import com.trs.moye.base.common.utils.AssertUtils;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellType;
import org.apache.poi.ss.usermodel.DateUtil;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;

/**
 * Xls文件列策略
 */
@Slf4j
public class XlsFileColumnStrategy implements FileColumnStrategy {

    @Override
    public List<ColumnResponse> getFtpFileColumns(FileTypeConfig fileTypeConfig, InputStream inputStream) {
        try (Workbook workbook = new HSSFWorkbook(inputStream)) {
            return parseExcelWorkbook(workbook);
        } catch (IOException e) {
            log.error("读取xls文件失败: {}", e.getMessage());
            return List.of();
        }
    }

    /**
     * 解析Excel表头
     *
     * @param workbook 工作簿
     * @return 表头列表
     * @throws IOException 读取Excel文件异常
     */
    public static List<ColumnResponse> parseExcelWorkbook(Workbook workbook) throws IOException {
        Sheet sheet = workbook.getSheetAt(0);
        int rowCount = sheet.getPhysicalNumberOfRows();
        if (rowCount < 0) {
            throw new IOException("读取xls文件表头失败，第一行数据为空，请检查文件格式！");
        }
        Row headers = sheet.getRow(0);
        AssertUtils.notEmpty(headers, "【%s】sheet页未发现标题行，可能是一个空sheet");
        Row data = rowCount > 1 ? sheet.getRow(1) : null;
        int cellCount = headers.getPhysicalNumberOfCells();
        List<ColumnResponse> tableColumns = new ArrayList<>();
        for (int i = 0; i < cellCount; i++) {
            Cell header = headers.getCell(i);
            String columnName = header.getStringCellValue();
            ColumnResponse tableColumn = new ColumnResponse();
            tableColumn.setEnName(columnName);
            tableColumn.setZhName(columnName);
            FieldType fieldType = FieldType.STRING;
            Cell cell = Objects.isNull(data) ? null : data.getCell(i);
            if (Objects.nonNull(cell)) {
                CellType cellType = cell.getCellType();
                fieldType = parseCellType(cellType, cell);
            }
            tableColumn.setType(fieldType.name());
            tableColumns.add(tableColumn);
        }
        return tableColumns;
    }

    private static FieldType parseCellType(CellType cellType, Cell cell) {
        if (CellType.NUMERIC.equals(cellType)) {
            return parseNumericType(cell);
        } else if (CellType.BOOLEAN.equals(cellType)) {
            return FieldType.BOOLEAN;
        }
        return FieldType.STRING;
    }

    private static FieldType parseNumericType(Cell cell) {
        if (DateUtil.isCellDateFormatted(cell)) {
            String dataFormatString = cell.getCellStyle().getDataFormatString();
            //包含小时
            if (dataFormatString.contains("h")) {
                //不包含年
                if (!dataFormatString.contains("y")) {
                    return FieldType.TIME;
                }
                return FieldType.DATETIME;
            } else {
                return FieldType.DATE;
            }
        } else if (cell.getNumericCellValue() == (int) cell.getNumericCellValue()) {
            return FieldType.INT;
        }
        return FieldType.DOUBLE;
    }
}
