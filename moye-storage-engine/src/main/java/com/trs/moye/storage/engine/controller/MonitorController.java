package com.trs.moye.storage.engine.controller;

import com.trs.moye.base.common.entity.mq.MqConsumeInfoResponse;
import com.trs.moye.base.common.exception.BizException;
import com.trs.moye.base.common.response.CountRemarkResponse;
import com.trs.moye.base.common.utils.DateTimeUtils.Formatter;
import com.trs.moye.base.data.connection.dao.DataConnectionMapper;
import com.trs.moye.base.data.connection.entity.DataConnection;
import com.trs.moye.base.data.model.dao.DataModelMapper;
import com.trs.moye.base.data.model.entity.DataModel;
import com.trs.moye.base.monitor.entity.TodayMonitorMetric;
import com.trs.moye.base.monitor.response.StorageSpeedPeriodResponse;
import com.trs.moye.storage.engine.db.DatabaseConnection;
import com.trs.moye.storage.engine.exception.DataStorageEngineException;
import com.trs.moye.storage.engine.mq.MqConnection;
import com.trs.moye.storage.engine.pojo.request.monitor.MonitorDTO;
import com.trs.moye.storage.engine.pojo.request.monitor.NewDataCountMonitorRequest;
import com.trs.moye.storage.engine.pojo.response.NewDataCountMonitorResponse;
import com.trs.moye.storage.engine.service.KafkaMonitorService;
import com.trs.moye.storage.engine.service.MonitorCenterSupportService;
import com.trs.moye.storage.engine.service.MonitorService;
import com.trs.moye.storage.engine.service.SearchService;
import com.trs.moye.storage.engine.utils.ConnectionUtils;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import javax.annotation.Resource;
import javax.validation.Valid;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * 提供给 监控服务 以查询数据源数据情况
 */
@RestController
@RequestMapping("/monitor")
public class MonitorController {

    @Resource
    MonitorService monitorService;
    @Resource
    private DataModelMapper dataModelMapper;
    @Resource
    private DataConnectionMapper dataConnectionMapper;
    @Resource
    private KafkaMonitorService kafkaMonitorService;
    @Resource
    private SearchService searchService;
    @Resource
    private MonitorCenterSupportService monitorCenterSupportService;

    /**
     * 获取数据源数据总量
     *
     * @param monitorDTO 数据源信息
     * @return 数据总量
     */
    @PostMapping("/total-count")
    public Long getDataSourceTotalCount(@RequestBody @Valid MonitorDTO monitorDTO) {
        DataModel dataSource = getDataModel(monitorDTO.getDataModelId());
        return monitorService.getDataSourceTotalCount(dataSource);
    }

    /**
     * 获取数据源数据积压量
     *
     * @param monitorDTO 数据源信息
     * @return 数据总量
     */
    @PostMapping("/lag")
    public Long getDataSourceLag(@RequestBody @Valid MonitorDTO monitorDTO) {
        DataModel dataSource = getDataModel(monitorDTO.getDataModelId());
        return monitorService.getDataSourceLag(dataSource);
    }

    private DataModel getDataModel(Integer metaDataId) {
        DataModel dataModel = dataModelMapper.selectById(metaDataId);
        if (Objects.isNull(dataModel)) {
            throw new DataStorageEngineException(String.format("主键为[id:%s]的数据建模不存在", metaDataId));
        }
        return dataModel;
    }

    /**
     * 计算数据源数据总体增加量<br>
     * <b>必须返回的参数: 本次数据总体的增加量，即这次数据源中数据增加了多少</b>
     * <ul>以下传入参数不同时出现:
     *     <li>上一次数据源数据总量</li>
     *     <li>上一次数据源数据增量字段及对应值</li>
     * </ul>
     * <ul>以下返回参数不同时出现:
     *      <li>本次数据源数据总量</li>
     *      <li>本次数据源数据增量字段及对应值</li>
     * </ul>
     *
     * @param monitorDTO 数据源信息
     * @return 数据总量
     */
    @PostMapping("/new-data-count")
    public NewDataCountMonitorResponse getNewDataCount(@RequestBody @Valid NewDataCountMonitorRequest monitorDTO) {
        DataModel dataSource = getDataModel(monitorDTO.getDataModelId());
        return monitorService.getNewDataCount(dataSource, monitorDTO);
    }

    /**
     * 获取消费信息
     *
     * @param monitorDTO 监控 DTO
     * @return {@link MqConsumeInfoResponse }
     * <AUTHOR>
     * @since 2025/02/17 16:02:00
     */
    @PostMapping("/mq-consume-info")
    public MqConsumeInfoResponse getMqConsumeInfo(@RequestBody @Valid MonitorDTO monitorDTO) {
        DataModel dataModel = getDataModel(monitorDTO.getDataModelId());
        return monitorService.getMqConsumeInfo(dataModel);
    }

    /**
     * 获取数据处理的kafka消费信息
     *
     * @param connectionId 连接id
     * @param topic        kafka主题
     * @param group        kafka消费组
     * @return {@link MqConsumeInfoResponse}
     */
    @GetMapping("/data-process/consume-info/one")
    public MqConsumeInfoResponse getDataProcessKafkaConsumeInfo(@RequestParam Integer connectionId,
        @RequestParam String topic,
        @RequestParam String group) {
        DataConnection dataConnection = dataConnectionMapper.selectById(connectionId);
        try (MqConnection connection = ConnectionUtils.getConnection(dataConnection.getConnectionParams(),
            dataConnection.getKerberosCertificate())) {
            return connection.getConsumeInfo(topic, group);
        } catch (Exception e) {
            throw new BizException(e, "获取【%s】连接的【%s】主题的【%s】消费信息发生异常", dataConnection.getName(), topic, group);
        }
    }

    /**
     * 获取数据处理的kafka消费信息
     *
     * @param connectionId 连接id
     * @param topic        主题
     * @param group        消费组
     * @return {@link MqConsumeInfoResponse}
     */
    @GetMapping("/data-process/today-monitor")
    public TodayMonitorMetric dataProcessTodayMonitor(@RequestParam Integer connectionId,
        @RequestParam String topic, @RequestParam String group) {
        DataConnection dataConnection = dataConnectionMapper.selectById(connectionId);
        try (MqConnection connection = ConnectionUtils.getConnection(dataConnection.getConnectionParams(),
            dataConnection.getKerberosCertificate())) {
            long todayStartOffset = connection.getTopicTodayOffset(topic);
            MqConsumeInfoResponse consumeInfo = connection.getConsumeInfo(topic, group);
            return new TodayMonitorMetric(consumeInfo, todayStartOffset);
        } catch (Exception e) {
            throw new BizException(e, "获取【%s】连接的【%s】主题今日起始偏移量发生异常", dataConnection.getName(), topic);
        }
    }

    /**
     * 获取数据处理的kafka消费信息列表
     *
     * @param topicGroupMap topic和group的映射关系，key为topic，value为group
     * @return 消费信息Map，key为topic，value为消费信息
     */
    @PostMapping("/data-process/consume-info/multiple")
    public Map<String, MqConsumeInfoResponse> getDataProcessKafkaConsumeInfoList(
        @RequestBody Map<String, String> topicGroupMap) {
        return kafkaMonitorService.getDataProcessKafkaConsumeInfoMap(topicGroupMap);
    }

    /**
     * 获取数据处理的kafka消费信息列表
     *
     * @param topicGroupMap topic集合
     * @return 消费信息Map，key为topic，value为消费信息
     */
    @PostMapping("/data-storage/today-monitor")
    public Map<String, TodayMonitorMetric> dataStorageTodayMonitor(@RequestBody Map<String, String> topicGroupMap) {
        return kafkaMonitorService.getTopicTodayStartOffsetMap(topicGroupMap);
    }

    /**
     * 获取连接表数据总量
     *
     * @param connectionId 连接id
     * @param tableName    表名
     * @return 连接表数据总量
     */
    @GetMapping("/table/data-total-count")
    public long getTableDataTotalCount(@RequestParam Integer connectionId,
        @RequestParam String tableName) {
        DataConnection dataConnection = dataConnectionMapper.selectById(connectionId);
        switch (dataConnection.getConnectionType().getCategory()) {
            case DATA_BASE -> {
                try (DatabaseConnection connection = ConnectionUtils.getConnection(dataConnection.getConnectionParams(),
                    dataConnection.getKerberosCertificate())) {
                    return connection.count(List.of(), tableName, dataConnection);
                } catch (Exception e) {
                    throw new BizException(e, "获取【%s】连接的【%s】表查询表数据总量发生异常", dataConnection.getName(), tableName);
                }
            }
            case MQ -> {
                try (MqConnection connection = ConnectionUtils.getConnection(dataConnection.getConnectionParams(),
                    dataConnection.getKerberosCertificate())) {
                    return connection.getTopicTotalOffset(tableName);
                } catch (Exception e) {
                    throw new BizException(e, "获取【%s】连接的【%s】表查询表数据总量发生异常", dataConnection.getName(), tableName);
                }
            }
            default -> throw new BizException("不支持的连接类型");
        }
    }

    /**
     * 获取存储点制定时间点之后的数据总量
     *
     * @param storageId 存储点id
     * @param time      时间点
     * @return 连接表数据总量
     */
    @GetMapping("/storage-point/data-count/time-point-after")
    CountRemarkResponse getDatabaseTimePointAfterDataCount(@RequestParam("storageId") Integer storageId,
        @RequestParam(name = "time") String time) {
        return monitorCenterSupportService.getDatabaseTimePointAfterDataCount(storageId, time);
    }

    /**
     * 存储点指定时间段的速率数据总量
     *
     * @param storageId 存储点id
     * @param time      时间点
     * @return 连接表数据总量
     */
    @GetMapping("/storage-point/speed-period-count")
    StorageSpeedPeriodResponse getStorageSpeedPeriodCount(@RequestParam("storageId") Integer storageId,
        @RequestParam(name = "time") String time) {
        return monitorCenterSupportService.getStorageSpeedPeriodCount(storageId, time);
    }

    /**
     * 获取内部kafka指定时间点之后的数据总量
     *
     * @param topic 主题
     * @param time  时间点, 格式为yyyy-MM-dd HH:mm:ss
     * @return 数据总量
     */
    @GetMapping("/internal-kafka/data-count/time-point-after")
    public long getInternalKafkaTimePointAfterDataCount(@RequestParam String topic, @RequestParam String time) {
        LocalDateTime parse = LocalDateTime.parse(time, Formatter.YYYY_MM_DD_HH_MM_SS.getDateTimeFormatter());
        return kafkaMonitorService.getTimePointAfterDataCount(topic, parse);
    }
}
