package com.trs.moye.storage.engine.pojo.response;

import com.trs.moye.base.data.storage.IncrementInfo;
import javax.validation.constraints.NotNull;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 数据源数据增加量监控传输类
 */
@NoArgsConstructor
@Data
public class NewDataCountMonitorResponse {

    /**
     * 数据增加量
     */
    @NotNull
    private Long newDataCount;

    /*
      以下参数 只有一个有值，数据要不以全量或者以增量信息增加
     */
    /**
     * 数据源总量信息
     */
    private Long amount;
    /**
     * 本次数据源增量信息
     */
    private IncrementInfo increment;

    public NewDataCountMonitorResponse(Long newDataCount, Long amount) {
        this.newDataCount = newDataCount;
        this.amount = amount;
    }

    public NewDataCountMonitorResponse(Long newDataCount, IncrementInfo increment) {
        this.newDataCount = newDataCount;
        this.increment = increment;
    }
}
