package com.trs.moye.storage.engine.common.restful;

import lombok.Builder;
import lombok.Data;

/**
 * 异常返回消息体
 *
 * <p>异常返回消息体用于封装异常返回的消息</p>
 *
 * <AUTHOR>
 */
@Data
@Builder
public class RestfulResponse {

    private Integer code;
    private String message;
    private Object data;


    /**
     * 成功返回消息体
     *
     * @param data 返回数据
     * @return RestfulResponse
     */
    public static RestfulResponse success(Object data) {
        return RestfulResponse.builder().code(200).message("操作成功").data(data).build();
    }

    /**
     * 失败返回消息题
     *
     * @param message 失败消息
     * @return RestfulResponse
     */
    public static RestfulResponse failed(String message) {
        return RestfulResponse.builder().code(500).message(message).build();
    }

    /**
     * 失败返回消息题
     *
     * @param exception 异常
     * @return RestfulResponse
     */
    public static RestfulResponse failed(Exception exception) {
        return RestfulResponse.failed("操作失败:" + exception.getMessage());
    }
}
