package com.trs.moye.storage.engine.pojo.request.search;

import lombok.Getter;

/**
 * 数据服务条件类型
 */
@Getter
public enum DataServiceConditionType {

    /**
     * 逻辑连接符
     */
    LOGIC("逻辑连接符"),

    /**
     * 引用
     */
    REFERENCE("引用"),

    /**
     * 普通条件
     */
    EXPRESSION("普通条件"),

    REPLACEABLE("替换条件");


    private final String name;

    DataServiceConditionType(String name) {
        this.name = name;
    }

}
