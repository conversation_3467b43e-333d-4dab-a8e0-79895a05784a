package com.trs.moye.storage.engine.seatunnel.job.config.strategy;

import com.trs.moye.base.data.connection.entity.DataConnection;
import com.trs.moye.base.data.model.entity.DataModelField;
import com.trs.moye.base.data.model.entity.DataSourceConfig;
import com.trs.moye.base.data.model.enums.DataSaveMode;
import com.trs.moye.base.data.storage.DataStorage;
import com.trs.moye.base.data.storage.IncrementInfo;
import com.trs.moye.storage.engine.pojo.request.task.TaskStartParam;
import com.trs.moye.storage.engine.seatunnel.job.config.sink.SeatunnelSinkConfig;
import com.trs.moye.storage.engine.seatunnel.job.config.source.SeatunnelSourceConfig;
import java.util.List;

/**
 * Seatunnel 数据源配置
 */
public interface SeatunnelJobConfigStrategy {

    String DOUBLE_QUOTES = "\".\"";
    String WHERE = " where ";
    String URL_FORMAT = "%s://%s:%s";
    DataSaveMode DEFAULT_DATA_SAVE_MODE = DataSaveMode.APPEND_DATA;
    String NOT_SUPPORT_DATA_SOURCE = "不支持的数据源类型: ";

    /**
     * 创建数据源配置
     *
     * @param dataSourceConfig 数据源配置
     * @param tableName        表名
     * @param fields           字段列表
     * @param incrementInfo    增量信息
     * @param taskStartParam   任务启动参数
     * @return {@link SeatunnelSourceConfig}
     */
    SeatunnelSourceConfig createSourceConfig(DataSourceConfig dataSourceConfig, String tableName,
        List<DataModelField> fields, IncrementInfo incrementInfo, TaskStartParam taskStartParam);

    /**
     * 创建数据接收配置
     *
     * @param connection    数据连接
     * @param storage       数据存储
     * @param incrementInfo 增量信息
     * @param fields        字段列表
     * @return {@link SeatunnelSinkConfig}
     */
    SeatunnelSinkConfig createSinkConfig(DataConnection connection, DataStorage storage,
        IncrementInfo incrementInfo, List<DataModelField> fields);

    /**
     * 获取主键字段
     *
     * @param dataModelFields 数据模型字段列表
     * @return 主键字段数组
     */
    default String[] getKeyFields(List<DataModelField> dataModelFields) {
        String[] primaryKeyFields = dataModelFields.stream()
            .filter(DataModelField::isPrimaryKey)
            .map(DataModelField::getEnName)
            .toArray(String[]::new);
        primaryKeyFields = (primaryKeyFields.length == 0) ? null : primaryKeyFields;
        return primaryKeyFields;
    }
}
