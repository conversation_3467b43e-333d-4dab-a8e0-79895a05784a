package com.trs.moye.storage.engine.pojo.request.nebula;

import com.trs.moye.base.data.storage.nebula.DeleteTagEdgeFieldDto;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2025-01-20 10:44
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
public class DeleteTagEdgeFieldsRequest extends BaseTagEdgeFieldsRequest {


    /**
     * 要添加的tag
     */
    private List<DeleteTagEdgeFieldDto> tag;
    /**
     * 要添加的edge
     */
    private List<DeleteTagEdgeFieldDto> edge;
}
