package com.trs.moye.storage.engine.db.nebula;

import com.fasterxml.jackson.databind.JsonNode;
import com.trs.moye.base.common.enums.FieldType;
import com.trs.moye.base.data.model.entity.DataModelField;
import com.trs.moye.base.data.model.entity.DataModelFieldAttributes;
import com.trs.moye.base.data.standard.entity.TagEdgeField;
import com.trs.moye.base.data.standard.entity.UpdateTagEdgeField;
import com.trs.moye.storage.engine.exception.DataStorageEngineException;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import org.apache.commons.collections4.CollectionUtils;
import org.jetbrains.annotations.NotNull;

/**
 * Nebula语句工厂
 *
 * <AUTHOR>
 * @since 2024/10/9
 */
public class NebulaSqlFactory {

    // 私有构造函数，防止实例化
    private NebulaSqlFactory() {
        throw new UnsupportedOperationException("This is a utility class and cannot be instantiated");
    }

    public static final String SHOW_TAGS = "SHOW TAGS;";
    public static final String SHOW_EDGES = "SHOW EDGES;";
    public static final String SHOW_TAG_INDEXES = "SHOW TAG INDEXES;";
    public static final String SHOW_EDGE_INDEXES = "SHOW EDGE INDEXES;";
    public static final String MOYE_TAG_TYPE = "moyeTagType";
    public static final String MOYE_EDGE_TYPE = "moyeEdgeType";
    public static final String VID = "vid";
    public static final String FROM = "from";
    public static final String TO = "to";

    /**
     * 构造完整插入语句
     *
     * @param fields      字段
     * @param ifNotExists 生成的语句是否带 IF NOT EXISTS
     * @return 完整插入语句
     */
    public static String buildCompleteCreateSql(List<DataModelField> fields, boolean ifNotExists) {
        StringBuilder sqlBuilder = new StringBuilder();
        fields.forEach(e -> {
            String sql = NebulaSqlFactory.buildCreateSql(e, ifNotExists);
            if (Objects.nonNull(sql) && !sql.isEmpty()) {
                sqlBuilder.append(sql).append("; ");
            }
            String indexSql = NebulaSqlFactory.buildIndexSql(e);
            if (Objects.nonNull(indexSql) && !indexSql.isEmpty()) {
                sqlBuilder.append(indexSql).append("; ");
            }
        });
        return sqlBuilder.toString().trim();
    }

    /**
     * 构造建表的sql
     *
     * @param dataModelField 对应属性
     * @param ifNotExists    生成的语句是否带 IF NOT EXISTS
     * @return 建表的语句
     */
    public static String buildCreateSql(DataModelField dataModelField, boolean ifNotExists) {
        String sql = "";
        if (FieldType.GRAPHICS_TAG.equals(dataModelField.getType())) {
            //标签
            sql = buildCreateTagSql(dataModelField, ifNotExists);
        } else if (FieldType.GRAPHICS_EDGE.equals(dataModelField.getType())) {
            //边
            sql = buildCreateEdgeSql(dataModelField, ifNotExists);
        }
        return sql;
    }

    private static String buildCreateEdgeSql(DataModelField dataModelField, boolean ifNotExists) {
        StringBuilder sb = new StringBuilder();
        sb.append("CREATE EDGE ").append(ifNotExists ? "IF NOT EXISTS `" : "`").append(dataModelField.getEnName())
            .append("` (");
        return buildCreateResultSql(dataModelField, sb);
    }

    private static String buildCreateTagSql(DataModelField dataModelField, boolean ifNotExists) {
        StringBuilder sb = new StringBuilder();
        sb.append("CREATE TAG ").append(ifNotExists ? "IF NOT EXISTS `" : "`").append(dataModelField.getEnName())
            .append("` (");
        return buildCreateResultSql(dataModelField, sb);
    }

    private static @NotNull
    String buildCreateResultSql(DataModelField dataModelField, StringBuilder sb) {
        DataModelFieldAttributes<?> fieldSchema = dataModelField.getFields();
        if (Objects.isNull(fieldSchema) || CollectionUtils.isEmpty(fieldSchema.getFields())) {
            throw new DataStorageEngineException(
                "nebula sql 构造失败, tag/edge [" + dataModelField.getEnName() + "] 下缺少属性");
        }

        // 使用类型安全的方法获取TagEdgeField类型的字段
        List<TagEdgeField> fields = fieldSchema.getTagEdgeFields();
        if (CollectionUtils.isEmpty(fields)) {
            throw new DataStorageEngineException(
                "nebula sql 构造失败, tag/edge [" + dataModelField.getEnName()
                    + "] 的字段类型不是TagEdgeField或字段列表为空");
        }

        sb.append(fields.stream()
            .map(field -> "`" + field.getEnName() + "` " + field.getType() +
                (nonNull(field) ? " NOT NULL" : " NULL") +
                (nonNull(field) && Objects.nonNull(field.getDefaultValue()) ? " DEFAULT '" + field.getDefaultValue()
                    + "'" : ""))
            .collect(Collectors.joining(", ")));

        sb.append(", ");

        // 去掉最后的逗号和空格
        if (sb.length() > 2) {
            sb.setLength(sb.length() - 2);
        }
        sb.append(")");
        return sb.toString();
    }

    private static boolean nonNull(TagEdgeField field) {
        return field.isNotNull();
    }


    /**
     * 构造创建索引的sql
     *
     * @param dataModelField 字段属性
     * @return 创建索引的sql
     */
    public static String buildIndexSql(DataModelField dataModelField) {
        String sql = "";
        if (FieldType.GRAPHICS_TAG.equals(dataModelField.getType())) {
            //标签
            sql = buildCreateDefaultTagIndexSql(dataModelField);
        } else if (FieldType.GRAPHICS_EDGE.equals(dataModelField.getType())) {
            //边
            sql = buildCreateDefaultEdgeIndexSql(dataModelField);
        }
        return sql;
    }

    private static String buildCreateDefaultEdgeIndexSql(DataModelField dataModelField) {
        return "CREATE EDGE INDEX IF NOT EXISTS `" + dataModelField.getEnName() + "_index` ON `"
            + dataModelField.getEnName()
            + "`() COMMENT \"" + "默认索引\"";
    }

    private static String buildCreateDefaultTagIndexSql(DataModelField dataModelField) {
        return "CREATE TAG INDEX IF NOT EXISTS `" + dataModelField.getEnName() + "_index` ON `"
            + dataModelField.getEnName()
            + "`() COMMENT \"" + "默认索引\"";
    }

    /**
     * 构造tag的insert语句
     *
     * @param tag 标签数据
     * @return insert tag的语句
     */
    public static StringBuilder buildInsertTagSql(JsonNode tag) {
        StringBuilder insertTag = new StringBuilder("INSERT VERTEX " + tag.path(MOYE_TAG_TYPE).asText() + " (");
        // 添加属性
        StringBuilder properties = new StringBuilder();
        Iterator<String> fieldNames = tag.fieldNames();
        boolean first = true;
        while (fieldNames.hasNext()) {
            String fieldName = fieldNames.next();
            if (fieldName.equals(MOYE_TAG_TYPE) || fieldName.equals(VID)) { // 忽略 moyeTagType 和 vid
                continue;
            }
            if (!first) {
                properties.append(", ");
            }
            properties.append(fieldName);  // 只添加字段名
            first = false;
        }
        insertTag.append(properties).append(") VALUES \"").append(tag.path(VID).asText()).append("\": (");
        // 添加属性值
        first = true;
        fieldNames = tag.fieldNames();  // 重新遍历属性以获取值
        while (fieldNames.hasNext()) {
            String fieldName = fieldNames.next();
            if (fieldName.equals(MOYE_TAG_TYPE) || fieldName.equals(VID)) {
                continue;
            }
            if (!first) {
                insertTag.append(", ");
            }
            JsonNode valueNode = tag.path(fieldName);
            if (valueNode.isNumber()) {
                insertTag.append(valueNode.asInt()); // 数字类型不加双引号
            } else {
                insertTag.append("\"").append(valueNode.asText()).append("\""); // 字符串类型加双引号
            }
            first = false;
        }
        insertTag.append(");");
        return insertTag;
    }

    /**
     * 构造edge的insert语句
     *
     * @param edge 边数据
     * @return insert edge的语句
     */
    public static StringBuilder buildInsertEdgeSql(JsonNode edge) {
        StringBuilder insertEdge = new StringBuilder("INSERT EDGE " + edge.path(MOYE_EDGE_TYPE).asText() + " ");
        // 添加属性
        StringBuilder properties = new StringBuilder("(");
        Iterator<String> fieldNames = edge.fieldNames();
        boolean first = true;
        while (fieldNames.hasNext()) {
            String fieldName = fieldNames.next();
            if (isEdgeDefaultProperties(fieldName)) {
                continue;
            }
            if (!first) {
                properties.append(", ");
            }
            properties.append(fieldName);  // 只添加字段名
            first = false;
        }
        insertEdge.append(properties).append(") VALUES \"").append(edge.path(FROM).asText()).append("\"->\"")
            .append(edge.path(TO).asText()).append("\": (");
        // 添加属性值，使用双引号
        first = true;
        fieldNames = edge.fieldNames();  // 重新遍历属性以获取值
        while (fieldNames.hasNext()) {
            String fieldName = fieldNames.next();
            if (isEdgeDefaultProperties(fieldName)) {
                continue;
            }
            if (!first) {
                insertEdge.append(", ");
            }
            JsonNode valueNode = edge.path(fieldName);
            if (valueNode.isNumber()) {
                insertEdge.append(valueNode.asText()); // 数字类型不加双引号
            } else {
                insertEdge.append("\"").append(valueNode.asText()).append("\""); // 字符串类型加双引号
            }
            first = false;
        }
        insertEdge.append(");");
        return insertEdge;
    }

    private static boolean isEdgeDefaultProperties(String fieldName) {
        return fieldName.equals(MOYE_EDGE_TYPE) || fieldName.equals(FROM) || fieldName.equals(TO);
    }

    /**
     * 构建添加标签或边的字段语句
     *
     * @param tagOrEdge 标签或边的名称
     * @param isTag     是否为标签，否则认为是边
     * @param fields    fields 字段列表
     * @return 语句字符串
     */
    public static String buildAddFieldsStatement(String tagOrEdge, boolean isTag, List<TagEdgeField> fields) {
        return buildAlterTagOrEdgeSegmentStatement(tagOrEdge, isTag)
            + buildAddFieldsSegmentStatement(fields) + ";";
    }

    private static String buildAlterTagOrEdgeSegmentStatement(String tagOrEdge, boolean isTag) {
        return "ALTER " + (isTag ? NebulaConnection.TAG.toUpperCase() : NebulaConnection.EDGE.toUpperCase()) + " "
            + tagOrEdge + " ";
    }

    private static String buildAddFieldsSegmentStatement(List<TagEdgeField> fields) {
        String template = "ADD (%s %s %s DEFAULT %s COMMENT '%s') ";
        List<String> list = new ArrayList<>(fields.size());
        for (TagEdgeField field : fields) {
            list.add(String.format(template
                , ("`" + field.getEnName() + "`")
                , field.getType()
                , (field.isNotNull() ? "NOT NULL" : "NULL")
                , field.getStatementDefaultValue()
                , field.getStatementComment()));
        }
        return String.join(",", list);
    }

    /**
     * 构建更新标签或边的字段语句
     *
     * @param tagOrEdge 标签或边的名称
     * @param isTag     是否为标签，否则认为是边
     * @param fields    fields 字段列表
     * @return 语句字符串
     */
    public static String buildUpdateFieldsStatement(String tagOrEdge, boolean isTag, List<UpdateTagEdgeField> fields) {
        return buildAlterTagOrEdgeSegmentStatement(tagOrEdge, isTag)
            + buildUpdateFieldsSegmentStatement(fields) + ";";
    }

    private static String buildUpdateFieldsSegmentStatement(List<UpdateTagEdgeField> fields) {
        String template = "CHANGE (%s %s %s DEFAULT %s COMMENT '%s') ";
        List<String> list = new ArrayList<>(fields.size());
        for (UpdateTagEdgeField field : fields) {
            list.add(String.format(template
                , ("`" + field.getOriginalEnName() + "`")
                , field.getType()
                , (field.isNotNull() ? "NOT NULL" : "NULL")
                , field.getStatementDefaultValue()
                , field.getStatementComment()));
        }
        return String.join(",", list);
    }

    /**
     * 构建删除标签或边的字段语句
     *
     * @param tagOrEdge 标签或边的名称
     * @param isTag     是否为标签，否则认为是边
     * @param fields    fields 字段列表
     * @return 语句字符串
     */
    public static String buildDeleteFieldsStatement(String tagOrEdge, boolean isTag, List<String> fields) {
        return buildAlterTagOrEdgeSegmentStatement(tagOrEdge, isTag)
            + buildDeleteFieldsSegmentStatement(fields) + ";";
    }

    private static String buildDeleteFieldsSegmentStatement(List<String> fields) {
        String template = "DROP (%s) ";
        List<String> list = new ArrayList<>(fields.size());
        for (String field : fields) {
            list.add(String.format(template, ("`" + field + "`")));
        }
        return String.join(",", list);
    }

    /**
     * 构建删除标签索引的语句
     *
     * @param indexName 索引名称
     * @return 语句字符串
     */
    public static String buildDropTagIndexSql(String indexName) {
        return String.format("DROP TAG INDEX IF EXISTS `%s`;", indexName);
    }

    /**
     * 构建删除边索引的语句
     *
     * @param indexName 索引名称
     * @return 语句字符串
     */
    public static String buildDropEdgeIndexSql(String indexName) {
        return String.format("DROP EDGE INDEX IF EXISTS `%s`;", indexName);
    }

    /**
     * 构建删除标签的语句
     *
     * @param tagName 标签名称
     * @return 语句字符串
     */
    public static String buildDropTagSql(String tagName) {
        return String.format("DROP TAG IF EXISTS `%s`;", tagName);
    }

    /**
     * 构建删除边的语句
     *
     * @param edgeName 边名称
     * @return 语句字符串
     */
    public static String buildDropEdgeSql(String edgeName) {
        return String.format("DROP EDGE IF EXISTS `%s`;", edgeName);
    }

    /**
     * 构建查询标签的语句
     *
     * @param tagName 标签名称
     * @return 语句字符串
     */
    public static String buildDescribeTagSql(String tagName) {
        return String.format("DESCRIBE TAG `%s`;", tagName);
    }

    /**
     * 构建查询边的语句
     *
     * @param edgeName 边名称
     * @return 语句字符串
     */
    public static String buildDescribeEdgeSql(String edgeName) {
        return String.format("DESCRIBE EDGE `%s`;", edgeName);
    }
}
