package com.trs.moye.storage.engine.pojo.request.task;

import com.trs.moye.base.data.execute.ExecuteMode;
import com.trs.moye.base.data.execute.ExecuteParams;
import javax.validation.constraints.NotNull;
import lombok.Data;

/**
 * 任务启动参数，如果某个类型任务启动需要设置参数，应该在这个类上添加配置，因此这个类可能是经常被添加属性的类
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-06-24 14:43
 */
@Data
public class TaskStartParam {

    /**
     * 任务id
     */
    @NotNull(message = "任务id不允许为空")
    private Integer dataModelId;


    /**
     * 执行模式 立即执行 定时调度
     */
    private ExecuteMode executeMode = ExecuteMode.IMMEDIATE;

    /**
     * 参数
     */
    private ExecuteParams executeParams;
}
