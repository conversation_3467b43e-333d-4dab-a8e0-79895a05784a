package com.trs.moye.storage.engine.seatunnel.client;

import com.trs.moye.storage.engine.exception.DataStorageEngineException;
import com.trs.moye.storage.engine.seatunnel.request.SeaTunnelJobConfig;
import com.trs.moye.storage.engine.seatunnel.response.SeaTunnelRunningJobInfo;

/**
 * SeaTunnel客户端
 *
 * <AUTHOR>
 */
public interface SeaTunnelClient {

    /**
     * 提交任务
     *
     * @param config 任务配置
     * @return 任务ID
     * @throws DataStorageEngineException 存储引擎报错
     */
    Long submitJob(SeaTunnelJobConfig config) throws DataStorageEngineException;

    /**
     * 停止任务
     *
     * @param jobId 任务ID
     * @throws DataStorageEngineException 存储引擎报错
     */
    void stopJob(Long jobId) throws DataStorageEngineException;

    /**
     * 获取任务状态
     *
     * @param jobId 任务ID
     * @return 任务状态
     * @throws DataStorageEngineException 存储引擎报错
     */
    SeaTunnelRunningJobInfo getJobInfo(Long jobId) throws DataStorageEngineException;

}
