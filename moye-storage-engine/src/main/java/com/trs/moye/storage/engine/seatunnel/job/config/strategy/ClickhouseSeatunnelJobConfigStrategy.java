package com.trs.moye.storage.engine.seatunnel.job.config.strategy;

import com.trs.moye.base.common.enums.FieldType;
import com.trs.moye.base.data.connection.entity.DataConnection;
import com.trs.moye.base.data.connection.entity.params.ClickhouseConnectionParams;
import com.trs.moye.base.data.model.entity.DataModelField;
import com.trs.moye.base.data.model.entity.DataSourceConfig;
import com.trs.moye.base.data.model.enums.DataSaveMode;
import com.trs.moye.base.data.storage.DataStorage;
import com.trs.moye.base.data.storage.IncrementInfo;
import com.trs.moye.storage.engine.pojo.request.task.TaskStartParam;
import com.trs.moye.storage.engine.seatunnel.job.config.schema.Schema;
import com.trs.moye.storage.engine.seatunnel.job.config.sink.ClickhouseSinkConfig;
import com.trs.moye.storage.engine.seatunnel.job.config.source.ClickhouseSourceConfig;
import java.util.List;
import java.util.Objects;

/**
 * Clickhouse 数据源配置
 */
public class ClickhouseSeatunnelJobConfigStrategy implements SeatunnelJobConfigStrategy {

    @Override
    public ClickhouseSourceConfig createSourceConfig(DataSourceConfig dataSourceConfig, String tableName,
        List<DataModelField> fields, IncrementInfo incrementInfo, TaskStartParam taskStartParam) {
        ClickhouseConnectionParams connectionParams = (ClickhouseConnectionParams) dataSourceConfig.getConnection()
            .getConnectionParams();
        Schema schema = Schema.of(fields);
        String query = buildQuery(incrementInfo, connectionParams, tableName);
        return ClickhouseSourceConfig.builder().host(connectionParams.getHost() + ":" + connectionParams.getPort())
            .username(connectionParams.getUsername()).password(connectionParams.getDecryptedPassword()).schema(schema)
            .database(connectionParams.getDatabase()).sql(query).build();
    }

    private String buildQuery(IncrementInfo incrementInfo, ClickhouseConnectionParams connectionParams,
        String tableName) {
        StringBuilder queryBuilder = new StringBuilder("select * from `").append(connectionParams.getDatabase())
            .append("`.`").append(tableName).append("`");
        if (Objects.nonNull(incrementInfo) && Objects.nonNull(incrementInfo.getIncrementValue())) {
            if (!FieldType.INT.equals(incrementInfo.getFieldType())) {
                // 非 int 类型：添加 toString() 转换
                queryBuilder.append(WHERE).append("toString(").append("`").append(incrementInfo.getFieldName())
                    .append("`) > toString('").append(incrementInfo.getIncrementValue()).append("')");
            } else {
                // int 类型：直接比较数值
                queryBuilder.append(WHERE).append("`").append(incrementInfo.getFieldName())
                    .append("` >'").append(incrementInfo.getIncrementValue()).append("'");
            }
        }
        return queryBuilder.toString();
    }

    @Override
    public ClickhouseSinkConfig createSinkConfig(DataConnection connection, DataStorage storage,
        IncrementInfo incrementInfo, List<DataModelField> fields) {
        DataSaveMode saveMode = storage.getSaveMode(DEFAULT_DATA_SAVE_MODE);
        String[] primaryKeyFields = fields.stream()
            .filter(DataModelField::isPrimaryKey)
            .map(DataModelField::getEnName)
            .toArray(String[]::new);
        String primaryKeyField = (primaryKeyFields.length == 0) ? null : primaryKeyFields[0];
        boolean supportUpsert = Objects.nonNull(saveMode) && saveMode.equals(DataSaveMode.CUSTOM_PROCESSING);
        //更新
        ClickhouseConnectionParams connectionParams = (ClickhouseConnectionParams) connection.getConnectionParams();
        return ClickhouseSinkConfig.builder().host(connectionParams.getHost() + ":" + connectionParams.getPort())
            .username(connectionParams.getUsername()).password(connectionParams.getDecryptedPassword())
            .database(connectionParams.getDatabase()).table(storage.getEnName()).primaryKey(primaryKeyField)
            .supportUpsert(supportUpsert).storageName(storage.getConnection().getName()).build();
    }
}
