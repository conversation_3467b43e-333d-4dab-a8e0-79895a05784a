package com.trs.moye.storage.engine.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.trs.moye.base.data.explore.TableExplorationResultEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 表数据探查结果 Mapper 接口
 */
@Mapper
public interface TableExplorationResultMapper extends BaseMapper<TableExplorationResultEntity> {

    /**
     * 根据连接ID和表名查询探查结果
     *
     * @param connectionId 连接ID
     * @param tableName    表名
     * @return TableExplorationResultEntity
     */
    TableExplorationResultEntity findByConnectionIdAndTableName(@Param("connectionId") Integer connectionId,
        @Param("tableName") String tableName);
}

