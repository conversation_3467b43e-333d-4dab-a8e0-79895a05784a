package com.trs.moye.storage.engine.seatunnel.enums;

/**
 * Schema save mode
 *
 * <p>Schema save mode is used to specify the behavior when saving data to a table that does not exist.</p>
 *
 * <AUTHOR>
 */
public enum SchemaSaveMode {

    // Will create when the table does not exist, delete and rebuild when the table is saved
    RECREATE_SCHEMA,

    // Will Created when the table does not exist, skipped when the table is saved
    CREATE_SCHEMA_WHEN_NOT_EXIST,

    // Error will be reported when the table does not exist
    ERROR_WHEN_SCHEMA_NOT_EXIST,
}
