package com.trs.moye.storage.engine.pojo.request.task;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 数据源其他一次性启动参数
 *
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class DatasourceStartParams {

    /**
     * 自定义
     */
    private static final String CUSTOM = "custom";
    /**
     * 最新
     */
    private static final String LATEST = "latest";
    /**
     * 最早
     */
    private static final String EARLIEST = "earliest";


    /**
     * mq消费指定offset
     */
    private Long offset;
    /**
     * offset重置类型 EARLIEST：重置到从最初开始消费 LATEST：重置到从最新开始消费 GROUP_OFFSET：根据默认消费组offset继续消费
     */
    private String offsetResetType;

    /**
     * FTP/SFTP选择的读取文件的开始时间类型 earliest-最早(全量) latest-最新(从当前时间开始) custom-自定义
     */
    private String startTimeType;

    /**
     * FTP/SFTP用户自定义时间
     */
    private String customTime;

    /**
     * 获取文件过滤时间
     *
     * @return 文件过滤时间
     */
    public String getFileFilterTime() {
        switch (startTimeType.toLowerCase()) {
            case LATEST:
                return DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss").format(LocalDateTime.now());
            case CUSTOM:
                return customTime;
            case EARLIEST:
            default:
                return "1970-01-01 00:00:00";
        }
    }
}
