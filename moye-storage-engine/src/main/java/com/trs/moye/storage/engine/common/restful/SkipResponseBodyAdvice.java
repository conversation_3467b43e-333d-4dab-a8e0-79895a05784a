package com.trs.moye.storage.engine.common.restful;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;
import org.springframework.stereotype.Component;

/**
 * 跳过返回参数判断
 *
 * <AUTHOR>
 * @since 2021/1/8 17:36
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
@Component
public @interface SkipResponseBodyAdvice {
}
