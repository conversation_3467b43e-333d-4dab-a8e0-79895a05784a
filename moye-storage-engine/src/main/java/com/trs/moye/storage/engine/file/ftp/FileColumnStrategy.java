package com.trs.moye.storage.engine.file.ftp;

import com.trs.moye.base.data.model.field.mapping.ColumnResponse;
import com.trs.moye.base.data.source.setting.file.FileTypeConfig;
import java.io.InputStream;
import java.util.List;

/**
 * FTP 数据源文件列策略
 */
public interface FileColumnStrategy {

    /**
     * 获取文件列
     *
     * @param fileTypeConfig 文件类型
     * @param inputStream    文件输入流
     * @return 文件列列表
     */
    List<ColumnResponse> getFtpFileColumns(FileTypeConfig fileTypeConfig, InputStream inputStream);
}
