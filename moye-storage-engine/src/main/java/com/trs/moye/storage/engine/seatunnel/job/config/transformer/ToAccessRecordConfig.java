package com.trs.moye.storage.engine.seatunnel.job.config.transformer;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import lombok.Builder;
import lombok.Builder.Default;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.SuperBuilder;

/**
 * 生成记录ID算子配置
 *
 * @since 2025-02-10
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
@SuperBuilder
public class ToAccessRecordConfig extends TransformConfig {

    @JsonProperty("plugin_name")
    @Builder.Default
    private String pluginName = "ToAccessRecord";

    @JsonProperty("storage_names")
    private List<String> storageNames;

    @JsonProperty("plugin_output")
    @Default
    private String pluginOutput = "access_record_output";


    /**
     * 是否记录完整日志
     */
    @JsonProperty("record_complete_logs")
    @Default
    private Boolean recordCompleteLogs = false;
}
