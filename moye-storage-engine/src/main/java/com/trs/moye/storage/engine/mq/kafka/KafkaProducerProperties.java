package com.trs.moye.storage.engine.mq.kafka;

import java.util.List;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @since 2023/7/5 10:11
 */
@Data
@Component
@ConfigurationProperties(prefix = "kafka.send")
public class KafkaProducerProperties {

    /**
     * kafka服务器地址
     */
    private List<String> bootStrapServers;
    /**
     * 分区数
     */
    private Integer partitions;
    /**
     * 副本数
     */
    private Short replicas;
    /**
     * 过期时间
     */
    private String retentionMs;
    /**
     * 发送消息最大长度（bytes）
     */
    private Integer maxRequestSize;

    private String host;

    private Integer port;

}
