package com.trs.moye.storage.engine.pojo.entity;

import com.jcraft.jsch.ChannelSftp.LsEntry;
import com.trs.moye.storage.engine.pojo.constant.UriConstants;
import com.trs.moye.storage.engine.utils.FileUtils;
import lombok.Data;
import org.apache.commons.net.ftp.FTPFile;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 2024-10-20 10:32
 */
@Data
public class FtpFileInfo {

    private String fileName;

    private String filePath;

    private String notSuffixFileName;

    private long timestamp;

    private boolean isDirectory;

    public FtpFileInfo(String workingDirectory, FTPFile ftpFile) {
        this.fileName = ftpFile.getName();
        this.filePath =
            UriConstants.URI_ROOT_PATH.equals(workingDirectory) ? UriConstants.URI_SEPARATOR + ftpFile.getName()
                : workingDirectory + UriConstants.URI_SEPARATOR + ftpFile.getName();
        this.notSuffixFileName = FileUtils.getNotSuffixFileName(ftpFile.getName());
        this.timestamp = ftpFile.getTimestamp().getTimeInMillis();
        this.isDirectory = ftpFile.isDirectory();
    }

    public FtpFileInfo(String workingDirectory, LsEntry ftpFile) {
        this.fileName = ftpFile.getFilename();
        this.filePath =
            UriConstants.URI_ROOT_PATH.equals(workingDirectory) ? UriConstants.URI_SEPARATOR + ftpFile.getFilename()
                : workingDirectory + UriConstants.URI_SEPARATOR + ftpFile.getFilename();
        this.notSuffixFileName = FileUtils.getNotSuffixFileName(ftpFile.getFilename());
        this.timestamp = ftpFile.getAttrs().getMTime() * 1000L;
        this.isDirectory = ftpFile.getAttrs().isDir();
    }
}
