package com.trs.moye.storage.engine.seatunnel.job.config.schema;

import java.util.List;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import lombok.Data;
import org.springframework.validation.annotation.Validated;

/**
 * 主键
 *
 * <AUTHOR>
 */
@Data
@Validated
public class PrimaryKey {

    /**
     * 主键名称
     */
    @NotBlank(message = "主键名称不能为空")
    private String name;
    @Size(min = 1, message = "主键列不能为空")
    private List<String> columns;
}
