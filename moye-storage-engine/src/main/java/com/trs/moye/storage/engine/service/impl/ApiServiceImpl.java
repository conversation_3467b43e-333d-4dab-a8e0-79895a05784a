package com.trs.moye.storage.engine.service.impl;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.type.TypeFactory;
import com.jayway.jsonpath.JsonPath;
import com.trs.moye.base.common.enums.FieldType;
import com.trs.moye.base.common.exception.BizException;
import com.trs.moye.base.common.utils.JsonUtils;
import com.trs.moye.base.data.model.dao.DataModelMapper;
import com.trs.moye.base.data.model.entity.DataModel;
import com.trs.moye.base.data.model.entity.DataSourceConfig;
import com.trs.moye.base.data.model.field.mapping.ColumnResponse;
import com.trs.moye.base.data.source.dao.DataSourceConfigMapper;
import com.trs.moye.base.data.source.enums.RequestMethod;
import com.trs.moye.base.data.source.setting.http.Authorize;
import com.trs.moye.base.data.source.setting.http.HttpDataSourceSettings;
import com.trs.moye.base.data.source.setting.http.RequestParams;
import com.trs.moye.base.data.source.setting.http.UrlInfo;
import com.trs.moye.base.data.storage.IncrementInfo;
import com.trs.moye.storage.engine.client.HttpRestClient;
import com.trs.moye.storage.engine.exception.DataStorageEngineException;
import com.trs.moye.storage.engine.pojo.request.connection.TestConnectionRequest;
import com.trs.moye.storage.engine.pojo.response.ConnectionTestDetailResponse;
import com.trs.moye.storage.engine.service.ApiService;
import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import javax.annotation.Resource;
import javax.validation.constraints.NotNull;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Response;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

/**
 * api接口的实现类
 *
 * <AUTHOR>
 * @since 2024/10/18
 */
@Service
@Slf4j
public class ApiServiceImpl implements ApiService {

    @Resource
    private DataModelMapper dataModelMapper;

    @Resource
    private DataSourceConfigMapper dataSourceConfigMapper;

    @Override
    public boolean testConnection(TestConnectionRequest request) {
        String httpUrl =
            request.getConnectionParams().getProtocol() + "://" + request.getConnectionParams().getHost() + ":"
                + request.getConnectionParams().getPort();
        try {
            return HttpRestClient.doSimpleConnectTest(httpUrl);
        } catch (BizException e) {
            log.error("请求http测试连接接口失败,请求地址 :{}, 异常: {}", httpUrl, e.getMessage());
            return false;
        }
    }

    /**
     * 测试连接 若失败, 返回失败原因
     *
     * @param request 测试连接请求参数
     * @return 测试结果
     */
    @Override
    public ConnectionTestDetailResponse testConnectionWithDetail(TestConnectionRequest request) {
        String httpUrl =
            request.getConnectionParams().getProtocol() + "://" + request.getConnectionParams().getHost() + ":"
            + request.getConnectionParams().getPort();
        try {
            HttpRestClient.doSimpleConnectTest(httpUrl);
            return ConnectionTestDetailResponse.success();
        } catch (Exception e) {
            log.error("请求http测试连接接口失败,请求地址 :{}, 异常: {}", httpUrl, e.getMessage());
            return ConnectionTestDetailResponse.failure(e);
        }
    }

    @Override
    public List<ColumnResponse> getTableFields(Integer connectionId, Integer dataModelId)
        throws JsonProcessingException {
        DataModel dataModel = dataModelMapper.selectById(dataModelId);
        DataSourceConfig dataSourceConfig = dataModel.getFirstDataSource();
        if (Objects.isNull(dataSourceConfig) || Objects.isNull(dataSourceConfig.getSettings())) {
            log.error("数据连接信息为空!");
            return List.of();
        }
        HttpDataSourceSettings httpSettings = getHttpDataSourceSettings(
            dataSourceConfig);
        //requestParam是空的话, 就是鉴权接口，可以直接调用
        if (httpSettings.isTokenRequest()) {
            String authorize = requestAuthorize(httpSettings.getAuthorize());
            return buildAuthorizeColumnResponse(authorize);
        }
        //非鉴权接口，未填result的jsonPath
        IncrementInfo incrementInfo = dataModel.getExecuteConfig().getIncrementInfo();
        String data = requestData(httpSettings, incrementInfo);
        if (Objects.isNull(httpSettings.getRequestParams().getResultPath())
            || httpSettings.getRequestParams().getResultPath().isEmpty()) {
            return buildAuthorizeColumnResponse(data);
        }

        //填写了jsonPath
        ObjectMapper objectMapper = new ObjectMapper();
        Object read = JsonPath.read(data, httpSettings.getRequestParams().getResultPath());
        if (read instanceof List && !((List<?>) read).isEmpty()) {
            Object firstElement = ((List<?>) read).get(0);
            String valueAsString = objectMapper.writeValueAsString(firstElement);
            log.info("valueAsString：{}", valueAsString);
            return buildAuthorizeColumnResponse(valueAsString);
        }
        return List.of();
    }

    @Override
    public String httpField(HttpDataSourceSettings httpSettings) {
        //requestParam是空的话, 就是鉴权接口，可以直接调用
        if (httpSettings.isTokenRequest()) {
            return requestAuthorize(httpSettings.getAuthorize());
        }
        httpSettings.getRequestParams().getRequestBody().buildDefaultPage();
        //非鉴权接口，未填result的jsonPath
        String data = requestData(httpSettings, null);
        if (Objects.isNull(httpSettings.getRequestParams().getResultPath())
            || httpSettings.getRequestParams().getResultPath().isEmpty()) {
            return data;
        }

        //填写了jsonPath
        Object read = JsonPath.read(data, httpSettings.getRequestParams().getResultPath());
        if (read instanceof List && !((List<?>) read).isEmpty()) {
            Object firstElement = ((List<?>) read).get(0);
            String elementAsString = JsonUtils.toJsonString(firstElement);
            log.info("elementAsString：{}", elementAsString);
            return elementAsString;
        }
        return read.toString();
    }

    private static HttpDataSourceSettings getHttpDataSourceSettings(DataSourceConfig dataSourceConfig) {
        return Optional.of(dataSourceConfig)
            .map(DataSourceConfig::getSettings)
            .filter(HttpDataSourceSettings.class::isInstance)
            .map(HttpDataSourceSettings.class::cast)
            .orElseThrow(() -> {
                log.error("数据连接信息为空或不支持该数据源类型!");
                return new RuntimeException("该接口只支持API类型,不支持其他的数据源类型");
            });
    }

    private List<ColumnResponse> buildAuthorizeColumnResponse(String responseDta)
        throws JsonProcessingException {
        ObjectMapper objectMapper = new ObjectMapper();
        List<ColumnResponse> columnResponses = new ArrayList<>();
        TypeFactory typeFactory = objectMapper.getTypeFactory();
        Map<String, Object> readValue = objectMapper.readValue(responseDta,
            typeFactory.constructMapType(LinkedHashMap.class, String.class, Object.class));
        readValue.forEach((key, value) -> {
            ColumnResponse columnResponse = new ColumnResponse();
            columnResponse.setZhName(key);
            columnResponse.setEnName(key);
            columnResponse.setType(FieldType.STRING.name());
            columnResponses.add(columnResponse);
        });
        return columnResponses;
    }

    /**
     * 请求认证接口，返回完整的信息
     *
     * @param authorize 鉴权信息
     * @return 认证信息
     */
    private static String requestAuthorize(Authorize authorize) {
        if (!authorize.isEnable()) {
            return null;
        }
        if (RequestMethod.GET.equals(authorize.getMethod())) {
            return doAuthorizeGetRequest(authorize);
        } else if (RequestMethod.POST.equals(authorize.getMethod())) {
            return doAuthorizePostRequest(authorize);
        } else {
            throw new IllegalArgumentException("不支持的请求方法: " + authorize.getMethod());
        }
    }

    private static String doAuthorizePostRequest(Authorize authorize) {
        Map<String, Object> body = authorize.createBody();
        Map<String, Object> params = authorize.createParams();
        Map<String, String> headers = authorize.createHeaders();
        String url = authorize.getUrl();
        try (Response response = HttpRestClient.post(url, body, params, headers)) {
            if (!response.isSuccessful() || Objects.isNull(response.body())) {
                String errorMsg = String.format(
                    "POST请求认证token失败,url: %s, params:%s, headers: %s, body: %s, response: %s", url, params,
                    headers, body, response.body());
                throw new DataStorageEngineException(errorMsg);
            }
            return response.body().string();
        } catch (IOException exception) {
            String errorMsg = String.format(
                "请求认证token失败:url: %s, params:%s, headers: %s, body: %s, response: %s", url, params,
                headers, body, exception);
            throw new DataStorageEngineException(errorMsg);
        }
    }

    @NotNull
    private static String doAuthorizeGetRequest(Authorize authorize) {
        Map<String, Object> params = authorize.createParams();
        Map<String, String> headers = authorize.createHeaders();
        String url = authorize.getUrl();
        try (Response response = HttpRestClient.get(url, params, headers)) {
            if (!response.isSuccessful() || Objects.isNull(response.body())) {
                String errorMsg = String.format(
                    "GET请求认证token失败,url: %s, params:%s, headers: %s, response: %s", url, params,
                    headers, response.body());
                throw new DataStorageEngineException(errorMsg);
            }
            return response.body().string();
        } catch (IOException exception) {
            String errorMsg = String.format(
                "请求认证token失败,url: %s, params:%s, headers: %s, response: %s", url, params,
                headers, exception);
            throw new DataStorageEngineException(errorMsg);
        }
    }

    /**
     * 获取token
     *
     * @param authorize 鉴权信息
     * @return token
     */
    private static String httpTokenGet(Authorize authorize) {
        String response = requestAuthorize(authorize);
        return StringUtils.isBlank(response) ? null : JsonPath.read(response, authorize.getTokenPath());
    }


    /**
     * 初始化上下文
     *
     * @param httpSettings  http设置
     * @param incrementInfo 增量信息
     * @return 上下文
     */
    public static Map<String, Object> createRequestContext(HttpDataSourceSettings httpSettings,
        IncrementInfo incrementInfo) {
        Map<String, Object> context = new HashMap<>();
        Authorize authorize = httpSettings.getAuthorize();
        //如果开启鉴权，需要提前请求到token
        if (Objects.nonNull(authorize) && authorize.isEnable()) {
            String token = httpTokenGet(authorize);
            if (StringUtils.isNotBlank(token)) {
                context.put("鉴权token", token);
            }
        }

        //如果开启了增量，需要获取增量字段
        if (httpSettings.isIncreaseJob()) {
            if (Objects.nonNull(incrementInfo) && Objects.nonNull(incrementInfo.getIncrementValue())) {
                String lastExecutionTime = incrementInfo.getIncrementValue().toString();
                context.put("时间增量", lastExecutionTime);
                return context;
            }
            RequestParams requestParams = httpSettings.getRequestParams();
            Object defaultValue = requestParams.getRequestBody().getDefaultValue("时间增量");
            if (Objects.nonNull(defaultValue) && !defaultValue.equals("")) {
                context.put("时间增量", defaultValue);
                return context;
            }
            context.put("时间增量", "1970-01-01 00:00:00");
        }
        return context;
    }

    private String requestData(HttpDataSourceSettings httpSettings, IncrementInfo incrementInfo) {
        Map<String, Object> context = createRequestContext(httpSettings, incrementInfo);
        RequestParams requestParams = httpSettings.getRequestParams();
        UrlInfo base = httpSettings.getBase();
        return doRequest(base, requestParams, context);
    }

    /**
     * 执行请求
     *
     * @param requestParams 请求参数
     * @param base          基础url
     * @param context       全局参数
     * @return 请求结果
     */
    private String doRequest(UrlInfo base, RequestParams requestParams, Map<String, Object> context) {
        String url = base.getUrl();
        RequestMethod method = base.getMethod();
        if (RequestMethod.GET.equals(method)) {
            return getRequest(url, requestParams, context);
        } else if (RequestMethod.POST.equals(method)) {
            return postRequest(url, requestParams, context);
        } else {
            throw new IllegalArgumentException("不支持的请求方法: " + method);
        }
    }

    private String getRequest(String uri, RequestParams requestParams, Map<String, Object> context) {
        Map<String, Object> params = requestParams.createQueryParams();
        Map<String, String> headers = requestParams.createHeaders(context);
        try (Response response = HttpRestClient.get(uri, params, headers)) {
            if (!response.isSuccessful() || Objects.isNull(response.body())) {
                String errorMsg = String.format(
                    "GET请求接口数据失败,url: %s, params:%s, headers: %s, response: %s", uri, params, headers,
                    response.body());
                throw new DataStorageEngineException(errorMsg);
            }
            return response.body().string();
        } catch (IOException exception) {
            String errorMsg = String.format(
                "请求接口数据失败,url: %s, params:%s, headers: %s", uri, params, headers);
            throw new DataStorageEngineException(errorMsg);
        }
    }

    private String postRequest(String uri, RequestParams requestParams, Map<String, Object> context) {
        Map<String, Object> body = requestParams.createBody(context);
        Map<String, Object> params = requestParams.createQueryParams();
        Map<String, String> headers = requestParams.createHeaders(context);
        try (Response response = HttpRestClient.post(uri, body, params, headers)) {
            if (!response.isSuccessful() || Objects.isNull(response.body())) {
                String errorMsg = String.format(
                    "POST请求接口数据失败,url: %s, params:%s, headers: %s, body: %s, response: %s",
                    uri, params, headers, body, response.body());
                throw new DataStorageEngineException(errorMsg);
            }
            return response.body().string();
        } catch (IOException exception) {
            String errorMsg = String.format(
                "POST请求接口数据失败,url: %s, params:%s, headers: %s, body: %s",
                uri, params, headers, body);
            throw new DataStorageEngineException(errorMsg);
        }
    }

}
