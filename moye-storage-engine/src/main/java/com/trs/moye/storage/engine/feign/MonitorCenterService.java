package com.trs.moye.storage.engine.feign;

import com.trs.moye.storage.engine.pojo.vo.NoticeVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;


/**
 * notice center调用
 *
 * <AUTHOR>
 */
@FeignClient(name = "moye-monitor-center", configuration = OpenFeignConfig.class)
public interface MonitorCenterService {

    /**
     * 推送storage engine消息
     *
     * @param noticeVO 消息内容
     */
    @PostMapping("/notice-center/storage/send")
    void sendStorageMessage(@RequestBody NoticeVO noticeVO);
}
