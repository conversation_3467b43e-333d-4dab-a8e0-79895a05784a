package com.trs.moye.storage.engine.service;

import com.trs.moye.storage.engine.pojo.request.search.CodeSearchParams;
import com.trs.moye.storage.engine.pojo.request.search.ConditionSearchParams;
import com.trs.moye.storage.engine.pojo.response.PageResult;
import java.util.List;
import java.util.Map;
import org.springframework.stereotype.Service;

/**
 * 查询db服务类
 */
@Service
public interface SearchService {

    /**
     * 查询列表
     *
     * @param connectionId 数据库连接
     * @param tableName    表名
     * @param request      查询参数
     * @return 查询结果
     */
    PageResult<Map<String, Object>> conditionQuery(Integer connectionId, String tableName,
        ConditionSearchParams request);

    /**
     * 执行sql查询
     *
     * @param connectionId 数据库连接
     * @param request      查询参数
     * @return 查询结果
     */
    PageResult<Map<String, Object>> codeQueryByDatabase(Integer connectionId, CodeSearchParams request);

    /**
     * 执行sql代码查询 存储
     *
     * @param connectionId 连接id
     * @param tableName    表名
     * @param request      查询参数
     * @return 查询结果
     */
    PageResult<Map<String, Object>> codeQueryByTable(Integer connectionId, String tableName,
        CodeSearchParams request);


    /**
     * 条件查询 嵌套 sql，即用条件包裹sql查询
     *
     * @param connectionId 连接id
     * @param sql          sql
     * @param searchParams 查询参数
     * @return 搜索结果
     */
    PageResult<Map<String, Object>> conditionalSubquery(Integer connectionId, String sql,
        ConditionSearchParams searchParams);

    /**
     * 代码查询 Nebula 原始数据
     *
     * @param connectionId 连接id
     * @param request      请求
     * @return {@link List }<{@link Map }<{@link String }, {@link Object }>>
     * <AUTHOR>
     * @since 2024/12/23 09:43:41
     */
    PageResult<Map<String, Object>> codeQueryNebulaRawData(Integer connectionId, CodeSearchParams request);

    /**
     * 查询数据量
     *
     * @param connectionId 连接id
     * @param tableName    表名
     * @return 数据量
     */
    Long count(Integer connectionId, String tableName);

    /**
     * 执行sql查询
     *
     * @param connectionId 连接id
     * @param sql          sql
     * @return 查询结果
     */
    List<Map<String, Object>> queryBySql(Integer connectionId, String sql);
}
