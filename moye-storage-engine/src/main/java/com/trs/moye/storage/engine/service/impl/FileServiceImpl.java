package com.trs.moye.storage.engine.service.impl;

import com.trs.moye.base.data.connection.dao.DataConnectionMapper;
import com.trs.moye.base.data.connection.entity.DataConnection;
import com.trs.moye.base.data.model.field.mapping.ColumnResponse;
import com.trs.moye.base.data.model.field.mapping.FieldMappingResponse;
import com.trs.moye.storage.engine.file.FileConnection;
import com.trs.moye.storage.engine.pojo.request.connection.FileColumnRequest;
import com.trs.moye.storage.engine.pojo.request.connection.FileTableRequest;
import com.trs.moye.storage.engine.pojo.request.connection.TestConnectionRequest;
import com.trs.moye.storage.engine.pojo.response.ConnectionTestDetailResponse;
import com.trs.moye.storage.engine.pojo.response.DirectoryAndFileResponse;
import com.trs.moye.storage.engine.service.FileService;
import com.trs.moye.storage.engine.utils.BizUtils;
import com.trs.moye.storage.engine.utils.ConnectionUtils;
import java.util.List;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @since 2024-10-09 21:13
 */
@Slf4j
@Service
public class FileServiceImpl implements FileService {

    public static final String FAIL_MESSAGE = "获取所有表信息失败";
    @Resource
    private DataConnectionMapper dataConnectionMapper;

    @Override
    public boolean testConnection(TestConnectionRequest request) {
        try (FileConnection connection = ConnectionUtils.getConnection(request.getConnectionParams())) {
            return connection.testConnection();
        } catch (Exception exception) {
            log.error(FAIL_MESSAGE, exception);
            throw new IllegalStateException(FAIL_MESSAGE, exception);
        }
    }

    /**
     * 测试连接 若失败, 返回失败原因
     *
     * @param request 连接信息
     * @return 测试结果
     */
    @Override
    public ConnectionTestDetailResponse testConnectionWithDetail(TestConnectionRequest request) {
        try (FileConnection connection = ConnectionUtils.getConnection(request.getConnectionParams())) {
            connection.testConnectionWithException();
            return ConnectionTestDetailResponse.success();
        } catch (Exception e) {
            log.error("连接测试失败", e);
            return ConnectionTestDetailResponse.failure(e);
        }
    }

    @Override
    public DirectoryAndFileResponse getTableList(Integer connectionId, FileTableRequest request) {
        DataConnection dataConnection = dataConnectionMapper.selectById(connectionId);
        try (FileConnection connection = ConnectionUtils.getConnection(dataConnection.getConnectionParams())) {
            return connection.getDirectoryAndFile(request);
        } catch (Exception exception) {
            log.error(FAIL_MESSAGE, exception);
            throw new IllegalStateException(FAIL_MESSAGE, exception);
        }
    }

    @Override
    public FieldMappingResponse getTableFields(Integer connectionId, FileColumnRequest request) {
        DataConnection dataConnection = dataConnectionMapper.selectById(connectionId);
        try (FileConnection connection = ConnectionUtils.getConnection(dataConnection.getConnectionParams())) {
            List<ColumnResponse> fileColumns = connection.getFileColumns(request);
            BizUtils.supplementColumnResponses(fileColumns);
            return new FieldMappingResponse(fileColumns,
                fileColumns.stream().map(BizUtils::convertToMoyeFieldResponse).toList());
        } catch (Exception exception) {
            log.error(FAIL_MESSAGE, exception);
            throw new IllegalStateException(FAIL_MESSAGE, exception);
        }
    }


}
