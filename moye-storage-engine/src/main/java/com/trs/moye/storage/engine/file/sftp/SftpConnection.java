package com.trs.moye.storage.engine.file.sftp;

import com.jcraft.jsch.ChannelSftp;
import com.jcraft.jsch.ChannelSftp.LsEntry;
import com.jcraft.jsch.JSch;
import com.jcraft.jsch.JSchException;
import com.jcraft.jsch.Session;
import com.jcraft.jsch.SftpException;
import com.trs.moye.base.common.exception.BizException;
import com.trs.moye.base.common.utils.DateTimeUtils.Formatter;
import com.trs.moye.base.common.utils.JsonUtils;
import com.trs.moye.base.data.connection.entity.params.ConnectionParams;
import com.trs.moye.base.data.connection.entity.params.FileConnectionParams;
import com.trs.moye.base.data.model.field.mapping.ColumnResponse;
import com.trs.moye.base.data.source.setting.file.FileTypeConfig;
import com.trs.moye.storage.engine.exception.DataStorageEngineException;
import com.trs.moye.storage.engine.file.AbstractFileConnection;
import com.trs.moye.storage.engine.file.ftp.FileColumnStrategy;
import com.trs.moye.storage.engine.file.ftp.FileColumnStrategyFactory;
import com.trs.moye.storage.engine.pojo.constant.FtpConstants;
import com.trs.moye.storage.engine.pojo.entity.FtpFileInfo;
import java.io.IOException;
import java.io.InputStream;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Vector;
import java.util.function.Predicate;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @since 2024-10-09 09:09
 */
@Slf4j
public class SftpConnection extends AbstractFileConnection {

    private final FileConnectionParams fileConnectionParams;
    private final Session session;
    private final ChannelSftp channel;

    public SftpConnection(ConnectionParams params) {
        if (!(params instanceof FileConnectionParams)) {
            throw new DataStorageEngineException(
                "不支持数据库连接类型: " + params.getClass().getName());
        }
        this.fileConnectionParams = (FileConnectionParams) params;
        try {
            JSch jsch = new JSch();
            session = jsch.getSession(fileConnectionParams.getUsername(), fileConnectionParams.getHost(),
                fileConnectionParams.getPort());
            session.setPassword(fileConnectionParams.getDecryptedPassword());
            session.setConfig("StrictHostKeyChecking", "no");
            session.connect(3000);
            channel = (ChannelSftp) session.openChannel("sftp");
            channel.connect();
        } catch (JSchException e) {
            throw new BizException(e, "创建sftp连接发生异常，连接信息：%s", JsonUtils.toJsonString(fileConnectionParams));
        }
    }


    private void closeConnection() {
        if (channel != null) {
            try {
                channel.exit();
            } catch (Exception e) {
                log.error("", e);
            }
        }
        if (session != null) {
            try {
                session.disconnect();
            } catch (Exception e) {
                log.error("", e);
            }
        }
    }

    @Override
    public boolean testConnection() {
        return channel.isConnected();
    }

    /**
     * 测试连接是否可用，抛出异常
     */
    @Override
    public void testConnectionWithException() throws DataStorageEngineException {
        if (!channel.isConnected()) {
            throw new DataStorageEngineException("SFTP通道未连接");
        }
    }

    private void changeWorkingDirectory(String workingDirectory) {
        try {
            channel.cd(workingDirectory);
        } catch (SftpException e) {
            throw new BizException(e, "切换ftp工作目录发生异常，工作目录：%s", workingDirectory);
        }
    }

    @Override
    protected void createDirectory(String workingDirectory, String directoryName) {
        try {
            changeWorkingDirectory(workingDirectory);
            // 检查目录是否已存在
            Vector<LsEntry> files = channel.ls(workingDirectory);
            boolean directoryExists = false;
            if (files != null) {
                for (LsEntry file : files) {
                    if (file.getAttrs().isDir() && directoryName.equals(file.getFilename())) {
                        directoryExists = true;
                        break;
                    }
                }
            }
            
            if (!directoryExists) {
                channel.mkdir(directoryName);
                log.info("SFTP目录创建成功: {}", directoryName);
            } else {
                log.info("SFTP目录已存在，跳过创建: {}", directoryName);
            }
        } catch (SftpException e) {
            // 忽略目录已存在的异常
            if (!"Directory already exists".equals(e.getMessage())) {
                throw new BizException(e, "创建sftp目录发生异常，目录名称：%s", directoryName);
            }
        }
    }

    @Override
    protected void moveFile(String workingDirectory, String fileName, String targetDirectory) {
        try {
            changeWorkingDirectory(workingDirectory);
            channel.rename(fileName, targetDirectory + "/" + fileName);
        } catch (SftpException e) {
            throw new BizException(e, "移动sftp文件发生异常，文件名称：%s，目标目录：%s", fileName, targetDirectory);
        }
    }

    protected void scanFtpFile(String workingDirectory, Predicate<FtpFileInfo> scanPredicate) {
        try {
            changeWorkingDirectory(workingDirectory);
            //说明是路径
            Vector<LsEntry> ftpFiles = channel.ls(workingDirectory);
            if (ftpFiles == null) {
                return;
            }
            for (LsEntry ftpFile : ftpFiles) {
                FtpFileInfo ftpFileInfo = new FtpFileInfo(workingDirectory, ftpFile);
                if (!FtpConstants.IGNORE_FILE_NAME_LIST.contains(ftpFileInfo.getFileName()) && !scanPredicate.test(
                    ftpFileInfo)) {
                    break;
                }
            }
        } catch (SftpException e) {
            throw new BizException(e, "扫描sftp【%s】文件发生异常", JsonUtils.toJsonString(fileConnectionParams));
        }
    }


    @Override
    protected List<ColumnResponse> getFileColumns(FileTypeConfig fileTypeConfig, FtpFileInfo file) {
        try (InputStream inputStream = channel.get(file.getFilePath())) {
            FileColumnStrategy strategy = FileColumnStrategyFactory.getStrategy(fileTypeConfig.getFileType());
            return strategy.getFtpFileColumns(fileTypeConfig, inputStream);
        } catch (SftpException | IOException e) {
            throw new DataStorageEngineException("读取csv文件表头失败，请检查文件格式！" + e);
        }
    }

    @Override
    public Object getMaxValue() {
        return LocalDateTime.now().format(Formatter.YYYY_MM_DD_HH_MM_SS.getDateTimeFormatter());
    }

    @Override
    public void close() {
        closeConnection();
    }

}
