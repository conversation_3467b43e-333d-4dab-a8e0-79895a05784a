package com.trs.moye.storage.engine.db.postgresql;

import com.trs.moye.base.data.model.entity.DataModel;
import com.trs.moye.base.data.model.entity.DataModelField;
import com.trs.moye.storage.engine.exception.DataStorageEngineException;
import java.util.List;
import java.util.Objects;
import org.apache.commons.lang3.StringUtils;

/**
 * Postgresql数据库sql工厂
 */
public class PostgresqlSqlFactory {

    // 私有构造函数，防止实例化
    private PostgresqlSqlFactory() {
        throw new UnsupportedOperationException("This is a utility class and cannot be instantiated");
    }

    private static final String SELECT_COLUMNS_SQL_TEMPLATE = "SELECT A.attname AS COLUMN_NAME,T.typname AS type_name,CASE WHEN A.atttypmod= -1 THEN T.typname WHEN T.typname='varchar' THEN T.typname || '(' || (A.atttypmod-4) || ')' WHEN T.typname='bpchar' THEN 'char' || '(' || (A.atttypmod-4) || ')' WHEN T.typname='numeric' OR T.typname='decimal' THEN T.typname || '(' || ((A.atttypmod-4)>> 16) || ', ' || ((A.atttypmod-4) & 65535) || ')' WHEN T.typname='bit' OR T.typname='bit varying' THEN T.typname || '(' || (A.atttypmod-4) || ')' WHEN T.typname IN ('time','timetz','timestamp','timestamptz') THEN T.typname || '(' || A.atttypmod || ')' ELSE T.typname || '' END AS full_type_name,CASE WHEN A.atttypmod= -1 THEN NULL WHEN T.typname IN ('varchar','bpchar','bit','bit varying') THEN A.atttypmod-4 WHEN T.typname IN ('numeric','decimal') THEN (A.atttypmod-4)>> 16 ELSE NULL END AS column_length,CASE WHEN A.atttypmod= -1 THEN NULL WHEN T.typname IN ('numeric','decimal') THEN (A.atttypmod-4) & 65535 WHEN T.typname IN ('time','timetz','timestamp','timestamptz') THEN A.atttypmod ELSE NULL END AS column_scale,d.description AS column_comment,pg_get_expr (ad.adbin,ad.adrelid) AS default_value,CASE WHEN A.attnotnull THEN 'NO' ELSE 'YES' END AS is_nullable FROM pg_class C JOIN pg_namespace n ON C.relnamespace=n.OID JOIN pg_attribute A ON A.attrelid=C.OID JOIN pg_type T ON A.atttypid=T.OID LEFT JOIN pg_description d ON C.OID=d.objoid AND A.attnum=d.objsubid LEFT JOIN pg_attrdef ad ON A.attnum=ad.adnum AND A.attrelid=ad.adrelid WHERE n.nspname='%s' AND C.relname='%s' AND A.attnum> 0 ORDER BY A.attnum;";

    /***
     * 创建查询表信息的sql
     *
     * @param schema schema
     * @param tableName 表名
     * @return sql
     */
    public static String createTableInfoSql(String schema, String tableName) {
        return String.format(SELECT_COLUMNS_SQL_TEMPLATE, schema, tableName);
    }

    /**
     * 查询表注释
     *
     * @param schema    schema
     * @param tableName 表名
     * @return sql
     */
    public static String createTableCommentSql(String schema, String tableName) {
        return String.format(String.format(
            String.format("SELECT obj_description(to_regclass('%s.%s')) AS table_comment", schema, tableName)));
    }

    /**
     * 创建查询所有表名的sql
     *
     * @param schema schema
     * @return sql
     */
    public static String createAllTableNamesAndCommentsSql(String schema) {
        return String.format("""
            SELECT
                c.relname AS table_name,
                obj_description(c.oid) AS table_comment
            FROM
                pg_class c
            LEFT JOIN
                pg_namespace n ON n.oid = c.relnamespace
            WHERE
                n.nspname='%s';
            """, schema);
    }

    /**
     * 创建ddl的sql
     *
     * @param table  表元数据
     * @param schema schema
     * @return ddl sql
     */
    public static String ddlSql(DataModel table, String schema) {
        StringBuilder createTableSql = new StringBuilder();
        createTableSql.append("CREATE TABLE ")
            .append(schema)
            .append(".")
            .append(table.getEnName())
            .append(" (\n");

        List<DataModelField> fields = table.getFields();
        List<String> columnSql = fields.stream().map(e -> PostgresqlSqlFactory.buildColumnSql(e, true))
            .toList();
        createTableSql.append(String.join(",\n", columnSql)).append("\n);\n");

        //注释
        fields.stream()
            .filter(column -> StringUtils.isNotBlank(column.getDescription()))
            .map(columns -> PostgresqlSqlFactory.buildColumnCommentSql(columns, table.getEnName(), schema))
            .forEach(createTableSql::append);
        return createTableSql.toString();
    }

    /**
     * 构建添加字段的sql
     *
     * @param tableName 表名
     * @param fields    字段信息
     * @return sql
     */
    public static String buildAddColumnSql(String tableName, List<DataModelField> fields) {
        StringBuilder addColumnSql = new StringBuilder();
        for (DataModelField field : fields) {
            addColumnSql.append("ALTER TABLE ")
                .append(tableName)
                .append(" ADD COLUMN ")
                .append(buildColumnSql(field, false))
                .append(";\n");
        }
        return addColumnSql.toString();
    }

    /**
     * 构建字段sql
     *
     * @param column          字段
     * @param createTableFlag 是否是创建表的请求
     * @return sql
     */
    private static String buildColumnSql(DataModelField column, boolean createTableFlag) {
        StringBuilder columnSql = new StringBuilder();
        columnSql.append("\"").append(column.getEnName()).append("\" ");

        // For simplicity, assume the column type in SeaTunnelDataType is the same as in PostgreSQL
        PostgresTypeConverter converter = new PostgresTypeConverter();
        String columnType = converter.reconvert(column).getColumnType();
        columnSql.append(columnType);

        // Add NOT NULL if column is not nullable
        if (!column.isNullable()) {
            if (Objects.isNull(column.getDefaultValue()) && !createTableFlag) {
                throw new DataStorageEngineException(
                    "Postgresql数据库类型非空字段新增时必须要有默认值! Column :" + column.getEnName() + "默认值为空");
            }
            columnSql.append(
                Objects.nonNull(column.getDefaultValue()) ? " DEFAULT '" + column.getDefaultValue() + "' NOT NULL "
                    : " NOT NULL ");
        }

        // Add primary key directly after the column if it is a primary key
        if (column.isPrimaryKey()) {
            columnSql.append(" PRIMARY KEY");
        }

        return columnSql.toString();
    }

    /**
     * 构建字段注释sql
     *
     * @param column    字段
     * @param tableName 表名
     * @param schema    模式名
     * @return sql
     */
    private static String buildColumnCommentSql(DataModelField column, String tableName, String schema) {
        return "COMMENT ON COLUMN "
            + schema
            + "."
            + tableName
            + "."
            + String.format("\"%s\"", column.getEnName())
            + " IS '"
            + column.getDescription().replace("'", "''")
            + "';\n";
    }

    /**
     * 获取最大值的sql
     *
     * @param schema    schema
     * @param dbName    库名
     * @param tableName 表名
     * @param filedName 字段名
     * @return 最大值sql
     */
    public static String buildMaxValueSql(String schema, String dbName, String tableName, String filedName) {
        return "select max(  "
            + filedName + ") as \"maxValue\" from "
            + dbName
            + "."
            + schema
            + "."
            + tableName;
    }
}
