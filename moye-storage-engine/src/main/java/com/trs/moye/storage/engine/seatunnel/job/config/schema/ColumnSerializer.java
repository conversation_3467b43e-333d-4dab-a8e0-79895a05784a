package com.trs.moye.storage.engine.seatunnel.job.config.schema;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;
import java.io.IOException;

/**
 * Column对象序列化器，处理多值并封装成数组
 */
public class ColumnSerializer extends JsonSerializer<Column> {

    @Override
    public void serialize(Column column, JsonGenerator gen, SerializerProvider provider) throws IOException {
        gen.writeStartObject();
        gen.writeStringField("name", column.getName());
        String type = column.getType().getType();
        if (column.isArray() && !column.isVectorType()) {
            gen.writeStringField("type", "array<" + type + ">");
        } else {
            gen.writeStringField("type", type);
        }
        if (column.getColumnLength() != null) {
            gen.writeNumberField("columnLength", column.getColumnLength());
        }
        if (column.getColumnScale() != null) {
            gen.writeNumberField("columnScale", column.getColumnScale());
        }
        gen.writeBooleanField("nullable", column.isNullable());
        if (column.getComment() != null) {
            gen.writeStringField("comment", column.getComment());
        }
        gen.writeEndObject();
    }
}
