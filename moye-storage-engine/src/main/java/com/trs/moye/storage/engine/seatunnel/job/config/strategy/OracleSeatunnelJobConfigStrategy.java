package com.trs.moye.storage.engine.seatunnel.job.config.strategy;

import com.trs.moye.base.data.connection.entity.DataConnection;
import com.trs.moye.base.data.connection.entity.params.OracleConnectionParams;
import com.trs.moye.base.data.model.entity.DataModelField;
import com.trs.moye.base.data.model.entity.DataSourceConfig;
import com.trs.moye.base.data.model.enums.DataSaveMode;
import com.trs.moye.base.data.storage.DataStorage;
import com.trs.moye.base.data.storage.IncrementInfo;
import com.trs.moye.storage.engine.pojo.request.task.TaskStartParam;
import com.trs.moye.storage.engine.seatunnel.enums.SchemaSaveMode;
import com.trs.moye.storage.engine.seatunnel.job.config.schema.Schema;
import com.trs.moye.storage.engine.seatunnel.job.config.sink.OracleSinkConfig;
import com.trs.moye.storage.engine.seatunnel.job.config.source.OracleSourceConfig;
import java.util.List;
import java.util.Objects;

/**
 * Hive 数据源配置
 */
public class OracleSeatunnelJobConfigStrategy implements SeatunnelJobConfigStrategy {

    @Override
    public OracleSourceConfig createSourceConfig(DataSourceConfig dataSourceConfig, String tableName,
        List<DataModelField> fields, IncrementInfo incrementInfo, TaskStartParam taskStartParam) {
        OracleConnectionParams connectionParams = (OracleConnectionParams) dataSourceConfig.getConnection()
            .getConnectionParams();
        String url = connectionParams.getJdbcUrl(null);
        Schema schema = Schema.of(fields);
        String query = buildQuery(incrementInfo, connectionParams, tableName);
        return OracleSourceConfig.builder().url(url).user(connectionParams.getUsername())
            .password(connectionParams.getDecryptedPassword()).query(query).schema(schema).build();
    }

    private String buildQuery(IncrementInfo incrementInfo, OracleConnectionParams connectionParams,
        String tableName) {
        StringBuilder queryBuilder = new StringBuilder("select * from \"").append(
            connectionParams.getSchema().toUpperCase()).append(DOUBLE_QUOTES).append(tableName).append("\"");
        if (Objects.nonNull(incrementInfo) && Objects.nonNull(incrementInfo.getIncrementValue())) {
            // 执行增量查询
            if (incrementInfo.isNumberType()) {
                queryBuilder.append(WHERE).append(incrementInfo.getFieldName()).append(" > ")
                    .append(incrementInfo.getIncrementValue());
            } else if (incrementInfo.isDateType()) {
                queryBuilder.append(WHERE).append(" \"").append(incrementInfo.getFieldName())
                    .append("\" >  TO_DATE('").append(incrementInfo.getIncrementValue())
                    .append("', 'YYYY-MM-DD HH24:MI:SS')");
            } else {
                queryBuilder.append(WHERE).append(incrementInfo.getFieldName()).append(" > '")
                    .append(incrementInfo.getIncrementValue()).append("'");
            }
        }
        return queryBuilder.toString();
    }

    @Override
    public OracleSinkConfig createSinkConfig(DataConnection connection, DataStorage storage,
        IncrementInfo incrementInfo, List<DataModelField> fields) {
        DataSaveMode saveMode = storage.getSaveMode(DEFAULT_DATA_SAVE_MODE);
        OracleConnectionParams connectionParams = (OracleConnectionParams) connection.getConnectionParams();
        String url = connectionParams.getJdbcUrl(null);
        String[] primaryKeyFields = getKeyFields(fields);
        //如果是更新
        boolean enableUpsert = false;
        boolean supportUpsertByQueryPrimaryKeyExist = false;
        if (primaryKeyFields != null && saveMode.equals(DataSaveMode.CUSTOM_PROCESSING)) {
            enableUpsert = true;
            supportUpsertByQueryPrimaryKeyExist = true;
            saveMode = DataSaveMode.APPEND_DATA;
        }

        return OracleSinkConfig.builder().url(url).user(connectionParams.getUsername())
            .password(connectionParams.getDecryptedPassword()).database(connectionParams.getServiceName())
            .table(connectionParams.getSchema().toUpperCase() + "." + storage.getEnName())
            .schemaSaveMode(SchemaSaveMode.CREATE_SCHEMA_WHEN_NOT_EXIST)
            .dataSaveMode(saveMode)
            .primaryKeys(primaryKeyFields)
            .supportUpsertByQueryPrimaryKeyExist(supportUpsertByQueryPrimaryKeyExist)
            .enableUpsert(enableUpsert)
            .storageName(storage.getConnection().getName())
            .generateSinkSql(true)
            .query(null)
            .build();
    }
}
