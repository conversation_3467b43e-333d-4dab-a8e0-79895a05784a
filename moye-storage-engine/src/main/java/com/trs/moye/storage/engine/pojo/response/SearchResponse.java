package com.trs.moye.storage.engine.pojo.response;

import java.util.List;
import java.util.Map;
import lombok.Builder;
import lombok.Data;

/**
 * 异常返回消息体
 *
 * <p>异常返回消息体用于封装异常返回的消息</p>
 *
 * <AUTHOR>
 */
@Data
@Builder
public class SearchResponse {

    private Boolean success;
    private String message;
    private List<Map<String, Object>> data;


    /**
     * 成功返回消息体
     *
     * @param data 返回数据
     * @return RestfulResponse
     */
    public static SearchResponse success(List<Map<String, Object>> data) {
        return SearchResponse.builder().success(true).message("操作成功").data(data).build();
    }

    /**
     * 失败返回消息体
     *
     * @param message 失败消息
     * @return RestfulResponse
     */
    public static SearchResponse failed(String message) {
        return SearchResponse.builder().success(false).message(message).build();
    }

    /**
     * 失败返回消息体
     *
     * @param exception 异常
     * @return RestfulResponse
     */
    public static SearchResponse failed(Exception exception) {
        return SearchResponse.failed("操作失败:" + exception.getMessage());
    }
}
