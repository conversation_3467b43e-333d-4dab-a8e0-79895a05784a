package com.trs.moye.storage.engine.service.impl;

import com.trs.moye.storage.engine.feign.MonitorCenterService;
import com.trs.moye.storage.engine.pojo.entity.StorageTask;
import com.trs.moye.storage.engine.pojo.vo.NoticeVO;
import com.trs.moye.storage.engine.seatunnel.client.SeaTunnelHttpClient;
import java.time.LocalDateTime;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 告警中心发送消息
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class NoticeService {

    @Resource
    private MonitorCenterService monitorCenterService;

    /**
     * 发送告警消息
     *
     * @param storageTask 任务信息
     * @param errMessage  错误信息
     */
    public void sendNotice(StorageTask storageTask, String errMessage) {
        NoticeVO vo = new NoticeVO();
        vo.setMetadataId(storageTask.getDataModelId());
        vo.setBatchNo(storageTask.getBatchNo());
        vo.setErrMessage(SeaTunnelHttpClient.getSeaTunnelExceptionMsg(errMessage));
        vo.setPublishTime(LocalDateTime.now());
        try {
            log.info("StorageNotice-- 发送消息到告警中心: {}", vo);
            monitorCenterService.sendStorageMessage(vo);
            log.info("发送消息到告警中心成功: {}", vo);
        } catch (Exception e) {
            log.error("发送消息到告警中心发生异常", e);
        }
    }
}
