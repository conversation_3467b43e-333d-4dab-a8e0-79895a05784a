package com.trs.moye.storage.engine.db.postgresql;

import com.google.common.base.Preconditions;
import com.trs.moye.base.data.model.entity.DataModelField;
import com.trs.moye.base.common.entity.field.DecimalAdvanceConfig;
import com.trs.moye.base.common.entity.field.FieldAdvanceConfig;
import com.trs.moye.base.common.entity.field.StringAdvanceConfig;
import com.trs.moye.base.common.enums.FieldType;
import com.trs.moye.storage.engine.db.BasicTypeDefine;
import com.trs.moye.storage.engine.db.BasicTypeDefine.BasicTypeDefineBuilder;
import com.trs.moye.storage.engine.db.TypeConverter;
import com.trs.moye.storage.engine.exception.DataStorageEngineException;
import com.trs.moye.storage.engine.utils.BizUtils;
import java.util.Objects;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;

/**
 * PostgresSQL 数据类型转换器
 */
@Slf4j
public class PostgresTypeConverter implements TypeConverter<BasicTypeDefine> {

    // Postgres jdbc driver maps several alias to real type, we use real type rather than alias:
    // boolean <=> bool
    public static final String PG_BOOLEAN = "bool";
    // bool[] <=> boolean[] <=> _bool
    public static final String PG_BOOLEAN_ARRAY = "_bool";
    public static final String PG_BYTEA = "bytea";
    // smallint <=> smallserial <=> int2
    public static final String PG_SMALLINT = "int2";
    public static final String PG_SMALLSERIAL = "smallserial";
    // smallint[] <=> int2[] <=> _int2
    public static final String PG_SMALLINT_ARRAY = "_int2";
    // integer <=> serial <=> int <=> int4
    public static final String PG_INTEGER = "int4";
    public static final String PG_SERIAL = "serial";
    // integer[] <=> int[] <=> _int4
    public static final String PG_INTEGER_ARRAY = "_int4";
    // bigint <=> bigserial <=> int8
    public static final String PG_BIGINT = "int8";
    public static final String PG_BIGSERIAL = "bigserial";
    // bigint[] <=> _int8
    public static final String PG_BIGINT_ARRAY = "_int8";
    // real <=> float4
    public static final String PG_REAL = "float4";
    // real[] <=> _float4
    public static final String PG_REAL_ARRAY = "_float4";
    // double precision <=> float8
    public static final String PG_DOUBLE_PRECISION = "float8";
    // double precision[] <=> _float8
    public static final String PG_DOUBLE_PRECISION_ARRAY = "_float8";
    // numeric <=> decimal
    public static final String PG_NUMERIC = "numeric";

    // money
    public static final String PG_MONEY = "money";

    // char <=> character <=> bpchar
    public static final String PG_CHAR = "bpchar";
    public static final String PG_CHARACTER = "character";
    // char[] <=> _character <=> _bpchar
    public static final String PG_CHAR_ARRAY = "_bpchar";
    // character varying <=> varchar
    public static final String PG_VARCHAR = "varchar";
    public static final String PG_CHARACTER_VARYING = "character varying";
    // character varying[] <=> varchar[] <=> _varchar
    public static final String PG_VARCHAR_ARRAY = "_varchar";
    public static final String PG_TEXT = "text";
    public static final String PG_TEXT_ARRAY = "_text";
    public static final String PG_JSON = "json";
    public static final String PG_JSONB = "jsonb";
    public static final String PG_XML = "xml";
    public static final String PG_UUID = "uuid";
    private static final String PG_GEOMETRY = "geometry";
    private static final String PG_GEOGRAPHY = "geography";
    public static final String PG_DATE = "date";
    // time without time zone <=> time
    public static final String PG_TIME = "time";
    // time with time zone <=> timetz
    public static final String PG_TIME_TZ = "timetz";
    // timestamp without time zone <=> timestamp
    public static final String PG_TIMESTAMP = "timestamp";
    // timestamp with time zone <=> timestamptz
    public static final String PG_TIMESTAMP_TZ = "timestamptz";

    public static final int MAX_PRECISION = 1000;
    public static final int DEFAULT_PRECISION = 38;
    public static final int MAX_SCALE = MAX_PRECISION - 1;
    public static final int DEFAULT_SCALE = 18;
    public static final int MAX_TIME_SCALE = 6;
    public static final int MAX_TIMESTAMP_SCALE = 6;
    public static final int MAX_VARCHAR_LENGTH = 10485760;
    public static final String FORMAT = "%s(%s)";

    @Override
    public DataModelField convert(BasicTypeDefine typeDefine) {
        DataModelField.DataModelFieldBuilder<?,?> builder =
            DataModelField.builder()
                .enName(typeDefine.getName())
                .isNullable(typeDefine.isNullable())
                .zhName(typeDefine.getComment());
        String pgDataType = typeDefine.getDataType().toLowerCase();
        switch (pgDataType) {
            case PG_BOOLEAN -> builder.type(FieldType.BOOLEAN);
            case PG_SMALLSERIAL, PG_SMALLINT -> builder.type(FieldType.SHORT);
            case PG_INTEGER, PG_SERIAL -> builder.type(FieldType.INT);
            case PG_BIGINT, PG_BIGSERIAL -> builder.type(FieldType.LONG);
            case PG_REAL -> builder.type(FieldType.FLOAT);
            case PG_DOUBLE_PRECISION -> builder.type(FieldType.DOUBLE);
            case PG_NUMERIC -> {
                Preconditions.checkArgument(typeDefine.getPrecision() > 0);
                builder.type(FieldType.DECIMAL);
                builder.advanceConfig(getDecimalAdvanceConfig(typeDefine));
            }
            case PG_MONEY -> {
                builder.type(FieldType.DECIMAL);
                DecimalAdvanceConfig moneyConfig = new DecimalAdvanceConfig();
                moneyConfig.setAccuracy(30);
                moneyConfig.setScale(2);
                builder.advanceConfig(moneyConfig);
            }
            case PG_CHAR, PG_CHARACTER, PG_VARCHAR, PG_CHARACTER_VARYING -> {
                builder.type(FieldType.STRING);
                StringAdvanceConfig charConfig = new StringAdvanceConfig();
                if (typeDefine.getLength() == null || typeDefine.getLength() <= 0) {
                    charConfig.setMaxLength(1L);
                } else {
                    charConfig.setMaxLength(typeDefine.getLength());
                }
                builder.advanceConfig(charConfig);
            }
            case PG_TEXT -> builder.type(FieldType.TEXT);
            case PG_UUID -> {
                builder.type(FieldType.STRING);
                StringAdvanceConfig uuidConfig = new StringAdvanceConfig();
                uuidConfig.setMaxLength(128L);
                builder.advanceConfig(uuidConfig);
            }
            case PG_JSON, PG_JSONB, PG_XML, PG_GEOMETRY, PG_GEOGRAPHY -> builder.type(FieldType.STRING);
            case PG_DATE -> builder.type(FieldType.DATE);
            case PG_TIME, PG_TIME_TZ -> builder.type(FieldType.TIME);
            case PG_TIMESTAMP, PG_TIMESTAMP_TZ -> builder.type(FieldType.DATETIME);
            default -> throw new DataStorageEngineException(
                String.format("不支持的数据类型: %s", pgDataType));
        }
        return BizUtils.buildAndSupplementAdvanceConfig(builder);
    }

    @NotNull
    private static DecimalAdvanceConfig getDecimalAdvanceConfig(BasicTypeDefine typeDefine) {
        DecimalAdvanceConfig decimalAdvanceConfig = new DecimalAdvanceConfig();
        decimalAdvanceConfig.setAccuracy(typeDefine.getPrecision());
        if (typeDefine.getPrecision() > DEFAULT_PRECISION) {
            decimalAdvanceConfig.setAccuracy(DEFAULT_PRECISION);
            decimalAdvanceConfig.setScale(DEFAULT_SCALE);
        } else {
            decimalAdvanceConfig.setAccuracy(typeDefine.getPrecision());
            decimalAdvanceConfig.setScale(typeDefine.getScale() == null ? 0 : typeDefine.getScale());
        }
        return decimalAdvanceConfig;
    }

    @Override
    public BasicTypeDefine reconvert(DataModelField column) {
        FieldAdvanceConfig config = column.getAdvanceConfig();
        BasicTypeDefine.BasicTypeDefineBuilder builder =
            BasicTypeDefine.builder()
                .name(column.getEnName())
                .nullable(column.isNullable())
                .comment(column.getZhName());
        switch (column.getType()) {
            case BOOLEAN -> {
                builder.columnType(PG_BOOLEAN);
                builder.dataType(PG_BOOLEAN);
            }
            case SHORT -> {
                builder.columnType(PG_SMALLINT);
                builder.dataType(PG_SMALLINT);
            }
            case INT -> {
                builder.columnType(PG_INTEGER);
                builder.dataType(PG_INTEGER);
            }
            case LONG -> {
                builder.columnType(PG_BIGINT);
                builder.dataType(PG_BIGINT);
            }
            case FLOAT -> {
                builder.columnType(PG_REAL);
                builder.dataType(PG_REAL);
            }
            case DOUBLE -> {
                builder.columnType(PG_DOUBLE_PRECISION);
                builder.dataType(PG_DOUBLE_PRECISION);
            }
            case DECIMAL -> buildDecimal(config, builder);
            case CHAR -> {
                builder.columnType(PG_BYTEA);
                builder.dataType(PG_BYTEA);
            }
            case STRING -> buildString(config, builder);
            case DATE -> {
                builder.columnType(PG_DATE);
                builder.dataType(PG_DATE);
            }
            case TIME -> buildTime(config, builder);
            case DATETIME -> buildDateTime(config, builder);
            case TEXT -> {
                builder.columnType(PG_TEXT);
                builder.dataType(PG_TEXT);
            }
            default -> throw new DataStorageEngineException(
                String.format("不支持的数据类型: %s", column.getType()));
        }
        return builder.build();
    }

    private static void buildDateTime(FieldAdvanceConfig config, BasicTypeDefineBuilder builder) {
        if (!(config instanceof DecimalAdvanceConfig stringConfig)) {
            builder.dataType(PG_TIMESTAMP);
            builder.columnType(PG_TIMESTAMP);
            return;
        }
        Integer timestampScale = stringConfig.getScale();
        if (timestampScale != null && timestampScale > MAX_TIMESTAMP_SCALE) {
            timestampScale = MAX_TIMESTAMP_SCALE;
        }
        if (timestampScale != null && timestampScale > 0) {
            builder.columnType(String.format(FORMAT, PG_TIMESTAMP, timestampScale));
        } else {
            builder.columnType(PG_TIMESTAMP);
        }
        builder.scale(timestampScale);
    }

    private static void buildTime(FieldAdvanceConfig config, BasicTypeDefineBuilder builder) {
        if (!(config instanceof DecimalAdvanceConfig stringConfig)) {
            builder.dataType(PG_TIME);
            builder.columnType(PG_TIME);
            return;
        }
        Integer timeScale = stringConfig.getScale();
        if (timeScale != null && timeScale > MAX_TIME_SCALE) {
            timeScale = MAX_TIME_SCALE;
        }
        if (timeScale != null && timeScale > 0) {
            builder.columnType(String.format(FORMAT, PG_TIME, timeScale));
        } else {
            builder.columnType(PG_TIME);
        }
        builder.scale(timeScale);
    }

    private static void buildString(FieldAdvanceConfig config, BasicTypeDefineBuilder builder) {
        if (Objects.isNull(config) || !(config instanceof StringAdvanceConfig stringConfig)) {
            builder.columnType(PG_TEXT);
            builder.dataType(PG_TEXT);
            return;
        }
        if (stringConfig.getMaxLength() != null && stringConfig.getMaxLength() <= MAX_VARCHAR_LENGTH) {
            builder.columnType(
                String.format(FORMAT, PG_VARCHAR, stringConfig.getMaxLength()));
            builder.dataType(PG_VARCHAR);
        } else {
            builder.columnType(PG_TEXT);
            builder.dataType(PG_TEXT);
        }
    }

    private static void buildDecimal(FieldAdvanceConfig config, BasicTypeDefineBuilder builder) {
        if (Objects.isNull(config) || !(config instanceof DecimalAdvanceConfig decimalConfig)) {
            builder.columnType(String.format("%s(%s,%s)", PG_NUMERIC, DEFAULT_PRECISION, DEFAULT_SCALE));
            builder.dataType(PG_NUMERIC);
            return;
        }
        int precision = decimalConfig.getAccuracy();
        int scale = decimalConfig.getScale();
        if (precision <= 0) {
            precision = DEFAULT_PRECISION;
            scale = DEFAULT_SCALE;
        } else if (precision > MAX_PRECISION) {
            scale = Math.max(0, scale - (precision - MAX_PRECISION));
            precision = MAX_PRECISION;
        }
        if (scale < 0) {
            scale = 0;
        } else if (scale > MAX_SCALE) {
            scale = MAX_SCALE;
        }
        builder.columnType(String.format("%s(%s,%s)", PG_NUMERIC, precision, scale));
        builder.precision(precision);
        builder.scale(scale);
        builder.dataType(PG_NUMERIC);
    }
}
