package com.trs.moye.storage.engine.controller;

import com.trs.moye.base.data.model.field.mapping.FieldMappingResponse;
import com.trs.moye.storage.engine.pojo.request.connection.CreateTableRequest;
import com.trs.moye.storage.engine.pojo.request.connection.TestConnectionRequest;
import com.trs.moye.storage.engine.pojo.response.ConnectionTestDetailResponse;
import com.trs.moye.storage.engine.pojo.response.StorageEngineResponse;
import com.trs.moye.storage.engine.pojo.response.alltable.TableResponse;
import com.trs.moye.storage.engine.service.MqConnectionService;
import java.util.List;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * mq连接
 */
@RequestMapping("/connection/mq")
@RestController
@Slf4j
public class MqConnectionController {

    @Resource
    private MqConnectionService connectionService;

    /**
     * 测试连接
     *
     * @param request 测试连接请求
     * @return 测试连接结果
     */
    @PostMapping("/test")
    public boolean testConnection(@RequestBody TestConnectionRequest request) {
        log.info("测试连接请求：{}", request);
        return connectionService.testConnection(request);
    }

    /**
     * 测试链接
     * 若失败, 返回失败原因
     *
     * @param request 测试连接请求
     * @return 测试连接结果
     */
    @PostMapping("/test-with-detail")
    public ConnectionTestDetailResponse testConnectionWithDetail(@RequestBody TestConnectionRequest request) {
        log.info("测试连接请求：{}", request);
        return connectionService.testConnectionWithDetail(request);
    }

    /**
     * 获取所有表
     *
     * @param connectionId 连接id
     * @return {@link TableResponse}
     */
    @GetMapping("/{connectionId}/table-list")
    public List<TableResponse> getAllTable(@PathVariable("connectionId") Integer connectionId) {
        return connectionService.getAllTables(connectionId);
    }

    /**
     * 获取表的字段信息
     *
     * @param connectionId 连接id
     * @param tableName    表名
     * @return {@link FieldMappingResponse}
     */
    @GetMapping("/{connectionId}/table-fields")
    FieldMappingResponse getTableFields(@PathVariable("connectionId") Integer connectionId,
        @RequestParam String tableName) {
        return connectionService.getTableFields(connectionId, tableName);
    }

    /**
     * 建表
     *
     * @param connectionId       连接id
     * @param createTableRequest 建表请求
     * @return 成功或失败
     */
    @PostMapping("/{connectionId}/create-table")
    StorageEngineResponse createTable(@PathVariable("connectionId") Integer connectionId,
        @RequestBody CreateTableRequest createTableRequest) {
        return connectionService.createTopicIfNotExists(connectionId, createTableRequest.getDataModelId(),
            createTableRequest.getDataStorageId(),
            createTableRequest.getSettings());
    }

    /**
     * 获取时间点之后的数据量
     *
     * @param connectionId 连接id
     * @param topic        主题名
     * @param time         时间点 格式为 yyyy-MM-dd HH:mm:ss
     * @return 数据量
     */
    @GetMapping("/{connectionId}/data-count/time-point-after")
    long getTimePointAfterDataCount(@PathVariable Integer connectionId,
        @RequestParam String topic, @RequestParam String time) {
        return connectionService.getTimePointAfterDataCount(connectionId, topic, time);
    }

    /**
     * 获取总数据量
     *
     * @param connectionId 连接id
     * @param topic        主题名
     * @return 数据量
     */
    @GetMapping("/{connectionId}/data-count/total")
    public long getDataTotalCount(@PathVariable Integer connectionId,
        @RequestParam String topic) {
        return connectionService.getDataTotalCount(connectionId, topic);
    }
}
