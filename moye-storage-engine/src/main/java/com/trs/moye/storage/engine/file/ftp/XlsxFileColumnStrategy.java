package com.trs.moye.storage.engine.file.ftp;

import static com.trs.moye.storage.engine.file.ftp.XlsFileColumnStrategy.parseExcelWorkbook;

import com.trs.moye.base.data.model.field.mapping.ColumnResponse;
import com.trs.moye.base.data.source.setting.file.FileTypeConfig;
import java.io.IOException;
import java.io.InputStream;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

/**
 * Xlsx文件列策略
 */
@Slf4j
public class XlsxFileColumnStrategy implements FileColumnStrategy {

    @Override
    public List<ColumnResponse> getFtpFileColumns(FileTypeConfig fileTypeConfig, InputStream inputStream) {
        try (Workbook workbook = new XSSFWorkbook(inputStream)) {
            return parseExcelWorkbook(workbook);
        } catch (IOException e) {
            log.error("读取xls文件失败: {}", e.getMessage());
            return List.of();
        }
    }
}
