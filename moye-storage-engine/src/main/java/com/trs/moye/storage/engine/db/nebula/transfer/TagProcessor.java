package com.trs.moye.storage.engine.db.nebula.transfer;

import com.trs.moye.storage.engine.pojo.constant.NebulaEntity;
import com.vesoft.nebula.client.graph.SessionPool;
import com.vesoft.nebula.client.graph.data.ResultSet;
import com.vesoft.nebula.client.graph.exception.AuthFailedException;
import com.vesoft.nebula.client.graph.exception.BindSpaceFailedException;
import com.vesoft.nebula.client.graph.exception.ClientServerIncompatibleException;
import com.vesoft.nebula.client.graph.exception.IOErrorException;
import java.util.List;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Component;
import com.vesoft.nebula.client.graph.data.ResultSet.Record;
/**
 * 标签处理器
 *
 * <AUTHOR>
 * @since 2024/12/27 15:23:44
 */
@Slf4j
@Component
public class TagProcessor extends AbstractNebulaEntityProcessor {

    @Override
    protected NebulaEntity getEntityType() {
        return NebulaEntity.TAG;
    }

    // 构建查询
    @Override
    protected String buildQueryForEntity(String tagName, Map<String, String> fieldsMap, int offset) {
        String matchNgql = "MATCH (v:" + tagName + ") RETURN id(v)";
        if (ObjectUtils.isNotEmpty(fieldsMap)) {
            matchNgql += "," + String.join(",", fieldsMap.values());
        }
        matchNgql += String.format(" SKIP %d LIMIT %d", offset, BATCH_SIZE);
        return matchNgql;
    }

    @Override
    protected long getTotalRecords(String tagName, SessionPool sourceSessionPool)
        throws IOErrorException, AuthFailedException, ClientServerIncompatibleException, BindSpaceFailedException {
        String query = "MATCH (v:" + tagName + ") RETURN count(*)";
        ResultSet totalRecordsResult = sourceSessionPool.execute(query);
        return totalRecordsResult.rowValues(0).get("count(*)").asLong();
    }

    // 执行插入操作
    @Override
    protected void executeInsertQuery(ResultSet resultSet, Map<String, String> fieldsMap, String entityName,
        SessionPool targetSessionPool)
        throws IOErrorException, AuthFailedException, ClientServerIncompatibleException, BindSpaceFailedException {
        StringBuilder values = new StringBuilder();
        for (int i = 0; i < resultSet.rowsSize(); i++) {
            Record nebulaRecord = resultSet.rowValues(i);
            String entityKey = nebulaRecord.get("id(v)").toString();
            List<String> value = getValueString(fieldsMap, nebulaRecord);
            values.append(String.format("%s:(%s)", entityKey,
                String.join(",", value))).append(",");
        }
        if (values.length() > 0) {
            values.deleteCharAt(values.length() - 1);
        }
        // 构建插入语句
        String insertQuery = buildInsertNGQL(fieldsMap, entityName, values.toString(), NebulaEntity.TAG);
        log.info("Insert Tag {} : {}", entityName, insertQuery);
        ResultSet result = targetSessionPool.execute(insertQuery);
        if (result.isSucceeded()) {
            log.info("Insert Tag {} success!!!", entityName);
        } else {
            log.error("Insert Tag {} failed (ErrorCode: {}), Error message: {}", entityName, result.getErrorCode(),
                result.getErrorMessage());
        }
    }


}