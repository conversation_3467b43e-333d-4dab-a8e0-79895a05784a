package com.trs.moye.storage.engine.pojo.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.trs.moye.storage.engine.seatunnel.enums.SeaTunnelStorageTraceType;
import java.time.LocalDateTime;
import lombok.Data;


/**
 * 存储任务追踪表
 *
 * <AUTHOR>
 * @since 2025/2/11 14:46
 */
@Data
@TableName(value = "storage_task_trace", autoResultMap = true)
public class StorageTaskTrace {

    /**
     * 主键id
     */
    private String id;

    /**
     * 贴源表id
     */
    private Integer dataModelId;

    /**
     * 批次号
     */
    private String batchNo;

    /**
     * 节点类型
     */
    private SeaTunnelStorageTraceType node;

    /**
     * 节点详情
     */
    private String details;


    /**
     * 是否出错
     */
    private Integer isError;


    /**
     * 开始时间
     */
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    private LocalDateTime endTime;

    /**
     * 存储时间
     */
    private LocalDateTime storageTime;

    /**
     * 处理时间，时长毫秒
     */
    private Long processingTime;


}