package com.trs.moye.storage.engine.file.ftp;

import com.trs.moye.base.data.connection.enums.FileExtension;

/**
 * 文件列策略工厂类
 */
public class FileColumnStrategyFactory {

    private FileColumnStrategyFactory() {

    }

    /**
     * 获取文件列策略
     *
     * @param fileExtension 文件扩展名
     * @return {@link FileColumnStrategy}
     */
    public static FileColumnStrategy getStrategy(FileExtension fileExtension) {
        if (fileExtension == null) {
            throw new IllegalArgumentException("File extension cannot be null");
        }

        return switch (fileExtension) {
            case TRS -> new TrsColumnStrategy();
            case CSV -> new CsvFileColumnStrategy();
            case JSON -> new JsonFileColumnStrategy();
            case XLS -> new XlsFileColumnStrategy();
            case XLSX -> new XlsxFileColumnStrategy();
            case BCP -> new BcpFileColumnStrategy();
            case TEXT -> new TextFileColumnStrategy();
        };
    }
}
