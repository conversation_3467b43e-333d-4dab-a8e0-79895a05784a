package com.trs.moye.storage.engine.seatunnel.job.config.strategy;

import com.trs.moye.base.data.connection.entity.DataConnection;
import com.trs.moye.base.data.connection.entity.params.MqConnectionParams;
import com.trs.moye.base.data.execute.KafkaExecuteParams;
import com.trs.moye.base.data.model.entity.DataModelField;
import com.trs.moye.base.data.model.entity.DataSourceConfig;
import com.trs.moye.base.data.storage.DataStorage;
import com.trs.moye.base.data.storage.IncrementInfo;
import com.trs.moye.storage.engine.pojo.request.task.TaskStartParam;
import com.trs.moye.storage.engine.seatunnel.job.config.schema.Schema;
import com.trs.moye.storage.engine.seatunnel.job.config.sink.KafkaSinkConfig;
import com.trs.moye.storage.engine.seatunnel.job.config.source.KafkaSourceConfig;
import java.util.List;
import java.util.Map;
import org.apache.commons.lang3.ObjectUtils;

/**
 * Kafka 数据源配置
 */
public class KafkaSeatunnelJobConfigStrategy implements SeatunnelJobConfigStrategy {

    @Override
    public KafkaSourceConfig createSourceConfig(DataSourceConfig dataSourceConfig, String tableName,
        List<DataModelField> fields, IncrementInfo incrementInfo, TaskStartParam taskStartParam) {
        MqConnectionParams connectionParams = (MqConnectionParams) dataSourceConfig.getConnection()
            .getConnectionParams();
        KafkaExecuteParams kafkaExecuteParams = (KafkaExecuteParams) taskStartParam.getExecuteParams();
        String consumerGroup =
            ObjectUtils.isEmpty(kafkaExecuteParams.getConsumerGroup()) ? connectionParams.buildConsumerGroup(
                fields.get(0).getDataModelId()) : kafkaExecuteParams.getConsumerGroup();
        //如果按照时间来消费需要设置时间戳
        return KafkaSourceConfig.builder()
            .bootstrapServers(connectionParams.getHost() + ":" + connectionParams.getPort())
            .topic(dataSourceConfig.getEnName())
            .consumerGroup(consumerGroup)
            .schema(Schema.of(fields))
            .startMode(kafkaExecuteParams.getOffsetResetType())
            .startModeTimestamp(kafkaExecuteParams.isTimestampType() ? kafkaExecuteParams.getTimestamp() : null)
            .kafkaConfig(Map.of("auto.offset.reset", "earliest"))
            .build();
    }

    @Override
    public KafkaSinkConfig createSinkConfig(DataConnection connection, DataStorage storage,
        IncrementInfo incrementInfo, List<DataModelField> fields) {
        MqConnectionParams connectionParams = (MqConnectionParams) connection.getConnectionParams();
        return KafkaSinkConfig.builder()
            .bootstrapServers(connectionParams.getHost() + ":" + connectionParams.getPort())
            .topic(storage.getEnName())
            .build();
    }
}
