package com.trs.moye.storage.engine.utils;

import java.util.UUID;

/**
 * 生成UUID
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025/2/12 14:14
 **/
public class UUIdUtils {


    /**
     * 生成一个随机的 UUID 字符串
     *
     * @return 返回 UUID 字符串（去掉横线，全小写）
     */
    public static String generateUUID() {
        UUID uuid = UUID.randomUUID(); // 生成一个随机的 UUID
        return uuid.toString().replace("-", "").toLowerCase(); // 去掉横线并转为小写
    }

    /**
     * 生成一个标准的 UUID 字符串
     *
     * @return 返回标准的 UUID 字符串（包含横线）
     */
    public static String generateStandardUUID() {
        return UUID.randomUUID().toString(); // 生成标准的 UUID 字符串
    }

}
