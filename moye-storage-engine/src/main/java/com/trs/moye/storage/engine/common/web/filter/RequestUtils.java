package com.trs.moye.storage.engine.common.web.filter;

import com.trs.moye.base.common.entity.TwoTuple;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * <AUTHOR>
 * @since 2025-03-06 16:59
 */
public class RequestUtils {

    private static final ThreadLocal<TwoTuple<HttpServletRequest, HttpServletResponse>> LOCAL = new ThreadLocal<>();

    private RequestUtils() {
    }

    static void set(HttpServletRequest request, HttpServletResponse response) {
        LOCAL.set(new TwoTuple<>(request, response));
    }

    static void remove() {
        LOCAL.remove();
    }

    /**
     * 获取当前请求
     *
     * @return {@link HttpServletRequest}
     */
    public static HttpServletRequest getRequest() {
        TwoTuple<HttpServletRequest, HttpServletResponse> twoTuple = LOCAL.get();
        return twoTuple == null ? null : twoTuple.getFirst();
    }

    /**
     * 获取当前响应
     *
     * @return {@link HttpServletResponse}
     */
    public static HttpServletResponse getResponse() {
        TwoTuple<HttpServletRequest, HttpServletResponse> twoTuple = LOCAL.get();
        return twoTuple == null ? null : twoTuple.getSecond();
    }

    /**
     * 获取请求头信息
     *
     * @param name 请求头名称
     * @return 请求头信息
     */
    public static String getRequestHeader(String name) {
        HttpServletRequest request = getRequest();
        if (request == null) {
            return null;
        }
        return request.getHeader(name);
    }
}
