package com.trs.moye.storage.engine.pool;

import com.trs.moye.storage.engine.common.MoyeConnection;
import com.trs.moye.storage.engine.exception.DataStorageEngineException;
import java.lang.reflect.Method;
import java.util.UUID;
import org.springframework.cglib.proxy.MethodInterceptor;
import org.springframework.cglib.proxy.MethodProxy;

/**
 * <AUTHOR>
 * @since 2025-02-13 17:06
 */
public class CloseMethodInterceptor implements MethodInterceptor, RealCloseAdvice {

    private String key;

    private ConnectionPool pool;

    private volatile boolean realCloseStatus = false;

    private final String proxyObjectIdentification;

    public CloseMethodInterceptor(String key, ConnectionPool pool) {
        this.key = key;
        this.pool = pool;
        this.proxyObjectIdentification = UUID.randomUUID().toString();
    }

    @Override
    public Object intercept(Object proxy, Method method, Object[] args, MethodProxy methodProxy) throws Throwable {
        if (method.getName().equals("close") && method.getParameterCount() == 0) {
            closeProxy(methodProxy, proxy, args);
            return null;
        } else if (method.getName().equals("sendAdviceSignal") && method.getParameterCount() == 0) {
            sendAdviceSignal();
            return null;
        } else if (method.getName().equals("getProxyObjectIdentification") && method.getParameterCount() == 0) {
            return getProxyObjectIdentification();
        } else {
            // 调用实际的方法
            return methodProxy.invokeSuper(proxy, args);
        }
    }


    @Override
    public void sendAdviceSignal() {
        this.realCloseStatus = true;
    }

    @Override
    public String getProxyObjectIdentification() {
        return proxyObjectIdentification;
    }

    private void closeProxy(MethodProxy methodProxy, Object proxy, Object[] args) {
        if (realCloseStatus) {
            // 关闭真实连接
            closeRealConnection(methodProxy, proxy, args);
        } else {
            // 归还连接到连接池
            giveBackConnection((MoyeConnection) proxy);
        }
    }

    private void closeRealConnection(MethodProxy methodProxy, Object proxy, Object[] args) {
        try {
            methodProxy.invokeSuper(proxy, args);
        } catch (Throwable e) {
            throw new DataStorageEngineException("关闭连接失败", e);
        } finally {
            clearReference();
        }
    }

    private void clearReference() {
        this.pool = null;
        this.key = null;
    }

    private void giveBackConnection(MoyeConnection connection) {
        pool.returnObject(key, connection);
        pool.getMonitor().removeBorrowRecord(key, connection);
    }
}
