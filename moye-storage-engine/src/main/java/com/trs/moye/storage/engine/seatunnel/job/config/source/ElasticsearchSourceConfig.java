package com.trs.moye.storage.engine.seatunnel.job.config.source;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.JsonNode;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.SuperBuilder;

/**
 * Elasticsearch source config
 *
 * <p>Elasticsearch source config is used to specify the configuration of the Elasticsearch source</p>
 *
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
@SuperBuilder
public class ElasticsearchSourceConfig extends SeatunnelSourceConfig {

    @JsonProperty("hosts")
    private String[] hosts;
    @JsonProperty("username")
    private String username;
    @JsonProperty("password")
    private String password;
    @JsonProperty("index")
    private String index;
    @JsonProperty("source")
    private String[] source;
    @JsonProperty("query")
    private JsonNode query;
    @JsonProperty("plugin_name")
    @Builder.Default
    private String pluginName = "Elasticsearch";
}
