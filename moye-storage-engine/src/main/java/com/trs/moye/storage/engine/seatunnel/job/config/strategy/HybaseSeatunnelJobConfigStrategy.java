package com.trs.moye.storage.engine.seatunnel.job.config.strategy;

import com.trs.moye.base.data.connection.entity.DataConnection;
import com.trs.moye.base.data.connection.entity.params.HybaseConnectionParams;
import com.trs.moye.base.data.model.entity.DataModelField;
import com.trs.moye.base.data.model.entity.DataSourceConfig;
import com.trs.moye.base.data.storage.DataStorage;
import com.trs.moye.base.data.storage.IncrementInfo;
import com.trs.moye.storage.engine.pojo.request.task.TaskStartParam;
import com.trs.moye.storage.engine.seatunnel.enums.SchemaSaveMode;
import com.trs.moye.storage.engine.seatunnel.job.config.schema.Schema;
import com.trs.moye.storage.engine.seatunnel.job.config.sink.HyBaseSinkConfig;
import com.trs.moye.storage.engine.seatunnel.job.config.source.HyBaseSourceConfig;
import java.util.List;
import java.util.Objects;
import org.jetbrains.annotations.Nullable;

/**
 * Clickhouse 数据源配置
 */
public class HybaseSeatunnelJobConfigStrategy implements SeatunnelJobConfigStrategy {

    @Override
    public HyBaseSourceConfig createSourceConfig(DataSourceConfig dataSourceConfig, String tableName,
        List<DataModelField> fields, IncrementInfo incrementInfo, TaskStartParam taskStartParam) {
        HybaseConnectionParams connectionParams = (HybaseConnectionParams) dataSourceConfig.getConnection()
            .getConnectionParams();
        Schema schema = Schema.of(fields);
        return HyBaseSourceConfig.builder().hosts(connectionParams.getHosts()).username(connectionParams.getUsername())
            .password(connectionParams.getDecryptedPassword()).schema(schema).query(buildQuery(incrementInfo))
            .database(connectionParams.getDatabase() + "." + tableName).build();
    }

    private static @Nullable String buildQuery(IncrementInfo incrementInfo) {
        String query = null;
        if (Objects.nonNull(incrementInfo) && Objects.nonNull(incrementInfo.getFieldName()) && Objects.nonNull(
            incrementInfo.getIncrementValue())) {
            query = incrementInfo.getFieldName() + ":[ \"" + incrementInfo.getIncrementValue() + "\" TO *}";
        }
        return query;
    }

    @Override
    public HyBaseSinkConfig createSinkConfig(DataConnection connection, DataStorage storage,
        IncrementInfo incrementInfo, List<DataModelField> fields) {
        HybaseConnectionParams connectionParams = (HybaseConnectionParams) connection.getConnectionParams();
        String host = String.format(URL_FORMAT, connectionParams.getProtocol(), connectionParams.getHost(),
            connectionParams.getPort());
        return HyBaseSinkConfig.builder().hosts(new String[]{host}).username(connectionParams.getUsername())
            .password(connectionParams.getDecryptedPassword())
            .database(connectionParams.getDatabase() + "." + storage.getEnName())
            .dataSaveMode(storage.getSaveMode(DEFAULT_DATA_SAVE_MODE))
            .schemaSaveMode(SchemaSaveMode.ERROR_WHEN_SCHEMA_NOT_EXIST)
            .storageName(storage.getConnection().getName())
            .build();
    }
}
