package com.trs.moye.storage.engine.seatunnel.job.config.transformer;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.SuperBuilder;

/**
 * 生成记录ID算子配置
 *
 * @since 2025-02-10
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
@SuperBuilder
public class GenerateRecordIdTransformConfig extends TransformConfig {

    @JsonProperty("plugin_name")
    @Builder.Default
    private String pluginName = "GenerateRecordId";

    @JsonProperty("plugin_output")
    @Builder.Default
    private String pluginOutput = "record_id_output";
}
