package com.trs.moye.storage.engine.seatunnel.job.config.sink;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.SuperBuilder;

/**
 * Kafka存储配置
 */
@EqualsAndHashCode(callSuper = true)
@Data
@SuperBuilder
public class KafkaSinkConfig extends SeatunnelSinkConfig {

    @JsonProperty("plugin_name")
    @Builder.Default
    private String pluginName = "Kafka";

    @JsonProperty("topic")
    private String topic;

    @JsonProperty("bootstrap.servers")
    private String bootstrapServers;

    @JsonProperty("format")
    @Builder.Default
    private String format = "json";
}
