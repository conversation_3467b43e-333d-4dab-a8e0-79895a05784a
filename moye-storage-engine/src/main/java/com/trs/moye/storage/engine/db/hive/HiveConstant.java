package com.trs.moye.storage.engine.db.hive;

/**
 * Hive常量
 */
public class HiveConstant {

    // 私有构造函数，防止实例化
    private HiveConstant() {
        throw new UnsupportedOperationException("This is a utility class and cannot be instantiated");
    }

    /**
     * 表的注释信息
     */
    public static final String PROPERTIES_KEY_TABLE_COMMENT = "tableComment";

    /**
     * 分桶的字段
     */
    public static final String PROPERTIES_KEY_CLUSTERED_FIELD = "clusteredField";

    /**
     * 分桶数量
     */
    public static final String PROPERTIES_KEY_BUCKET_NUM = "bucketNumber";

    /**
     * 分桶排序字段
     */
    public static final String PROPERTIES_KEY_CLUSTERED_SORT = "clusteredSort";

    /**
     * 字段分割方式
     */
    public static final String PROPERTIES_KEY_FIELDS_TERMINAL = "fieldsTerminal";

    /**
     * 集合的分割方式
     */
    public static final String PROPERTIES_KEY_COLLECTION_TERMINAL = "collectionTerminal";

    /**
     * map元素的分割方式
     */
    public static final String PROPERTIES_KEY_MAP_TERMINAL = "mapTerminal";

    /**
     * 文件编码方式 textFile  ORCFile Parquet
     */
    public static final String PROPERTIES_KEY_FORMAT = "tableFormat";

}