package com.trs.moye.storage.engine.db.doris;

import com.trs.moye.base.common.enums.FieldType;
import com.trs.moye.base.data.model.entity.AbstractField;
import com.trs.moye.base.data.model.entity.DataModel;
import com.trs.moye.base.data.model.entity.DataModelField;
import com.trs.moye.base.data.storage.setting.DorisDataStorageSettings;
import com.trs.moye.base.mcp.SearchableField;
import com.trs.moye.base.mcp.TableInfo;
import com.trs.moye.storage.engine.db.BasicTypeDefine;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Doris SQL工厂类
 */
public class DorisSqlFactory {

    /**
     * 生成获取表名和注释的SQL语句
     *
     * @param dbName 数据库名称
     * @return {@link String}
     */
    public static String buildAllTableNamesAndCommentsSql(String dbName) {
        return String.format("SELECT TABLE_NAME as table_name, TABLE_COMMENT as table_comment " +
            "FROM INFORMATION_SCHEMA.TABLES " +
            "WHERE TABLE_SCHEMA = '%s'", dbName);
    }

    /**
     * 生成ddl创建语句
     *
     * @param tableInfo 表信息
     * @return {@link String}
     */
    public static String ddlSql(TableInfo tableInfo) {
        StringBuilder sql = new StringBuilder();
        sql.append("CREATE TABLE IF NOT EXISTS ").append(tableInfo.getEnName()).append(" (");

        // 添加字段定义
        sql.append(
            buildColumnsIdentifySql(tableInfo.getFields().stream().map(SearchableField::toDataModelField).toList()));

        sql.append(") ENGINE=OLAP \n");

        // 添加表注释
        if (tableInfo.getZhName() != null && !tableInfo.getZhName().isEmpty()) {
            sql.append(" COMMENT '").append(tableInfo.getZhName()).append("' \n");
        }
        sql.append("PROPERTIES (\n"
            + "\"replication_allocation\" = \"tag.location.default: 1\""
            + ")");

        return sql.toString();
    }

    /**
     * 创建表DDL
     *
     * @param dataModel 数据建模
     * @param settings  数据存储设置
     * @return {@link String}
     * <AUTHOR>
     * @since 2025/3/26 15:20
     */
    public String ddlSql(DataModel dataModel, DorisDataStorageSettings settings) {
        StringBuilder sql = new StringBuilder();
        // 添加副本数属性
        StringBuilder properties = new StringBuilder("PROPERTIES ( \n");
        properties.append(String.format("""
            "replication_num" = "%d"
            """, settings.getReplicas()));

        sql.append("CREATE TABLE IF NOT EXISTS ").append(dataModel.getEnName()).append(" (");

        // 添加字段定义
        sql.append(buildColumnsIdentifySql(dataModel.getFields()));

        sql.append(") ENGINE=OLAP \n");

        // 添加主键定义
        List<DataModelField> primaryKey = dataModel.getFields().stream().filter(DataModelField::isPrimaryKey).toList();
        if (!primaryKey.isEmpty()) {
            String keys = primaryKey.stream().map(AbstractField::getEnName).collect(Collectors.joining(","));
            sql.append(" UNIQUE KEY (").append(keys).append(")");
        }

        // 添加表注释
        if (dataModel.getZhName() != null && !dataModel.getZhName().isEmpty()) {
            sql.append(" COMMENT '").append(dataModel.getZhName()).append("' \n");
        }

        // 添加分区键
        if (settings.isPartitioned()) {
            String partitionStatement = String.format("AUTO PARTITION BY RANGE (date_trunc(%s, '%s'))()",
                settings.getPartitionField(),
                settings.getPartitionGranularity().name().toLowerCase());
            sql.append(partitionStatement).append(" \n");
            properties.append(",\n").append(String.format("""
                       "dynamic_partition.enable" = "true",
                       "dynamic_partition.prefix" = "p",
                       "dynamic_partition.start" = "-%d",
                       "dynamic_partition.end" = "0",
                       "dynamic_partition.time_unit" = "%s"
                    """, settings.getPartitionKeepNum(),
                settings.getPartitionGranularity().name().toLowerCase()));
        }

        dataModel.getFields().stream().filter(DataModelField::isPrimaryKey)
            .findAny().ifPresent(primaryKeyField ->
                sql.append(" DISTRIBUTED BY HASH(").append(primaryKeyField.getEnName()).append(") \n"));

        // 添加副本数属性
        properties.append(")\n");
        sql.append(properties);
        return sql.toString();
    }

    /**
     * 生成删除表的SQL语句
     *
     * @param dataModel   数据建模
     * @param fieldsToAdd 待添加的字段列表
     * @return {@link String}
     */
    public String buildAddFieldSql(DataModel dataModel, List<DataModelField> fieldsToAdd) {
        StringBuilder sql = new StringBuilder();
        sql.append("ALTER TABLE ").append(dataModel.getEnName());

        for (DataModelField field : fieldsToAdd) {
            sql.append(" ADD COLUMN ").append(buildColumnIdentifySql(field));
        }

        return sql.toString();
    }

    /**
     * 生成获取表字段的SQL语句
     *
     * @param dbName    数据库名称
     * @param tableName 表名
     * @return {@link String}
     * <AUTHOR>
     * @since 2025/3/26 15:31
     */
    public String buildGetTableColumnsSql(String dbName, String tableName) {
        return String.format("SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE, COLUMN_COMMENT " +
            "FROM INFORMATION_SCHEMA.COLUMNS " +
            "WHERE TABLE_SCHEMA = '%s' AND TABLE_NAME = '%s'", dbName, tableName);
    }

    /**
     * 生成获取表结构的SQL语句
     *
     * @param dbName    数据库名称
     * @param tableName 表名
     * @return {@link String}
     * <AUTHOR>
     * @since 2025/3/26 15:34
     */
    public String buildGetTableCommentSql(String dbName, String tableName) {
        return String.format("SELECT TABLE_COMMENT " +
            "FROM INFORMATION_SCHEMA.TABLES " +
            "WHERE TABLE_SCHEMA = '%s' AND TABLE_NAME = '%s'", dbName, tableName);
    }

    /**
     * 获取该表该字段最大值的sql
     *
     * @param dbName    数据库名称
     * @param tableName 表名
     * @param filedName 字段名
     * @return {@link String}
     * <AUTHOR>
     * @since 2025/3/26 15:35
     */
    public String buildMaxValueSql(String dbName, String tableName, String filedName) {
        return String.format("SELECT MAX(%s) FROM %s.%s", filedName, dbName, tableName);
    }

    private static String buildColumnsIdentifySql(List<DataModelField> columns) {
        DataModelField autoIncrementIdField = new DataModelField();
        autoIncrementIdField.setEnName("aid");
        autoIncrementIdField.setZhName("自增主键");
        autoIncrementIdField.setType(FieldType.LONG);
        autoIncrementIdField.setTypeName("BIGINT");
        autoIncrementIdField.setNullable(false);
        autoIncrementIdField.setPrimaryKey(true);
        autoIncrementIdField.setDescription("自增主键");
        List<DataModelField> mutableColumns = new java.util.ArrayList<>(columns);
        mutableColumns.add(0, autoIncrementIdField);
        return mutableColumns.stream()
            .map(DorisSqlFactory::buildColumnIdentifySql)
            .collect(Collectors.joining(", "));
    }

    private static String buildColumnIdentifySql(DataModelField column) {
        StringBuilder sql = new StringBuilder();
        sql.append(column.getEnName()).append(" ");

        // 将DataModelField转换为BasicTypeDefine
        BasicTypeDefine typeDefine = convertToBasicTypeDefine(column);

        // 根据字段类型生成对应的SQL类型
        String dorisType = DorisTypeConverter.convertToDorisType(FieldType.valueOf(typeDefine.getDataType()), null);
        sql.append(dorisType);

        // 添加是否可空
        if (!typeDefine.isNullable()) {
            sql.append(" NOT NULL");
        }

        // 添加默认值
        if (typeDefine.getDefaultValue() != null) {
            sql.append(" DEFAULT ")
                .append(formatDefaultValue(typeDefine.getDefaultValue(), FieldType.valueOf(typeDefine.getDataType())));
        }

        // 自增主键
        if ("aid".equals(column.getEnName())) {
            sql.append(" AUTO_INCREMENT");
        }

        // 添加字段注释
        if (typeDefine.getComment() != null && !typeDefine.getComment().isEmpty()) {
            sql.append(" COMMENT '").append(typeDefine.getComment()).append("'");
        }

        return sql.toString();
    }

    /**
     * 格式化默认值
     *
     * @param defaultValue 默认值
     * @param fieldType    字段类型
     * @return 格式化后的默认值
     */
    private static String formatDefaultValue(Object defaultValue, FieldType fieldType) {
        if (defaultValue == null) {
            return "NULL";
        }

        switch (fieldType) {
            case CHAR, STRING, TEXT -> {
                return "'" + defaultValue.toString().replace("'", "''") + "'";
            }
            case DATE, DATETIME, TIME -> {
                return "'" + defaultValue + "'";
            }
            case BOOLEAN -> {
                return defaultValue.toString().toUpperCase();
            }
            default -> {
                return defaultValue.toString();
            }
        }
    }

    /**
     * 将DataModelField转换为BasicTypeDefine
     *
     * @param field 字段信息
     * @return BasicTypeDefine
     */
    private static BasicTypeDefine convertToBasicTypeDefine(DataModelField field) {
        return BasicTypeDefine.builder()
            .name(field.getEnName())
            .columnType(field.getTypeName())
            .dataType(field.getType().name())
            .nullable(field.isNullable())
            .comment(field.getZhName())
            .build();
    }

    /**
     * 构建加载数据的SQL语句
     *
     * @param tableInfo 表信息
     * @return 加载数据的SQL语句
     */
    public String buildLoadDataSql(TableInfo tableInfo) {
        String template = """
            INSERT INTO %s
            SELECT null,* FROM %s
            """;
        return String.format(template, tableInfo.getEnName(), tableInfo.getS3Path());
    }
}