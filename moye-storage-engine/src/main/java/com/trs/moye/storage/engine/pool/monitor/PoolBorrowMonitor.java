package com.trs.moye.storage.engine.pool.monitor;

import com.trs.moye.storage.engine.common.MoyeConnection;
import com.trs.moye.storage.engine.pool.RealCloseAdvice;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @since 2025-06-10 11:05
 */
@Component
public class PoolBorrowMonitor {

    private final Map<String, Map<String, BorrowRecord>> borrowRecordMapMap = new ConcurrentHashMap<>(16);

    /**
     * 添加借阅记录
     *
     * @param identification 标识
     * @param connection     连接
     */
    public void addBorrowRecord(String identification, MoyeConnection connection) {
        String proxyObjectIdentification = ((RealCloseAdvice) connection).getProxyObjectIdentification();
        borrowRecordMapMap.computeIfAbsent(identification, k -> new ConcurrentHashMap<>(16))
            .put(proxyObjectIdentification, new BorrowRecord(connection));
    }


    /**
     * 移除借阅记录
     *
     * @param identification 标识
     * @param connection     连接
     */
    public void removeBorrowRecord(String identification, MoyeConnection connection) {
        Map<String, BorrowRecord> borrowRecordMap = borrowRecordMapMap.get(identification);
        if (borrowRecordMap != null) {
            String proxyObjectIdentification = ((RealCloseAdvice) connection).getProxyObjectIdentification();
            borrowRecordMap.remove(proxyObjectIdentification);
        }
    }

    /**
     * 获取借阅记录
     *
     * @param identification 标识
     * @return 借阅记录
     */
    public Map<String, BorrowRecord> getBorrowRecordMap(String identification) {
        return borrowRecordMapMap.get(identification);
    }

    /**
     * 获取借阅报告
     *
     * @param identification 连接标识
     * @return 报告文本
     */
    public String getBorrowReport(String identification) {
        Map<String, BorrowRecord> borrowRecordMap = borrowRecordMapMap.get(identification);
        if (ObjectUtils.isEmpty(borrowRecordMap)) {
            return "没有正在被借出的连接";
        }
        StringBuilder builder = new StringBuilder();
        for (BorrowRecord borrowRecord : borrowRecordMap.values()) {
            builder.append(borrowRecord.getReport()).append("\n");
        }
        return builder.toString();
    }
}
