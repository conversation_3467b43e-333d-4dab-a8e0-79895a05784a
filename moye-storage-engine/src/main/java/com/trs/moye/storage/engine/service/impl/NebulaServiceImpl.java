package com.trs.moye.storage.engine.service.impl;

import com.trs.moye.base.data.connection.enums.ConnectionType;
import com.trs.moye.base.data.model.dao.DataModelMapper;
import com.trs.moye.base.data.model.entity.DataModel;
import com.trs.moye.base.data.model.enums.CreateTableStatus;
import com.trs.moye.base.data.storage.DataStorage;
import com.trs.moye.base.common.entity.TwoTuple;
import com.trs.moye.base.common.exception.BizException;
import com.trs.moye.base.common.utils.AssertUtils;
import com.trs.moye.storage.engine.db.nebula.NebulaConnection;
import com.trs.moye.base.data.storage.nebula.AddTagEdgeFieldDto;
import com.trs.moye.base.data.storage.nebula.DeleteTagEdgeFieldDto;
import com.trs.moye.base.data.storage.nebula.UpdateTagEdgeFieldDto;
import com.trs.moye.storage.engine.pojo.request.nebula.AddTagEdgeFieldsRequest;
import com.trs.moye.storage.engine.pojo.request.nebula.DeleteTagEdgeFieldsRequest;
import com.trs.moye.storage.engine.pojo.request.nebula.UpdateTagEdgeFieldsRequest;
import com.trs.moye.storage.engine.service.NebulaService;
import com.trs.moye.storage.engine.utils.ConnectionUtils;
import java.util.List;
import java.util.function.Consumer;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @since 2025-01-21 14:35
 */
@Slf4j
@Service
public class NebulaServiceImpl implements NebulaService {

    @Resource
    private DataModelMapper dataModelMapper;

    @Override
    public void addTagEdgeFields(AddTagEdgeFieldsRequest request) {
        operateDataModelNebulaConnection(request.getDataModelId(), connection -> addTagEdgeFields(connection, request));
    }

    private void addTagEdgeFields(NebulaConnection connection, AddTagEdgeFieldsRequest request) {
        if (ObjectUtils.isNotEmpty(request.getTag())) {
            for (AddTagEdgeFieldDto tag : request.getTag()) {
                connection.addTagFields(tag.getEnName(), tag.getFields());
            }
        }
        if (ObjectUtils.isNotEmpty(request.getEdge())) {
            for (AddTagEdgeFieldDto edge : request.getEdge()) {
                connection.addEdgeFields(edge.getEnName(), edge.getFields());
            }
        }
    }

    private void operateDataModelNebulaConnection(Integer dataModelId, Consumer<NebulaConnection> consumer) {
        TwoTuple<DataModel, List<DataStorage>> twoTuple = getDataModelNebulaStoragePoints(
            dataModelId);
        DataModel dataModel = twoTuple.getFirst();
        List<DataStorage> nebulaStoragePoints = twoTuple.getSecond();
        for (DataStorage storage : nebulaStoragePoints) {
            try (NebulaConnection connection = ConnectionUtils.getConnection(
                storage.getConnection().getConnectionParams(), null)) {
                consumer.accept(connection);
            } catch (Exception e) {
                throw new BizException(e, "数据模型【%s】操作【%s】存储点失败，失败信息：%s"
                    , dataModel.getZhName()
                    , storage.getConnection().getName()
                    , e.getMessage());
            }
        }
    }

    private TwoTuple<DataModel, List<DataStorage>> getDataModelNebulaStoragePoints(Integer dataModelId) {
        DataModel dataModel = dataModelMapper.selectById(dataModelId);
        AssertUtils.notEmpty(dataModel, "主键为【%s】的数据建模不存在", dataModelId);
        List<DataStorage> nebulaStoragePoints = dataModel.getDataStorages().stream().filter(
                dataStorage -> dataStorage.getConnection().getConnectionType() == ConnectionType.NEBULA)
            .toList();
        AssertUtils.notEmpty(nebulaStoragePoints, "数据模型【%s】不存在 Nebula 存储点", dataModel.getZhName());
        for (DataStorage storagePoint : nebulaStoragePoints) {
            AssertUtils.equals(CreateTableStatus.SUCCESS, storagePoint.getCreateTableStatus()
                , "【%s】数据建模，【%s】存储点，建表状态不是成功状态", dataModel.getZhName()
                , storagePoint.getConnection().getName());
        }
        return new TwoTuple<>(dataModel, nebulaStoragePoints);
    }

    @Override
    public void updateTagEdgeFields(UpdateTagEdgeFieldsRequest request) {
        operateDataModelNebulaConnection(request.getDataModelId(),
            connection -> updateTagEdgeFields(connection, request));
    }

    private void updateTagEdgeFields(NebulaConnection connection, UpdateTagEdgeFieldsRequest request) {
        if (ObjectUtils.isNotEmpty(request.getTag())) {
            for (UpdateTagEdgeFieldDto tag : request.getTag()) {
                connection.updateTagFields(tag.getEnName(), tag.getFields());
            }
        }
        if (ObjectUtils.isNotEmpty(request.getEdge())) {
            for (UpdateTagEdgeFieldDto edge : request.getEdge()) {
                connection.updateEdgeFields(edge.getEnName(), edge.getFields());
            }
        }
    }

    @Override
    public void deleteTagEdgeFields(DeleteTagEdgeFieldsRequest request) {
        operateDataModelNebulaConnection(request.getDataModelId(),
            connection -> deleteTagEdgeFields(connection, request));
    }

    private void deleteTagEdgeFields(NebulaConnection connection, DeleteTagEdgeFieldsRequest request) {
        if (ObjectUtils.isNotEmpty(request.getTag())) {
            for (DeleteTagEdgeFieldDto tag : request.getTag()) {
                connection.deleteTagFields(tag.getEnName(), tag.getFields());
            }
        }
        if (ObjectUtils.isNotEmpty(request.getEdge())) {
            for (DeleteTagEdgeFieldDto edge : request.getEdge()) {
                connection.deleteEdgeFields(edge.getEnName(), edge.getFields());
            }
        }
    }
}
