package com.trs.moye.storage.engine.db.nebula.transfer;

import com.trs.moye.storage.engine.pojo.constant.NebulaEntity;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 检查表字段策略工厂
 *
 * <AUTHOR>
 * @since 2024/12/16 10:37:15
 */
@Component
public class NebulaEntityProcessorFactory {

    private final List<EntityProcessor> processorList;

    @Autowired
    public NebulaEntityProcessorFactory(List<EntityProcessor> processorList) {
        this.processorList = processorList;
    }


    /**
     * 获取对应的处理器
     *
     * @param entityType 实体类型
     * @return {@link EntityProcessor }
     * <AUTHOR>
     * @since 2024/12/27 10:36:55
     */
    public EntityProcessor getEntityProcessor(NebulaEntity entityType) {
        return processorList.stream()
            .filter(strategy -> strategy.isSupport(entityType))
            .findFirst()
            .orElseThrow(() -> new IllegalArgumentException("不支持的Nebula实体类型：" + entityType));
    }
}

