package com.trs.moye.storage.engine.log;

import ch.qos.logback.classic.sift.ContextBasedDiscriminator;
import ch.qos.logback.classic.spi.ILoggingEvent;

/**
 * HikariPool线程日志鉴别器
 *
 * <AUTHOR>
 */
public class ThreadDiscriminator extends ContextBasedDiscriminator {
    String key = "threadName";

    @Override
    public String getDiscriminatingValue(ILoggingEvent event) {
        if (event.getThreadName().startsWith("HikariPool")) {
            return "HikariPool";
        } else {
            return "main";
        }
    }

    @Override
    public String getDefaultValue() {
        return key;
    }

    @Override
    public String getKey() {
        return key;
    }

    @Override
    public void setKey(String key) {
        this.key = key;
    }
}
