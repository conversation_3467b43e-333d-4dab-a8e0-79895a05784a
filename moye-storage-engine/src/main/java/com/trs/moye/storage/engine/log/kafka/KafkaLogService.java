package com.trs.moye.storage.engine.log.kafka;

import com.trs.moye.base.common.log.api.ApiLog;
import com.trs.moye.base.common.log.api.ApiLogTrace;
import com.trs.moye.base.common.log.enums.LogTopicIdEnum;
import com.trs.moye.base.common.log.operate.OperateLog;
import com.trs.moye.base.common.entity.TopicConfig;
import com.trs.moye.base.common.utils.ClickhouseLogUtils;
import com.trs.moye.base.common.utils.JsonUtils;
import java.util.Collection;
import java.util.Objects;
import java.util.Properties;
import java.util.concurrent.atomic.AtomicLong;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.producer.Callback;
import org.apache.kafka.clients.producer.KafkaProducer;
import org.apache.kafka.clients.producer.ProducerConfig;
import org.apache.kafka.clients.producer.ProducerRecord;
import org.apache.kafka.common.serialization.StringSerializer;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @since 2024-11-12 16:50
 */
@Slf4j
@Component
public class KafkaLogService implements ApplicationRunner {

    @Resource
    private KafkaLogServerConfig logServerConfig;

    private KafkaProducer<String, String> kafkaProducer;

    private final AtomicLong sendFailCount = new AtomicLong(0);

    private TopicConfig apiLogTopicConfig;

    private TopicConfig apiLogTraceTopicConfig;

    private TopicConfig operateLogTopicConfig;

    private final Callback callback = (metadata, exception) -> {
        if (exception != null) {
            sendFailCount.getAndIncrement();
        }
    };

    @Override
    public void run(ApplicationArguments args) {
        parseTopicNames();
        createKafkaProducer();
    }

    private void parseTopicNames() {
        for (TopicConfig topicConfig : logServerConfig.getTopicConfigs()) {
            if (topicConfig.getId() == LogTopicIdEnum.API_LOG) {
                apiLogTopicConfig = topicConfig;
            }
            if (topicConfig.getId() == LogTopicIdEnum.API_LOG_TRACER) {
                apiLogTraceTopicConfig = topicConfig;
            }
            if (topicConfig.getId() == LogTopicIdEnum.OPERATE_LOG) {
                operateLogTopicConfig = topicConfig;
            }
        }
    }

    private void createKafkaProducer() {
        Properties props = new Properties();
        props.putAll(logServerConfig.getProduceConfig());
        props.put(ProducerConfig.BOOTSTRAP_SERVERS_CONFIG, logServerConfig.getBootstrapServers());
        props.put(ProducerConfig.KEY_SERIALIZER_CLASS_CONFIG, StringSerializer.class.getName());
        props.put(ProducerConfig.VALUE_SERIALIZER_CLASS_CONFIG, StringSerializer.class.getName());
        kafkaProducer = new KafkaProducer<>(props);
    }

    /**
     * 发送api日志集合
     *
     * @param apiLogs api日志集合
     */
    public void sendApiLogs(Collection<ApiLog> apiLogs) {
        if (Objects.isNull(apiLogs)) {
            return;
        }
        for (ApiLog apiLog : apiLogs) {
            sendApiLog(apiLog);
        }
    }

    /**
     * 发送api日志
     *
     * @param apiLog api日志
     */
    public void sendApiLog(ApiLog apiLog) {
        sendLog(apiLogTopicConfig, apiLog);
    }

    /**
     * 发送api日志追踪集合
     *
     * @param traces 追踪集合
     */
    public void sendApiLogTracers(Collection<ApiLogTrace> traces) {
        if (Objects.isNull(traces)) {
            return;
        }
        for (ApiLogTrace apiLog : traces) {
            sendApiLogTracer(apiLog);
        }
    }

    /**
     * 发送api日志追踪
     *
     * @param apiLogTrace 追踪
     */
    public void sendApiLogTracer(ApiLogTrace apiLogTrace) {
        sendLog(apiLogTraceTopicConfig, apiLogTrace);
    }

    /**
     * 发送操作日志
     *
     * @param operateLog api日志
     */
    public void sendOperateLog(OperateLog operateLog) {
        sendLog(operateLogTopicConfig, operateLog);
    }

    /**
     * 发送操作日志
     *
     * @param topicConfig 主题配置
     * @param logData 日志数据
     */
    private void sendLog(TopicConfig topicConfig, Object logData) {
        if (topicConfig == null || logData == null) {
            return;
        }
        if (!logServerConfig.isEnable() || !topicConfig.isEnable()) {
            if (log.isDebugEnabled()) {
                log.debug("【{}】kafka日志未开启，丢弃日志：{}", topicConfig.getId(), JsonUtils.toJsonString(logData));
            }
            return;
        }
        kafkaProducer.send(new ProducerRecord<>(topicConfig.getName(), ClickhouseLogUtils.toJsonString(logData)),
            callback);
    }

}
