package com.trs.moye.storage.engine.pojo.vo;

import java.time.LocalDateTime;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 站内消息vo
 *
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class NoticeVO {

    private String id;

    /**
     * 关联元数据id
     */
    private Integer metadataId;
    /**
     * 消息大类
     */
    private Integer type;
    /**
     * 消息小类
     */
    private String subType;
    /**
     * 批次号
     */
    private String batchNo;
    /**
     * 报错信息
     */
    private String errMessage;
    /**
     * 推送时间
     */
    private LocalDateTime publishTime;

}
