package com.trs.moye.storage.engine.db.mysql;

import com.google.common.base.Preconditions;
import com.trs.moye.base.common.entity.field.DecimalAdvanceConfig;
import com.trs.moye.base.common.entity.field.FieldAdvanceConfig;
import com.trs.moye.base.common.entity.field.StringAdvanceConfig;
import com.trs.moye.base.common.enums.FieldType;
import com.trs.moye.base.data.model.entity.DataModelField;
import com.trs.moye.storage.engine.db.BasicTypeDefine;
import com.trs.moye.storage.engine.db.BasicTypeDefine.BasicTypeDefineBuilder;
import com.trs.moye.storage.engine.db.TypeConverter;
import com.trs.moye.storage.engine.exception.DataStorageEngineException;
import com.trs.moye.storage.engine.utils.BizUtils;
import java.util.Objects;
import lombok.extern.slf4j.Slf4j;

/**
 * MySQL data type converter.
 */
@Slf4j
public class MySqlTypeConverter implements TypeConverter<BasicTypeDefine> {

    // -------------------------string----------------------------
    public static final String MYSQL_CHAR = "CHAR";
    public static final String MYSQL_VARCHAR = "VARCHAR";
    public static final String MYSQL_DATETIME = "DATETIME";
    public static final String MYSQL_TIME = "TIME";
    public static final String MYSQL_TIMESTAMP = "TIMESTAMP";
    public static final int DEFAULT_PRECISION = 38;
    public static final int MAX_PRECISION = 65;
    public static final int DEFAULT_SCALE = 18;
    public static final int MAX_SCALE = 30;
    public static final int MAX_TIME_SCALE = 6;
    public static final int MAX_TIMESTAMP_SCALE = 6;
    public static final long POWER_2_8 = (long) Math.pow(2, 8);
    public static final long POWER_2_16 = (long) Math.pow(2, 16);
    public static final long POWER_2_24 = (long) Math.pow(2, 24);
    public static final long POWER_2_32 = (long) Math.pow(2, 32);
    public static final long MAX_VARBINARY_LENGTH = POWER_2_16 - 4;
    // ============================data types=====================
    static final String MYSQL_NULL = "NULL";
    static final String MYSQL_BIT = "BIT";
    // -------------------------number----------------------------
    static final String MYSQL_TINYINT = "TINYINT";
    static final String MYSQL_TINYINT_UNSIGNED = "TINYINT UNSIGNED";
    static final String MYSQL_SMALLINT = "SMALLINT";
    static final String MYSQL_SMALLINT_UNSIGNED = "SMALLINT UNSIGNED";
    static final String MYSQL_MEDIUMINT = "MEDIUMINT";
    static final String MYSQL_MEDIUMINT_UNSIGNED = "MEDIUMINT UNSIGNED";
    static final String MYSQL_INT = "INT";
    static final String MYSQL_INT_UNSIGNED = "INT UNSIGNED";
    static final String MYSQL_INTEGER = "INTEGER";
    static final String MYSQL_INTEGER_UNSIGNED = "INTEGER UNSIGNED";
    static final String MYSQL_BIGINT = "BIGINT";
    static final String MYSQL_BIGINT_UNSIGNED = "BIGINT UNSIGNED";
    static final String MYSQL_DECIMAL = "DECIMAL";
    static final String MYSQL_DECIMAL_UNSIGNED = "DECIMAL UNSIGNED";
    static final String MYSQL_FLOAT = "FLOAT";
    static final String MYSQL_FLOAT_UNSIGNED = "FLOAT UNSIGNED";
    static final String MYSQL_DOUBLE = "DOUBLE";
    static final String MYSQL_DOUBLE_UNSIGNED = "DOUBLE UNSIGNED";
    static final String MYSQL_TINYTEXT = "TINYTEXT";
    static final String MYSQL_MEDIUMTEXT = "MEDIUMTEXT";
    static final String MYSQL_TEXT = "TEXT";
    static final String MYSQL_LONGTEXT = "LONGTEXT";
    static final String MYSQL_JSON = "JSON";
    static final String MYSQL_ENUM = "ENUM";
    // ------------------------------time-------------------------
    static final String MYSQL_DATE = "DATE";
    static final String MYSQL_YEAR = "YEAR";
    // ------------------------------blob-------------------------
    static final String MYSQL_TINYBLOB = "TINYBLOB";
    static final String MYSQL_MEDIUMBLOB = "MEDIUMBLOB";
    static final String MYSQL_BLOB = "BLOB";
    static final String MYSQL_LONGBLOB = "LONGBLOB";
    static final String MYSQL_BINARY = "BINARY";
    static final String MYSQL_VARBINARY = "VARBINARY";
    static final String MYSQL_GEOMETRY = "GEOMETRY";
    static final String MYSQL_POINT = "POINT";
    static final String MYSQL_POLYGON = "POLYGON";
    static final String MYSQL_LINESTRING = "LINESTRING";

    @Override
    public DataModelField convert(BasicTypeDefine typeDefine) {
        DataModelField.DataModelFieldBuilder<?, ?> builder =
            DataModelField.builder()
                .enName(typeDefine.getName())
                .isNullable(typeDefine.isNullable())
                .zhName(typeDefine.getComment());

        String mysqlDataType = typeDefine.getDataType().toUpperCase();
        if (typeDefine.isUnsigned() && !(mysqlDataType.endsWith(" UNSIGNED"))) {
            mysqlDataType = mysqlDataType + " UNSIGNED";
        }
        switch (mysqlDataType) {
            case MYSQL_BIT -> buildBit(typeDefine, builder, new StringAdvanceConfig());
            case MYSQL_TINYINT -> buildTinyInt(typeDefine, builder);
            case MYSQL_TINYINT_UNSIGNED, MYSQL_SMALLINT -> builder.type(FieldType.SHORT);
            case MYSQL_SMALLINT_UNSIGNED, MYSQL_MEDIUMINT, MYSQL_MEDIUMINT_UNSIGNED, MYSQL_INT, MYSQL_INTEGER,
                MYSQL_YEAR -> builder.type(FieldType.INT);
            case MYSQL_INT_UNSIGNED, MYSQL_INTEGER_UNSIGNED, MYSQL_BIGINT, MYSQL_BIGINT_UNSIGNED ->
                builder.type(FieldType.LONG);
            case MYSQL_FLOAT, MYSQL_FLOAT_UNSIGNED -> builder.type(FieldType.FLOAT);
            case MYSQL_DOUBLE, MYSQL_DOUBLE_UNSIGNED -> builder.type(FieldType.DOUBLE);
            case MYSQL_DECIMAL, MYSQL_DECIMAL_UNSIGNED -> buildDecimal(typeDefine, new DecimalAdvanceConfig(), builder);
            case MYSQL_ENUM, MYSQL_CHAR, MYSQL_VARCHAR -> buildVarchar(typeDefine, builder, new StringAdvanceConfig());
            case MYSQL_TINYTEXT, MYSQL_TEXT, MYSQL_MEDIUMTEXT, MYSQL_LONGTEXT, MYSQL_JSON, MYSQL_GEOMETRY, MYSQL_POINT, MYSQL_POLYGON, MYSQL_LINESTRING -> builder.type(
                FieldType.TEXT);
            // 二进制类型映射成char
            case MYSQL_LONGBLOB, MYSQL_BLOB, MYSQL_MEDIUMBLOB, MYSQL_TINYBLOB, MYSQL_BINARY, MYSQL_VARBINARY -> builder.type(
                FieldType.CHAR);
            case MYSQL_DATE -> builder.type(FieldType.DATE);
            case MYSQL_TIME -> builder.type(FieldType.TIME);
            case MYSQL_DATETIME, MYSQL_TIMESTAMP -> builder.type(FieldType.DATETIME);
            default -> throw new DataStorageEngineException(String.format("不支持的数据类型: %s", mysqlDataType));
        }
        return BizUtils.buildAndSupplementAdvanceConfig(builder);
    }

    private static void buildLongText(DataModelField.DataModelFieldBuilder<?, ?> builder, StringAdvanceConfig config) {
        builder.type(FieldType.STRING);
        config.setMaxLength(POWER_2_32 - 1);
        builder.advanceConfig(config);
    }

    private static void buildMediumText(DataModelField.DataModelFieldBuilder<?, ?> builder,
        StringAdvanceConfig config) {
        builder.type(FieldType.STRING);
        config.setMaxLength(POWER_2_24 - 1);
        builder.advanceConfig(config);
    }

    private static void buildText(DataModelField.DataModelFieldBuilder<?, ?> builder, StringAdvanceConfig config) {
        builder.type(FieldType.STRING);
        config.setMaxLength(POWER_2_16 - 1);
        builder.advanceConfig(config);
    }

    private static void buildTinyText(DataModelField.DataModelFieldBuilder<?, ?> builder, StringAdvanceConfig config) {
        builder.type(FieldType.STRING);
        config.setMaxLength(POWER_2_8 - 1);
        builder.advanceConfig(config);
    }

    private static void buildVarchar(BasicTypeDefine typeDefine, DataModelField.DataModelFieldBuilder<?, ?> builder,
        StringAdvanceConfig config) {
        builder.type(FieldType.STRING);
        if (typeDefine.getLength() == null || typeDefine.getLength() <= 0) {
            config.setMaxLength(100L);
        } else {
            config.setMaxLength(typeDefine.getLength());
        }
        builder.advanceConfig(config);
    }

    private static void buildDecimal(BasicTypeDefine typeDefine, DecimalAdvanceConfig config,
        DataModelField.DataModelFieldBuilder<?, ?> builder) {
        Preconditions.checkArgument(typeDefine.getPrecision() > 0);
        config.setAccuracy(typeDefine.getPrecision());
        if (typeDefine.getPrecision() > MAX_PRECISION) {
            config.setAccuracy(MAX_PRECISION);
        } else {
            config.setAccuracy(typeDefine.getPrecision());
        }
        if (typeDefine.getScale() > MAX_SCALE) {
            config.setScale(MAX_SCALE);
        } else {
            config.setScale(typeDefine.getScale());
        }
        builder.type(FieldType.DECIMAL);
        builder.advanceConfig(config);
    }

    private static void buildTinyInt(BasicTypeDefine typeDefine, DataModelField.DataModelFieldBuilder<?, ?> builder) {
        if (typeDefine.getColumnType().equalsIgnoreCase("tinyint(1)")) {
            builder.type(FieldType.BOOLEAN);
        } else {
            builder.type(FieldType.CHAR);
        }
    }

    private static void buildBit(BasicTypeDefine typeDefine, DataModelField.DataModelFieldBuilder<?, ?> builder,
        StringAdvanceConfig config) {
        if (typeDefine.getLength() == null || typeDefine.getLength() <= 0) {
            builder.type(FieldType.BOOLEAN);
        } else if (typeDefine.getLength() == 1) {
            builder.type(FieldType.BOOLEAN);
        } else {
            builder.type(FieldType.STRING);
            // BIT(M) -> BYTE(M/8)
            long byteLength = typeDefine.getLength() / 8;
            byteLength += typeDefine.getLength() % 8 > 0 ? 1 : 0;
            config.setMaxLength(byteLength);
            builder.advanceConfig(config);
        }
    }

    @Override
    public BasicTypeDefine reconvert(DataModelField column) {
        FieldAdvanceConfig config = column.getAdvanceConfig();
        BasicTypeDefine.BasicTypeDefineBuilder builder =
            BasicTypeDefine.builder()
                .name(column.getEnName())
                .nullable(column.isNullable())
                .comment(column.getZhName());
        buildDataType(column, builder, config);
        return builder.build();
    }

    private static void buildDataType(DataModelField column, BasicTypeDefineBuilder builder,
        FieldAdvanceConfig config) {
        switch (column.getType()) {
            case BOOLEAN -> {
                builder.columnType(String.format("%s(%s)", MYSQL_TINYINT, 1));
                builder.dataType(MYSQL_TINYINT);
                builder.length(1L);
            }
            case SHORT -> {
                builder.columnType(MYSQL_SMALLINT);
                builder.dataType(MYSQL_SMALLINT);
            }
            case INT -> {
                builder.columnType(MYSQL_INT);
                builder.dataType(MYSQL_INT);
            }
            case LONG -> {
                builder.columnType(MYSQL_BIGINT);
                builder.dataType(MYSQL_BIGINT);
            }
            case FLOAT -> {
                builder.columnType(MYSQL_FLOAT);
                builder.dataType(MYSQL_FLOAT);
            }
            case DOUBLE -> {
                builder.columnType(MYSQL_DOUBLE);
                builder.dataType(MYSQL_DOUBLE);
            }
            case DECIMAL -> buildDecimalProperties(builder, config);
            case CHAR -> {
                builder.columnType(MYSQL_BLOB);
                builder.dataType(MYSQL_BLOB);
            }
            case STRING -> {
                builder.columnType(MYSQL_LONGTEXT);
                builder.dataType(MYSQL_LONGTEXT);
                buildStringProperties(builder, config);
            }
            case DATE -> {
                builder.columnType(MYSQL_DATE);
                builder.dataType(MYSQL_DATE);
            }
            case TIME -> {
                builder.dataType(MYSQL_TIME);
                builder.columnType(MYSQL_TIME);
            }
            case DATETIME -> {
                builder.dataType(MYSQL_DATETIME);
                builder.columnType(MYSQL_DATETIME);
            }
            case TEXT -> {
                builder.columnType(MYSQL_LONGTEXT);
                builder.dataType(MYSQL_LONGTEXT);
            }
            case ENTITY, COMPOUND, TAG, OBJECT -> {
                builder.columnType(MYSQL_JSON);
                builder.dataType(MYSQL_JSON);
            }
            default -> throw new DataStorageEngineException(
                String.format("不支持的数据类型: %s", column.getType()));
        }
    }

    private static void buildStringProperties(BasicTypeDefineBuilder builder, FieldAdvanceConfig config) {
        if (config == null) {
            return;
        }
        if (!(config instanceof StringAdvanceConfig stringConfig)) {
            return;
        }
        if (stringConfig.getMaxLength() == null || stringConfig.getMaxLength() <= 0) {
            builder.columnType(MYSQL_LONGTEXT);
            builder.dataType(MYSQL_LONGTEXT);
        } else if (stringConfig.getMaxLength() < POWER_2_8) {
            builder.columnType(
                String.format("%s(%s)", MYSQL_VARCHAR, stringConfig.getMaxLength()));
            builder.dataType(MYSQL_VARCHAR);
        } else if (stringConfig.getMaxLength() < POWER_2_16) {
            builder.columnType(MYSQL_TEXT);
            builder.dataType(MYSQL_TEXT);
        } else if (stringConfig.getMaxLength() < POWER_2_24) {
            builder.columnType(MYSQL_MEDIUMTEXT);
            builder.dataType(MYSQL_MEDIUMTEXT);
        }
    }

    private static void buildDecimalProperties(BasicTypeDefineBuilder builder, FieldAdvanceConfig config) {
        if (Objects.isNull(config)) {
            return;
        }
        if (!(config instanceof DecimalAdvanceConfig decimalConfig)) {
            return;
        }
        int precision = decimalConfig.getAccuracy();
        int scale = decimalConfig.getScale();
        if (precision <= 0) {
            precision = DEFAULT_PRECISION;
            scale = DEFAULT_SCALE;
        } else if (precision > MAX_PRECISION) {
            scale = Math.max(0, scale - (precision - MAX_PRECISION));
            precision = MAX_PRECISION;
        }
        if (scale < 0) {
            scale = 0;
        } else if (scale > MAX_SCALE) {
            scale = MAX_SCALE;
        }
        builder.columnType(String.format("%s(%s,%s)", MYSQL_DECIMAL, precision, scale));
        builder.precision(precision);
        builder.scale(scale);
    }
}
