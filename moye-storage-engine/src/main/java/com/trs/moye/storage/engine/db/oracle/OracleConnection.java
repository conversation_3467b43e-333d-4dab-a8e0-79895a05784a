package com.trs.moye.storage.engine.db.oracle;

import com.trs.moye.base.data.connection.entity.params.ConnectionParams;
import com.trs.moye.base.data.connection.entity.params.OracleConnectionParams;
import com.trs.moye.base.data.model.entity.DataModel;
import com.trs.moye.base.data.model.entity.DataModelField;
import com.trs.moye.base.data.storage.setting.DataStorageSettings;
import com.trs.moye.storage.engine.db.AbstractJdbcDataConnection;
import com.trs.moye.storage.engine.db.BasicTypeDefine;
import com.trs.moye.storage.engine.db.TypeConverter;
import com.trs.moye.storage.engine.exception.DataStorageEngineException;
import com.trs.moye.storage.engine.pojo.request.search.Condition;
import com.trs.moye.storage.engine.pojo.response.StorageEngineResponse;
import com.trs.moye.storage.engine.pojo.response.alltable.TableResponse;
import com.trs.moye.storage.engine.utils.SqlUtils;
import java.sql.Blob;
import java.sql.Clob;
import java.sql.NClob;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.ResultSetMetaData;
import java.sql.SQLException;
import java.sql.Statement;
import java.sql.Types;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.validation.constraints.NotNull;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;

/**
 * Oracle数据库连接
 */
@EqualsAndHashCode(callSuper = true)
@Getter
@Setter
@Slf4j
public class OracleConnection extends AbstractJdbcDataConnection {

    private final String schema;


    @Override
    protected String buildPaginationSql(String originalSql, long offset, long limit) {
        // 检查 SQL 查询中是否已包含分页参数
        if (originalSql.contains("ROWNUM")) {
            return originalSql; // 如果已带分页参数，直接返回原始 SQL
        }
        limit = offset >= 1L ? offset + limit : limit;
        return "SELECT * FROM ( SELECT TMP.*, ROWNUM ROW_ID FROM ( " + originalSql + " ) TMP WHERE ROWNUM <=" + limit
            + ") WHERE ROW_ID > " + offset;
    }

    /**
     * 根据moye的连接信息创建数据库连接
     *
     * @param connectionParams moye连接信息
     */
    public OracleConnection(@NotNull ConnectionParams connectionParams) {
        if (connectionParams instanceof OracleConnectionParams oracleConnectionParams) {
            this.jdbcConnection = createJdbcConnection(oracleConnectionParams, null);
            this.schema = oracleConnectionParams.getSchema();
        } else {
            throw new DataStorageEngineException("不支持数据库连接类型: " + connectionParams.getClass().getName());
        }
    }

    @Override
    public StorageEngineResponse createTable(DataModel table, DataStorageSettings settings) {
        try (Statement statement = jdbcConnection.createStatement()) {
            String sql = OracleSqlFactory.ddlSql(table);
            log.info("创建表SQL：{}", sql);
            statement.execute(sql);
            return StorageEngineResponse.success();
        } catch (SQLException e) {
            log.error("创建表失败：", e);
            return StorageEngineResponse.fail("执行建表语句失败！" + e.getMessage());
        }
    }


    @Override
    public DataModel getTableInfo(String tableName) {
        String tableInfoSql = OracleSqlFactory.createGetTableInfoSql(schema, tableName);
        List<DataModelField> fields = createFields(tableInfoSql);
        String tableComment = getTableComment(tableName);
        DataModel table = new DataModel();
        table.setEnName(tableName);
        table.setFields(fields);
        table.setZhName(tableComment);
        return table;
    }

    @Override
    public List<BasicTypeDefine> getTableFieldDefines(String tableName) {
        String tableInfoSql = OracleSqlFactory.createGetTableInfoSql(schema, tableName);
        return getTableFieldDefinesByTableInfoSql(tableInfoSql);
    }

    private String getTableComment(String tableName) {
        String sql = OracleSqlFactory.createTableCommentSql(schema, tableName);
        try (PreparedStatement preparedStatement = jdbcConnection.prepareStatement(
            sql); ResultSet resultSet = preparedStatement.executeQuery()) {
            if (resultSet.next()) {
                return resultSet.getString("comments");
            }
        } catch (SQLException e) {
            log.error("获取表注释失败：", e);
        }
        return "";
    }

    private List<DataModelField> createFields(String tableInfoSql) {
        OracleTypeConverter oracleTypeConverter = new OracleTypeConverter();
        return getTableFieldDefinesByTableInfoSql(tableInfoSql).stream().map(oracleTypeConverter::convert)
            .toList();
    }

    private List<BasicTypeDefine> getTableFieldDefinesByTableInfoSql(String tableInfoSql) {
        log.info("获取表字段信息SQL：{}", tableInfoSql);
        List<BasicTypeDefine> fields = new ArrayList<>();
        try (PreparedStatement preparedStatement = jdbcConnection.prepareStatement(
            tableInfoSql); ResultSet resultSet = preparedStatement.executeQuery()) {
            while (resultSet.next()) {
                String columnName = resultSet.getString("COLUMN_NAME");
                // e.g NUMBER
                String typeName = resultSet.getString("TYPE_NAME");
                // e.g NUMBER(10, 2)
                String fullTypeName = resultSet.getString("FULL_TYPE_NAME");
                long columnLength = resultSet.getLong("COLUMN_LENGTH");
                int columnPrecision = resultSet.getInt("COLUMN_PRECISION");
                int columnScale = resultSet.getInt("COLUMN_SCALE");
                String columnComment = resultSet.getString("COLUMN_COMMENT");
                Object defaultValue = resultSet.getObject("DEFAULT_VALUE");
                boolean isNullable = resultSet.getString("IS_NULLABLE").equals("YES");

                BasicTypeDefine typeDefine = BasicTypeDefine.builder().name(columnName).columnType(fullTypeName)
                    .dataType(typeName).length(columnLength).precision(columnPrecision).scale(columnScale)
                    .nullable(isNullable).defaultValue(defaultValue).comment(columnComment).build();
                fields.add(typeDefine);
            }
        } catch (SQLException e) {
            log.error("获取表字段信息失败：", e);
        }
        return fields;
    }

    @Override
    public StorageEngineResponse addFields(String tableName, List<DataModelField> fields) {
        DataModel tableInfo = getTableInfo(tableName);
        List<DataModelField> fieldsToAdd = fields.stream().filter(field -> !tableInfo.contains(field))
            .toList();
        if (fieldsToAdd.isEmpty()) {
            return StorageEngineResponse.success();
        }
        try (Statement preparedStatement = jdbcConnection.createStatement()) {
            tableName = "\"" + tableName + "\"";
            String addSql = OracleSqlFactory.buildAddColumnSql(tableName, fieldsToAdd);
            log.info("添加字段SQL：{}", addSql);
            preparedStatement.execute(addSql);
            return StorageEngineResponse.success();
        } catch (SQLException e) {
            log.error("添加字段失败：", e);
            return StorageEngineResponse.fail("添加字段失败: " + e.getMessage());
        }
    }

    @Override
    public long count(List<Condition> conditions, String tablePath) {
        return 0;
    }

    @Override
    public Object getMaxValue(String tableName, String filedName) {
        log.info("获取Oracle表 ：{}, 字段 : {} 的最大值", tableName, filedName);
        try (Statement preparedStatement = jdbcConnection.createStatement()) {
            String sql = OracleSqlFactory.buildMaxValueSql(tableName, filedName);
            ResultSet resultSet = preparedStatement.executeQuery(sql);
            return resultSet.next() ? resultSet.getString(1) : null; // 直接返回最大值或null
        } catch (SQLException e) {
            log.error("获取最大值失败：", e);
            return null;
        }
    }

    @Override
    public TypeConverter<BasicTypeDefine> getTypeConverter() {
        return new OracleTypeConverter();
    }

    @Override
    public List<TableResponse> getAllTables() {
        String sql = OracleSqlFactory.getAllTableNamesAndCommentSql(schema);
        log.info("获取所有表名SQL：{}", sql);
        List<TableResponse> tableList = new ArrayList<>();
        try (PreparedStatement preparedStatement = jdbcConnection.prepareStatement(sql);
            ResultSet resultSet = preparedStatement.executeQuery()) {
            while (resultSet.next()) {
                String tableName = resultSet.getString("table_name");
                String tableComment = resultSet.getString("table_comment");
                tableList.add(new TableResponse(tableName, tableComment));
            }
            return tableList;
        } catch (SQLException e) {
            log.error("获取表信息失败：", e);
            return List.of();
        }
    }

    @Override
    public List<Map<String, Object>> executeQuery(String sql) {
        List<Map<String, Object>> result = new ArrayList<>();
        log.info("执行sql：{}", sql);
        try (PreparedStatement preparedStatement = jdbcConnection.prepareStatement(sql);
            ResultSet resultSet = preparedStatement.executeQuery()) {
            while (resultSet.next()) {
                Map<String, Object> row = new HashMap<>();
                ResultSetMetaData metaData = resultSet.getMetaData();
                int columnCount = metaData.getColumnCount();
                for (int i = 1; i <= columnCount; i++) {
                    String columnName = getColumnName(metaData, i);
                    // 根据列的数据类型处理数据
                    switch (metaData.getColumnType(i)) {
                        case Types.BLOB -> row.put(columnName, getBlobData(resultSet, i));
                        case Types.CLOB, Types.NCLOB -> row.put(columnName,
                            getLargeObjectData(resultSet, i, metaData.getColumnType(i)));
                        default -> SqlUtils.dealType(metaData, i, resultSet, row, columnName, true);
                    }
                }
                result.add(row);
            }
        } catch (SQLException e) {
            log.error("执行sql失败：", e);
            throw new DataStorageEngineException(e.getMessage());
        }
        return result;
    }

    // 获取列名的方法
    private String getColumnName(ResultSetMetaData metaData, int index) throws SQLException {
        String name =
            metaData.getCatalogName(index) == null ? metaData.getColumnName(index) : metaData.getColumnLabel(index);
        return name.replaceAll("^[^.]+\\.", "");
    }

    private String getLargeObjectData(ResultSet resultSet, int index, int columnType) throws SQLException {
        if (columnType == Types.CLOB) {
            Clob clob = resultSet.getClob(index);
            return clob != null ? clob.getSubString(1, (int) clob.length()) : null;
        } else if (columnType == Types.NCLOB) {
            NClob nclob = resultSet.getNClob(index);
            return nclob != null ? nclob.getSubString(1, (int) nclob.length()) : null;
        }
        return null;
    }

    // 获取 BLOB 数据的方法
    private byte[] getBlobData(ResultSet resultSet, int index) throws SQLException {
        Blob blob = resultSet.getBlob(index);
        if (blob != null) {
            return blob.getBytes(1, (int) blob.length());
        }
        return new byte[0];
    }
}
