package com.trs.moye.storage.engine.service.impl;

import static com.trs.moye.storage.engine.seatunnel.enums.SeaTunnelJobStatus.CANCELED;
import static com.trs.moye.storage.engine.seatunnel.enums.SeaTunnelJobStatus.FAILED;
import static com.trs.moye.storage.engine.seatunnel.enums.SeaTunnelJobStatus.FINISHED;
import static com.trs.moye.storage.engine.seatunnel.enums.SeatunnelEventType.JOB_STATUS_CHANGE;

import com.trs.moye.base.common.enums.ModelLayer;
import com.trs.moye.base.common.exception.BizException;
import com.trs.moye.base.common.utils.DataProcessUtils;
import com.trs.moye.base.common.utils.DateTimeUtils;
import com.trs.moye.base.common.utils.DateTimeUtils.Formatter;
import com.trs.moye.base.common.utils.JsonUtils;
import com.trs.moye.base.common.utils.SnowflakeIdUtil;
import com.trs.moye.base.data.connection.entity.DataConnection;
import com.trs.moye.base.data.connection.entity.params.ConnectionParams;
import com.trs.moye.base.data.connection.entity.params.HiveConnectionParams;
import com.trs.moye.base.data.connection.entity.params.HybaseConnectionParams;
import com.trs.moye.base.data.connection.entity.params.OracleConnectionParams;
import com.trs.moye.base.data.connection.enums.ConnectionType;
import com.trs.moye.base.data.connection.enums.DataSourceCategory;
import com.trs.moye.base.data.execute.ExecuteMode;
import com.trs.moye.base.data.execute.KafkaExecuteParams;
import com.trs.moye.base.data.execute.RocketMQExecuteParams;
import com.trs.moye.base.data.model.dao.DataModelExecuteConfigMapper;
import com.trs.moye.base.data.model.dao.DataModelFieldMapper;
import com.trs.moye.base.data.model.dao.DataModelMapper;
import com.trs.moye.base.data.model.entity.DataModel;
import com.trs.moye.base.data.model.entity.DataModelExecuteConfig;
import com.trs.moye.base.data.model.entity.DataModelField;
import com.trs.moye.base.data.model.enums.CreateTableStatus;
import com.trs.moye.base.data.source.enums.KafkaOffsetResetType;
import com.trs.moye.base.data.source.enums.RocketMQOffsetResetType;
import com.trs.moye.base.data.source.setting.file.FtpDataSourceSettings;
import com.trs.moye.base.data.storage.DataStorage;
import com.trs.moye.base.data.storage.dao.DataStorageMapper;
import com.trs.moye.storage.engine.common.MoyeConnection;
import com.trs.moye.storage.engine.dao.DataAccessTraceMapper;
import com.trs.moye.storage.engine.dao.DataProcessTraceMapper;
import com.trs.moye.storage.engine.dao.StorageTaskMapper;
import com.trs.moye.storage.engine.dao.StorageTaskTraceMapper;
import com.trs.moye.storage.engine.db.DatabaseConnection;
import com.trs.moye.storage.engine.db.hive.HiveConnection;
import com.trs.moye.storage.engine.db.hybase.HybaseConnection;
import com.trs.moye.storage.engine.db.oracle.OracleConnection;
import com.trs.moye.storage.engine.exception.DataStorageEngineException;
import com.trs.moye.storage.engine.file.FileConnection;
import com.trs.moye.storage.engine.mq.kafka.KafkaProducerProperties;
import com.trs.moye.storage.engine.pojo.entity.DataAccessTrace;
import com.trs.moye.storage.engine.pojo.entity.DataProcessTrace;
import com.trs.moye.storage.engine.pojo.entity.StorageTask;
import com.trs.moye.storage.engine.pojo.entity.StorageTask.TaskWriteCount;
import com.trs.moye.storage.engine.pojo.entity.StorageTaskTrace;
import com.trs.moye.storage.engine.pojo.request.connection.FileCleanupRequest;
import com.trs.moye.storage.engine.pojo.request.task.FileTaskStartParam;
import com.trs.moye.storage.engine.pojo.request.task.TaskStartParam;
import com.trs.moye.storage.engine.seatunnel.SeatunnelEvent;
import com.trs.moye.storage.engine.seatunnel.client.SeaTunnelClient;
import com.trs.moye.storage.engine.seatunnel.enums.SeaTunnelJobStatus;
import com.trs.moye.storage.engine.seatunnel.enums.SeaTunnelStorageTraceType;
import com.trs.moye.storage.engine.seatunnel.job.config.SeatunnelJobConfigFactory;
import com.trs.moye.storage.engine.seatunnel.request.SeaTunnelJobConfig;
import com.trs.moye.storage.engine.seatunnel.response.SeaTunnelRunningJobInfo;
import com.trs.moye.storage.engine.service.JobService;
import com.trs.moye.storage.engine.utils.ConnectionUtils;
import com.trs.moye.storage.engine.utils.UUIdUtils;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.stream.Collectors;
import javax.annotation.PreDestroy;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.kafka.clients.admin.AdminClient;
import org.apache.kafka.clients.admin.CreateTopicsResult;
import org.apache.kafka.clients.admin.NewTopic;
import org.apache.kafka.common.config.TopicConfig;
import org.jetbrains.annotations.Nullable;
import org.springframework.stereotype.Service;

/**
 * Seatunnel 任务服务实现类
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class SeatunnelJobServiceImpl implements JobService {

    @Resource
    private SeaTunnelClient seaTunnelClient;

    @Resource
    private StorageTaskMapper storageTaskMapper;

    @Resource
    private NoticeService noticeService;

    @Resource
    private DataModelMapper dataModelMapper;

    @Resource
    private DataModelExecuteConfigMapper dataModelExecuteConfigMapper;

    @Resource
    private KafkaProducerProperties producerProperties;

    @Resource
    private AdminClient adminClient;

    @Resource
    private DataModelFieldMapper dataModelFieldMapper;

    @Resource
    private DataStorageMapper dataStorageMapper;

    @Resource
    private DataAccessTraceMapper dataAccessTraceMapper;

    @Resource
    private DataProcessTraceMapper dataProcessTraceMapper;

    @Resource
    private SeatunnelJobConfigFactory seatunnelJobConfigFactory;
    @Resource
    private StorageTaskTraceMapper storageTaskTraceMapper;

    // 线程池用于多数据源切换
    private final ExecutorService executor = Executors.newFixedThreadPool(20);

    /**
     * 关闭线程池
     */
    @PreDestroy
    public void preDestroy() {
        executor.shutdown();
    }


    @Override
    public void submitFileJob(FileTaskStartParam taskStartParam) {
        log.info("开始提交文件接入任务: {}", JsonUtils.toJsonString(taskStartParam));
        Integer dataModelId = taskStartParam.getDataModelId();
        DataModel dataModel = dataModelMapper.selectById(dataModelId);
        extracted(taskStartParam, dataModel);
    }

    @Override
    public void submitJob(TaskStartParam taskStartParam) throws DataStorageEngineException {
        log.info("开始提交数据接入任务: {}", JsonUtils.toJsonString(taskStartParam));
        Integer dataModelId = taskStartParam.getDataModelId();
        DataModel dataModel = dataModelMapper.selectById(dataModelId);
        extracted(taskStartParam, dataModel);
    }

    private void extracted(TaskStartParam taskStartParam, DataModel dataModel) {
        Integer dataModelId = taskStartParam.getDataModelId();
        // 更新任务的增量信息
        String batchNo = buildBatchNo(dataModelId, taskStartParam.getExecuteMode());
        SeaTunnelJobConfig jobConfig = seatunnelJobConfigFactory.createSeatunnelJob(dataModel, batchNo, taskStartParam);
        log.info("seatunnel任务解析结果: {}", JsonUtils.toJsonString(jobConfig));
        LocalDateTime startTime = LocalDateTime.now();
        Long jobId = null;
        SeaTunnelJobStatus status = SeaTunnelJobStatus.UNKNOWABLE;
        String errorMessage = null;
        try {
            jobId = seaTunnelClient.submitJob(jobConfig);
            log.info("提交seatunnel任务成功, jobId: {}, batchNo: {}", jobId, batchNo);
        } catch (Exception e) {
            status = SeaTunnelJobStatus.FAILED;
            errorMessage = e.getMessage();
            log.error("提交seatunnel任务失败, batchNo: {}", batchNo, e);
        } finally {
            //构造seatunnel任务参数 切换到子线程中，写ck数据源。
            LocalDateTime endTime = LocalDateTime.now();
            Long finalJobId = jobId;
            SeaTunnelJobStatus finalStatus = status;
            String finalErrorMessage = errorMessage;
            executor.submit(() -> {
                StorageTask storageTask = createOdsStorageTask(taskStartParam, dataModelId, dataModel,
                    batchNo, finalJobId, jobConfig);
                storageTask.setExecutionStatus(finalStatus);
                storageTask.setErrorMessage(finalErrorMessage);
                storageTaskMapper.insert(storageTask);
                //存储链路信息到storage_task_trace
                createStorageTaskTrace(dataModel, batchNo, finalJobId, jobConfig, startTime, endTime);
            });
        }
    }

    private StorageTask createOdsStorageTask(TaskStartParam taskStartParam, Integer dataModelId, DataModel dataModel,
        String batchNo, Long finalJobId, SeaTunnelJobConfig jobConfig) {
        StorageTask storageTask = buildStorageTask(dataModelId, dataModel.getLayer(),
            dataModel.getDataStorages(),
            taskStartParam.getExecuteMode(), batchNo);
        storageTask.setStorageJobId(finalJobId);
        storageTask.setStorageJobConfig(JsonUtils.parseObjectNode(JsonUtils.toJsonString(jobConfig)));
        storageTask.setStorageIds(dataModel.getDataStorages().stream().map(DataStorage::getId).toList());
        return storageTask;
    }

    @Override
    public void submitDwdJob(TaskStartParam taskStartParam) {
        log.info("开始提交要素库数据接入任务: {}", JsonUtils.toJsonString(taskStartParam));
        Integer dataModelId = taskStartParam.getDataModelId();
        DataModel dataModel = dataModelMapper.selectById(dataModelId);
        //查询算子编排选择的存储，并过滤掉MQ类型的存储点
        List<DataStorage> storages = dataStorageMapper.selectStreamOperatorStorage(dataModelId).stream()
            .filter(storage -> storage.getConnection().getConnectionType().getCategory() != DataSourceCategory.MQ)
            .toList();
        String batchNo = buildBatchNo(dataModelId, taskStartParam.getExecuteMode());
        //拼接jobConfig
        List<Pair<Integer, SeaTunnelJobConfig>> jobConfigs = seatunnelJobConfigFactory.createDwdJobConfigs(dataModel,
            storages, batchNo);

        List<Long> jobIds = new ArrayList<>();
        //提交seatunnel任务
        jobConfigs.forEach(jobConfig -> {
            Integer storageId = jobConfig.getKey();
            String singleBatchNo = batchNo + "_" + storageId;
            SeaTunnelJobConfig seaTunnelJobConfig = jobConfig.getValue();
            Long jobId = null;
            LocalDateTime startTime = LocalDateTime.now();
            try {
                jobId = seaTunnelClient.submitJob(seaTunnelJobConfig);
                // 存储 jobId 和对应的 jobConfig
                if (Objects.nonNull(jobId)) {
                    jobIds.add(jobId);
                }
            } catch (Exception e) {
                log.error("提交seatunnel任务失败, batchNo: {}", singleBatchNo, e);
            } finally {
                ExecuteMode executeMode = taskStartParam.getExecuteMode();
                StorageTask batch = createDwdStorageTask(dataModel, storages, executeMode, singleBatchNo, jobId,
                    seaTunnelJobConfig);
                log.info("插入storageTask日志：{}", JsonUtils.toJsonString(batch));
                storageTaskMapper.insert(batch);
                //存储链路信息到storage_task_trace
                LocalDateTime endTime = LocalDateTime.now();
                createStorageTaskTrace(dataModel, singleBatchNo, jobId, seaTunnelJobConfig, startTime, endTime);
            }

        });
        log.info("提交任务的jobIds: {}", jobIds);
    }

    private StorageTask createDwdStorageTask(DataModel dataModel, List<DataStorage> storages,
        ExecuteMode executeMode, String singleBatchNo, Long jobId, SeaTunnelJobConfig seaTunnelJobConfig) {
        StorageTask storageTask = buildStorageTask(dataModel.getId(), dataModel.getLayer(), storages, executeMode,
            singleBatchNo);
        storageTask.setStorageJobId(jobId);
        storageTask.setStorageJobConfig(JsonUtils.parseObjectNode(JsonUtils.toJsonString(seaTunnelJobConfig)));
        storageTask.setStorageIds(storages.stream().map(DataStorage::getId).toList());
        return storageTask;
    }

    private void createStorageTaskTrace(DataModel dataModel, String batchNo, Long finalJobId,
        SeaTunnelJobConfig jobConfigNodes, LocalDateTime startTime, LocalDateTime endTime) {
        try {
            StorageTaskTrace storageTaskTrace = new StorageTaskTrace();
            storageTaskTrace.setId(UUIdUtils.generateUUID());
            storageTaskTrace.setDataModelId(dataModel.getId());
            storageTaskTrace.setBatchNo(batchNo);
            storageTaskTrace.setNode(SeaTunnelStorageTraceType.TASK_SUBMIT);
            Map<String, Object> map = new HashMap<>();
            map.put("工作ID", finalJobId);
            map.put("任务配置", jobConfigNodes);
            storageTaskTrace.setDetails(JsonUtils.toJsonString(map));
            storageTaskTrace.setStartTime(startTime);
            storageTaskTrace.setEndTime(endTime);
            storageTaskTrace.setIsError(Objects.isNull(finalJobId) ? 1 : 0);
            storageTaskTrace.setStorageTime(LocalDateTime.now());
            storageTaskTraceMapper.insert(storageTaskTrace);
        } catch (Exception e) {
            log.error("提交任务，存储storageTaskTrace记录失败！, batchNo: {}，dataModelId{},jobId:{}", batchNo,
                dataModel.getId(), finalJobId, e);
        }
    }

    @Override
    public void stopJobSingle(String storageJobId) {
        StorageTask storageTask = storageTaskMapper.selectByJobId(storageJobId);
        if (Objects.isNull(storageTask)) {
            log.error("无法停止单个任务, jobId为{}的调度监控为空", storageJobId);
            return;
        }
        long jobId = Long.parseLong(storageJobId);
        try {
            seaTunnelClient.stopJob(jobId);
        } catch (Exception e) {
            log.error("停止单个任务失败，storageJobId = {}", jobId);
        }
        storageTask.setExecutionStatus(CANCELED);
        storageTaskMapper.updateJob(storageTask);
    }

    /**
     * 获取批次号
     *
     * @param modelId     数据模型id
     * @param executeMode 执行模式
     * @return 批次号
     */
    public static String buildBatchNo(Integer modelId, ExecuteMode executeMode) {
        return "T" + modelId + "-" + executeMode.name().toLowerCase() + "-" + LocalDateTime.now()
            .format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
    }

    /**
     * 构建批次信息
     *
     * @param dataModelId 数据模型id
     * @param layer       数据建模分层
     * @param storages    存储点
     * @param executeMode 执行模式
     * @param batchNo     批次号
     * @return 批次信息
     */
    public StorageTask buildStorageTask(Integer dataModelId, ModelLayer layer, List<DataStorage> storages,
        ExecuteMode executeMode, String batchNo) {
        StorageTask storageTask = new StorageTask();
        storageTask.setId(SnowflakeIdUtil.newId());
        storageTask.setDataModelId(dataModelId);
        storageTask.setLayerId(layer.getId());
        storageTask.setAccessMode(executeMode);
        storageTask.setBatchNo(batchNo);
        storageTask.setReadSuccessCount(0L);
        storageTask.setReadFailCount(0L);
        storageTask.setStartTime(LocalDateTime.now());
        storageTask.setAllowSync(false);
        TaskWriteCount[] writeCounts = storageTask.buildWriteCounts(storages);
        storageTask.setWriteCountInfo(writeCounts);
        return storageTask;
    }

    @Override
    public void stopJob(Integer dataModelId) throws DataStorageEngineException {
        log.info("停止数据同步任务: {}", dataModelId);
        List<StorageTask> storageTask = storageTaskMapper.selectByTaskIdRunning(dataModelId);
        if (Objects.isNull(storageTask)) {
            throw new IllegalArgumentException("未找到对应的任务，dataModelId = " + dataModelId);
        }
        storageTask.forEach(task -> {
            try {
                seaTunnelClient.stopJob(task.getStorageJobId());
            } catch (Exception e) {
                log.error("停止任务失败，storageJobId = {}", task.getStorageJobId());
            }
        });
    }

    @Override
    public SeaTunnelRunningJobInfo getRunningJobStatus(Long jobId) throws DataStorageEngineException {
        return seaTunnelClient.getJobInfo(jobId);
    }

    @Override
    public void recordSeaTunnelEvent(SeatunnelEvent event) {
        log.info("记录SeaTunnel事件: {}", JsonUtils.toJsonString(event));
        if (!JOB_STATUS_CHANGE.equals(event.eventType())) {
            log.info("事件类型不是任务状态变更，不做任何操作。eventType = {}", event.eventType());
            return;
        }

        StorageTask storageTask = storageTaskMapper.selectByJobId(event.getJobId());
        if (storageTask == null) {
            log.info("未找到对应的任务，不做任何操作。jobId = {}", event.getJobId());
            return;
        }

        DataModel dataModel = dataModelMapper.selectById(storageTask.getDataModelId());
        if (Objects.isNull(dataModel)) {
            log.info("未找到dataModelId为{}的数据源，停止更新任务增量信息以及判断相应任务是否停止",
                storageTask.getDataModelId());
            return;
        }

        SeaTunnelRunningJobInfo jobInfo = seaTunnelClient.getJobInfo(Long.valueOf(event.getJobId()));

        // 如果是需要记录链路信息的节点，记录链路信息
        if (event.getStorageTraceType() != null) {
            addStorageTaskTrace(event, storageTask, dataModel, jobInfo);
        }

        switch (event.jobStatus()) {
            case FINISHED:
                handleFinishedJob(event, storageTask, dataModel, jobInfo);
                break;
            case FAILED:
                handleFailedJob(event, storageTask, dataModel, jobInfo);
                break;
            case CANCELED:
                handleCanceledJob(event, storageTask, dataModel, jobInfo);
                break;
            default:
                log.info("任务状态为其他状态，不做任何操作。jobStatus = {}", event.jobStatus());
                break;
        }
        storageTask.setExecutionStatus(event.jobStatus());
        storageTaskMapper.updateJob(storageTask);
    }

    private void handleFinishedJob(SeatunnelEvent event, StorageTask storageTask, DataModel dataModel,
        SeaTunnelRunningJobInfo jobInfo) {

        //更新任务增量信息
        updateJobIncrement(dataModel, event, storageTask.getBatchNo());

        //如果是mq类型的话 更新为GROUP_OFFSET
        updateMqOffsetType(dataModel);

        // 更新实际写入数量
        updateWriteCounts(storageTask, dataModel, jobInfo, true);

        //完成也可能存储错误的数据插入，需要推送错误信息
        sendErrorMessage(storageTask, dataModel);

        storageTask.setReadSuccessCount(jobInfo.getMetrics().getSourceReceivedCount());
        storageTask.setEndTime(event.createdTime());
    }

    private void handleFailedJob(SeatunnelEvent event, StorageTask storageTask, DataModel dataModel,
        SeaTunnelRunningJobInfo jobInfo) {
        String batchNo = storageTask.getBatchNo();
        List<String> connectionNames = dataModel.getDataStorages().stream()
            .map(dataStorage -> dataStorage.getConnection().getName())
            .toList();
        List<DataAccessTrace> failedRecords = dataAccessTraceMapper.selectErrorByBatchNoAndStorageNames(batchNo,
            connectionNames);

        if (Objects.nonNull(failedRecords) && !failedRecords.isEmpty()) {
            List<Long> recordIds = failedRecords.stream().map(DataAccessTrace::getRecordId).toList();
            List<DataProcessTrace> dataProcessTraces = dataProcessTraceMapper.selectByRecordIds(recordIds);
            if (Objects.nonNull(dataProcessTraces) && !dataProcessTraces.isEmpty()) {
                String errorMsg = jobInfo.getErrorMsg();
                dataProcessTraces.forEach(dataProcessTrace -> {
                    dataProcessTrace.setErrorMsg(errorMsg);
                    dataProcessTrace.setIsError(1);
                    dataProcessTraceMapper.updateById(dataProcessTrace);
                });
            }
        }
        updateWriteCounts(storageTask, dataModel, jobInfo, true);
        storageTask.setErrorMessage(jobInfo.getErrorMsg());
        storageTask.setEndTime(event.createdTime());
    }

    private void handleCanceledJob(SeatunnelEvent event, StorageTask storageTask, DataModel dataModel,
        SeaTunnelRunningJobInfo jobInfo) {
        updateWriteCounts(storageTask, dataModel, jobInfo, false);
        storageTask.setEndTime(event.createdTime());
    }

    private void updateMqOffsetType(DataModel dataModel) {
        DataConnection connection = dataModel.getFirstDataSource().getConnection();
        if (ConnectionType.KAFKA == connection.getConnectionType()) {
            updateKafkaType(dataModel);
        } else if (ConnectionType.ROCKETMQ.equals(connection.getConnectionType())) {
            updateRocketMQType(dataModel);
        }
    }

    private void updateWriteCounts(StorageTask storageTask, DataModel dataModel, SeaTunnelRunningJobInfo jobInfo,
        boolean needWait) {
        log.info("----开始更新写入数量----");
        // 延迟后执行的逻辑
        dataModel.getDataStorages().forEach(dataStorage -> Arrays.stream(storageTask.getWriteCountInfo())
            .filter(wc -> wc.getStorageId().equals(dataStorage.getId()))
            .findFirst()
            .ifPresent(writeCount -> {
                String tablePath = dataModel.getDataStorage(writeCount.getStorageId()).getTablePath();
                if (needWait) {
                    try {
                        Thread.sleep(5000L);
                    } catch (InterruptedException ignore) {
                        Thread.currentThread().interrupt();
                        log.info("更新写入数量线程被中断，跳过等待");
                    }
                }
                // 查询失败数量
                List<DataAccessTrace> dataAccessTraces = dataAccessTraceMapper.selectErrorByBatchNoAndStorageNames(
                    storageTask.getBatchNo(), List.of(dataStorage.getConnection().getName()));
                log.info("当前存储点:{}, 更新写入失败数量: {}", tablePath, dataAccessTraces.size());
                long failCount = dataAccessTraces.size();
                writeCount.setFailCount(failCount);
                //更新成功数量
                Map<String, Long> tableSinkWriteCount = jobInfo.getMetrics().getTableSinkWriteCount();
                Long count = tableSinkWriteCount.getOrDefault(tablePath, 0L);
                long successCount = Math.max(0, count - failCount);
                log.info("当前存储点:{}, tableSinkWriteCount: {}, failCount: {} , 更新写入数量 : {}", tablePath,
                    count, failCount, successCount);
                writeCount.setSuccessCount(successCount);
            }));
    }

    private void addStorageTaskTrace(SeatunnelEvent event, StorageTask storageTask, DataModel dataModel,
        SeaTunnelRunningJobInfo jobInfo) {
        StorageTaskTrace storageTaskTrace = new StorageTaskTrace();
        storageTaskTrace.setId(UUIdUtils.generateUUID());
        storageTaskTrace.setDataModelId(dataModel.getId());
        storageTaskTrace.setBatchNo(storageTask.getBatchNo());
        storageTaskTrace.setNode(event.getStorageTraceType());
        storageTaskTrace.setDetails(buildDetails(event.jobStatus(), event, storageTask, jobInfo));
        storageTaskTrace.setIsError(FAILED.equals(event.jobStatus()) ? 1 : 0);
        storageTaskTrace.setStorageTime(LocalDateTime.now());
        storageTaskTrace.setStartTime(DateTimeUtils.parse(event.getCreatedTime()));
        log.info("开始插入storageTaskTrace记录，storageTaskTrace:{}", storageTaskTrace);
        storageTaskTraceMapper.insert(storageTaskTrace);
    }

    private String buildDetails(SeaTunnelJobStatus jobStatus, SeatunnelEvent event, StorageTask storageTask,
        SeaTunnelRunningJobInfo jobInfo) {
        log.info("开始构建详情信息，readSuccessCount:{}", jobInfo.getMetrics().getSourceReceivedCount());
        String createdTime = DateTimeUtils.parseStr(event.getCreatedTime());
        Map<String, Object> map = new HashMap<>();

        // 公共逻辑：设置 readSuccessCount 和 readFailCount
        if (FAILED.equals(jobStatus) || FINISHED.equals(jobStatus)) {
            map.put("读取成功条数", jobInfo.getMetrics().getSourceReceivedCount());
            map.put("读取失败条数", storageTask.getReadFailCount());
        }

        // 根据 jobStatus 设置特定字段
        switch (jobStatus) {
            case RUNNING -> map.put("开始时间", createdTime);
            case FAILED -> map.put("错误信息", storageTask.getErrorMessage());
            case FINISHED -> map.put("结束时间", createdTime);
            case CANCELED -> map.put("取消时间", createdTime);
            default -> log.debug("未知的任务状态: {}", jobStatus);
        }

        return JsonUtils.toJsonString(map);
    }

    private void updateKafkaType(DataModel dataModel) {
        log.info("dataModelId为{}的数据源,进行KAFKA消费模式的判断", dataModel.getId());
        DataModelExecuteConfig executeConfig = dataModel.getExecuteConfig();
        if (Objects.isNull(executeConfig)) {
            log.info("dataModelId为{}的数据源,executeConfig为空", dataModel.getId());
            return;
        }
        KafkaExecuteParams executeParams = getKafkaExecuteParams(executeConfig);
        if (executeParams.isGroupOffsetType()) {
            return;
        }
        log.info("更新dataModelId为{}的数据源的KAFKA执行参数,从{}变为GROUP_OFFSET", dataModel.getId(),
            executeParams.getOffsetResetType());
        executeParams.setOffsetResetType(KafkaOffsetResetType.GROUP_OFFSETS);
        executeParams.setConnectionType(ConnectionType.KAFKA);
        dataModelExecuteConfigMapper.updateExecuteParamsByDataModelId(dataModel.getId(),
            JsonUtils.toJsonString(executeParams));
    }

    private KafkaExecuteParams getKafkaExecuteParams(DataModelExecuteConfig executeConfig) {
        if (Objects.isNull(executeConfig.getExecuteParams())) {
            return new KafkaExecuteParams();
        }
        return (KafkaExecuteParams) executeConfig.getExecuteParams();
    }

    private void updateRocketMQType(DataModel dataModel) {
        log.info("dataModelId为{}的数据源,进行ROCKETMQ消费模式的判断", dataModel.getId());
        DataModelExecuteConfig executeConfig = dataModel.getExecuteConfig();
        if (Objects.isNull(executeConfig)) {
            log.info("dataModelId为{}的数据源,executeConfig为空", dataModel.getId());
            return;
        }
        RocketMQExecuteParams executeParams = getRocketMQExecuteParams(executeConfig);
        if (executeParams.isGroupOffsetType()) {
            return;
        }
        log.info("更新dataModelId为{}的数据源的ROCKETMQ执行参数,从{}变为GROUP_OFFSET", dataModel.getId(),
            executeParams.getOffsetResetType());
        executeParams.setOffsetResetType(RocketMQOffsetResetType.CONSUME_FROM_GROUP_OFFSETS);
        executeParams.setConnectionType(ConnectionType.ROCKETMQ);
        dataModelExecuteConfigMapper.updateExecuteParamsByDataModelId(dataModel.getId(),
            JsonUtils.toJsonString(executeParams));
    }

    private RocketMQExecuteParams getRocketMQExecuteParams(DataModelExecuteConfig executeConfig) {
        if (executeConfig.getExecuteParams() == null) {
            return new RocketMQExecuteParams();
        }
        return (RocketMQExecuteParams) executeConfig.getExecuteParams();
    }

    private void updateJobIncrement(DataModel dataModel, SeatunnelEvent event, String batchNo) {
        log.info("开始更新任务增量信息");
        StorageTaskTrace storageTaskTrace = buildStorageTaskTraceIncrement(event, batchNo,
            dataModel.getId());
        if (!dataModel.isIncreaseJob()) {
            log.info("任务 {} 不是增量任务，停止更新任务增量信息", dataModel.getZhName());
            storageTaskTrace.setDetails(
                JsonUtils.toJsonString(Map.of("增量信息", "该任务非增量任务,若需要增量请检查增量字段是否配置")));
            storageTaskTrace.setIsError(0);
            storageTaskTraceMapper.insert(storageTaskTrace);
            return;
        }
        log.info("更新executeConfig表");
        updateExecuteConfig(dataModel, storageTaskTrace);
        // 如果是Ftp和Sftp数据源的任务，清理掉已经读取过的文件到备份路径中。
        ConnectionParams connectionParams = dataModel.getFirstDataSource().getConnection().getConnectionParams();
        MoyeConnection connection = ConnectionUtils.getConnection(connectionParams);
        if (connection instanceof FileConnection fileConnection) {
            log.info("开始清理Ftp/Sftp数据源已读取文件，dataModelId: {}", dataModel.getId());
            executor.submit(() -> cleanupHistoryFile(dataModel, fileConnection));
        }
    }

    private static void cleanupHistoryFile(DataModel dataModel, FileConnection fileConnection) {
        try (fileConnection) {
            FileCleanupRequest cleanupRequest = new FileCleanupRequest();
            FtpDataSourceSettings settings = (FtpDataSourceSettings) dataModel.getFirstDataSource().getSettings();
            cleanupRequest.setWorkingDirectory(settings.getWorkingDirectory());
            cleanupRequest.setCleanupBeforeTime(LocalDateTime.now());
            cleanupRequest.setBackupDirectoryName(
                "backup_" + DateTimeUtils.format(LocalDate.now(), Formatter.YYYYMMDD));
            fileConnection.cleanupFiles(cleanupRequest);
            log.info("已清理Ftp/Sftp数据源已读取文件，dataModelId: {}", dataModel.getId());
        } catch (Exception e) {
            log.error("Ftp/Sftp数据源文件清理失败，dataModelId: {}", dataModel.getId(), e);
        }
    }

    private StorageTaskTrace buildStorageTaskTraceIncrement(SeatunnelEvent event, String batchNo, Integer dataModelId) {
        log.info("开始创建增量信息节点");
        StorageTaskTrace storageTaskTrace = new StorageTaskTrace();
        storageTaskTrace.setId(UUIdUtils.generateUUID());
        storageTaskTrace.setDataModelId(dataModelId);
        storageTaskTrace.setBatchNo(batchNo);
        storageTaskTrace.setNode(SeaTunnelStorageTraceType.INCREMENT_UPDATE);
        storageTaskTrace.setStorageTime(LocalDateTime.now());
        storageTaskTrace.setStartTime(DateTimeUtils.parse(event.getCreatedTime()));
        return storageTaskTrace;
    }

    private void updateExecuteConfig(DataModel dataModel, StorageTaskTrace storageTaskTrace) {
        Object incrementValue;
        String connectionTypeName = dataModel.getFirstDataSource().getConnection().getConnectionType().getLabel();
        String incrementField =
            Objects.nonNull(dataModel.getExecuteConfig().getIncrementInfo()) ? dataModel.getExecuteConfig()
                .getIncrementInfo().getFieldName() : "空";
        try {
            incrementValue = getIncrementValue(dataModel);
            if (incrementValue == null) {
                log.error("connectionType:{} : 获取最大值为空!!", connectionTypeName);
                return;
            }
            storageTaskTrace.setDetails(JsonUtils.toJsonString(
                Map.of("增量信息字段: {" + incrementField + "}",
                    "更新后的增量值: {" + incrementValue + "}")));
            storageTaskTrace.setIsError(0);
            storageTaskTraceMapper.insert(storageTaskTrace);
            // 更新增量值
            log.info("dataModelId: {}, connectionType:{} , 更新增量值: {}",
                dataModel.getId(), connectionTypeName, incrementValue);
            dataModelExecuteConfigMapper.updateIncrementValueByDataModelId(incrementValue, dataModel.getId());
        } catch (Exception e) {
            log.error("connectionType:{} : 更新增量信息失败!", connectionTypeName, e);
            storageTaskTrace.setDetails(JsonUtils.toJsonString(
                Map.of("增量信息字段" + incrementField,
                    "更新增量失败:" + e.getMessage())));
            storageTaskTrace.setIsError(1);
            storageTaskTraceMapper.insert(storageTaskTrace);
        }
    }

    private Object getIncrementValue(DataModel dataModel) {
        DataConnection dataConnection = dataModel.getFirstDataSource().getConnection();
        ConnectionParams connectionParams = dataConnection.getConnectionParams();
        try {
            if (dataConnection.isDBConnection()) { //db类型
                return handleDbMaxValue(dataModel, connectionParams, dataConnection);
            } else if (dataConnection.isFileConnection()) {
                //ftp类型增量目前只支持时间
                return LocalDateTime.now().format(Formatter.YYYY_MM_DD_HH_MM_SS.getDateTimeFormatter());
            } else if (dataConnection.isHttpConnection()) {
                return handleApiMaxValue(dataModel);
            }
        } catch (Exception e) {
            log.error("dataModelId: {},connectionType:{} : 获取最大值异常!", dataModel.getId(), e.getMessage());
        }
        return null; // 不支持的类型返回null
    }

    private Object handleApiMaxValue(DataModel dataModel) throws Exception {
        List<DataModelField> dataModelFields = dataModelFieldMapper.selectByDataModelId(dataModel.getId());
        DataModelField dataModelField = dataModelFields.stream().filter(DataModelField::isIncrement).findFirst()
            .orElse(null);
        DataStorage dataStorage = dataModel.getDataStorages().stream()
            .filter(storageDbInfo -> CreateTableStatus.SUCCESS.equals(storageDbInfo.getCreateTableStatus()))
            .findFirst() // 获取第一个匹配的元素
            .orElse(null);
        if (Objects.isNull(dataStorage) || Objects.isNull(dataModelField)) {
            return LocalDateTime.now().format(Formatter.YYYY_MM_DD_HH_MM_SS.getDateTimeFormatter());
        }
        DataConnection dataStorageConnection = dataStorage.getConnection();
        ConnectionParams storageConnectionParams = dataStorageConnection.getConnectionParams();
        return queryApiStorageDataBaseMaxValue(dataModel, storageConnectionParams, dataStorageConnection,
            dataModelField);
    }

    private Object queryApiStorageDataBaseMaxValue(DataModel dataModel, ConnectionParams storageConnectionParams,
        DataConnection dataStorageConnection, DataModelField dataModelField) throws Exception {
        try (DatabaseConnection connection = ConnectionUtils.getConnection(storageConnectionParams,
            dataStorageConnection.getKerberosCertificate())) {
            DataModelExecuteConfig dataModelExecuteConfig = dataModelExecuteConfigMapper.selectByDataModelId(
                dataModel.getId());
            if (Objects.isNull(dataModelExecuteConfig) || Objects.isNull(dataModelExecuteConfig.getIncrementInfo())) {
                log.info(
                    "dataModelId: {}, connectionType:{} : dataModelExecuteConfig或者增量信息为空!, 无法查询API类型的最大值!",
                    dataModel.getId(), storageConnectionParams.getConnectionType().getLabel());
                return LocalDateTime.now().format(Formatter.YYYY_MM_DD_HH_MM_SS.getDateTimeFormatter());
            }
            String tableName = dataModel.getEnName();
            if (connection instanceof HybaseConnection) {
                tableName = ((HybaseConnectionParams) storageConnectionParams).getDatabase() + "." + tableName;
            } else if (connection instanceof HiveConnection) {
                tableName = ((HiveConnectionParams) storageConnectionParams).getDatabase();
            }
            return connection.getMaxValue(tableName, dataModelField.getEnName());
        }
    }

    private @Nullable
    Object handleDbMaxValue(DataModel dataModel, ConnectionParams connectionParams,
        DataConnection dataConnection) throws Exception {
        log.info("dataModelId: {}, connectionType: {}", dataModel.getId(),
            connectionParams.getConnectionType().getLabel());
        if (!dataConnection.getConnectionType().isHyBaseType()) {
            log.info("非海贝处理逻辑：获取最大值");
            return getMaxValue(dataModel, connectionParams, dataConnection);
        }
        log.info("海贝数据源，获取增量值");
        return handleHyBaseIncrement(dataModel, connectionParams, dataConnection);
    }

    private Object handleHyBaseIncrement(DataModel dataModel, ConnectionParams connectionParams,
        DataConnection dataConnection) throws Exception {
        DataModelExecuteConfig dataModelExecuteConfig = dataModelExecuteConfigMapper.selectByDataModelId(
            dataModel.getId());
        boolean isDateType = dataModelExecuteConfig.getIncrementInfo().isDateType();
        if (isDateType) {
            log.info("海贝数据源，增量类型为时间, 返回当前时间: {}", LocalDateTime.now());
            return LocalDateTime.now().format(Formatter.YYYY_MM_DD_HH_MM_SS.getDateTimeFormatter());
        } else {
            boolean isNumberType = dataModelExecuteConfig.getIncrementInfo().isNumberType();
            if (isNumberType) {
                log.info("海贝数据源，增量类型为数字, 获取最大值");
                return getMaxValue(dataModel, connectionParams, dataConnection);
            } else {
                log.error("海贝数据源不支持的增量类型：{}", dataModelExecuteConfig.getIncrementInfo().getFieldType());
            }
        }
        return null;
    }

    private @Nullable
    Object getMaxValue(DataModel dataModel, ConnectionParams connectionParams,
        DataConnection dataConnection) throws Exception {
        try (DatabaseConnection connection = ConnectionUtils.getConnection(connectionParams,
            dataConnection.getKerberosCertificate())) {
            DataModelExecuteConfig dataModelExecuteConfig = dataModelExecuteConfigMapper.selectByDataModelId(
                dataModel.getId());
            if (Objects.isNull(dataModelExecuteConfig) || Objects.isNull(dataModelExecuteConfig.getIncrementInfo())) {
                log.info(
                    "dataModelId: {}, connectionType:{} : dataModelExecuteConfig或者增量信息为空!, 无法查询最大值!",
                    dataModel.getId(), connectionParams.getConnectionType().getLabel());
                return null;
            }
            String tableName = dataModel.getFirstDataSource().getEnName();
            if (connection instanceof HybaseConnection) {
                tableName = ((HybaseConnectionParams) connectionParams).getDatabase() + "." + tableName;
            } else if (connection instanceof OracleConnection) {
                tableName = ((OracleConnectionParams) connectionParams).getSchema() + "." + tableName;
            }
            return connection.getMaxValue(tableName, dataModelExecuteConfig.getIncrementInfo().getFieldName());
        }
    }

    private void sendErrorMessage(StorageTask storageTask, DataModel dataModel) {
        //如果是完成的情况下可能存在部分数据失败，需要对数据进行细粒度判断
        //由于要素库有多个存储点，一个存储点对应一个任务，如果单个任务失败的话需要通过对应的存储点去查询
        //这里保存的是连接ID
        List<Integer> storageIds = storageTask.getStorageIds();
        List<String> storageNames = new ArrayList<>();
        dataModel.getDataStorages().forEach(dataStorage -> {
            if (storageIds.contains(dataStorage.getId())) {
                storageNames.add(dataStorage.getZhName());
            }
        });
        log.info("查询接入数据错误列表参数，批次号={},存储点名称={}", storageTask.getBatchNo(), storageNames);
        List<DataAccessTrace> dataAccessTraces = dataAccessTraceMapper.selectErrorByBatchNoAndStorageNames(
            storageTask.getBatchNo(), storageNames);
        if (!dataAccessTraces.isEmpty()) {
            List<DataAccessTrace> groupedTraces = dataAccessTraces.stream()
                .collect(Collectors.groupingBy(DataAccessTrace::getStorageName))
                .values().stream()
                .map(traces -> traces.get(0))
                .toList();
            StringBuilder sb = new StringBuilder();
            for (DataAccessTrace groupedTrace : groupedTraces) {
                sb.append("【").append(dataModel.getLayer().getLabel()).append("】")
                    .append(dataModel.getZhName())
                    .append("数据存储异常！").append("，任务ID：").append(storageTask.getId())
                    .append("，存储点名称：")
                    .append(groupedTrace.getStorageName()).append("，详细信息:")
                    .append(groupedTrace.getErrorMessage())
                    .append("\n");
            }
            log.info("发送存储错误日志{}", JsonUtils.toJsonString(storageTask));
            noticeService.sendNotice(storageTask, sb.toString());
            log.info("storageTaskId:{}，成功推送错误信息！详细信息：{}", storageTask.getId(), sb);
        } else {
            log.info("storageTaskId:{}任务完成！错误数据列表为空。", storageTask.getId());
        }
    }

    @Override
    public StorageTask updateJobStatus(String storageJobId) {
        log.info("开始更新seatunnel任务状态, storageJobId = {}", storageJobId);
        SeaTunnelRunningJobInfo jobInfo;
        try {
            jobInfo = seaTunnelClient.getJobInfo(Long.parseLong(storageJobId));
        } catch (Exception exception) {
            log.error("查询seatunnel已完成任务状态失败！");
            throw new DataStorageEngineException("查询seatunnel已完成任务状态失败！");
        }

        StorageTask storageTask = storageTaskMapper.selectByJobId(storageJobId);
        log.info("同步执行状态,jobInfo =  {}", jobInfo);
        log.info("同步执行状态,storageTask =  {}", storageTask);
        if (Objects.isNull(jobInfo)) {
            storageTask.setExecutionStatus(CANCELED);
            storageTask.setEndTime(LocalDateTime.now());
            storageTaskMapper.updateJob(storageTask);
            return null;
        }
        if (storageTask.getStorageJobId().equals(jobInfo.getJobId())) {
            storageTask.setExecutionStatus(jobInfo.getJobStatus());
            storageTask.setErrorMessage(jobInfo.getErrorMsg());
            storageTask.setReadSuccessCount(jobInfo.getMetrics().getSourceReceivedCount());
            DataModel dataModel = dataModelMapper.selectById(storageTask.getDataModelId());
            updateWriteCounts(storageTask, dataModel, jobInfo, false);
            storageTaskMapper.updateJob(storageTask);
        }
        return storageTask;
    }

    @Override
    public String createStorageOperatorTopic(Integer dataModelId) throws BizException {
        DataModel dataModel = dataModelMapper.selectById(dataModelId);
        List<DataStorage> dataStorage = dataModel.getDataStorages();
        if (Objects.isNull(dataStorage) || dataStorage.isEmpty()) {
            throw new BizException("未设置数据存储");
        }
        return dataStorage.stream()
            .filter(e -> e.getConnection().getConnectionType().getCategory() == DataSourceCategory.DATA_BASE)
            .map(e -> {
                String topicName = DataProcessUtils.buildDataStorageTopic(dataModelId, e.getId());
                createTopic(topicName);
                return topicName;
            }).collect(Collectors.joining(","));
    }

    private void createTopic(String topicName) {
        if (!checkTopicExist(topicName)) {
            NewTopic newTopic = new NewTopic(topicName, producerProperties.getPartitions(),
                producerProperties.getReplicas());
            Map<String, String> configMap = new HashMap<>();
            configMap.put(TopicConfig.RETENTION_MS_CONFIG, producerProperties.getRetentionMs());
            configMap.put(TopicConfig.RETENTION_BYTES_CONFIG, "10737418240");
            newTopic.configs(configMap);
            CreateTopicsResult result = adminClient.createTopics(List.of(newTopic));
            try {
                log.info("创建topic：{}", topicName);
                result.all().get();
                log.warn("创建topic[{}]成功！", topicName);
            } catch (ExecutionException e) {
                log.error("创建topic[{}]失败！", topicName, e);
                throw new BizException("创建topic失败");
            } catch (InterruptedException e) {
                log.error("创建topic[{}]线程中断！", topicName, e);
                Thread.currentThread().interrupt();
            }
        }
    }

    private boolean checkTopicExist(String topicName) {
        try {
            Set<String> topicNames = adminClient.listTopics().names().get();
            return topicNames.contains(topicName);
        } catch (ExecutionException e) {
            log.error("获取topic列表失败！", e);
        } catch (InterruptedException e) {
            log.error("获取topic列表线程中断！", e);
            Thread.currentThread().interrupt();
        }
        return false;
    }
}
