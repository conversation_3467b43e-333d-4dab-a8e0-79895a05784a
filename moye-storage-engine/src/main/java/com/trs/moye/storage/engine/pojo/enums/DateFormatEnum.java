package com.trs.moye.storage.engine.pojo.enums;


import com.trs.moye.base.common.enums.TimeUnit;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * 日期格式 的枚举
 */
@NoArgsConstructor
@AllArgsConstructor
public enum DateFormatEnum {

    YEAR("yyyy", TimeUnit.YEAR),

    MONTH("yyyyMM", TimeUnit.MONTH),

    MONTH_HYPHEN("yyyy-MM", TimeUnit.MONTH),

    DAY("yyyyMMdd", TimeUnit.DAY),

    DAY_HYPHEN("yyyy-MM-dd", TimeUnit.DAY);

    @Getter
    private String format;

    @Getter
    private TimeUnit timeUnit;

}
