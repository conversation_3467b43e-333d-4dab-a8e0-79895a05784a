package com.trs.moye.storage.engine.db.elasticsearch;

import static com.trs.moye.storage.engine.db.elasticsearch.ElasticsearchRequestBuilder.FAILED_CREATE_FIELD;
import static com.trs.moye.storage.engine.db.elasticsearch.ElasticsearchRequestBuilder.FAILED_CREATE_INDEX;
import static com.trs.moye.storage.engine.db.elasticsearch.ElasticsearchRequestBuilder.FAILED_GETTING_FIELD_MESSAGE;
import static com.trs.moye.storage.engine.db.elasticsearch.ElasticsearchRequestBuilder.FAIL_SEARCH_ES;
import static com.trs.moye.storage.engine.db.elasticsearch.ElasticsearchRequestBuilder.FIELD;
import static com.trs.moye.storage.engine.db.elasticsearch.ElasticsearchRequestBuilder.GROUP_BY;
import static com.trs.moye.storage.engine.db.elasticsearch.ElasticsearchRequestBuilder.MAPPING;
import static com.trs.moye.storage.engine.db.elasticsearch.ElasticsearchRequestBuilder.MAPPING_INDEX_FAILED;
import static com.trs.moye.storage.engine.db.elasticsearch.ElasticsearchRequestBuilder.MAX;
import static com.trs.moye.storage.engine.db.elasticsearch.ElasticsearchRequestBuilder.MIN;
import static com.trs.moye.storage.engine.db.elasticsearch.ElasticsearchRequestBuilder.PROPERTIES;
import static com.trs.moye.storage.engine.db.elasticsearch.ElasticsearchRequestBuilder.SEARCH;
import static com.trs.moye.storage.engine.db.elasticsearch.ElasticsearchRequestBuilder.SEARCH_INDEX_FAILED;
import static com.trs.moye.storage.engine.db.elasticsearch.ElasticsearchRequestBuilder.SUM;
import static com.trs.moye.storage.engine.db.elasticsearch.ElasticsearchRequestBuilder.S_COUNT;
import static com.trs.moye.storage.engine.db.elasticsearch.ElasticsearchRequestBuilder.VALUE;
import static com.trs.moye.storage.engine.service.impl.SearchServiceImpl.DATA_SERVICE_COUNT_NUM;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.NullNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.trs.moye.base.common.exception.BizException;
import com.trs.moye.base.common.log.api.ApiLogTracerUtils;
import com.trs.moye.base.common.utils.JsonUtils;
import com.trs.moye.base.data.connection.entity.DataConnection;
import com.trs.moye.base.data.connection.entity.KerberosCertificate;
import com.trs.moye.base.data.connection.entity.params.ConnectionParams;
import com.trs.moye.base.data.connection.entity.params.ElasticSearchConnectionParams;
import com.trs.moye.base.data.model.entity.DataModel;
import com.trs.moye.base.data.model.entity.DataModelField;
import com.trs.moye.base.data.storage.setting.DataStorageSettings;
import com.trs.moye.storage.engine.db.BasicTypeDefine;
import com.trs.moye.storage.engine.db.DatabaseConnection;
import com.trs.moye.storage.engine.db.TypeConverter;
import com.trs.moye.storage.engine.exception.DataStorageEngineException;
import com.trs.moye.storage.engine.pojo.enums.ServiceOperatorType;
import com.trs.moye.storage.engine.pojo.request.search.CodeSearchParams;
import com.trs.moye.storage.engine.pojo.request.search.Condition;
import com.trs.moye.storage.engine.pojo.request.search.ConditionSearchParams;
import com.trs.moye.storage.engine.pojo.response.StorageEngineResponse;
import com.trs.moye.storage.engine.pojo.response.alltable.TableResponse;
import com.trs.moye.storage.engine.utils.SqlToDslParser;
import com.trs.moye.storage.engine.utils.SqlUtils;
import java.io.IOException;
import java.security.KeyManagementException;
import java.security.KeyStoreException;
import java.security.NoSuchAlgorithmException;
import java.util.ArrayList;
import java.util.Base64;
import java.util.Collections;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Objects;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Stream;
import javax.net.ssl.SSLContext;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.Header;
import org.apache.http.HttpHost;
import org.apache.http.HttpStatus;
import org.apache.http.auth.AuthScope;
import org.apache.http.auth.UsernamePasswordCredentials;
import org.apache.http.client.CredentialsProvider;
import org.apache.http.conn.ssl.TrustAllStrategy;
import org.apache.http.impl.client.BasicCredentialsProvider;
import org.apache.http.message.BasicHeader;
import org.apache.http.ssl.SSLContexts;
import org.apache.http.util.EntityUtils;
import org.elasticsearch.client.Request;
import org.elasticsearch.client.Response;
import org.elasticsearch.client.ResponseException;
import org.elasticsearch.client.RestClient;
import org.elasticsearch.client.RestClientBuilder;
import org.jetbrains.annotations.Nullable;

/**
 * Elasticsearch 数据库连接
 */
@Slf4j
@EqualsAndHashCode(callSuper = true)
@Data
public class ElasticsearchConnection extends DatabaseConnection {

    public static final String KEY_AS_STRING = "key_as_string";
    public static final String DOC_COUNT = "doc_count";
    public static final String COUNT_PARAM = "count";
    public static final String HITS = "hits";
    public static final String KEY = "key";
    public static final String COUNT_NAME = "COUNT";
    public static final String AGGREGATIONS = "aggregations";
    public static final String VALUE_AS_STRING = "value_as_string";
    public static final String UNIQUE_COUNT = "unique_count";
    public static final String BUCKETS = "buckets";
    private final RestClient restClient;
    private final ElasticsearchTypeConverter converter = new ElasticsearchTypeConverter();

    /**
     * 根据moye的连接信息创建数据库连接
     *
     * @param connectionParams moye连接信息
     * @param authCertificate  kerberos证书
     */
    public ElasticsearchConnection(@NotNull ConnectionParams connectionParams,
        KerberosCertificate authCertificate) {
        if (connectionParams instanceof ElasticSearchConnectionParams esConnectionParams) {
            this.restClient = createEsConnection(esConnectionParams, authCertificate);
        } else {
            throw new DataStorageEngineException("不支持数据库连接类型: " + connectionParams.getClass().getName());
        }
    }

    private static RestClient createEsConnection(@NotNull ElasticSearchConnectionParams connectionParams,
        KerberosCertificate authCertificate) {
        HttpHost[] esHosts = Stream.of(connectionParams.getHost().split(","))
            .map(host -> new HttpHost(host.trim(), connectionParams.getPort(), connectionParams.getProtocol()))
            .toArray(HttpHost[]::new);
        RestClientBuilder builder = RestClient.builder(esHosts);
        //如果开启kerberos认证
        if (Objects.nonNull(authCertificate)) {
            System.setProperty("java.security.krb5.conf", authCertificate.getKrb5Path());
            builder.setHttpClientConfigCallback(
                new SpnegoHttpClientConfigCallbackHandler(authCertificate.getPrincipal(),
                    authCertificate.getKeytabPath(), false));
        }
        //如果开启用户名密码认证
        else if (connectionParams.getUsername() != null && connectionParams.getDecryptedPassword() != null) {
            final CredentialsProvider credentialsProvider = new BasicCredentialsProvider();
            credentialsProvider.setCredentials(AuthScope.ANY,
                new UsernamePasswordCredentials(connectionParams.getUsername(),
                    connectionParams.getDecryptedPassword()));
            builder.setHttpClientConfigCallback(httpClientBuilder -> {
                httpClientBuilder.setDefaultCredentialsProvider(credentialsProvider);

                try {
                    SSLContext sslContext = SSLContexts.custom().loadTrustMaterial(new TrustAllStrategy()).build();
                    httpClientBuilder.setSSLContext(sslContext);
                } catch (NoSuchAlgorithmException | KeyManagementException | KeyStoreException e) {
                    log.error("创建SSLContext失败", e);
                    throw new DataStorageEngineException("创建SSLContext失败");
                }

                return httpClientBuilder;
            });
            //华为云es需要使用basic auth
            String auth = Base64.getEncoder()
                .encodeToString(
                    (connectionParams.getUsername() + ":" + connectionParams.getDecryptedPassword()).getBytes());
            builder.setDefaultHeaders(new Header[]{new BasicHeader("Authorization", "Basic " + auth)});
        }
        return builder.build();
    }

    @Override
    public boolean testConnection() {
        try {
            return
                this.restClient.performRequest(new Request("GET", "/_cluster/health")).getStatusLine().getStatusCode()
                    == HttpStatus.SC_OK;
        } catch (IOException e) {
            log.error("Elasticsearch连接测试失败", e);
            return false;
        }
    }

    /**
     * 测试连接是否可用，抛出异常
     */
    @Override
    public void testConnectionWithException() throws IOException, DataStorageEngineException {
        Response response = this.restClient.performRequest(new Request("GET", "/_cluster/health"));
        if (response.getStatusLine().getStatusCode() != HttpStatus.SC_OK) {
            String errorMsg = EntityUtils.toString(response.getEntity());
            throw new DataStorageEngineException(
                "Elasticsearch连接测试失败，状态码: " + response.getStatusLine().getStatusCode() + "，错误信息: "
                    + errorMsg);
        }
    }

    @Override
    public StorageEngineResponse createTable(DataModel table, DataStorageSettings settings) {
        try {
            String endpoint = String.format("/%s", table.getEnName());
            Request request = new Request("PUT", endpoint);
            request.setJsonEntity(ElasticsearchRequestBuilder.getCreateTableEntity(table));
            Response response = restClient.performRequest(request);
            if (response == null) {
                throw new DataStorageEngineException(FAILED_CREATE_INDEX);
            }
            return response.getStatusLine().getStatusCode() == HttpStatus.SC_OK ? StorageEngineResponse.success()
                : StorageEngineResponse.fail(FAILED_CREATE_INDEX);
        } catch (IOException e) {
            log.error(FAILED_CREATE_INDEX, e);
            return StorageEngineResponse.fail(FAILED_CREATE_INDEX +  " : " + e.getMessage());
        }
    }

    @Override
    public DataModel getTableInfo(String tableName) {
        DataModel result = new DataModel();
        result.setZhName(tableName);
        result.setFields(getTableFields(tableName));
        return result;
    }

    @Override
    public List<BasicTypeDefine> getTableFieldDefines(String tableName) {
        return parseIndexProperties(tableName, ElasticsearchConnection::parseFieldDefines);
    }

    private List<DataModelField> getTableFields(String tableName) {
        return parseIndexProperties(tableName, this::parseModelFields);
    }

    private <T> List<T> parseIndexProperties(String tableName, Function<JsonNode, List<T>> parser) {
        try {
            String entity = getEsMappings(tableName);
            ObjectNode responseJson = JsonUtils.parseObjectNode(entity);
            JsonNode index = responseJson.get(tableName);
            JsonNode properties = index.path("mappings").path(PROPERTIES);
            if (properties == null) {
                return new ArrayList<>();
            }
            return parser.apply(properties);
        } catch (IOException e) {
            log.error(FAILED_GETTING_FIELD_MESSAGE, e);
            throw new DataStorageEngineException(FAILED_GETTING_FIELD_MESSAGE);
        }
    }

    private String getEsMappings(String tableName) throws IOException {
        String endpoint = String.format("/%s/_mappings", tableName);
        Request request = new Request("GET", endpoint);
        Response response = restClient.performRequest(request);
        if (response == null) {
            throw new DataStorageEngineException(FAILED_GETTING_FIELD_MESSAGE);
        }
        if (response.getStatusLine().getStatusCode() != HttpStatus.SC_OK) {
            throw new DataStorageEngineException(String.format("获取ES索引字段失败，GET %s 返回状态码=%d", endpoint,
                response.getStatusLine().getStatusCode()));
        }
        String entity = EntityUtils.toString(response.getEntity());
        log.info("请求es索引：GET {} response={}", endpoint, entity);
        return entity;
    }

    private List<DataModelField> parseModelFields(JsonNode properties) {
        return parseFieldDefines(properties).stream().map(converter::convert).toList();
    }

    /**
     * 解析es索引的字段定义
     *
     * @param properties es索引的properties
     * @return 字段定义列表
     */
    public static List<BasicTypeDefine> parseFieldDefines(JsonNode properties) {
        List<BasicTypeDefine> fieldDefines = new ArrayList<>();
        parseFieldDefines(properties, fieldDefines);
        return fieldDefines;
    }

    private static void parseFieldDefines(JsonNode properties, List<BasicTypeDefine> fields) {
        for (Iterator<Entry<String, JsonNode>> it = properties.fields(); it.hasNext(); ) {
            Entry<String, JsonNode> fieldEntry = it.next();
            JsonNode fieldProperty = fieldEntry.getValue();
            //有嵌套
            if (fieldProperty.get(PROPERTIES) != null) {
                parseFieldDefines(fieldProperty.get(PROPERTIES), fields);
            } else {
                String originalType = fieldProperty.get("type").asText();
                BasicTypeDefine.BasicTypeDefineBuilder builder = BasicTypeDefine.builder()
                    .name(fieldEntry.getKey())
                    .columnType(originalType)
                    .dataType(originalType)
                    .nullable(true);
                JsonNode meta = fieldProperty.get("meta");
                if (meta != null && meta.has("comment")) {
                    String comment = meta.get("comment").asText();
                    builder.comment(comment);
                }
                //如果是向量类型需要解析维度
                if (originalType.equals("dense_vector")) {
                    builder.scale(fieldProperty.get("dims").asInt());
                }
                if (originalType.equals("date")) {
                    if (fieldProperty.has("format")) {
                        // 处理日期格式
                        String format = fieldProperty.get("format").asText();
                        builder.format(format);
                    }
                }

                BasicTypeDefine basicTypeDefine = builder.build();
                fields.add(basicTypeDefine);
            }
        }
    }

    @Override
    public boolean dropTable(String tableName) {
        String endpoint = String.format("/%s", tableName);
        Request request = new Request("DELETE", endpoint);
        try {
            Response response = restClient.performRequest(request);
            if (response == null) {
                throw new DataStorageEngineException("删除ES索引失败");
            }
            return response.getStatusLine().getStatusCode() == HttpStatus.SC_OK;
        } catch (IOException ex) {
            log.error("删除ES索引失败", ex);
            return false;
        }
    }

    @Override
    public List<TableResponse> getAllTables() {
        String endpoint = "/_cat/indices?format=json";
        Request request = new Request("GET", endpoint);
        try {
            Response response = restClient.performRequest(request);
            if (response == null) {
                throw new DataStorageEngineException("获取ES索引列表失败");
            }
            if (response.getStatusLine().getStatusCode() == HttpStatus.SC_OK) {
                String entity = EntityUtils.toString(response.getEntity());
                return JsonUtils.toList(entity, Map.class).stream().map(map -> map.get("index").toString())
                    .map(index -> new TableResponse(index, index)).toList();
            } else {
                throw new DataStorageEngineException(String.format("获取ES索引列表失败，GET %s 返回状态码=%d", endpoint,
                    response.getStatusLine().getStatusCode()));
            }
        } catch (IOException ex) {
            log.error("获取ES索引列表失败", ex);
            return List.of();
        }
    }

    @Override
    public StorageEngineResponse addFields(String tableName, List<DataModelField> fields) {
        try {
            JsonNode esTableInfo = getEsTableInfo(tableName);
            String endpoint = String.format(MAPPING, tableName);
            Request request = new Request("PUT", endpoint);
            request.setJsonEntity(ElasticsearchRequestBuilder.getAddFieldEntity(fields, esTableInfo));
            Response response = restClient.performRequest(request);
            if (response == null) {
                throw new DataStorageEngineException(FAILED_CREATE_FIELD);
            }
            return response.getStatusLine().getStatusCode() == HttpStatus.SC_OK ? StorageEngineResponse.success()
                : StorageEngineResponse.fail(FAILED_CREATE_FIELD);
        } catch (IOException e) {
            log.error(FAILED_CREATE_FIELD, e);
            return StorageEngineResponse.fail(FAILED_CREATE_FIELD + " : " + e.getMessage());
        }
    }

    @Override
    public long count(List<Condition> conditions, String tablePath) {
        return 0;
    }

    @Override
    public long count(List<Condition> conditions, String tablePath, DataConnection dataConnection) {
        try {
            ConditionSearchParams searchParams = new ConditionSearchParams();
            searchParams.setConditions(conditions);
            searchParams.setOperationType(ServiceOperatorType.QUERY);
            ObjectNode dsl = ElasticsearchRequestBuilder.buildQuery(tablePath, searchParams, dataConnection,
                restClient);
            String endpoint = String.format("/%s/_count", tablePath);
            Request request = new Request("POST", endpoint);
            ElasticsearchRequestBuilder.removeNoUseField(dsl);
            request.setJsonEntity(dsl.toString());
            log.info("查询ES count请求参数：{}", dsl);
            ApiLogTracerUtils.appendInputDetail("count查询语句", JsonUtils.toJsonString(dsl));
            Response response = restClient.performRequest(request);
            if (response == null) {
                throw new DataStorageEngineException(SEARCH_INDEX_FAILED);
            }
            if (response.getStatusLine().getStatusCode() == HttpStatus.SC_OK) {
                String entity = EntityUtils.toString(response.getEntity());
                JsonNode responseJson = JsonUtils.parseObjectNode(entity);
                return responseJson.get(COUNT_PARAM).asLong();
            } else {
                throw new DataStorageEngineException(String.format("查询ES索引失败，GET %s 返回状态码=%d", endpoint,
                    response.getStatusLine().getStatusCode()));
            }
        } catch (IOException e) {
            log.error(FAIL_SEARCH_ES, e);
            return 0L;
        }
    }

    @Override
    public Object getMaxValue(String tableName, String filedName) {
        try {
            String endpoint = String.format(SEARCH, tableName);
            Request request = new Request("POST", endpoint);
            // 构建查询参数
            String queryParam = ElasticsearchRequestBuilder.getMaxValueQueryParam(filedName);
            request.setJsonEntity(queryParam);
            log.info("查询ES 最大值执行sql：{}", queryParam);
            Response response = restClient.performRequest(request);
            if (response.getStatusLine().getStatusCode() == HttpStatus.SC_OK) {
                String entity = EntityUtils.toString(response.getEntity());
                JsonNode responseJson = JsonUtils.parseObjectNode(entity);
                log.info("查询ES 最大值结果：{}", responseJson);
                JsonNode jsonNode = responseJson.get(AGGREGATIONS).get("max_value");
                JsonNode valueNode = Objects.isNull(jsonNode.get(VALUE_AS_STRING)) ? jsonNode.get(VALUE)
                    : jsonNode.get(VALUE_AS_STRING);
                if (valueNode instanceof NullNode) {
                    return null;
                }
                return valueNode.asText(null);
            } else {
                throw new DataStorageEngineException(
                    String.format("查询ES最大值失败，返回状态码=%d", response.getStatusLine().getStatusCode()));
            }
        } catch (IOException e) {
            log.error(FAIL_SEARCH_ES, e);
            return null;
        }
    }

    /**
     * 执行列表查询 并返回结果
     *
     * @param sql        查询语句
     * @param dataModels 数据建模id
     * @return 结果
     */
    public List<Map<String, Object>> executeQuery(@NotBlank(message = "检索语句不能为空") String sql,
        List<DataModel> dataModels)
        throws IOException {
        SqlToDslParser sqlToDslParser = new SqlToDslParser(dataModels);
        return getResults(sql, sqlToDslParser);
    }

    @Override
    public List<Map<String, Object>> executeQuery(String sql) throws IOException {
        SqlToDslParser sqlToDslParser = new SqlToDslParser(null);
        if (sqlToDslParser.isSqlQuery(sql)) {
            return getResults(sql, sqlToDslParser);
        } else {
            Response response = getEsQueryResponse(sql);
            if (response.getStatusLine().getStatusCode() == HttpStatus.SC_OK) {
                String entity = EntityUtils.toString(response.getEntity());
                return getResult(entity, null, null);
            } else {
                throw new DataStorageEngineException(
                    String.format("查询ES索引失败，返回状态码=%d", response.getStatusLine().getStatusCode()));
            }
        }

    }

    private List<Map<String, Object>> getResults(@NotBlank(message = "检索语句不能为空") String sql,
        SqlToDslParser sqlToDslParser)
        throws IOException {
        log.info("es查询 - 执行sql：{}", sql);
        String alias = null;
        if (sqlToDslParser.isSqlQuery(sql)) {
            Pattern pattern = Pattern.compile("(?i)count\\s*\\([^)]*\\)\\s+as\\s+['\"`]?([\\w]+)['\"`]?",
                Pattern.CASE_INSENSITIVE);
            Matcher matcher = pattern.matcher(sql);
            if (matcher.find()) {
                alias = matcher.group(1);
            }
            log.info("把SQL转换为DSL开始");
            sql = sqlToDslParser.convertSqlToDsl(sql);
            log.info("===============SQL转换为DSL结果：{}============", sql);
            log.info("把SQL转换为DSL结束");
        }
        Response response = getEsQueryResponse(sql);
        if (response.getStatusLine().getStatusCode() == HttpStatus.SC_OK) {
            String entity = EntityUtils.toString(response.getEntity());
            log.info("es查询 - 查询结果：{}", entity);
            return getResult(entity, null, alias);
        } else {
            throw new DataStorageEngineException(
                String.format("查询ES索引失败， 返回状态码=%d", response.getStatusLine().getStatusCode()));
        }
    }

    private Response getEsQueryResponse(String sql) throws IOException {
        // 使用正则表达式解析 DSL 字符串
        Matcher matcher = matchDslRule(sql.trim());
        String method = matcher.group(1); // GET
        String endpoint = matcher.group(2); // /dwd_testes/_search
        String jsonQuery = matcher.group(3); // 查询体
        // 创建请求
        Request request = new Request(method, endpoint);
        request.setJsonEntity(jsonQuery);
        // 执行请求
        Response response = restClient.performRequest(request);
        if (response == null) {
            throw new DataStorageEngineException(SEARCH_INDEX_FAILED);
        }
        return response;
    }

    private static Matcher matchDslRule(String sql) {
        String regex = "^(\\w+)\\s+(/\\S+)\\s+(\\{.*})$";
        Pattern pattern = Pattern.compile(regex, Pattern.DOTALL);
        Matcher matcher = pattern.matcher(sql.trim());
        if (!matcher.matches()) {
            throw new IllegalArgumentException("Invalid SQL format. Expected format: 'GET /index/_search {...}'");
        }
        return matcher;
    }

    @Override
    public List<Map<String, Object>> executeQueryByParams(String tableName, ConditionSearchParams searchParams,
        DataConnection dataConnection) {
        try {
            ObjectNode dsl = buildDsl(tableName, searchParams, dataConnection);
            log.info("数据服务condition最终转换的dsl为：{}", dsl);
            //判断使用es的_search还是_count
            String endpoint = determineEndPoint(tableName, searchParams, dsl);
            //执行查询
            return executeQueryAndReturn(searchParams, endpoint, dsl);
        } catch (Exception e) {
            log.error(FAIL_SEARCH_ES, e);
            String result = getEsExceptionReason(e);
            throw new DataStorageEngineException(FAIL_SEARCH_ES + result);
        }
    }

    private static String determineEndPoint(String tableName,
        ConditionSearchParams searchParams, ObjectNode dsl) {
        String endpoint;
        if (ServiceOperatorType.COUNT.equals(searchParams.getOperationType())
            && Objects.isNull(searchParams.getGroupFields())) {
            endpoint = String.format(S_COUNT, tableName);
            //移除count查询不需要的参数
            ElasticsearchRequestBuilder.removeNoUseField(dsl);
        } else {
            endpoint = String.format(SEARCH, tableName);
        }
        return endpoint;
    }

    /**
     * 构建DSL查询语句
     *
     * @param tableName      表名
     * @param searchParams   查询参数
     * @param dataConnection 数据连接
     * @return DSL查询语句
     */
    public ObjectNode buildDsl(String tableName, ConditionSearchParams searchParams,
        DataConnection dataConnection) {
        ObjectNode dsl;
        boolean vectorQuery = searchParams.isVectorQuery();
        if (vectorQuery) {
            //向量字段走另一个逻辑
            ArrayNode knnNode = ElasticsearchRequestBuilder.buildKnnNode(tableName, searchParams, dataConnection,
                restClient);
            dsl = ElasticsearchRequestBuilder.buildVectorQuery(searchParams, knnNode);
            if (Objects.nonNull(searchParams.getEsBoostConfigs()) && !searchParams.getEsBoostConfigs().isEmpty()) {
                //如果有boost配置，则添加到dsl中
                ElasticsearchRequestBuilder.updateBoost(dsl, searchParams.getEsBoostConfigs(), true);
            }
        } else {
            //普通字段
            dsl = ElasticsearchRequestBuilder.getDsl(tableName, searchParams, dataConnection, restClient);
            ElasticsearchRequestBuilder.renameKeyInSources(dsl);
            if (ServiceOperatorType.STATISTIC.equals(searchParams.getOperationType())) {
                ElasticsearchRequestBuilder.replaceAggregationKeys(dsl);
            }
        }
        if (Objects.nonNull(searchParams.getEsBoostConfigs()) && !searchParams.getEsBoostConfigs().isEmpty()) {
            //如果有boost配置，则添加到dsl中
            ElasticsearchRequestBuilder.updateBoost(dsl, searchParams.getEsBoostConfigs(), false);
        }

        if (Objects.nonNull(searchParams.getDistinctFieldName())) {
            //如果有主键，则添加去重逻辑, dsl拼接collapse
            String distinctFieldName = searchParams.getDistinctFieldName();
            dsl.putObject("collapse").put(FIELD, distinctFieldName);
            // 添加唯一计数聚合
            ObjectNode aggsNode = dsl.putObject("aggs");
            ObjectNode uniqueCountNode = aggsNode.putObject(UNIQUE_COUNT);
            ObjectNode cardinalityNode = uniqueCountNode.putObject("cardinality");
            cardinalityNode.put(FIELD, distinctFieldName);
        }
        return dsl;
    }

    private List<Map<String, Object>> executeQueryAndReturn(ConditionSearchParams searchParams, String endpoint,
        ObjectNode dsl)
        throws IOException {
        Request request = new Request("GET", endpoint);
        request.setJsonEntity(dsl.toString());
        Response response = restClient.performRequest(request);
        if (response == null) {
            throw new DataStorageEngineException(SEARCH_INDEX_FAILED);
        }
        if (response.getStatusLine().getStatusCode() == HttpStatus.SC_OK) {
            String entity = EntityUtils.toString(response.getEntity());
            log.info("数据服务查询ES结果：{}", entity);
            if (searchParams.isNormalQuery()) {
                return getList(searchParams, entity);
            } else {
                return getCountResult(entity);
            }
        } else {
            throw new DataStorageEngineException(String.format("查询ES索引失败，GET %s 返回状态码=%d", endpoint,
                response.getStatusLine().getStatusCode()));
        }
    }

    private List<Map<String, Object>> getList(ConditionSearchParams searchParams,
        String entity) {
        return getResult(entity, searchParams.getReturnFields(), null);
    }

    private JsonNode getEsTableInfo(String tableName) throws IOException {
        String mapping = String.format(MAPPING, tableName);
        Request mappingRequest = new Request("GET", mapping);
        Response mappingResponse = restClient.performRequest(mappingRequest);
        if (mappingResponse == null || mappingResponse.getStatusLine().getStatusCode() != HttpStatus.SC_OK) {
            throw new DataStorageEngineException(MAPPING_INDEX_FAILED);
        }
        String entity = EntityUtils.toString(mappingResponse.getEntity());
        JsonNode responseJson = JsonUtils.parseObjectNode(entity);
        return responseJson.get(tableName).get("mappings").get(PROPERTIES);
    }

    private String getEsExceptionReason(Exception e) {
        if (e instanceof ResponseException responseException) {
            try {
                Response response = responseException.getResponse();
                String responseStr = new String(response.getEntity().getContent().readAllBytes());
                JsonNode jsonNode = JsonUtils.parseJsonNode(responseStr);
                JsonNode rootCause = jsonNode.get("error").withArray("root_cause").get(0);
                return rootCause.get("type").asText() + ": " + rootCause.get("reason").asText();
            } catch (Exception exception) {
                log.error("parse ResponseException error: ", exception);
                return e.getMessage();
            }
        }
        return e.getMessage();
    }

    private List<Map<String, Object>> getCountResult(String entity) {
        // 解析 JSON 响应
        JsonNode responseJson = JsonUtils.parseObjectNode(entity);
        JsonNode aggregations = responseJson.get(AGGREGATIONS);

        // 处理无聚合字段的情况
        if (aggregations == null || aggregations.isEmpty()) {
            return handleCount(responseJson, null);
        }
        // 处理聚合结果
        Map<String, Object> aggResult = processAggregations(aggregations);
        return aggResult.isEmpty() ? new ArrayList<>() : List.of(aggResult);
    }

    /**
     * 处理聚合结果并返回键值对映射
     *
     * @param aggregations 聚合结果的JsonNode
     * @return 键值对映射，或空Map表示需要返回bucketList
     */
    private Map<String, Object> processAggregations(JsonNode aggregations) {
        Map<String, Object> result = new HashMap<>();
        Iterator<String> fieldNames = aggregations.fieldNames();

        while (fieldNames.hasNext()) {
            String field = fieldNames.next();
            JsonNode aggNode = aggregations.get(field);

            // 处理value类型的聚合
            JsonNode valueNode = aggNode.get(VALUE);
            if (valueNode != null && !valueNode.isMissingNode()) {
                result.put(field, extractNodeValue(valueNode));
                continue;
            }

            // 处理bucket类型的聚合
            List<Map<String, Object>> bucketList = getAggResult(aggNode, null);
            if (bucketList != null) {
                return Collections.emptyMap(); // 返回空Map表示需要返回bucketList
            }
        }

        return result;
    }

    /**
     * 从JsonNode中提取适当类型的值
     *
     * @param node JsonNode对象
     * @return 提取的值，可能是数字或字符串
     */
    private static Object extractNodeValue(JsonNode node) {
        if (node.isNumber()) {
            return node.numberValue();
        }
        return node.asText();
    }

    private List<Map<String, Object>> getResult(String response, List<String> returnFields, String aliasName) {
        JsonNode responseJson = JsonUtils.parseObjectNode(response);
        try {
            // 优先处理聚合结果
            List<Map<String, Object>> bucketList = handleAggregationsResult(returnFields, aliasName, responseJson);
            if (bucketList != null) {
                return bucketList;
            }
            // 原有处理count和hits的逻辑
            return handleCountAndHits(returnFields, aliasName, responseJson);
        } catch (Exception e) {
            throw new BizException("ES 查询结果解析失败！", e);
        }
    }

    private static  List<Map<String, Object>> handleCountAndHits(
        List<String> returnFields,
        String aliasName, JsonNode responseJson) {
        if (!responseJson.has(HITS) && responseJson.has(COUNT_PARAM)) {
            //es查询结果为count
            return handleCount(responseJson, aliasName);
        }
        JsonNode agg = responseJson.get(AGGREGATIONS);
        List<Map<String, Object>> result = new ArrayList<>();
        Map<String, Object> countMap = new HashMap<>();
        if (Objects.nonNull(agg) && agg.has(UNIQUE_COUNT)) {
            JsonNode uniqueCountNode = agg.get(UNIQUE_COUNT);
            JsonNode valueNode = uniqueCountNode.get(VALUE);
            if (valueNode != null && !valueNode.isNull()) {
                long uniqueCount = valueNode.asLong();
                countMap.put(DATA_SERVICE_COUNT_NUM, uniqueCount);
                result.add(countMap);
            }
        } else {
            JsonNode countValue = responseJson.get(HITS).get("total").get("value");
            countMap.put(DATA_SERVICE_COUNT_NUM, countValue.asInt());
            result.add(countMap);
        }
        JsonNode hits = responseJson.get(HITS).get(HITS);
        for (JsonNode hit : hits) {
            handleHits(returnFields, hit, result);
        }
        return result;
    }

    /**
     * 处理聚合结果
     *
     * @param returnFields 返回的字段列表，如果为空则处理所有聚合字段
     * @param aliasName    别名，如果有的话
     * @param responseJson 响应的JsonNode对象
     * @return 包含聚合结果的列表，如果没有聚合结果则返回null
     */
    public static @Nullable
    List<Map<String, Object>> handleAggregationsResult(List<String> returnFields,
        String aliasName,
        JsonNode responseJson) {
        // 检查是否存在聚合结果
        if (!responseJson.has(AGGREGATIONS)) {
            return null;
        }
        JsonNode aggregations = responseJson.get(AGGREGATIONS);
        Map<String, Object> aggResult = new HashMap<>();
        // 获取需要处理的聚合字段
        List<String> aggFields = determineAggregationFields(returnFields, aggregations);
        // 处理每个聚合字段
        for (String field : aggFields) {
            List<Map<String, Object>> bucketList = handleAggregations(aliasName, field, aggregations, aggResult);
            if (bucketList != null) {
                return bucketList;
            }
        }
        //返回聚合结果(如果有)
        return aggResult.isEmpty() ? null : List.of(aggResult);
    }

    /**
     * 确定需要处理的聚合字段列表
     *
     * @param returnFields 返回的字段列表
     * @param aggregations 聚合结果的JsonNode
     * @return 确定的聚合字段列表
     */
    private static List<String> determineAggregationFields(List<String> returnFields, JsonNode aggregations) {
        if (returnFields != null && !returnFields.isEmpty()) {
            return returnFields;
        }

        List<String> fields = new ArrayList<>();
        aggregations.fieldNames().forEachRemaining(fields::add);
        return fields;
    }

    private static void handleHits(List<String> returnFields, JsonNode hit, List<Map<String, Object>> result) {
        JsonNode source = hit.get("_source");
        Map<String, Object> fieldMap = new HashMap<>();
        if (returnFields == null || returnFields.isEmpty()) {
            // 如果 returnFields 为空，则返回所有字段
            source.fields().forEachRemaining(entry -> fieldMap.put(entry.getKey(), entry.getValue()));
        } else {
            // 否则只返回指定的字段
            returnFields.forEach(field -> {
                JsonNode value = source.path(field);
                if (!value.isMissingNode()) {
                    fieldMap.put(field, value);
                }
            });
        }
        if (!fieldMap.isEmpty()) {
            result.add(fieldMap);
        }
    }

    private static List<Map<String, Object>> handleCount(JsonNode responseJson,
        String aliasName) {
        if (Objects.nonNull(aliasName)) {
            return List.of(Map.of(aliasName, responseJson.get(COUNT_PARAM).asLong()));
        }
        return List.of(Map.of(COUNT_PARAM, responseJson.get(COUNT_PARAM).asLong()));
    }

    /**
     * 处理聚合结果
     *
     * @param aliasName    别名，如果有的话
     * @param field        聚合字段名
     * @param aggregations 聚合结果的JsonNode
     * @param aggResult    聚合结果的Map，用于存储处理后的结果
     * @return 如果是bucket类型的聚合，返回包含聚合结果的列表；如果是value类型的聚合，返回null
     */
    public static @Nullable
    List<Map<String, Object>> handleAggregations(String aliasName, String field,
        JsonNode aggregations,
        Map<String, Object> aggResult) {
        if (!aggregations.has(field)) {
            return null;
        }
        JsonNode aggNode = aggregations.get(field);
        JsonNode valueNode = aggNode.get(VALUE);
        // 处理value类型的聚合结果
        if (valueNode != null && !valueNode.isMissingNode()) {
            aggResult.put(field, extractNodeValue(valueNode));
            return null;
        }

        // 处理bucket类型的聚合结果
        return handleBucketAggregation(aliasName, field, aggregations, aggNode);
    }

    private static @Nullable
    List<Map<String, Object>> handleBucketAggregation(
        String aliasName,
        String field,
        JsonNode aggregations,
        JsonNode aggNode) {
        List<Map<String, Object>> bucketList = getAggResult(aggNode, aliasName);
        if (bucketList == null) {
            return null;
        }
        String nullField = field.replace("group_by_", "");
        if (aggregations.has("null_" + nullField)) {
            JsonNode nullNode = aggregations.get("null_" + nullField);
            Map<String, Object> nullEntry = createNullEntry(nullField, aliasName, nullNode);
            bucketList.add(nullEntry);
        }
        return bucketList;
    }

    private static Map<String, Object> createNullEntry(
        String field,
        String aliasName,
        JsonNode nullNode) {
        return Map.of(
            field, "",
            aliasName != null ? aliasName : COUNT_PARAM,
            nullNode.get(DOC_COUNT).asLong()
        );
    }

    /**
     * 获取聚合结果
     *
     * @param aggNode 聚合结果的JsonNode
     * @param alias   别名，如果有的话
     * @return 聚合结果的列表，每个元素是一个Map，包含聚合字段和对应的值
     */
    public static @Nullable
    List<Map<String, Object>> getAggResult(JsonNode aggNode, String alias) {
        JsonNode bucketsNode = aggNode.get(BUCKETS);
        if (bucketsNode == null || !bucketsNode.isArray()) {
            return null;
        }
        List<Map<String, Object>> bucketList = new ArrayList<>();
        for (JsonNode bucket : bucketsNode) {
            Map<String, Object> bucketMap = processBucket(bucket, alias);
            if (!bucketMap.isEmpty()) {
                bucketList.add(bucketMap);
            }
        }
        return bucketList.isEmpty() ? null : bucketList;
    }

    private static Map<String, Object> processBucket(JsonNode bucket, String alias) {
        Map<String, Object> bucketMap = new HashMap<>();
        JsonNode keyNode = bucket.get(KEY);
        if (keyNode != null) {
            processKeyNode(bucketMap, keyNode, bucket, alias);
        } else {
            processMetrics(bucketMap, bucket);
        }

        return bucketMap;
    }


    private static void processKeyNode(Map<String, Object> bucketMap,
        JsonNode keyNode,
        JsonNode bucket,
        String alias) {
        if (keyNode.isObject()) {
            dealObjectKeyNod(alias, bucket, bucketMap);
        } else {
            bucketMap.put(keyNode.asText(), bucket.get(DOC_COUNT).asLong());
        }
    }

    private static void processMetrics(Map<String, Object> bucketMap, JsonNode bucket) {
        Stream.of(MAX, SUM, MIN, "avg")
            .filter(bucket::has)
            .forEach(metric -> {
                JsonNode valueNode = bucket.get(metric).get(VALUE);
                if (valueNode != null && !valueNode.isMissingNode()) {
                    bucketMap.put(metric, valueNode.isNumber()
                        ? valueNode.numberValue() : valueNode.asText());
                }
            });
    }

    private static void dealObjectKeyNod(String alias, JsonNode bucket, Map<String, Object> bucketMap) {
        //如果结果bucket中有key包含sum, min, max则不需要doc_count, 否则字段会多余一个count
        if (!isSumMaxMin(bucket)) {
            bucketMap.put(Objects.nonNull(alias) ? alias : COUNT_PARAM, bucket.get(DOC_COUNT).asLong());
        }
        Iterator<Entry<String, JsonNode>> fields = bucket.fields();
        while (fields.hasNext()) {
            Entry<String, JsonNode> entry = fields.next();
            String fieldName = entry.getKey();
            if (fieldName.equalsIgnoreCase(DOC_COUNT)) {
                continue;
            }
            JsonNode fieldValue = entry.getValue();
            // 根据值的类型添加到map
            if (fieldValue.isTextual()) {
                bucketMap.put(fieldName, fieldValue.asText());
            } else if (fieldValue.isNumber()) {
                bucketMap.put(fieldName, fieldValue.numberValue());
            } else if (fieldValue.isBoolean()) {
                bucketMap.put(fieldName, fieldValue.asBoolean());
            } else if (fieldValue.isObject()) {
                // 如果是对象，遍历其内部的键值对
                fieldValue.fields().forEachRemaining(value -> {
                    String innerKey = value.getKey();
                    JsonNode innerValue = value.getValue();
                    // 这里使用 prefix 来避免键名冲突
                    bucketMap.put(innerKey, innerValue.asText());
                });
            } else {
                bucketMap.put(fieldName, fieldValue.toString());
            }
        }
    }

    /**
     * 是否有key包含sum, min, max
     *
     * @param bucket 结果集
     * @return 是否包含
     */
    private static boolean isSumMaxMin(JsonNode bucket) {
        Iterator<String> fieldNames = bucket.fieldNames();
        while (fieldNames.hasNext()) {
            String fieldName = fieldNames.next();
            if (fieldName.contains(SUM) || fieldName.contains(MAX) || fieldName.contains(MIN)) {
                return true; // 找到包含 min、max 或 sum 的字段
            }
        }
        return false; // 没有找到
    }

    /**
     * 构造默认的分页参数
     *
     * @param request 代码模式查询参数
     */
    @Override
    public String buildDefaultPageParam(CodeSearchParams request) {
        long offset = request.getPageParams().getOffset();
        Integer pageSize = request.getPageParams().getPageSize();
        String sql = request.getCode();
        SqlToDslParser sqlToDslParser = new SqlToDslParser(null);
        if (sqlToDslParser.isSqlQuery(request.getCode())) {
            if (sql.toUpperCase().contains("LIMIT") || sql.toUpperCase().contains("OFFSET")) {
                return request.getCode();  // 如果已带分页参数，直接返回原始 SQL
            }
            String modifiedSql = String.format("%s LIMIT %d OFFSET %d", sql, pageSize, offset);
            // 更新请求的 SQL 查询
            request.setCode(modifiedSql);
        } else {
            // 检查是否已带分页参数
            if (sql.contains("\"from\":") || sql.contains("\"size\":")) {
                return request.getCode(); // 如果已带分页参数，直接返回
            }
            // 根据 SQL 查询的最后一个 "}" 位置插入分页参数
            // 更新请求的 SQL 查询
            return sql.replaceFirst("(})$",
                String.format(",%n  \"from\": %d,%n  \"size\": %d%n}", offset, pageSize));
        }
        return request.getCode();
    }

    @Override
    public long countNotNormalQuery(ConditionSearchParams searchParams, String tableName,
        DataConnection dataConnection) {
        String sqlQuery = SqlUtils.getQuerySql(tableName, searchParams, dataConnection);
        JsonNode dsl = ElasticsearchRequestBuilder.translateSqlToDsl(sqlQuery, restClient);
        String endpoint = String.format(SEARCH, tableName);
        Request request = new Request("GET", endpoint);
        request.setJsonEntity(dsl.toString());
        Response response;
        try {
            response = restClient.performRequest(request);
        } catch (IOException e) {
            throw new DataStorageEngineException(SEARCH_INDEX_FAILED);
        }
        if (response == null) {
            throw new DataStorageEngineException(SEARCH_INDEX_FAILED);
        }
        // 从响应中提取聚合结果
        long documentCount;
        try {
            ObjectMapper objectMapper = new ObjectMapper();
            JsonNode responseJson = objectMapper.readTree(response.getEntity().getContent());
            // 获取聚合结果
            documentCount = responseJson.path(AGGREGATIONS).path(GROUP_BY).path(BUCKETS).size();
        } catch (IOException e) {
            throw new DataStorageEngineException("Error parsing response");
        }
        return documentCount; // 返回聚合计数
    }


    @Override
    public TypeConverter<BasicTypeDefine> getTypeConverter() {
        return new ElasticsearchTypeConverter();
    }

    @Override
    public void close() throws Exception {
        restClient.close();
    }

    @Override
    public String getDdl(String tableName) {
        try {
            return getEsMappings(tableName);
        } catch (IOException e) {
            throw new DataStorageEngineException("获取索引映射失败", e);
        }
    }

    @Override
    public boolean isSelectSql(String sql) {
        //es中还有sql转dsl的语句，因此额外加上select开头的
        String sqlStr = sql.trim().toUpperCase(Locale.ROOT);
        return sqlStr.startsWith("GET") || sqlStr.startsWith("SELECT");
    }
}
