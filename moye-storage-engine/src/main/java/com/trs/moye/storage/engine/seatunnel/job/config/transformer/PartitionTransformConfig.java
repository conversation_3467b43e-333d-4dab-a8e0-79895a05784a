package com.trs.moye.storage.engine.seatunnel.job.config.transformer;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Builder.Default;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.SuperBuilder;

/**
 * 分区字段算子配置
 *
 * <AUTHOR>
 * @since 2025-06-04
 */
@EqualsAndHashCode(callSuper = true)
@Data
@SuperBuilder
public class PartitionTransformConfig extends TransformConfig {

    @JsonProperty("plugin_name")
    @Default
    private String pluginName = "Partition";

    @JsonProperty("partition_format")
    private String partitionFormat;

    @JsonProperty("partition_field")
    private String partitionField;

    @JsonProperty("partition_by")
    private String partitionBy;

    @JsonProperty("plugin_output")
    @Default
    private String pluginOutput = "partition_output";
}
