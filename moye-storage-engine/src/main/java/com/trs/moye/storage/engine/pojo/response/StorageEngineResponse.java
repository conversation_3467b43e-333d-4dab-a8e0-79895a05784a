package com.trs.moye.storage.engine.pojo.response;

import lombok.AllArgsConstructor;
import lombok.Data;

/**
 * 存储引擎响应结果
 *
 * <AUTHOR>
 * @since 2024-09-27 17:30
 */
@Data
@AllArgsConstructor
public class StorageEngineResponse {

    /**
     * 是否成功
     */
    private boolean success;

    /**
     * 错误信息
     */
    private String message;

    /**
     * 成功
     *
     * @return {@link StorageEngineResponse}
     */
    public static StorageEngineResponse success() {
        return new StorageEngineResponse(true, "操作成功");
    }

    /**
     * 失败
     *
     * @param message 错误信息
     * @return {@link StorageEngineResponse}
     */
    public static StorageEngineResponse fail(String message) {
        return new StorageEngineResponse(false, message);
    }
}