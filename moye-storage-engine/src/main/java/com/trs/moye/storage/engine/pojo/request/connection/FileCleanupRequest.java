package com.trs.moye.storage.engine.pojo.request.connection;

import java.time.LocalDateTime;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 文件清理请求
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class FileCleanupRequest extends FileBaseRequest {

    /**
     * 清理此时间之前的文件
     */
    private LocalDateTime cleanupBeforeTime;

    /**
     * 备份目录名称
     */
    private String backupDirectoryName;

} 