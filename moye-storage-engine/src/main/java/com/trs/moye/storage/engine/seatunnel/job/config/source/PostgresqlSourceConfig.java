package com.trs.moye.storage.engine.seatunnel.job.config.source;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.SuperBuilder;

/**
 * Postgresql source config
 *
 * <p>Postgresql source config is used to specify the configuration of the Postgresql source</p>
 *
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
@SuperBuilder
public class PostgresqlSourceConfig extends SeatunnelSourceConfig {

    @JsonProperty("url")
    public String url;
    @JsonProperty("driver")
    @Builder.Default
    public String driver = "org.postgresql.Driver";
    @JsonProperty("user")
    public String user;
    @JsonProperty("password")
    public String password;
    @JsonProperty("query")
    public String query;
    @JsonProperty("partition_column")
    private String partitionColumn;
    @JsonProperty("plugin_name")
    @Builder.Default
    private String pluginName = "Jdbc";
}
