package com.trs.moye.storage.engine.controller;

import com.trs.moye.storage.engine.pool.PoolInfo;
import com.trs.moye.storage.engine.pool.PoolUtils;
import com.trs.moye.storage.engine.pool.monitor.PoolBorrowMonitor;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @since 2025-02-13 13:46
 */
@RestController
@RequestMapping("/connection/pool")
@Slf4j
public class ConnectionPoolController {

    @Resource
    private PoolBorrowMonitor poolBorrowMonitor;

    /**
     * 获取连接池信息
     *
     * @return 连接池信息
     */
    @GetMapping("/info")
    public PoolInfo getPoolInfo() {
        return PoolUtils.getPoolInfo();
    }

    /**
     * 获取连接池借出报告
     *
     * @param identification 连接池标识
     * @return 借出报告
     */
    @GetMapping("/borrow/report")
    public String getBorrowReport(@RequestParam String identification) {
        return poolBorrowMonitor.getBorrowReport(identification);
    }
}
