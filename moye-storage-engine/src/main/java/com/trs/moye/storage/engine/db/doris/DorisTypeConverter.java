package com.trs.moye.storage.engine.db.doris;

import com.trs.moye.base.common.enums.FieldType;
import com.trs.moye.base.data.model.entity.DataModelField;
import com.trs.moye.storage.engine.db.BasicTypeDefine;
import com.trs.moye.storage.engine.db.TypeConverter;

/**
 * Doris类型转换器
 */
public class DorisTypeConverter implements TypeConverter<BasicTypeDefine> {

    // 字符串类型
    private static final String TYPE_CHAR = "CHAR";
    private static final String TYPE_VARCHAR = "VARCHAR";
    private static final String TYPE_STRING = "STRING";
    private static final String TYPE_TEXT = "TEXT";

    // 数值类型
    private static final String TYPE_TINYINT = "TINYINT";
    private static final String TYPE_SMALLINT = "SMALLINT";
    private static final String TYPE_INT = "INT";
    private static final String TYPE_BIGINT = "BIGINT";
    private static final String TYPE_LARGEINT = "LARGEINT";
    private static final String TYPE_FLOAT = "FLOAT";
    private static final String TYPE_DOUBLE = "DOUBLE";
    private static final String TYPE_DECIMAL = "DECIMAL";

    // 日期时间类型
    private static final String TYPE_DATE = "DATE";
    private static final String TYPE_DATETIME = "DATETIME";
    private static final String TYPE_TIME = "TIME";

    // 布尔类型
    private static final String TYPE_BOOLEAN = "BOOLEAN";

    // 向量类型
    private static final String TYPE_INT_VECTOR = "INT_VECTOR";
    private static final String TYPE_FLOAT_VECTOR = "FLOAT_VECTOR";
    private static final String TYPE_BYTE_VECTOR = "BYTE_VECTOR";

    // 几何类型
    private static final String TYPE_GEOMETRY = "GEOMETRY";

    // 对象类型
    private static final String TYPE_JSON = "JSON";

    // 特殊类型
    private static final String[] OBJECT_TYPES = {
        "HLL", "BITMAP", "ARRAY", "MAP", "STRUCT", "QUANTILE_STATE", "AGG_STATE"
    };

    @Override
    public DataModelField convert(BasicTypeDefine typeDefine) {
        DataModelField field = new DataModelField();
        field.setEnName(typeDefine.getName());
        field.setZhName(typeDefine.getComment());
        field.setNullable(typeDefine.isNullable());
        FieldType fieldType = convertDorisType(typeDefine.getDataType());
        field.setType(fieldType);
        field.setTypeName(fieldType.getLabel());
        return field;
    }

    @Override
    public BasicTypeDefine reconvert(DataModelField field) {
        return BasicTypeDefine.builder()
            .name(field.getEnName())
            .columnType(field.getTypeName())
            .dataType(field.getType().name())
            .nullable(field.isNullable())
            .comment(field.getZhName())
            .build();
    }

    /**
     * 将系统内部类型转换为Doris数据类型
     *
     * @param fieldType 系统内部类型
     * @param length    字段长度（可选）
     * @return Doris数据类型
     */
    public static String convertToDorisType(FieldType fieldType, Integer length) {
        return switch (fieldType) {
            // 字符串类型
            case CHAR -> TYPE_CHAR + (length != null ? "(" + length + ")" : "(1)");
            case STRING -> TYPE_STRING;
            case TEXT -> TYPE_TEXT;

            // 数值类型
            case SHORT -> TYPE_SMALLINT;
            case INT -> TYPE_INT;
            case LONG -> TYPE_BIGINT;
            case FLOAT -> TYPE_FLOAT;
            case DOUBLE -> TYPE_DOUBLE;
            case DECIMAL -> TYPE_DECIMAL;

            // 日期时间类型
            case DATE -> TYPE_DATE;
            case DATETIME -> TYPE_DATETIME;
            case TIME -> TYPE_TIME;

            // 布尔类型
            case BOOLEAN -> TYPE_BOOLEAN;

            // 向量类型
            case INT_VECTOR -> TYPE_INT_VECTOR;
            case FLOAT_VECTOR -> TYPE_FLOAT_VECTOR;
            case BYTE_VECTOR -> TYPE_BYTE_VECTOR;

            // 几何类型
            case GEOMETRY -> TYPE_GEOMETRY;

            // 对象类型
            case OBJECT -> TYPE_JSON;

            default -> TYPE_VARCHAR + "(190)"; // 默认使用VARCHAR(190)
        };
    }

    /**
     * 将Doris数据类型转换为系统内部类型
     *
     * @param dorisType Doris数据类型
     * @return 系统内部类型
     */
    private FieldType convertDorisType(String dorisType) {
        // 移除类型中的长度和精度信息
        String baseType = dorisType.split("\\(")[0].toUpperCase();

        // 检查是否为对象类型
        for (String objectType : OBJECT_TYPES) {
            if (objectType.equals(baseType)) {
                return FieldType.OBJECT;
            }
        }

        return switch (baseType) {
            // 字符串类型
            case TYPE_CHAR -> FieldType.CHAR;
            case TYPE_VARCHAR, TYPE_STRING -> FieldType.STRING;
            case TYPE_TEXT -> FieldType.TEXT;

            // 数值类型
            case TYPE_TINYINT, TYPE_SMALLINT -> FieldType.SHORT;
            case TYPE_INT -> FieldType.INT;
            case TYPE_BIGINT, TYPE_LARGEINT -> FieldType.LONG;
            case TYPE_FLOAT -> FieldType.FLOAT;
            case TYPE_DOUBLE -> FieldType.DOUBLE;
            case TYPE_DECIMAL -> FieldType.DECIMAL;

            // 日期时间类型
            case TYPE_DATE -> FieldType.DATE;
            case TYPE_DATETIME -> FieldType.DATETIME;
            case TYPE_TIME -> FieldType.TIME;

            // 布尔类型
            case TYPE_BOOLEAN -> FieldType.BOOLEAN;

            // 向量类型
            case TYPE_INT_VECTOR -> FieldType.INT_VECTOR;
            case TYPE_FLOAT_VECTOR -> FieldType.FLOAT_VECTOR;
            case TYPE_BYTE_VECTOR -> FieldType.BYTE_VECTOR;

            // 几何类型
            case TYPE_GEOMETRY -> FieldType.GEOMETRY;

            // 对象类型
            case TYPE_JSON -> FieldType.OBJECT;

            default -> throw new IllegalArgumentException("Unsupported Doris type: " + dorisType);
        };
    }
} 