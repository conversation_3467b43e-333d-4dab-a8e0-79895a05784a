package com.trs.moye.storage.engine.mq.kafka;

import static org.apache.kafka.clients.CommonClientConfigs.BOOTSTRAP_SERVERS_CONFIG;
import static org.apache.kafka.clients.CommonClientConfigs.GROUP_ID_CONFIG;
import static org.apache.kafka.clients.consumer.ConsumerConfig.AUTO_OFFSET_RESET_CONFIG;
import static org.apache.kafka.clients.consumer.ConsumerConfig.KEY_DESERIALIZER_CLASS_CONFIG;
import static org.apache.kafka.clients.consumer.ConsumerConfig.VALUE_DESERIALIZER_CLASS_CONFIG;

import com.fasterxml.jackson.databind.JsonNode;
import com.trs.moye.base.common.entity.mq.MqConsumeInfoResponse;
import com.trs.moye.base.common.exception.BizException;
import com.trs.moye.base.common.utils.AssertUtils;
import com.trs.moye.base.common.utils.DateTimeUtils;
import com.trs.moye.base.common.utils.JsonUtils;
import com.trs.moye.base.common.utils.StringUtils;
import com.trs.moye.base.data.connection.entity.params.ConnectionParams;
import com.trs.moye.base.data.connection.entity.params.MqConnectionParams;
import com.trs.moye.base.data.model.entity.DataModel;
import com.trs.moye.base.data.model.field.mapping.ColumnResponse;
import com.trs.moye.base.data.storage.setting.DataStorageSettings;
import com.trs.moye.base.data.storage.setting.KafkaDataStorageSettings;
import com.trs.moye.storage.engine.mq.MqConnection;
import com.trs.moye.storage.engine.pojo.request.search.ConditionSearchParams;
import com.trs.moye.storage.engine.pojo.response.StorageEngineResponse;
import com.trs.moye.storage.engine.pojo.response.alltable.TableResponse;
import com.trs.moye.storage.engine.utils.BizUtils;
import com.trs.moye.storage.engine.utils.KafkaTopicUtils;
import java.time.Duration;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Properties;
import java.util.Set;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.kafka.clients.admin.AdminClient;
import org.apache.kafka.clients.admin.AdminClientConfig;
import org.apache.kafka.clients.admin.CreateTopicsResult;
import org.apache.kafka.clients.admin.ListOffsetsResult.ListOffsetsResultInfo;
import org.apache.kafka.clients.admin.ListTopicsResult;
import org.apache.kafka.clients.admin.NewTopic;
import org.apache.kafka.clients.admin.TopicDescription;
import org.apache.kafka.clients.consumer.ConsumerConfig;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.apache.kafka.clients.consumer.ConsumerRecords;
import org.apache.kafka.clients.consumer.KafkaConsumer;
import org.apache.kafka.common.TopicPartition;
import org.apache.kafka.common.config.TopicConfig;
import org.apache.kafka.common.serialization.StringDeserializer;


/**
 * Kafka连接类
 */
@Slf4j
public class KafkaConnection implements MqConnection {

    private final AdminClient admin;
    private final KafkaConsumer<String, String> consumer;

    /**
     * 创建Kafka连接
     *
     * @param connectionParams 连接参数
     */
    public KafkaConnection(ConnectionParams connectionParams) {
        if (connectionParams instanceof MqConnectionParams param) {
            this.admin = createAdmin(param);
            this.consumer = createConsumer(param);
        } else {
            throw new IllegalArgumentException("不支持的连接参数类型: " + connectionParams.getClass().getName());
        }
    }

    /**
     * 创建KafkaConsumer
     *
     * @param connectionParams 连接信息
     * @return {@link KafkaConsumer}
     */
    private static KafkaConsumer<String, String> createConsumer(MqConnectionParams connectionParams) {
        Properties props = new Properties();
        props.put(BOOTSTRAP_SERVERS_CONFIG, connectionParams.getHost() + ":" + connectionParams.getPort());
        String groupName =
            "moye-test-connection" + LocalDateTime.now()
                .format(DateTimeUtils.Formatter.YYYY_MM_DD_HH_MM_SS_NO_SPLIT.getDateTimeFormatter());
        props.put(GROUP_ID_CONFIG, groupName);
        props.put(KEY_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class.getName());
        props.put(VALUE_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class.getName());
        props.put(AUTO_OFFSET_RESET_CONFIG, "latest");
        props.put(ConsumerConfig.ENABLE_AUTO_COMMIT_CONFIG, false);
        return new KafkaConsumer<>(props);
    }

    /**
     * 创建AdminClient
     *
     * @param connectionParams 连接信息
     * @return {@link AdminClient}
     */
    private static AdminClient createAdmin(MqConnectionParams connectionParams) {
        Properties props = new Properties();
        props.put(BOOTSTRAP_SERVERS_CONFIG, connectionParams.getHost() + ":" + connectionParams.getPort());
        props.put(AdminClientConfig.REQUEST_TIMEOUT_MS_CONFIG, "2000");
        return AdminClient.create(props);
    }

    @Override
    public boolean testConnection() {
        try {
            admin.describeCluster().nodes().get(2, TimeUnit.SECONDS);
            return true;
        } catch (Exception ex) {
            log.error("Kafka连接测试失败", ex);
            return false;
        }
    }

    /**
     * 测试连接是否可用，抛出异常
     */
    @Override
    public void testConnectionWithException() throws InterruptedException, ExecutionException, TimeoutException {
        admin.describeCluster().nodes().get(2, TimeUnit.SECONDS);
    }

    @Override
    public List<TableResponse> getAllTables() {
        try {
            //连接测试，报错之后代表信息有误
            ListTopicsResult listTopics = admin.listTopics();
            Set<String> topicList = listTopics.names().get();
            return topicList.stream()
                .map(topic -> new TableResponse(topic, topic))
                .toList();
        } catch (Exception ex) {
            log.error("Failed to get topic list from Kafka", ex);
            return List.of();
        }
    }

    @Override
    public List<ColumnResponse> getTableFields(String topic) {
        List<TopicPartition> partitions = getPartitions(topic);
        JsonNode message = getKafkaMessage(partitions);
        if (message != null) {
            return BizUtils.parseJsonObjectFields(message);
        } else {
            log.warn("no data get from Kafka topic: {}", topic);
            return List.of();
        }
    }


    /**
     * 从Kafka中获取数据；只要有一个分区查到数据就直接返回
     *
     * @param partitions 分区
     * @return json格式消息
     */
    JsonNode getKafkaMessage(List<TopicPartition> partitions) {
        for (TopicPartition partition : partitions) {
            JsonNode message = processPartition(partition);
            if (message != null) {
                return message;
            }
        }
        return null;
    }

    /**
     * 从Kafka中获取数据
     *
     * @param partition 分区
     * @return json格式消息
     */
    private JsonNode processPartition(TopicPartition partition) {
        consumer.assign(Collections.singletonList(partition));
        consumer.seekToEnd(Collections.singletonList(partition));
        long lastOffset = consumer.position(partition) - 1;
        if (lastOffset < 0) {
            return null;
        }
        consumer.seek(partition, lastOffset);
        ConsumerRecords<String, String> records = consumer.poll(Duration.ofSeconds(1));
        for (ConsumerRecord<String, String> consumerRecord : records) {
            if (consumerRecord.offset() == lastOffset) {
                JsonNode jsonNode = JsonUtils.parseJsonNode(consumerRecord.value());
                log.info("Fetched data from Kafka: {}", jsonNode.toString());
                // 检查是否为数组并取第一条记录
                if (jsonNode.isArray() && !jsonNode.isEmpty()) {
                    return jsonNode.get(0);
                } else {
                    return jsonNode;
                }
            }
        }
        return null;
    }

    /**
     * json 消息解析为字段信息
     *
     * @param message json消息
     * @return 字段信息
     */
    private List<ColumnResponse> toColumnResponseList(JsonNode message) {
        List<ColumnResponse> result = new ArrayList<>();
        Iterator<Entry<String, JsonNode>> fields = message.fields();
        while (fields.hasNext()) {
            Entry<String, JsonNode> entry = fields.next();
            ColumnResponse columnResponse = new ColumnResponse();
            columnResponse.setEnName(entry.getKey());
            columnResponse.setZhName(entry.getKey());
            columnResponse.setType(BizUtils.getType(entry.getValue()).name());
            result.add(columnResponse);
        }
        return result;
    }

    private List<TopicPartition> getPartitions(String topic) {
        return KafkaTopicUtils.getTopicPartitions(admin, topic);
    }

    @Override
    public void close() throws Exception {
        admin.close();
        consumer.close();
    }

    @Override
    public boolean topicExists(String topic) {
        try {
            AssertUtils.notEmpty(topic, "Topic不能为空");
            Set<String> topicNames = admin.listTopics().names().get();
            return topicNames.contains(topic);
        } catch (ExecutionException e) {
            throw new BizException(e, "获取topic列表失败");
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            throw new BizException("获取topic列表线程中断！");
        }
    }


    @Override
    public StorageEngineResponse createTopicIfNotExists(DataModel model, DataStorageSettings storageSettings) {
        String topic = model.getEnName();
        AssertUtils.notEmpty(topic, "Topic不能为空");
        try {
            if (topicExists(topic)) {
                log.info("字段变更记录topic已存在！不需要创建");
                return StorageEngineResponse.success();
            }
            KafkaDataStorageSettings settings = (KafkaDataStorageSettings) storageSettings;
            NewTopic newTopic = new NewTopic(topic, settings.getPartitions(),
                settings.getReplicas());
            Map<String, String> configMap = new HashMap<>();
            configMap.put(org.apache.kafka.common.config.TopicConfig.RETENTION_MS_CONFIG,
                "" + settings.getSaveDuration());
            configMap.put(TopicConfig.RETENTION_BYTES_CONFIG, "10737418240");
            newTopic.configs(configMap);
            CreateTopicsResult result = admin.createTopics(List.of(newTopic));
            result.all().get();
            log.info("创建topic[{}]成功！", topic);
            return StorageEngineResponse.success();
        } catch (InterruptedException e) {
            String message = String.format("创建topic[%s]发生中断异常！", topic);
            log.error(message, e);
            Thread.currentThread().interrupt();
            return StorageEngineResponse.fail(message);
        } catch (Exception e) {
            String message = String.format("创建topic[%s]发生异常！", topic);
            log.error(message, e);
            return StorageEngineResponse.fail(message);
        }
    }

    @Override
    public List<Map<String, Object>> queryMessages(String topic, ConditionSearchParams searchParams) {
        int msgCount = 10;
        return queryMessages(topic, msgCount);
    }

    private List<Map<String, Object>> queryMessages(String topic, int msgCount) {
        TopicDescription topicDesc = KafkaTopicUtils.getTopicDescription(admin, topic);
        AssertUtils.notNull(topicDesc, "Topic[%s]不存在", topic);
        Map<TopicPartition, ListOffsetsResultInfo> latestOffsetMap = KafkaTopicUtils.getTopicLatestOffsetMap(admin,
            topicDesc);
        if (ObjectUtils.isEmpty(latestOffsetMap)) {
            return Collections.emptyList();
        }
        Map<TopicPartition, ListOffsetsResultInfo> earliestOffsetMap = KafkaTopicUtils.getTopicEarliestOffsetMap(
            admin, topicDesc);
        List<ConsumerRecord<String, String>> messageList = new ArrayList<>();
        for (Entry<TopicPartition, ListOffsetsResultInfo> entry : latestOffsetMap.entrySet()) {
            TopicPartition partition = entry.getKey();
            ListOffsetsResultInfo earliestOffset = earliestOffsetMap.get(partition);
            if (earliestOffset == null) {
                continue;
            }
            long startOffset = Math.max(entry.getValue().offset() - msgCount + 1, earliestOffset.offset());
            messageList.addAll(queryPartitionMessages(partition, startOffset, entry.getValue().offset()));
        }
        if (messageList.isEmpty()) {
            return Collections.emptyList();
        }
        messageList.sort(Comparator.comparingLong(ConsumerRecord::timestamp));
        return buildDataList(messageList.subList(Math.max(0, messageList.size() - msgCount), messageList.size()));
    }

    private List<ConsumerRecord<String, String>> queryPartitionMessages(TopicPartition partition, long startOffset,
        long endOffset) {
        if (endOffset == 0 || startOffset == endOffset) {
            return Collections.emptyList();
        }
        consumer.assign(Collections.singletonList(partition));
        consumer.seek(partition, startOffset);
        List<ConsumerRecord<String, String>> messageList = new ArrayList<>();
        long startTime = System.currentTimeMillis();
        long timeout = 1500;
        long endOffset1 = endOffset - 1;
        Duration pollDuration = Duration.ofMillis(150L);
        whileLoop:
        while (System.currentTimeMillis() - startTime < timeout) {
            ConsumerRecords<String, String> records = consumer.poll(pollDuration);
            if (records.isEmpty()) {
                continue;
            }
            for (ConsumerRecord<String, String> consumerRecord : records) {
                messageList.add(consumerRecord);
                if (consumerRecord.offset() >= endOffset1) {
                    break whileLoop;
                }
            }
        }
        return messageList;
    }

    private List<Map<String, Object>> buildDataList(List<ConsumerRecord<String, String>> messageList) {
        List<Map<String, Object>> dataList = new ArrayList<>();
        for (ConsumerRecord<String, String> message : messageList) {
            String value = message.value();
            if (StringUtils.isEmpty(value)) {
                dataList.add(new HashMap<>());
            } else {
                dataList.add(JsonUtils.toMap(value, String.class, Object.class));
            }
            dataList.get(dataList.size() - 1).put(MqConnection.MQ_INTERNAL_OFFSET, message.offset());
            dataList.get(dataList.size() - 1).put(MqConnection.MQ_INTERNAL_TIMESTAMP, message.timestamp());
            dataList.get(dataList.size() - 1).put(MqConnection.MQ_INTERNAL_PARTITION, message.partition());
        }
        return dataList;
    }

    @Override
    public long getTopicTotalOffset(String topic) {
        TopicDescription topicDescription = KafkaTopicUtils.getTopicDescription(admin, topic);
        if (topicDescription == null) {
            return 0L;
        }
        return KafkaTopicUtils.getTopicTotalOffset(admin, topicDescription);
    }

    @Override
    public MqConsumeInfoResponse getConsumeInfo(String topic, String group) {
        LocalDateTime now = LocalDateTime.now();
        try {
            // 检查单个topic是否存在
            if (!KafkaTopicUtils.checkTopicExists(admin, topic)) {
                MqConsumeInfoResponse consumeInfo = new MqConsumeInfoResponse();
                consumeInfo.setCollectTime(now);
                consumeInfo.setLag(0L);
                consumeInfo.setCurrentOffset(0L);
                consumeInfo.setEndOffset(0L);
                return consumeInfo;
            }
            TopicDescription topicDescription = admin.describeTopics(
                Collections.singleton(topic)).allTopicNames().get().get(topic);
            MqConsumeInfoResponse consumeInfo = KafkaTopicUtils.getTopicConsumeInfo(admin, topicDescription, group);
            consumeInfo.setCollectTime(now);
            return consumeInfo;
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            throw new BizException(e, "获取【%s】主题消费信息发生中断异常", topic);
        } catch (Exception e) {
            throw new BizException(e, "获取【%s】主题消费信息发生异常", topic);
        }
    }


    @Override
    public long getTopicTimestampOffset(String topic, long timestamp) {
        return KafkaTopicUtils.getTopicTimestampOffset(admin, topic, timestamp);
    }

    @Override
    public long getTopicTodayOffset(String topic) {
        return KafkaTopicUtils.getTopicTodayOffset(admin, topic);
    }
}
