package com.trs.moye.storage.engine.db.nebula.transfer;

import com.vesoft.nebula.client.graph.data.ResultSet.Record;
import com.trs.moye.storage.engine.pojo.constant.NebulaEntity;
import com.vesoft.nebula.client.graph.SessionPool;
import com.vesoft.nebula.client.graph.data.ResultSet;
import com.vesoft.nebula.client.graph.exception.AuthFailedException;
import com.vesoft.nebula.client.graph.exception.BindSpaceFailedException;
import com.vesoft.nebula.client.graph.exception.ClientServerIncompatibleException;
import com.vesoft.nebula.client.graph.exception.IOErrorException;
import java.util.List;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Component;

/**
 * 边处理器
 *
 * <AUTHOR>
 * @since 2024/12/27 15:23:35
 */
@Slf4j
@Component
public class EdgeProcessor extends AbstractNebulaEntityProcessor {

    @Override
    protected NebulaEntity getEntityType() {
        return NebulaEntity.EDGE;
    }

    @Override
    protected String buildQueryForEntity(String edgeName, Map<String, String> fieldsMap, int offset) {
        String matchNgql = "MATCH ()-[e:" + edgeName + "]->() RETURN src(e),dst(e),rank(e)";
        if (ObjectUtils.isNotEmpty(fieldsMap)) {
            matchNgql += "," + String.join(",", fieldsMap.values());
        }
        matchNgql += String.format(" SKIP %d LIMIT %d", offset, BATCH_SIZE);
        return matchNgql;
    }

    @Override
    protected long getTotalRecords(String edgeName, SessionPool sourceSessionPool)
        throws IOErrorException, AuthFailedException, ClientServerIncompatibleException, BindSpaceFailedException {
        String query = "MATCH ()-[e:" + edgeName + "]->() RETURN count(*)";
        ResultSet totalRecordsResult = sourceSessionPool.execute(query);
        return totalRecordsResult.rowValues(0).get("count(*)").asLong();
    }

    @Override
    protected void executeInsertQuery(ResultSet resultSet, Map<String, String> fieldsMap, String entityName,
        SessionPool targetSessionPool)
        throws IOErrorException, AuthFailedException, ClientServerIncompatibleException, BindSpaceFailedException {
        StringBuilder values = new StringBuilder();
        for (int i = 0; i < resultSet.rowsSize(); i++) {
            Record nebulaRecord = resultSet.rowValues(i);
            String srcId = nebulaRecord.get("src(e)").toString();
            String dstId = nebulaRecord.get("dst(e)").toString();
            String rank = nebulaRecord.get("rank(e)").toString();
            String edgeKey = String.format("%s->%s@%s", srcId, dstId, rank);
            List<String> value = getValueString(fieldsMap, nebulaRecord);
            values.append(String.format("%s:(%s)", edgeKey,
                String.join(",", value))).append(",");
        }
        if (values.length() > 0) {
            values.deleteCharAt(values.length() - 1);
        }

        // 构建插入语句
        String insertQuery = buildInsertNGQL(fieldsMap, entityName, values.toString(), NebulaEntity.EDGE);
        ResultSet result = targetSessionPool.execute(insertQuery);
        log.info("Insert Edge {} : {}", entityName, insertQuery);
        if (result.isSucceeded()) {
            log.info("Insert Edge {} success!!!", entityName);
        } else {
            log.error("Insert Edge {} failed (ErrorCode: {}), Error message: {}", entityName, result.getErrorCode(),
                result.getErrorMessage());
        }
    }
}