<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.trs.moye.storage.engine.dao.TableExplorationResultMapper">
    <resultMap id="BaseResultMap" type="com.trs.moye.base.data.explore.TableExplorationResultEntity">
        <!--@mbg.generated-->
        <!--@Table mcp_table_exploration_result-->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="connection_id" jdbcType="BIGINT" property="connectionId"/>
        <result column="table_name" jdbcType="VARCHAR" property="tableName"/>
        <result column="schema_name" jdbcType="VARCHAR" property="schemaName"/>
        <result column="result" jdbcType="VARCHAR" property="result"
            typeHandler="com.trs.moye.base.common.typehandler.CustomJacksonTypeHandler"/>
        <result column="status" jdbcType="VARCHAR" property="status"/>
        <result column="created_at" jdbcType="TIMESTAMP" property="createdAt"/>
        <result column="updated_at" jdbcType="TIMESTAMP" property="updatedAt"/>
    </resultMap>

    <select id="findByConnectionIdAndTableName" resultMap="BaseResultMap">
        <!--@mbg.generated-->
        <!--@Table mcp_table_exploration_result-->
        SELECT id,
        connection_id,
        table_name,
        schema_name,
        result,
        status,
        created_at,
        updated_at
        FROM mcp_table_exploration_result
        WHERE connection_id = #{connectionId}
        AND table_name = #{tableName}
    </select>
</mapper>