<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.trs.moye.storage.engine.dao.DataAccessTraceMapper">
    <select id="selectByBatchNo" resultType="java.lang.Long">
        select record_id
        from data_access_trace
        where batch_no = #{batchNo}
    </select>

    <select id="selectAllByBatchNoAndStorageName" resultType="com.trs.moye.storage.engine.pojo.entity.DataAccessTrace">
        select *
        from data_access_trace
        where batch_no = #{batchNo}
          and storage_name = #{storageName}
    </select>
    <select id="selectErrorByBatchNoAndStorageNames"
        resultType="com.trs.moye.storage.engine.pojo.entity.DataAccessTrace">
        select *
        from data_access_trace
        where batch_no = #{batchNo}
        and is_error = 1
        <if test="storageNames != null and storageNames.size() > 0">
            and storage_name in
            <foreach item="name" collection="storageNames" separator="," open="(" close=")">
                #{name}
            </foreach>
        </if>
    </select>
</mapper>