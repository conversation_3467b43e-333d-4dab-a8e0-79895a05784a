#server configuration
server.port=18084
server.servlet.context-path=/storage
# 连接池配置
spring.datasource.hikari.maximum-pool-size=100
spring.datasource.hikari.minimum-idle=30
spring.datasource.hikari.idle-timeout=3000
spring.datasource.hikari.connection-timeout=5000
spring.datasource.hikari.max-lifetime=900000

#seatunnel configuration
com.trs.seatunnel.host=seatunnel-svc
com.trs.seatunnel.port=5801
com.trs.seatunnel.schema=http
com.trs.seatunnel.default-parallelism=1

#logging configuration
logging.file.path=./logs

#mybatis-plus configuration
#mybatis-plus.configuration.log-impl=org.apache.ibatis.logging.stdout.StdOutImpl

# xxl-job配置
xxl.job.executor.appname=moye-storage-engine

#nlp 向量化
nlp-service.word-embedding-url=http://**************:7000/nlp/embedding

