package com.trs.moye.storage.engine.mq.kafka;

import com.trs.moye.base.common.utils.JsonUtils;
import com.trs.moye.base.data.connection.entity.params.KafkaConnectionParams;
import com.trs.moye.base.data.connection.enums.ConnectionType;
import com.trs.moye.base.data.model.field.mapping.ColumnResponse;
import java.util.List;
import java.util.Map;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

class KafkaConnectionTest {

    private KafkaConnectionParams connectionParams;

    @BeforeEach
    void setUp() {
        connectionParams = new KafkaConnectionParams();
        connectionParams.setConnectionType(ConnectionType.KAFKA);
        connectionParams.setHost("**************");
        connectionParams.setPort(9092);
    }

    @Test
    void emptyTest() {
        Assertions.assertTrue(true);
    }

    //    @Test
    void getTableFields() {
        String topic = "ods_mlx_mlx0515_dev";
        try (KafkaConnection connection = new KafkaConnection(connectionParams)) {
            List<ColumnResponse> fields = connection.getTableFields(topic);
            System.out.println(topic + "数据量: " + fields.size());
            System.out.println(topic + "数据列表: " + JsonUtils.toJsonString(fields));
        } catch (Exception e) {
            throw new RuntimeException(e);
        }

    }

    //    @Test
    void queryTopicNewestMessages() {
        try (KafkaConnection connection = new KafkaConnection(connectionParams)) {
            queryTopicNewestMessages0(connection, "ods_mlx_mlx0515_dev");
        } catch (Exception e) {
            throw new RuntimeException(e);
        }

    }

    void queryTopicNewestMessages0(KafkaConnection connection, String topic) {
        List<Map<String, Object>> messages = connection.queryMessages(topic, null);
        System.out.println(topic + "数据量: " + messages.size());
        System.out.println(topic + "数据列表: " + JsonUtils.toJsonString(messages));
    }
}