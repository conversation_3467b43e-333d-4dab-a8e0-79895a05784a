package com.trs.moye.storage.engine.db.elasticsearch;

import static org.junit.jupiter.api.Assertions.assertEquals;

import com.fasterxml.jackson.databind.JsonNode;
import com.trs.moye.base.common.utils.JsonUtils;
import com.trs.moye.storage.engine.db.BasicTypeDefine;
import java.util.List;
import java.util.stream.Stream;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;

/**
 * es解析测试
 *
 * <AUTHOR>
 * @since 2025/7/16 10:40
 */
class ElasticsearchConnectionTest {


    @ParameterizedTest
    @MethodSource("propertiesProvider")
    void parseFieldsTest(String json, int expectedSize) {
        JsonNode properties = JsonUtils.parseJsonNode(json);
        List<BasicTypeDefine> fields = ElasticsearchConnection.parseFieldDefines(properties);
        assertEquals(expectedSize, fields.size());
    }

    private static Stream<Arguments> propertiesProvider() {
        return Stream.of(
            Arguments.of("""
                {
                        "capture_time": {
                          "type": "date"
                        },
                        "car_brand": {
                          "type": "text",
                          "fields": {
                            "keyword": {
                              "type": "keyword",
                              "ignore_above": 256
                            }
                          }
                        },
                        "device_id": {
                          "type": "text",
                          "fields": {
                            "keyword": {
                              "type": "keyword",
                              "ignore_above": 256
                            }
                          }
                        },
                        "device_name": {
                          "type": "text",
                          "fields": {
                            "keyword": {
                              "type": "keyword",
                              "ignore_above": 256
                            }
                          }
                        },
                        "image_url": {
                          "type": "text",
                          "fields": {
                            "keyword": {
                              "type": "keyword",
                              "ignore_above": 256
                            }
                          }
                        },
                        "latitude": {
                          "type": "text",
                          "fields": {
                            "keyword": {
                              "type": "keyword",
                              "ignore_above": 256
                            }
                          }
                        },
                        "license_plate": {
                          "type": "text",
                          "fields": {
                            "keyword": {
                              "type": "keyword",
                              "ignore_above": 256
                            }
                          }
                        },
                        "longitude": {
                          "type": "text",
                          "fields": {
                            "keyword": {
                              "type": "keyword",
                              "ignore_above": 256
                            }
                          }
                        },
                        "mask_left": {
                          "type": "text",
                          "fields": {
                            "keyword": {
                              "type": "keyword",
                              "ignore_above": 256
                            }
                          }
                        },
                        "mask_left_idnumber": {
                          "type": "text",
                          "fields": {
                            "keyword": {
                              "type": "keyword",
                              "ignore_above": 256
                            }
                          }
                        },
                        "mask_left_name": {
                          "type": "text",
                          "fields": {
                            "keyword": {
                              "type": "keyword",
                              "ignore_above": 256
                            }
                          }
                        },
                        "mask_left_similaritydegree": {
                          "type": "text",
                          "fields": {
                            "keyword": {
                              "type": "keyword",
                              "ignore_above": 256
                            }
                          }
                        },
                        "mask_left_smiall_image_url": {
                          "type": "text",
                          "fields": {
                            "keyword": {
                              "type": "keyword",
                              "ignore_above": 256
                            }
                          }
                        },
                        "mask_left_url": {
                          "type": "text",
                          "fields": {
                            "keyword": {
                              "type": "keyword",
                              "ignore_above": 256
                            }
                          }
                        },
                        "mask_right": {
                          "type": "text",
                          "fields": {
                            "keyword": {
                              "type": "keyword",
                              "ignore_above": 256
                            }
                          }
                        },
                        "mask_right_idnumber": {
                          "type": "text",
                          "fields": {
                            "keyword": {
                              "type": "keyword",
                              "ignore_above": 256
                            }
                          }
                        },
                        "mask_right_name": {
                          "type": "text",
                          "fields": {
                            "keyword": {
                              "type": "keyword",
                              "ignore_above": 256
                            }
                          }
                        },
                        "mask_right_similaritydegree": {
                          "type": "text",
                          "fields": {
                            "keyword": {
                              "type": "keyword",
                              "ignore_above": 256
                            }
                          }
                        },
                        "mask_right_url": {
                          "type": "text",
                          "fields": {
                            "keyword": {
                              "type": "keyword",
                              "ignore_above": 256
                            }
                          }
                        },
                        "trs_id": {
                          "type": "text",
                          "fields": {
                            "keyword": {
                              "type": "keyword",
                              "ignore_above": 256
                            }
                          }
                        },
                        "trs_in_time": {
                          "type": "date"
                        },
                        "trs_source_from": {
                          "type": "text",
                          "fields": {
                            "keyword": {
                              "type": "keyword",
                              "ignore_above": 256
                            }
                          }
                        },
                        "trs_source_time": {
                          "type": "date"
                        }
                      }
                """, 23), // 有效JSON数组
            Arguments.of("""
                {
                        "age": {
                          "type": "integer",
                          "meta": {
                            "comment": "age"
                          }
                        },
                        "bookNum": {
                          "type": "long"
                        },
                        "boolean_field": {
                          "type": "boolean"
                        },
                        "datetime": {
                          "type": "keyword",
                          "meta": {
                            "comment": "datetime"
                          }
                        },
                        "datetime_001": {
                          "type": "keyword"
                        },
                        "datetime_002": {
                          "type": "date"
                        },
                        "exclude_text": {
                          "type": "text",
                          "fields": {
                            "keyword": {
                              "type": "keyword",
                              "ignore_above": 256
                            }
                          }
                        },
                        "is_approved": {
                          "type": "boolean",
                          "null_value": false
                        },
                        "name": {
                          "type": "keyword",
                          "meta": {
                            "comment": "name"
                          }
                        },
                        "new_boolean_field": {
                          "type": "boolean"
                        },
                        "sex": {
                          "type": "keyword",
                          "meta": {
                            "comment": "sex"
                          }
                        },
                        "test_add": {
                          "type": "text",
                          "fields": {
                            "keyword": {
                              "type": "keyword",
                              "ignore_above": 256
                            }
                          }
                        },
                        "test_float": {
                          "type": "double",
                          "meta": {
                            "comment": "test_float"
                          }
                        },
                        "test_string": {
                          "type": "keyword",
                          "meta": {
                            "comment": "test_string"
                          }
                        },
                        "test_text": {
                          "type": "text",
                          "meta": {
                            "comment": "测试长文本"
                          }
                        },
                        "test_vector": {
                          "type": "dense_vector",
                          "dims": 768,
                          "meta": {
                            "comment": "向量化"
                          }
                        },
                        "text_01": {
                          "type": "text"
                        },
                        "timestamp": {
                          "type": "double"
                        },
                        "trs_moye_batch_no": {
                          "type": "keyword",
                          "meta": {
                            "comment": "数据中台批次号"
                          }
                        },
                        "trs_moye_input_time": {
                          "type": "date",
                          "format": "yyyy-MM-dd HH:mm:ss || yyyy-MM-dd'T'HH:mm:ss'.000+08:00' || yyyy-MM-dd'T'HH:mm:ss || yyyy-MM-dd'T'HH:mm",
                          "meta": {
                            "comment": "数据中台入库时间"
                          }
                        },
                        "trs_moye_record_id": {
                          "type": "keyword",
                          "meta": {
                            "comment": "数据中台任务数据记录id"
                          }
                        },
                        "trs_moye_task_id": {
                          "type": "long",
                          "meta": {
                            "comment": "数据中台任务主键"
                          }
                        }
                      }
                """, 22),
            Arguments.of("""
                {
                        "IR_CHANNEL": {
                          "type": "keyword",
                          "meta": {
                            "comment": "IR_CHANNEL"
                          }
                        },
                        "IR_CMTCOUNT": {
                          "type": "keyword",
                          "meta": {
                            "comment": "IR_CMTCOUNT"
                          }
                        },
                        "IR_COLLECT_COUNT": {
                          "type": "keyword",
                          "meta": {
                            "comment": "IR_COLLECT_COUNT"
                          }
                        },
                        "IR_HKEY": {
                          "type": "keyword",
                          "meta": {
                            "comment": "IR_HKEY"
                          }
                        },
                        "IR_JOINCOUNT": {
                          "type": "keyword",
                          "meta": {
                            "comment": "IR_JOINCOUNT"
                          }
                        },
                        "IR_LASTTIME": {
                          "type": "keyword",
                          "meta": {
                            "comment": "IR_LASTTIME"
                          }
                        },
                        "IR_LOADTIME": {
                          "type": "keyword",
                          "meta": {
                            "comment": "IR_LOADTIME"
                          }
                        },
                        "IR_PRCOUNT": {
                          "type": "keyword",
                          "meta": {
                            "comment": "IR_PRCOUNT"
                          }
                        },
                        "IR_RDCOUNT": {
                          "type": "keyword",
                          "meta": {
                            "comment": "IR_RDCOUNT"
                          }
                        },
                        "IR_SERVICEID": {
                          "type": "keyword",
                          "meta": {
                            "comment": "IR_SERVICEID"
                          }
                        },
                        "IR_SHARECOUNT": {
                          "type": "keyword",
                          "meta": {
                            "comment": "IR_SHARECOUNT"
                          }
                        },
                        "IR_SID": {
                          "type": "keyword",
                          "meta": {
                            "comment": "IR_SID"
                          }
                        },
                        "IR_SITENAME": {
                          "type": "keyword",
                          "meta": {
                            "comment": "IR_SITENAME"
                          }
                        },
                        "IR_URLNAME": {
                          "type": "keyword",
                          "meta": {
                            "comment": "IR_URLNAME"
                          }
                        },
                        "SOURCE": {
                          "type": "keyword",
                          "meta": {
                            "comment": "SOURCE"
                          }
                        },
                        "fff": {
                          "type": "nested",
                          "properties": {
                            "alias": {
                              "type": "text",
                              "fields": {
                                "keyword": {
                                  "type": "keyword",
                                  "ignore_above": 256
                                }
                              },
                              "meta": {
                                "comment": "别名"
                              }
                            },
                            "areaCode": {
                              "type": "text",
                              "fields": {
                                "keyword": {
                                  "type": "keyword",
                                  "ignore_above": 256
                                }
                              },
                              "meta": {
                                "comment": "属地地区代码"
                              }
                            },
                            "birthday": {
                              "type": "text",
                              "fields": {
                                "keyword": {
                                  "type": "keyword",
                                  "ignore_above": 256
                                }
                              },
                              "meta": {
                                "comment": "出生年月"
                              }
                            },
                            "category": {
                              "type": "text",
                              "fields": {
                                "keyword": {
                                  "type": "keyword",
                                  "ignore_above": 256
                                }
                              },
                              "meta": {
                                "comment": "分类"
                              }
                            },
                            "censusRegister": {
                              "type": "text",
                              "fields": {
                                "keyword": {
                                  "type": "keyword",
                                  "ignore_above": 256
                                }
                              },
                              "meta": {
                                "comment": "户籍"
                              }
                            },
                            "company": {
                              "type": "text",
                              "fields": {
                                "keyword": {
                                  "type": "keyword",
                                  "ignore_above": 256
                                }
                              },
                              "meta": {
                                "comment": "单位"
                              }
                            },
                            "education": {
                              "type": "text",
                              "fields": {
                                "keyword": {
                                  "type": "keyword",
                                  "ignore_above": 256
                                }
                              },
                              "meta": {
                                "comment": "教育经历"
                              }
                            },
                            "excludeWord": {
                              "type": "text",
                              "fields": {
                                "keyword": {
                                  "type": "keyword",
                                  "ignore_above": 256
                                }
                              },
                              "meta": {
                                "comment": "排除词"
                              }
                            },
                            "level": {
                              "type": "text",
                              "fields": {
                                "keyword": {
                                  "type": "keyword",
                                  "ignore_above": 256
                                }
                              },
                              "meta": {
                                "comment": "级别"
                              }
                            },
                            "name": {
                              "type": "text",
                              "fields": {
                                "keyword": {
                                  "type": "keyword",
                                  "ignore_above": 256
                                }
                              },
                              "meta": {
                                "comment": "姓名"
                              }
                            },
                            "post": {
                              "type": "text",
                              "fields": {
                                "keyword": {
                                  "type": "keyword",
                                  "ignore_above": 256
                                }
                              },
                              "meta": {
                                "comment": "当前职务"
                              }
                            },
                            "remark": {
                              "type": "text",
                              "fields": {
                                "keyword": {
                                  "type": "keyword",
                                  "ignore_above": 256
                                }
                              },
                              "meta": {
                                "comment": "备注"
                              }
                            },
                            "sensitiveLevel": {
                              "type": "long",
                              "meta": {
                                "comment": "敏感系数"
                              }
                            },
                            "sex": {
                              "type": "text",
                              "fields": {
                                "keyword": {
                                  "type": "keyword",
                                  "ignore_above": 256
                                }
                              },
                              "meta": {
                                "comment": "性别"
                              }
                            },
                            "spouse": {
                              "type": "text",
                              "fields": {
                                "keyword": {
                                  "type": "keyword",
                                  "ignore_above": 256
                                }
                              },
                              "meta": {
                                "comment": "配偶"
                              }
                            },
                            "workArea": {
                              "type": "text",
                              "fields": {
                                "keyword": {
                                  "type": "keyword",
                                  "ignore_above": 256
                                }
                              },
                              "meta": {
                                "comment": "属地"
                              }
                            }
                          }
                        },
                        "ggg": {
                          "type": "nested"
                        },
                        "trs_moye_batch_no": {
                          "type": "keyword",
                          "meta": {
                            "comment": "数据中台批次号"
                          }
                        },
                        "trs_moye_input_time": {
                          "type": "date",
                          "format": "yyyy-MM-dd HH:mm:ss || yyyy-MM-dd'T'HH:mm:ss'.000+08:00' || yyyy-MM-dd'T'HH:mm:ss || yyyy-MM-dd'T'HH:mm",
                          "meta": {
                            "comment": "数据中台入库时间"
                          }
                        },
                        "trs_moye_record_id": {
                          "type": "keyword",
                          "meta": {
                            "comment": "数据中台任务数据记录id"
                          }
                        },
                        "trs_moye_task_id": {
                          "type": "integer",
                          "meta": {
                            "comment": "数据中台任务主键"
                          }
                        },
                        "ttt": {
                          "type": "nested"
                        }
                      }
                """, 37)
        );
    }
}