package com.trs.moye.storage.engine.db.hive;

import static org.assertj.core.api.Assertions.assertThat;

import com.trs.moye.base.data.connection.entity.params.HiveConnectionParams;
import com.trs.moye.base.data.connection.enums.ConnectionType;
import com.trs.moye.storage.engine.db.DatabaseMetadata;
import java.io.IOException;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.SQLException;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.condition.EnabledIf;

/**
 * HiveConnectionTest
 *
 * <AUTHOR>
 * @since 2024/11/22 15:12
 */
@Slf4j
class HiveConnectionTest {

    private static final String HOST = "**************";
    private static final Integer PORT = 10000;
    private static final String DATABASE = "default";
    private static final String USER = "root";
    private static final String PASSWORD = "zE9Ll6c!nZm4";

    private static boolean isDatabaseAccessible() {
        String url = "jdbc:hive2://" + HOST + ":" + PORT + "/" + DATABASE;
        try (Connection connection = DriverManager.getConnection(url, USER, PASSWORD)) {
            return true;
        } catch (SQLException e) {
            log.warn("连接示例数据库[{}][{}][{}]失败，跳过部分测试", url, USER, PASSWORD);
            return false;
        }
    }

    @Test
    @EnabledIf("isDatabaseAccessible")
    void getDatabaseSchema() {
        HiveConnectionParams params = new HiveConnectionParams();
        params.setConnectionType(ConnectionType.HIVE);
        params.setHost(HOST);
        params.setPort(PORT);
        params.setDatabase(DATABASE);
        params.setUsername(USER);
        params.setPassword(PASSWORD);
        try (HiveConnection connection = new HiveConnection(params, null)) {
            DatabaseMetadata databaseSchema = connection.getMetadata();
            assertThat(databaseSchema.getDatabases()).extracting(DatabaseMetadata.Database::getDatabaseName)
                .contains("default");
        } catch (IOException e) {
            log.warn("连接hive失败", e);
        }
    }
}