package com.trs.moye.storage.engine.utils;

import static org.junit.jupiter.api.Assertions.assertEquals;

import com.trs.moye.base.common.utils.AesUtils;
import org.junit.jupiter.api.Test;

class AESUtilTest {

    private static final String RAW_PASSWORD = "1234";
    private static final String KEY = "trs-moye12345678";
    private static final String ENCODED_PASSWORD = "5OVVNIXpN9mMXbO/tz2e4g==";

    @Test
    void testEncrypt() {
        // 调用加密方法
        String encryptedPassword = AesUtils.encrypt(RAW_PASSWORD, KEY);
        // 断言加密结果是否与预期相同
        assertEquals(ENCODED_PASSWORD, encryptedPassword);
    }

    @Test
    void testDecrypt() {
        // 调用解密方法
        String decryptedPassword = AesUtils.decrypt(ENCODED_PASSWORD, KEY);
        // 断言解密结果是否与预期相同
        assertEquals(RAW_PASSWORD, decryptedPassword);
    }
}