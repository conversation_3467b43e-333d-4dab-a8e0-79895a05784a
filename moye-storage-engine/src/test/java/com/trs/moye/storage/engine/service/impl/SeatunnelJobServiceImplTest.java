package com.trs.moye.storage.engine.service.impl;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.when;

import com.trs.moye.base.data.execute.ExecuteMode;
import com.trs.moye.base.data.model.entity.DataModel;
import com.trs.moye.base.data.storage.DataStorage;
import com.trs.moye.base.common.utils.JsonUtils;
import com.trs.moye.storage.engine.mq.kafka.KafkaProducerProperties;
import com.trs.moye.storage.engine.seatunnel.job.config.SeatunnelJobConfigFactory;
import com.trs.moye.storage.engine.seatunnel.request.SeaTunnelJobConfig;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;
import org.apache.commons.lang3.tuple.Pair;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

class SeatunnelJobServiceImplTest {

    @Mock
    private KafkaProducerProperties producerProperties;

    @InjectMocks
    private SeatunnelJobConfigFactory seatunnelJobService;

    @BeforeEach
    public void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void buildBatchNo() {
        LocalDateTime fixedDateTime = LocalDateTime.of(2023, 10, 10, 12, 0);

        try (MockedStatic<LocalDateTime> mocked = Mockito.mockStatic(LocalDateTime.class)) {
            mocked.when(LocalDateTime::now).thenReturn(fixedDateTime);
            int modelId = 1;
            ExecuteMode accessMode = ExecuteMode.IMMEDIATE;
            String batchNo = SeatunnelJobServiceImpl.buildBatchNo(modelId, accessMode);
            assertThat(batchNo).isEqualTo("T1-immediate-20231010120000");

            modelId = 2;
            accessMode = ExecuteMode.FIXED_TIME;
            batchNo = SeatunnelJobServiceImpl.buildBatchNo(modelId, accessMode);
            assertThat(batchNo).isEqualTo("T2-fixed_time-20231010120000");
        }
    }

    @Test
    void testGetJobNameWithOdsPrefix() {
        String enName = "ods_example";
        Integer modelId = 1;
        String expected = "moye_storage_ods_1_ods_example";
        String actual = SeatunnelJobConfigFactory.getJobName(enName, modelId);
        assertEquals(expected, actual);
    }

    @Test
    void testGetJobNameWithDwdPrefix() {
        String enName = "dwd_example";
        Integer modelId = 2;
        String expected = "moye_storage_dwd_2_dwd_example";
        String actual = SeatunnelJobConfigFactory.getJobName(enName, modelId);
        assertEquals(expected, actual);
    }

    @Test
    void testGetJobNameWithNoPrefix() {
        String enName = "unknown_example";
        Integer modelId = 3;
        String expected = "";
        String actual = SeatunnelJobConfigFactory.getJobName(enName, modelId);
        assertEquals(expected, actual);
    }

    @Test
    void testGetJobNameWithEmptyEnName() {
        String enName = "";
        Integer modelId = 5;
        String expected = "";
        String actual = SeatunnelJobConfigFactory.getJobName(enName, modelId);
        assertEquals(expected, actual);
    }

    @Test
    void testDwdJobConfig() {
        when(producerProperties.getBootStrapServers()).thenReturn(List.of("**************:9092"));
        when(producerProperties.getHost()).thenReturn("**************");
        when(producerProperties.getPort()).thenReturn(9092);
        String dataModelJson =
            "{\"id\":3204,\"createMode\":\"NORMAL\",\"executeStatus\":\"START\",\"zhName\":\"流处理-kafka_test_data\",\"enName\":\"dwd_kafka_test_data\",\"businessCategoryId\":189,\"description\":\"\",\"dataSourceConfig\":{\"id\":3914,\"enName\":\"kafka_test_data\",\"zhName\":\"kafka_test_data\",\"connectionId\":43,\"connection\":{\"id\":43,\"name\":\"蒙利幸51kafka\",\"connectionType\":\"KAFKA\",\"connectionParams\":{\"host\":\"**************\",\"port\":9092,\"protocol\":\"http\",\"username\":\"\",\"password\":\"qwer1234\",\"connectionType\":\"KAFKA\",\"saslMechanism\":\"PLAIN\"},\"isSource\":true,\"testSuccess\":true,\"dbconnection\":false,\"httpConnection\":false,\"fileConnection\":false},\"jobMode\":\"BATCH\"},\"dataStorage\":[{\"id\":6223,\"enName\":\"dwd_kafka_test_data\",\"zhName\":\"流处理-kafka_test_data\",\"connectionId\":1279,\"connection\":{\"id\":1279,\"name\":\"MySQL_data_service\",\"connectionType\":\"MYSQL\",\"connectionParams\":{\"host\":\"**************\",\"port\":33066,\"protocol\":\"http\",\"username\":\"root\",\"password\":\"!QAZ2wsx1234\",\"connectionType\":\"MYSQL\",\"database\":\"mysql_51_test\"},\"isSource\":false,\"testSuccess\":true,\"dbconnection\":true,\"httpConnection\":false,\"fileConnection\":false}},{\"id\":12602,\"enName\":\"dwd_kafka_test_data\",\"zhName\":\"流处理-kafka_test_data\",\"connectionId\":61,\"connection\":{\"id\":61,\"name\":\"CK(34)\",\"connectionType\":\"CLICK_HOUSE\",\"connectionParams\":{\"host\":\"**************\",\"port\":8123,\"protocol\":\"http\",\"username\":\"moye\",\"password\":\"trsadmin@1234\",\"connectionType\":\"CLICK_HOUSE\",\"database\":\"moye_dev\"},\"isSource\":false,\"testSuccess\":true,\"dbconnection\":true,\"httpConnection\":false,\"fileConnection\":false}},{\"id\":12776,\"enName\":\"dwd_kafka_test_data\",\"zhName\":\"流处理-kafka_test_data\",\"connectionId\":558,\"connection\":{\"id\":558,\"name\":\"TRS-CLICK_HOUSE-数据存储\",\"connectionType\":\"CLICK_HOUSE\",\"connectionParams\":{\"host\":\"**************\",\"port\":38123,\"protocol\":\"http\",\"username\":\"admin\",\"password\":\"trsadmin@1234\",\"connectionType\":\"CLICK_HOUSE\",\"database\":\"auto_data_storage_ck\"},\"isSource\":false,\"testSuccess\":true,\"dbconnection\":true,\"httpConnection\":false,\"fileConnection\":false}}],\"fields\":[{\"id\":44721,\"dataModelId\":3204,\"zhName\":\"id\",\"enName\":\"id\",\"type\":\"STRING\",\"typeName\":\"字符串\",\"advanceConfig\":{},\"primary\":true,\"increment\":false,\"partition\":false,\"nullable\":false,\"multiValue\":false,\"statistic\":false,\"builtIn\":false,\"fieldsSchema\":{},\"seatunnelType\":\"string\"},{\"id\":44722,\"dataModelId\":3204,\"zhName\":\"zhName\",\"enName\":\"zhName\",\"type\":\"STRING\",\"typeName\":\"字符串\",\"advanceConfig\":{},\"primary\":false,\"increment\":false,\"partition\":false,\"nullable\":false,\"multiValue\":false,\"statistic\":false,\"builtIn\":false,\"fieldsSchema\":{},\"seatunnelType\":\"string\"},{\"id\":44723,\"dataModelId\":3204,\"zhName\":\"enName\",\"enName\":\"enName\",\"type\":\"STRING\",\"typeName\":\"字符串\",\"advanceConfig\":{},\"primary\":false,\"increment\":false,\"partition\":false,\"nullable\":true,\"multiValue\":false,\"statistic\":false,\"builtIn\":false,\"fieldsSchema\":{},\"seatunnelType\":\"string\"},{\"id\":44724,\"dataModelId\":3204,\"zhName\":\"createMode\",\"enName\":\"createMode\",\"type\":\"STRING\",\"typeName\":\"字符串\",\"advanceConfig\":{},\"primary\":false,\"increment\":false,\"partition\":false,\"nullable\":true,\"multiValue\":false,\"statistic\":false,\"builtIn\":false,\"fieldsSchema\":{},\"seatunnelType\":\"string\"},{\"id\":44725,\"dataModelId\":3204,\"zhName\":\"executeStatus\",\"enName\":\"executeStatus\",\"type\":\"STRING\",\"typeName\":\"字符串\",\"advanceConfig\":{},\"primary\":false,\"increment\":false,\"partition\":false,\"nullable\":true,\"multiValue\":false,\"statistic\":false,\"builtIn\":false,\"fieldsSchema\":{},\"seatunnelType\":\"string\"},{\"id\":44726,\"dataModelId\":3204,\"zhName\":\"businessCategory\",\"enName\":\"businessCategory\",\"type\":\"STRING\",\"typeName\":\"字符串\",\"advanceConfig\":{},\"primary\":false,\"increment\":false,\"partition\":false,\"nullable\":true,\"multiValue\":false,\"statistic\":false,\"builtIn\":false,\"fieldsSchema\":{},\"seatunnelType\":\"string\"},{\"id\":44727,\"dataModelId\":3204,\"zhName\":\"layer\",\"enName\":\"layer\",\"type\":\"STRING\",\"typeName\":\"字符串\",\"advanceConfig\":{},\"primary\":false,\"increment\":false,\"partition\":false,\"nullable\":true,\"multiValue\":false,\"statistic\":false,\"builtIn\":false,\"fieldsSchema\":{},\"seatunnelType\":\"string\"},{\"id\":44728,\"dataModelId\":3204,\"zhName\":\"description\",\"enName\":\"description\",\"type\":\"STRING\",\"typeName\":\"字符串\",\"advanceConfig\":{},\"primary\":false,\"increment\":false,\"partition\":false,\"nullable\":true,\"multiValue\":false,\"statistic\":false,\"builtIn\":false,\"fieldsSchema\":{},\"seatunnelType\":\"string\"},{\"id\":44729,\"dataModelId\":3204,\"zhName\":\"createTime\",\"enName\":\"createTime\",\"type\":\"STRING\",\"typeName\":\"字符串\",\"advanceConfig\":{},\"primary\":false,\"increment\":false,\"partition\":false,\"nullable\":true,\"multiValue\":false,\"statistic\":false,\"builtIn\":false,\"fieldsSchema\":{},\"seatunnelType\":\"string\"},{\"id\":44730,\"dataModelId\":3204,\"zhName\":\"updateTime\",\"enName\":\"updateTime\",\"type\":\"STRING\",\"typeName\":\"字符串\",\"advanceConfig\":{},\"primary\":false,\"increment\":false,\"partition\":false,\"nullable\":true,\"multiValue\":false,\"statistic\":false,\"builtIn\":false,\"fieldsSchema\":{},\"seatunnelType\":\"string\"},{\"id\":44731,\"dataModelId\":3204,\"zhName\":\"字数统计\",\"enName\":\"count\",\"type\":\"INT\",\"typeName\":\"整型\",\"advanceConfig\":{\"enumValues\":[],\"type\":\"INT\"},\"primary\":false,\"increment\":false,\"partition\":false,\"nullable\":true,\"multiValue\":false,\"statistic\":false,\"builtIn\":false,\"fieldsSchema\":{},\"seatunnelType\":\"int\"},{\"id\":46085,\"dataModelId\":3204,\"zhName\":\"数据中台入库时间\",\"enName\":\"trs_moye_input_time\",\"type\":\"DATETIME\",\"typeName\":\"日期时间\",\"description\":\"数据中台内部专用字段\",\"advanceConfig\":{\"minValue\":\"1990-01-0100:00:00\",\"maxValue\":\"2100-01-0100:00:00\",\"enumValues\":[],\"type\":\"DATETIME\"},\"primary\":false,\"increment\":false,\"partition\":false,\"nullable\":false,\"multiValue\":false,\"statistic\":false,\"builtIn\":true,\"fieldsSchema\":{},\"seatunnelType\":\"timestamp\"},{\"id\":46086,\"dataModelId\":3204,\"zhName\":\"数据中台批次号\",\"enName\":\"trs_moye_batch_no\",\"type\":\"STRING\",\"typeName\":\"字符串\",\"description\":\"数据中台内部专用字段\",\"advanceConfig\":{\"enumValues\":[],\"type\":\"STRING\"},\"primary\":false,\"increment\":false,\"partition\":false,\"nullable\":false,\"multiValue\":false,\"statistic\":false,\"builtIn\":true,\"fieldsSchema\":{},\"seatunnelType\":\"string\"},{\"id\":46087,\"dataModelId\":3204,\"zhName\":\"数据中台任务主键\",\"enName\":\"trs_moye_task_id\",\"type\":\"LONG\",\"typeName\":\"长整型\",\"description\":\"数据中台内部专用字段11\",\"advanceConfig\":{\"enumValues\":[],\"type\":\"INT\"},\"primary\":false,\"increment\":false,\"partition\":false,\"nullable\":false,\"multiValue\":false,\"statistic\":false,\"builtIn\":true,\"fieldsSchema\":{},\"seatunnelType\":\"bigint\"}],\"executeConfig\":{\"id\":2553,\"dataModelId\":3204,\"incrementInfo\":{\"numberType\":false,\"dateType\":false},\"increment\":true},\"scheduleConfig\":{\"id\":3182,\"dataModelId\":3204,\"executeMode\":\"REALTIME\"}}";
        String storageJson =
            "[{\"id\":6223,\"enName\":\"dwd_kafka_test_data\",\"zhName\":\"流处理-kafka_test_data\",\"connectionId\":1279,\"connection\":{\"id\":1279,\"name\":\"MySQL_data_service\",\"connectionType\":\"MYSQL\",\"connectionParams\":{\"host\":\"**************\",\"port\":33066,\"protocol\":\"http\",\"username\":\"root\",\"password\":\"!QAZ2wsx1234\",\"connectionType\":\"MYSQL\",\"database\":\"mysql_51_test\"},\"isSource\":false,\"testSuccess\":true,\"fileConnection\":false,\"dbconnection\":true,\"httpConnection\":false},\"dataModelId\":3204,\"createTableStatus\":\"SUCCESS\",\"fieldIds\":[44721,44722,44723,44724,44725,44726,44727,44728,44729,44730,44731,46085,46086,46087],\"settings\":{\"connectionType\":\"MYSQL\"}},{\"id\":12602,\"enName\":\"dwd_kafka_test_data\",\"zhName\":\"流处理-kafka_test_data\",\"connectionId\":61,\"connection\":{\"id\":61,\"name\":\"CK(34)\",\"connectionType\":\"CLICK_HOUSE\",\"connectionParams\":{\"host\":\"**************\",\"port\":8123,\"protocol\":\"http\",\"username\":\"moye\",\"password\":\"trsadmin@1234\",\"connectionType\":\"CLICK_HOUSE\",\"database\":\"moye_dev\"},\"isSource\":false,\"testSuccess\":true,\"fileConnection\":false,\"dbconnection\":true,\"httpConnection\":false},\"dataModelId\":3204,\"createTableStatus\":\"SUCCESS\",\"fieldIds\":[44721,44722,44723,44724,44725,44726,44727,44728,44729,44730,44731,46085,46086,46087],\"settings\":{\"connectionType\":\"CLICK_HOUSE\",\"orderField\":\"id\",\"engine\":\"MergeTree\"}}]";
        String batchNo = "T3204-fixed_time-20241122110800";
        DataModel dataModel = JsonUtils.parseObject(dataModelJson, DataModel.class);
        List<DataStorage> dataStorages = JsonUtils.toList(storageJson, DataStorage.class);
        List<Pair<Integer, SeaTunnelJobConfig>> map = seatunnelJobService.createDwdJobConfigs(dataModel, dataStorages,
            batchNo);
        List<SeaTunnelJobConfig> jobConfigs = map.stream()
            .map(Pair::getValue)
            .collect(Collectors.toCollection(ArrayList::new));

        String expected =
            "[{\"env\":{\"job.retry.times\":0,\"job.mode\":\"BATCH\",\"job.name\":\"moye_storage_dwd_3204_dwd_kafka_test_data\"},\"source\":[{\"plugin_output\":\"source_table\",\"schema\":{\"columns\":[{\"name\":\"id\",\"type\":\"string\",\"nullable\":false},{\"name\":\"zhName\",\"type\":\"string\",\"nullable\":false},{\"name\":\"enName\",\"type\":\"string\",\"nullable\":true},{\"name\":\"createMode\",\"type\":\"string\",\"nullable\":true},{\"name\":\"executeStatus\",\"type\":\"string\",\"nullable\":true},{\"name\":\"businessCategory\",\"type\":\"string\",\"nullable\":true},{\"name\":\"layer\",\"type\":\"string\",\"nullable\":true},{\"name\":\"description\",\"type\":\"string\",\"nullable\":true},{\"name\":\"createTime\",\"type\":\"string\",\"nullable\":true},{\"name\":\"updateTime\",\"type\":\"string\",\"nullable\":true},{\"name\":\"count\",\"type\":\"int\",\"nullable\":true},{\"name\":\"trs_moye_input_time\",\"type\":\"timestamp\",\"nullable\":false},{\"name\":\"trs_moye_batch_no\",\"type\":\"string\",\"nullable\":false},{\"name\":\"trs_moye_task_id\",\"type\":\"bigint\",\"nullable\":false}]},\"trs_moye_batch_no\":\"T3204-fixed_time-20241122110800_6223\",\"trs_moye_task_id\":3204,\"plugin_name\":\"Kafka\",\"topic\":\"moye_storage_3204_6223\",\"bootstrap.servers\":\"**************:9092\",\"consumer.group\":\"moye_dwd_seatunnel_moye_storage_3204_6223\",\"format\":\"json\",\"format_error_handle_way\":\"fail\",\"start_mode\":\"EARLIEST\"}],\"transform\":[{\"plugin_input\":[\"source_table\"],\"plugin_output\":\"audit_output\",\"trs_moye_batch_no\":\"T3204-fixed_time-20241122110800_6223\",\"trs_moye_task_id\":3204,\"plugin_name\":\"Audit\"},{\"plugin_input\":[\"audit_output\"],\"plugin_output\":\"record_id_output\",\"plugin_name\":\"GenerateRecordId\"},{\"plugin_input\":[\"record_id_output\"],\"plugin_output\":\"access_record_output\",\"plugin_name\":\"ToAccessRecord\",\"storage_names\":[\"MySQL_data_service\",\"CK(34)\",\"TRS-CLICK_HOUSE-数据存储\"]}],\"sink\":[{\"plugin_input\":[\"audit_output\"],\"plugin_name\":\"Jdbc\",\"url\":\"******************************************************************************************************************************\",\"driver\":\"com.mysql.cj.jdbc.Driver\",\"user\":\"root\",\"password\":\"!QAZ2wsx1234\",\"batch_size\":20000,\"database\":\"mysql_51_test\",\"table\":\"dwd_kafka_test_data\",\"data_save_mode\":\"APPEND_DATA\",\"schema_save_mode\":\"ERROR_WHEN_SCHEMA_NOT_EXIST\",\"generate_sink_sql\":true,\"enable_upsert\":false,\"support_upsert_by_query_primary_key_exist\":false,\"primary_keys\":[\"id\"],\"storage_name\":\"MySQL_data_service\"},{\"plugin_input\":[\"access_record_output\"],\"plugin_name\":\"Kafka\",\"topic\":\"moye_access_data\",\"bootstrap.servers\":\"**************:9092\",\"format\":\"json\"}]},{\"env\":{\"job.retry.times\":0,\"job.mode\":\"BATCH\",\"job.name\":\"moye_storage_dwd_3204_dwd_kafka_test_data\"},\"source\":[{\"plugin_output\":\"source_table\",\"schema\":{\"columns\":[{\"name\":\"id\",\"type\":\"string\",\"nullable\":false},{\"name\":\"zhName\",\"type\":\"string\",\"nullable\":false},{\"name\":\"enName\",\"type\":\"string\",\"nullable\":true},{\"name\":\"createMode\",\"type\":\"string\",\"nullable\":true},{\"name\":\"executeStatus\",\"type\":\"string\",\"nullable\":true},{\"name\":\"businessCategory\",\"type\":\"string\",\"nullable\":true},{\"name\":\"layer\",\"type\":\"string\",\"nullable\":true},{\"name\":\"description\",\"type\":\"string\",\"nullable\":true},{\"name\":\"createTime\",\"type\":\"string\",\"nullable\":true},{\"name\":\"updateTime\",\"type\":\"string\",\"nullable\":true},{\"name\":\"count\",\"type\":\"int\",\"nullable\":true},{\"name\":\"trs_moye_input_time\",\"type\":\"timestamp\",\"nullable\":false},{\"name\":\"trs_moye_batch_no\",\"type\":\"string\",\"nullable\":false},{\"name\":\"trs_moye_task_id\",\"type\":\"bigint\",\"nullable\":false}]},\"trs_moye_batch_no\":\"T3204-fixed_time-20241122110800_12602\",\"trs_moye_task_id\":3204,\"plugin_name\":\"Kafka\",\"topic\":\"moye_storage_3204_12602\",\"bootstrap.servers\":\"**************:9092\",\"consumer.group\":\"moye_dwd_seatunnel_moye_storage_3204_12602\",\"format\":\"json\",\"format_error_handle_way\":\"fail\",\"start_mode\":\"EARLIEST\"}],\"transform\":[{\"plugin_input\":[\"source_table\"],\"plugin_output\":\"audit_output\",\"trs_moye_batch_no\":\"T3204-fixed_time-20241122110800_12602\",\"trs_moye_task_id\":3204,\"plugin_name\":\"Audit\"},{\"plugin_input\":[\"audit_output\"],\"plugin_output\":\"record_id_output\",\"plugin_name\":\"GenerateRecordId\"},{\"plugin_input\":[\"record_id_output\"],\"plugin_output\":\"access_record_output\",\"plugin_name\":\"ToAccessRecord\",\"storage_names\":[\"MySQL_data_service\",\"CK(34)\",\"TRS-CLICK_HOUSE-数据存储\"]}],\"sink\":[{\"plugin_input\":[\"audit_output\"],\"host\":\"**************:8123\",\"database\":\"moye_dev\",\"table\":\"dwd_kafka_test_data\",\"username\":\"moye\",\"password\":\"trsadmin@1234\",\"primary_key\":\"id\",\"plugin_name\":\"Clickhouse\",\"support_upsert\":false,\"storage_name\":\"CK(34)\"},{\"plugin_input\":[\"access_record_output\"],\"plugin_name\":\"Kafka\",\"topic\":\"moye_access_data\",\"bootstrap.servers\":\"**************:9092\",\"format\":\"json\"}]}]";
        assertEquals(expected, JsonUtils.toJsonString(jobConfigs));
    }

    @Test
    void testDwdJobConfigWithoutAudit() {
        when(producerProperties.getBootStrapServers()).thenReturn(List.of("**************:9092"));
        when(producerProperties.getHost()).thenReturn("**************");
        when(producerProperties.getPort()).thenReturn(9092);
        String dataModelJson =
            "{\"id\":3204,\"createMode\":\"NORMAL\",\"executeStatus\":\"START\",\"zhName\":\"流处理-kafka_test_data\",\"enName\":\"dwd_kafka_test_data\",\"businessCategoryId\":189,\"description\":\"\",\"dataSourceConfig\":{\"id\":3914,\"enName\":\"kafka_test_data\",\"zhName\":\"kafka_test_data\",\"connectionId\":43,\"connection\":{\"id\":43,\"name\":\"蒙利幸51kafka\",\"connectionType\":\"KAFKA\",\"connectionParams\":{\"host\":\"**************\",\"port\":9092,\"protocol\":\"http\",\"username\":\"\",\"password\":\"qwer1234\",\"connectionType\":\"KAFKA\",\"saslMechanism\":\"PLAIN\"},\"isSource\":true,\"testSuccess\":true,\"dbconnection\":false,\"httpConnection\":false,\"fileConnection\":false},\"jobMode\":\"BATCH\"},\"dataStorage\":[{\"id\":6223,\"enName\":\"dwd_kafka_test_data\",\"zhName\":\"流处理-kafka_test_data\",\"connectionId\":1279,\"connection\":{\"id\":1279,\"name\":\"MySQL_data_service\",\"connectionType\":\"MYSQL\",\"connectionParams\":{\"host\":\"**************\",\"port\":33066,\"protocol\":\"http\",\"username\":\"root\",\"password\":\"!QAZ2wsx1234\",\"connectionType\":\"MYSQL\",\"database\":\"mysql_51_test\"},\"isSource\":false,\"testSuccess\":true,\"dbconnection\":true,\"httpConnection\":false,\"fileConnection\":false}},{\"id\":12602,\"enName\":\"dwd_kafka_test_data\",\"zhName\":\"流处理-kafka_test_data\",\"connectionId\":61,\"connection\":{\"id\":61,\"name\":\"CK(34)\",\"connectionType\":\"CLICK_HOUSE\",\"connectionParams\":{\"host\":\"**************\",\"port\":8123,\"protocol\":\"http\",\"username\":\"moye\",\"password\":\"trsadmin@1234\",\"connectionType\":\"CLICK_HOUSE\",\"database\":\"moye_dev\"},\"isSource\":false,\"testSuccess\":true,\"dbconnection\":true,\"httpConnection\":false,\"fileConnection\":false}},{\"id\":12776,\"enName\":\"dwd_kafka_test_data\",\"zhName\":\"流处理-kafka_test_data\",\"connectionId\":558,\"connection\":{\"id\":558,\"name\":\"TRS-CLICK_HOUSE-数据存储\",\"connectionType\":\"CLICK_HOUSE\",\"connectionParams\":{\"host\":\"**************\",\"port\":38123,\"protocol\":\"http\",\"username\":\"admin\",\"password\":\"trsadmin@1234\",\"connectionType\":\"CLICK_HOUSE\",\"database\":\"auto_data_storage_ck\"},\"isSource\":false,\"testSuccess\":true,\"dbconnection\":true,\"httpConnection\":false,\"fileConnection\":false}}],\"fields\":[{\"id\":44721,\"dataModelId\":3204,\"zhName\":\"id\",\"enName\":\"id\",\"type\":\"STRING\",\"typeName\":\"字符串\",\"advanceConfig\":{},\"primary\":false,\"increment\":false,\"partition\":false,\"nullable\":false,\"multiValue\":false,\"statistic\":false,\"builtIn\":false,\"fieldsSchema\":{},\"seatunnelType\":\"string\"},{\"id\":44722,\"dataModelId\":3204,\"zhName\":\"zhName\",\"enName\":\"zhName\",\"type\":\"STRING\",\"typeName\":\"字符串\",\"advanceConfig\":{},\"primary\":false,\"increment\":false,\"partition\":false,\"nullable\":false,\"multiValue\":false,\"statistic\":false,\"builtIn\":false,\"fieldsSchema\":{},\"seatunnelType\":\"string\"},{\"id\":44723,\"dataModelId\":3204,\"zhName\":\"enName\",\"enName\":\"enName\",\"type\":\"STRING\",\"typeName\":\"字符串\",\"advanceConfig\":{},\"primary\":false,\"increment\":false,\"partition\":false,\"nullable\":true,\"multiValue\":false,\"statistic\":false,\"builtIn\":false,\"fieldsSchema\":{},\"seatunnelType\":\"string\"},{\"id\":44724,\"dataModelId\":3204,\"zhName\":\"createMode\",\"enName\":\"createMode\",\"type\":\"STRING\",\"typeName\":\"字符串\",\"advanceConfig\":{},\"primary\":false,\"increment\":false,\"partition\":false,\"nullable\":true,\"multiValue\":false,\"statistic\":false,\"builtIn\":false,\"fieldsSchema\":{},\"seatunnelType\":\"string\"},{\"id\":44725,\"dataModelId\":3204,\"zhName\":\"executeStatus\",\"enName\":\"executeStatus\",\"type\":\"STRING\",\"typeName\":\"字符串\",\"advanceConfig\":{},\"primary\":false,\"increment\":false,\"partition\":false,\"nullable\":true,\"multiValue\":false,\"statistic\":false,\"builtIn\":false,\"fieldsSchema\":{},\"seatunnelType\":\"string\"},{\"id\":44726,\"dataModelId\":3204,\"zhName\":\"businessCategory\",\"enName\":\"businessCategory\",\"type\":\"STRING\",\"typeName\":\"字符串\",\"advanceConfig\":{},\"primary\":false,\"increment\":false,\"partition\":false,\"nullable\":true,\"multiValue\":false,\"statistic\":false,\"builtIn\":false,\"fieldsSchema\":{},\"seatunnelType\":\"string\"},{\"id\":44727,\"dataModelId\":3204,\"zhName\":\"layer\",\"enName\":\"layer\",\"type\":\"STRING\",\"typeName\":\"字符串\",\"advanceConfig\":{},\"primary\":false,\"increment\":false,\"partition\":false,\"nullable\":true,\"multiValue\":false,\"statistic\":false,\"builtIn\":false,\"fieldsSchema\":{},\"seatunnelType\":\"string\"},{\"id\":44728,\"dataModelId\":3204,\"zhName\":\"description\",\"enName\":\"description\",\"type\":\"STRING\",\"typeName\":\"字符串\",\"advanceConfig\":{},\"primary\":false,\"increment\":false,\"partition\":false,\"nullable\":true,\"multiValue\":false,\"statistic\":false,\"builtIn\":false,\"fieldsSchema\":{},\"seatunnelType\":\"string\"},{\"id\":44729,\"dataModelId\":3204,\"zhName\":\"createTime\",\"enName\":\"createTime\",\"type\":\"STRING\",\"typeName\":\"字符串\",\"advanceConfig\":{},\"primary\":false,\"increment\":false,\"partition\":false,\"nullable\":true,\"multiValue\":false,\"statistic\":false,\"builtIn\":false,\"fieldsSchema\":{},\"seatunnelType\":\"string\"},{\"id\":44730,\"dataModelId\":3204,\"zhName\":\"updateTime\",\"enName\":\"updateTime\",\"type\":\"STRING\",\"typeName\":\"字符串\",\"advanceConfig\":{},\"primary\":false,\"increment\":false,\"partition\":false,\"nullable\":true,\"multiValue\":false,\"statistic\":false,\"builtIn\":false,\"fieldsSchema\":{},\"seatunnelType\":\"string\"},{\"id\":44731,\"dataModelId\":3204,\"zhName\":\"字数统计\",\"enName\":\"count\",\"type\":\"INT\",\"typeName\":\"整型\",\"advanceConfig\":{\"enumValues\":[],\"type\":\"INT\"},\"primary\":false,\"increment\":false,\"partition\":false,\"nullable\":true,\"multiValue\":false,\"statistic\":false,\"builtIn\":false,\"fieldsSchema\":{},\"seatunnelType\":\"int\"}],\"executeConfig\":{\"id\":2553,\"dataModelId\":3204,\"incrementInfo\":{\"numberType\":false,\"dateType\":false},\"increment\":true},\"scheduleConfig\":{\"id\":3182,\"dataModelId\":3204,\"executeMode\":\"REALTIME\"}}";
        String storageJson =
            "[{\"id\":6223,\"enName\":\"dwd_kafka_test_data\",\"zhName\":\"流处理-kafka_test_data\",\"connectionId\":1279,\"connection\":{\"id\":1279,\"name\":\"MySQL_data_service\",\"connectionType\":\"MYSQL\",\"connectionParams\":{\"host\":\"**************\",\"port\":33066,\"protocol\":\"http\",\"username\":\"root\",\"password\":\"!QAZ2wsx1234\",\"connectionType\":\"MYSQL\",\"database\":\"mysql_51_test\"},\"isSource\":false,\"testSuccess\":true,\"fileConnection\":false,\"dbconnection\":true,\"httpConnection\":false},\"dataModelId\":3204,\"createTableStatus\":\"SUCCESS\",\"fieldIds\":[44721,44722,44723,44724,44725,44726,44727,44728,44729,44730,44731,46085,46086,46087],\"settings\":{\"connectionType\":\"MYSQL\"}},{\"id\":12602,\"enName\":\"dwd_kafka_test_data\",\"zhName\":\"流处理-kafka_test_data\",\"connectionId\":61,\"connection\":{\"id\":61,\"name\":\"CK(34)\",\"connectionType\":\"CLICK_HOUSE\",\"connectionParams\":{\"host\":\"**************\",\"port\":8123,\"protocol\":\"http\",\"username\":\"moye\",\"password\":\"trsadmin@1234\",\"connectionType\":\"CLICK_HOUSE\",\"database\":\"moye_dev\"},\"isSource\":false,\"testSuccess\":true,\"fileConnection\":false,\"dbconnection\":true,\"httpConnection\":false},\"dataModelId\":3204,\"createTableStatus\":\"SUCCESS\",\"fieldIds\":[44721,44722,44723,44724,44725,44726,44727,44728,44729,44730,44731,46085,46086,46087],\"settings\":{\"connectionType\":\"CLICK_HOUSE\",\"orderField\":\"id\",\"engine\":\"MergeTree\"}}]";
        String batchNo = "T3204-fixed_time-20241122110800";
        DataModel dataModel = JsonUtils.parseObject(dataModelJson, DataModel.class);
        List<DataStorage> dataStorages = JsonUtils.toList(storageJson, DataStorage.class);
        List<Pair<Integer, SeaTunnelJobConfig>> map = seatunnelJobService.createDwdJobConfigs(dataModel, dataStorages,
            batchNo);
        List<SeaTunnelJobConfig> jobConfigs = map.stream()
            .map(Pair::getValue)
            .collect(Collectors.toCollection(ArrayList::new));
        String expected =
            "[{\"env\":{\"job.retry.times\":0,\"job.mode\":\"BATCH\",\"job.name\":\"moye_storage_dwd_3204_dwd_kafka_test_data\"},\"source\":[{\"plugin_output\":\"source_table\",\"schema\":{\"columns\":[{\"name\":\"id\",\"type\":\"string\",\"nullable\":false},{\"name\":\"zhName\",\"type\":\"string\",\"nullable\":false},{\"name\":\"enName\",\"type\":\"string\",\"nullable\":true},{\"name\":\"createMode\",\"type\":\"string\",\"nullable\":true},{\"name\":\"executeStatus\",\"type\":\"string\",\"nullable\":true},{\"name\":\"businessCategory\",\"type\":\"string\",\"nullable\":true},{\"name\":\"layer\",\"type\":\"string\",\"nullable\":true},{\"name\":\"description\",\"type\":\"string\",\"nullable\":true},{\"name\":\"createTime\",\"type\":\"string\",\"nullable\":true},{\"name\":\"updateTime\",\"type\":\"string\",\"nullable\":true},{\"name\":\"count\",\"type\":\"int\",\"nullable\":true}]},\"trs_moye_batch_no\":\"T3204-fixed_time-20241122110800_6223\",\"trs_moye_task_id\":3204,\"plugin_name\":\"Kafka\",\"topic\":\"moye_storage_3204_6223\",\"bootstrap.servers\":\"**************:9092\",\"consumer.group\":\"moye_dwd_seatunnel_moye_storage_3204_6223\",\"format\":\"json\",\"format_error_handle_way\":\"fail\",\"start_mode\":\"EARLIEST\"}],\"transform\":[{\"plugin_input\":[\"source_table\"],\"plugin_output\":\"audit_output\",\"trs_moye_batch_no\":\"T3204-fixed_time-20241122110800_6223\",\"trs_moye_task_id\":3204,\"plugin_name\":\"Audit\"},{\"plugin_input\":[\"audit_output\"],\"plugin_output\":\"record_id_output\",\"plugin_name\":\"GenerateRecordId\"},{\"plugin_input\":[\"record_id_output\"],\"plugin_output\":\"access_record_output\",\"plugin_name\":\"ToAccessRecord\",\"storage_names\":[\"MySQL_data_service\",\"CK(34)\",\"TRS-CLICK_HOUSE-数据存储\"]}],\"sink\":[{\"plugin_input\":[\"source_table\"],\"plugin_name\":\"Jdbc\",\"url\":\"******************************************************************************************************************************\",\"driver\":\"com.mysql.cj.jdbc.Driver\",\"user\":\"root\",\"password\":\"!QAZ2wsx1234\",\"batch_size\":20000,\"database\":\"mysql_51_test\",\"table\":\"dwd_kafka_test_data\",\"data_save_mode\":\"APPEND_DATA\",\"schema_save_mode\":\"ERROR_WHEN_SCHEMA_NOT_EXIST\",\"generate_sink_sql\":true,\"enable_upsert\":false,\"support_upsert_by_query_primary_key_exist\":false,\"storage_name\":\"MySQL_data_service\"},{\"plugin_input\":[\"access_record_output\"],\"plugin_name\":\"Kafka\",\"topic\":\"moye_access_data\",\"bootstrap.servers\":\"**************:9092\",\"format\":\"json\"}]},{\"env\":{\"job.retry.times\":0,\"job.mode\":\"BATCH\",\"job.name\":\"moye_storage_dwd_3204_dwd_kafka_test_data\"},\"source\":[{\"plugin_output\":\"source_table\",\"schema\":{\"columns\":[{\"name\":\"id\",\"type\":\"string\",\"nullable\":false},{\"name\":\"zhName\",\"type\":\"string\",\"nullable\":false},{\"name\":\"enName\",\"type\":\"string\",\"nullable\":true},{\"name\":\"createMode\",\"type\":\"string\",\"nullable\":true},{\"name\":\"executeStatus\",\"type\":\"string\",\"nullable\":true},{\"name\":\"businessCategory\",\"type\":\"string\",\"nullable\":true},{\"name\":\"layer\",\"type\":\"string\",\"nullable\":true},{\"name\":\"description\",\"type\":\"string\",\"nullable\":true},{\"name\":\"createTime\",\"type\":\"string\",\"nullable\":true},{\"name\":\"updateTime\",\"type\":\"string\",\"nullable\":true},{\"name\":\"count\",\"type\":\"int\",\"nullable\":true}]},\"trs_moye_batch_no\":\"T3204-fixed_time-20241122110800_12602\",\"trs_moye_task_id\":3204,\"plugin_name\":\"Kafka\",\"topic\":\"moye_storage_3204_12602\",\"bootstrap.servers\":\"**************:9092\",\"consumer.group\":\"moye_dwd_seatunnel_moye_storage_3204_12602\",\"format\":\"json\",\"format_error_handle_way\":\"fail\",\"start_mode\":\"EARLIEST\"}],\"transform\":[{\"plugin_input\":[\"source_table\"],\"plugin_output\":\"audit_output\",\"trs_moye_batch_no\":\"T3204-fixed_time-20241122110800_12602\",\"trs_moye_task_id\":3204,\"plugin_name\":\"Audit\"},{\"plugin_input\":[\"audit_output\"],\"plugin_output\":\"record_id_output\",\"plugin_name\":\"GenerateRecordId\"},{\"plugin_input\":[\"record_id_output\"],\"plugin_output\":\"access_record_output\",\"plugin_name\":\"ToAccessRecord\",\"storage_names\":[\"MySQL_data_service\",\"CK(34)\",\"TRS-CLICK_HOUSE-数据存储\"]}],\"sink\":[{\"plugin_input\":[\"source_table\"],\"host\":\"**************:8123\",\"database\":\"moye_dev\",\"table\":\"dwd_kafka_test_data\",\"username\":\"moye\",\"password\":\"trsadmin@1234\",\"plugin_name\":\"Clickhouse\",\"support_upsert\":false,\"storage_name\":\"CK(34)\"},{\"plugin_input\":[\"access_record_output\"],\"plugin_name\":\"Kafka\",\"topic\":\"moye_access_data\",\"bootstrap.servers\":\"**************:9092\",\"format\":\"json\"}]}]";
        assertEquals(expected, JsonUtils.toJsonString(jobConfigs));
    }
}