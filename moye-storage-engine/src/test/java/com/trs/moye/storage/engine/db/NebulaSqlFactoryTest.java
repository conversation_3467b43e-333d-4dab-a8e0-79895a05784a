package com.trs.moye.storage.engine.db;

import static org.assertj.core.api.Assertions.assertThat;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.trs.moye.base.data.model.entity.DataModelField;
import com.trs.moye.base.data.model.entity.DataModelFieldAttributes;
import com.trs.moye.base.data.standard.entity.TagEdgeField;
import com.trs.moye.base.common.enums.FieldType;
import com.trs.moye.storage.engine.db.nebula.NebulaConnection;
import com.trs.moye.storage.engine.db.nebula.NebulaSqlFactory;
import java.util.List;
import org.junit.jupiter.api.Test;

/**
 * Nebula 测试类
 *
 * <AUTHOR>
 * @since 2024/10/9
 */
class NebulaSqlFactoryTest {


    public static final DataModelFieldAttributes FIELDS = new DataModelFieldAttributes("id", List.of(
        new TagEdgeField("姓名", "name", "string", true, null, "description"),
        new TagEdgeField("年龄", "age", "int", false, null, "description"),
        new TagEdgeField("主键", "id", "int", true, null, "description")
    ));

    @Test
    void testCreateSql() {
        DataModelField.DataModelFieldBuilder<?, ?> builder = DataModelField.builder();
        builder.enName("tag_create_test")
            .zhName("标签")
            .type(FieldType.GRAPHICS_TAG)
            .fields(
                FIELDS);
        DataModelField dataModelField = builder.build();
        String sql = NebulaSqlFactory.buildCreateSql(dataModelField, true);
        assertThat(sql).isEqualTo(
            "CREATE TAG IF NOT EXISTS `tag_create_test` (`name` string NOT NULL, `age` int NULL, `id` int NOT NULL)");
    }

    @Test
    void testCreateSqlEdge() {
        DataModelField.DataModelFieldBuilder<?, ?> builder = DataModelField.builder();
        builder.enName("edge_create_test")
            .zhName("边")
            .type(FieldType.GRAPHICS_EDGE)
            .fields(
                FIELDS);
        DataModelField dataModelField = builder.build();
        String sql = NebulaSqlFactory.buildCreateSql(dataModelField, true);
        assertThat(sql).isEqualTo(
            "CREATE EDGE IF NOT EXISTS `edge_create_test` (`name` string NOT NULL, `age` int NULL, `id` int NOT NULL)");
    }

    @Test
    void testCreateIndexSql() {
        DataModelField.DataModelFieldBuilder<?, ?> builder = DataModelField.builder();
        builder.enName("tag_create_test")
            .zhName("标签")
            .type(FieldType.GRAPHICS_TAG)
            .fields(
                FIELDS);
        DataModelField dataModelField = builder.build();
        String sql = NebulaSqlFactory.buildIndexSql(dataModelField);
        assertThat(sql).isEqualTo(
            "CREATE TAG INDEX IF NOT EXISTS `tag_create_test_index` ON `tag_create_test`() COMMENT \"默认索引\"");
    }

    @Test
    void testCreateIndexSqlEdge() {
        DataModelField.DataModelFieldBuilder<?, ?> builder = DataModelField.builder();
        builder.enName("edge_create_test")
            .zhName("边")
            .type(FieldType.GRAPHICS_EDGE)
            .fields(
                FIELDS);
        DataModelField dataModelField = builder.build();
        String sql = NebulaSqlFactory.buildIndexSql(dataModelField);
        assertThat(sql).isEqualTo(
            "CREATE EDGE INDEX IF NOT EXISTS `edge_create_test_index` ON `edge_create_test`() COMMENT \"默认索引\"");
    }

    @Test
    void testJsonExtract() throws JsonProcessingException {
        String str = "{\"errors\":[{\"code\":0}],\"results\":[{\"spaceName\":\"moye\",\"data\":[{\"meta\":[{\"type\":\"vertex\",\"id\":\"111\"},{\"type\":\"edge\",\"id\":{\"ranking\":0,\"type\":21,\"dst\":\"222\",\"src\":\"111\",\"name\":\"test_create_edge\"}},{\"type\":\"vertex\",\"id\":\"222\"}],\"row\":[{\"test_create_tag.id\":1,\"test_create_tag.name\":\"tch\",\"test_create_tag.age\":27},{\"age\":18,\"name\":\"同事1\",\"id\":1},{\"test_create_tag.age\":26,\"test_create_tag.id\":2,\"test_create_tag.name\":\"zhb\"}]},{\"meta\":[{\"type\":\"vertex\",\"id\":\"222\"},{\"type\":\"edge\",\"id\":{\"ranking\":0,\"type\":21,\"dst\":\"333\",\"src\":\"222\",\"name\":\"test_create_edge\"}},{\"type\":\"vertex\",\"id\":\"333\"}],\"row\":[{\"test_create_tag.id\":2,\"test_create_tag.name\":\"zhb\",\"test_create_tag.age\":26},{\"age\":10,\"name\":\"同事2\",\"id\":2},{\"test_create_tag.age\":28,\"test_create_tag.id\":3,\"test_create_tag.name\":\"ly\"}]},{\"meta\":[{\"type\":\"vertex\",\"id\":\"333\"},{\"type\":\"edge\",\"id\":{\"ranking\":0,\"type\":21,\"dst\":\"111\",\"src\":\"333\",\"name\":\"test_create_edge\"}},{\"type\":\"vertex\",\"id\":\"111\"}],\"row\":[{\"test_create_tag.id\":3,\"test_create_tag.name\":\"ly\",\"test_create_tag.age\":28},{\"age\":100,\"name\":\"同事3\",\"id\":3},{\"test_create_tag.age\":27,\"test_create_tag.id\":1,\"test_create_tag.name\":\"tch\"}]}],\"columns\":[\"v\",\"e\",\"v2\"],\"errors\":{\"code\":0},\"latencyInUs\":5160}]}";
        ObjectMapper objectMapper = new ObjectMapper(); // 创建 ObjectMapper 实例
        JsonNode jsonNode = objectMapper.readTree(str); // 转换为 JsonNode
        ObjectNode objectNode = NebulaConnection.getFinalResult(jsonNode, objectMapper.createObjectNode());
        String str2 = "{\"tag\":[{\"id\":\"1\",\"moyeTagType\":\"test_create_tag\",\"vid\":\"111\",\"name\":\"tch\",\"age\":\"27\"},{\"age\":\"26\",\"moyeTagType\":\"test_create_tag\",\"vid\":\"222\",\"id\":\"2\",\"name\":\"zhb\"},{\"age\":\"28\",\"moyeTagType\":\"test_create_tag\",\"vid\":\"333\",\"id\":\"3\",\"name\":\"ly\"}],\"edge\":[{\"moyeEdgeType\":\"test_create_edge\",\"from\":\"111\",\"to\":\"222\",\"age\":\"18\",\"name\":\"同事1\",\"id\":\"1\"},{\"moyeEdgeType\":\"test_create_edge\",\"from\":\"222\",\"to\":\"333\",\"age\":\"10\",\"name\":\"同事2\",\"id\":\"2\"},{\"moyeEdgeType\":\"test_create_edge\",\"from\":\"333\",\"to\":\"111\",\"age\":\"100\",\"name\":\"同事3\",\"id\":\"3\"}]}";
        String string = objectNode.toString();
        assertThat(string).isEqualTo(str2);
    }

    @Test
    void testInsertSql() throws JsonProcessingException {
        String json = """
            {"tag":[{"id":4,"moyeTagType":"test_create_tag","vid":"vid-tch","name":"谭朝宏","age":27},{"age":25,"moyeTagType":"test_create_tag","vid":"vid-zhb","id":2,"name":"张晗冰"},{"age":2,"moyeTagType":"test_create_tag","vid":"vid-ly","id":3,"name":"来一"}],"edge":[{"moyeEdgeType":"test_create_edge","from":"vid-tch","to":"vid-zhb","edge_name":"谭朝宏和张晗冰是伙伴","relation":"谭朝宏和张晗冰是好伙伴"},{"moyeEdgeType":"test_create_edge","from":"vid-zhb","to":"vid-ly","edge_name":"张晗冰和来一是伙伴","relation":"张晗冰和来一是好伙伴"},{"moyeEdgeType":"test_create_edge","from":"vid-ly","to":"vid-tch","edge_name":"来一和谭朝宏是伙伴","relation":"来一和谭朝宏是好伙伴"}]}
            """;
        ObjectMapper objectMapper = new ObjectMapper(); // 创建 ObjectMapper 实例
        JsonNode jsonNode = objectMapper.readTree(json); // 转换为 JsonNode
        JsonNode tags = jsonNode.path("tag");
        JsonNode edges = jsonNode.path("edge");
        StringBuilder finalSql = new StringBuilder();
        for (JsonNode tag : tags) {
            finalSql.append(NebulaSqlFactory.buildInsertTagSql(tag)).append("\n");
        }
        for (JsonNode edge : edges) {
            finalSql.append(NebulaSqlFactory.buildInsertEdgeSql(edge)).append("\n");
        }
        assertThat(finalSql).hasToString("""
            INSERT VERTEX test_create_tag (id, name, age) VALUES "vid-tch": (4, "谭朝宏", 27);
            INSERT VERTEX test_create_tag (age, id, name) VALUES "vid-zhb": (25, 2, "张晗冰");
            INSERT VERTEX test_create_tag (age, id, name) VALUES "vid-ly": (2, 3, "来一");
            INSERT EDGE test_create_edge (edge_name, relation) VALUES "vid-tch"->"vid-zhb": ("谭朝宏和张晗冰是伙伴", "谭朝宏和张晗冰是好伙伴");
            INSERT EDGE test_create_edge (edge_name, relation) VALUES "vid-zhb"->"vid-ly": ("张晗冰和来一是伙伴", "张晗冰和来一是好伙伴");
            INSERT EDGE test_create_edge (edge_name, relation) VALUES "vid-ly"->"vid-tch": ("来一和谭朝宏是伙伴", "来一和谭朝宏是好伙伴");
            """);
    }
}
