package com.trs.moye.storage.engine.db;

import static com.trs.moye.storage.engine.utils.SqlUtils.buildSqlWhere;
import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertEquals;

import com.jayway.jsonpath.JsonPath;
import com.trs.moye.base.data.connection.entity.DataConnection;
import com.trs.moye.base.data.connection.enums.ConnectionType;
import com.trs.moye.base.common.utils.JsonUtils;
import com.trs.moye.storage.engine.db.mysql.MysqlConnection;
import com.trs.moye.base.data.connection.entity.params.MysqlConnectionParams;
import com.trs.moye.storage.engine.pojo.dto.PageParams;
import com.trs.moye.storage.engine.pojo.request.search.Condition;
import com.trs.moye.storage.engine.pojo.request.search.ConditionSearchParams;
import com.trs.moye.storage.engine.pojo.request.search.DataServiceConditionType;
import com.trs.moye.storage.engine.pojo.request.search.DataServiceField;
import com.trs.moye.storage.engine.pojo.request.search.SortField;
import com.trs.moye.storage.engine.pojo.request.search.ValueObject;
import com.trs.moye.storage.engine.pojo.response.PageResult;
import com.trs.moye.storage.engine.utils.SqlUtils;
import java.io.IOException;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.condition.EnabledIf;

@Slf4j
class AbstractJdbcDataConnectionTest {

    private static final String HOST = "**************";
    private static final Integer PORT = 33066;
    private static final String DATABASE = "test";
    private static final String USER = "root";
    private static final String PASSWORD = "!QAZ2wsx1234";
    public static final String CONDITIONS = "$.conditions[*]";

    private static boolean isDatabaseAccessible() {
        String url = "jdbc:mysql://" + HOST + ":" + PORT + "/" + DATABASE;
        try (Connection connection = DriverManager.getConnection(url, USER, PASSWORD)) {
            return true;
        } catch (SQLException e) {
            log.warn("连接示例数据库[{}][{}][{}]失败，跳过部分测试", url, USER, PASSWORD);
            return false;
        }
    }


    @Test
    void testConditions() {
        String jsonString = "{\"conditions\":[{\"type\":\"EXPRESSION\",\"key\":{\"id\":\"129048\",\"zhName\":\"数据中台入库时间\",\"enName\":\"trs_moye_input_time\",\"typeName\":\"日期时间\",\"type\":\"DATETIME\",\"advanceConfig\":{\"type\":\"DATETIME\",\"enumValues\":[],\"minValue\":\"1700-01-01 00:00:00\",\"maxValue\":\"2100-01-01 00:00:00\"}},\"values\":[{\"value\":\"2024-09-12 10:42:00\"}],\"operator\":\">\"},{\"type\":\"LOGIC\",\"values\":[],\"operator\":\"or\"},{\"type\":\"EXPRESSION\",\"key\":{\"id\":\"129050\",\"zhName\":\"数据中台任务主键\",\"enName\":\"trs_moye_task_id\",\"typeName\":\"整型\",\"type\":\"INT\",\"advanceConfig\":{\"type\":\"INT\",\"enumValues\":[]}},\"values\":[{\"value\":\"1\"}],\"operator\":\">\"},{\"type\":\"LOGIC\",\"values\":[],\"operator\":\"and\"},{\"type\":\"EXPRESSION\",\"key\":{\"id\":\"129038\",\"zhName\":\"birthday\",\"enName\":\"birthday\",\"typeName\":\"字符串\",\"type\":\"STRING\"},\"values\":[{\"value\":\"adsf\"}],\"operator\":\"in\"}]}";
        Object array = JsonPath.read(jsonString, CONDITIONS);
        List<Condition> conditions = JsonUtils.toList(array.toString(), Condition.class);
        String sqlWhere = buildSqlWhere(conditions, ConnectionType.MYSQL,false);
        assertThat(sqlWhere).isEqualTo(
            "trs_moye_input_time >'2024-09-12 10:42:00' OR trs_moye_task_id >'1' AND birthday in ('adsf')");

        String jsonString2 = "{\"conditions\":[{\"type\":\"EXPRESSION\",\"key\":{\"id\":\"129050\",\"zhName\":\"数据中台任务主键\",\"enName\":\"trs_moye_task_id\",\"typeName\":\"整型\",\"type\":\"INT\",\"advanceConfig\":{\"type\":\"INT\",\"enumValues\":[]}},\"values\":[{\"value\":\"12\"}],\"operator\":\"=\"},{\"type\":\"LOGIC\",\"values\":[],\"operator\":\"and\"},{\"type\":\"EXPRESSION\",\"key\":{\"id\":\"129048\",\"zhName\":\"数据中台入库时间\",\"enName\":\"trs_moye_input_time\",\"typeName\":\"日期时间\",\"type\":\"DATETIME\",\"advanceConfig\":{\"type\":\"DATETIME\",\"enumValues\":[],\"minValue\":\"1700-01-01 00:00:00\",\"maxValue\":\"2100-01-01 00:00:00\"}},\"values\":[],\"operator\":\"isNotNull\"},{\"type\":\"LOGIC\",\"values\":[],\"operator\":\"or\"},{\"type\":\"EXPRESSION\",\"key\":{\"id\":\"129038\",\"zhName\":\"deposit\",\"enName\":\"deposit\",\"typeName\":\"字符串\",\"type\":\"STRING\"},\"values\":[{\"value\":\"123123123123\"},{\"value\":\"adsf\"}],\"operator\":\"contain\"},{\"type\":\"LOGIC\",\"values\":[],\"operator\":\"and\"},{\"type\":\"EXPRESSION\",\"key\":{\"id\":\"129040\",\"zhName\":\"gender\",\"enName\":\"gender\",\"typeName\":\"字符串\",\"type\":\"STRING\"},\"values\":[{\"value\":\"123\"},{\"value\":\"asdf\"}],\"operator\":\"notContain\"}]}";
        Object array2 = JsonPath.read(jsonString2, CONDITIONS);
        List<Condition> conditions2 = JsonUtils.toList(array2.toString(), Condition.class);
        String sqlWhere2 = buildSqlWhere(conditions2, ConnectionType.MYSQL,false);
        assertThat(sqlWhere2).isEqualTo(
            "trs_moye_task_id ='12' AND (trs_moye_input_time IS NOT NULL) OR ( deposit LIKE '%123123123123%' OR deposit LIKE '%adsf%' ) AND ( gender NOT LIKE '%123%' AND gender NOT LIKE '%asdf%' OR (gender IS NULL OR gender = '') )");

        String jsonString3 = "{\"pageable\":{\"pageIndex\":1,\"pageSize\":10},\"conditions\":[{\"type\":\"EXPRESSION\",\"key\":{\"id\":\"129038\",\"zhName\":\"llmops_bjr_xm\",\"enName\":\"llmops_bjr_xm\",\"typeName\":\"字符串\",\"type\":\"STRING\"},\"values\":[{\"value\":\"张丽\"}],\"operator\":\"contain\"},{\"type\":\"LOGIC\",\"values\":[],\"operator\":\"and\"},{\"type\":\"EXPRESSION\",\"key\":{\"id\":\"129038\",\"zhName\":\"llmops_jqfl\",\"enName\":\"llmops_jqfl\",\"typeName\":\"字符串\",\"type\":\"STRING\"},\"values\":[{\"value\":\"毒品交易\"}],\"operator\":\"contain\"}],\"aggs\":[]}";
        Object array3 = JsonPath.read(jsonString3, CONDITIONS);
        List<Condition> conditions3 = JsonUtils.toList(array3.toString(), Condition.class);
        String sqlWhere3 = buildSqlWhere(conditions3, ConnectionType.MYSQL,false);
        assertThat(sqlWhere3).isEqualTo(
            "( llmops_bjr_xm LIKE '%张丽%' ) AND ( llmops_jqfl LIKE '%毒品交易%' )");
    }

    @Test
    void testBuildSortStatement() {
        List<SortField> sortFields = List.of(new SortField("name", SortField.Order.ASC),
            new SortField("age", SortField.Order.DESC));
        String sortStatement = SqlUtils.buildSortStatement(sortFields, ConnectionType.MYSQL);
        String expected = "ORDER BY name ASC, age DESC";
        assertEquals(sortStatement, expected);
    }


    @Test
    @EnabledIf("isDatabaseAccessible")
    void executeQueryByParamsAndSql() throws SQLException, IOException {
        // 准备测试表
        prepareTables();

        MysqlConnectionParams params = new MysqlConnectionParams();
        params.setConnectionType(ConnectionType.MYSQL);
        params.setHost(HOST);
        params.setPort(PORT);
        params.setDatabase(DATABASE);
        params.setUsername(USER);
        params.setPassword(PASSWORD);

        final String PERSON_NAME = "person_name";
        final String COMPANY_NAME = "company_name";
        try (MysqlConnection mysqlConnection = new MysqlConnection(params)) {
            String queryStatement =
                "SELECT " + "p.id AS person_id, " + "p.name AS person_name, " + "c.id AS company_id, "
                    + "c.name AS company_name " + "FROM storage_engine_test_person p "
                    + "LEFT JOIN storage_engine_test_company c ON p.company_id = c.id";

            DataServiceField field = new DataServiceField();
            field.setEnName(COMPANY_NAME);
            List<ValueObject> values = new ArrayList<>();
            values.add(new ValueObject("Company A"));
            values.add(new ValueObject("Company C"));
            Condition condition = new Condition(DataServiceConditionType.EXPRESSION, field, values, "in");

            PageParams pageParams = new PageParams();
            pageParams.setPageNum(1);
            pageParams.setPageSize(10);

            ConditionSearchParams searchParams = new ConditionSearchParams();
            searchParams.setReturnFields(List.of(PERSON_NAME, COMPANY_NAME));
            searchParams.setConditions(List.of(condition));
            searchParams.setPageParams(pageParams);
            PageResult<Map<String, Object>> result = mysqlConnection.executeQueryByParamsAndSql(queryStatement,
                searchParams, ConnectionType.MYSQL);

            assertThat(result.getTotal()).isEqualTo(3);
            assertThat(result.getItems()).contains(Map.of(PERSON_NAME, "Alice", COMPANY_NAME, "Company A"));
            assertThat(result.getItems()).doesNotContain(Map.of(PERSON_NAME, "Charlie", COMPANY_NAME, "Company B"));
        }
    }

    void prepareTables() throws SQLException {
        String url = "jdbc:mysql://" + HOST + ":" + PORT + "/" + DATABASE;
        try (Connection connection = DriverManager.getConnection(url, USER,
            PASSWORD); Statement statement = connection.createStatement()) {

            String dropCompanyTable = "DROP TABLE IF EXISTS `storage_engine_test_company`";
            String createCompanyTable =
                "CREATE TABLE IF NOT EXISTS `storage_engine_test_company` (\n" + "`id` int DEFAULT NULL,\n"
                    + "`name` varchar(255)\n" + ") ENGINE=InnoDB DEFAULT CHARSET=utf8";
            String insertCompanies =
                "INSERT INTO `storage_engine_test_company` (`id`, `name`) VALUES " + "(1, 'Company A'), "
                    + "(2, 'Company B'), " + "(3, 'Company C')";
            statement.execute(dropCompanyTable);
            statement.execute(createCompanyTable);
            statement.execute(insertCompanies);

            String dropPersonTable = "DROP TABLE IF EXISTS `storage_engine_test_person`";
            String createPersonTable =
                "CREATE TABLE IF NOT EXISTS `storage_engine_test_person` (\n" + "`id` int DEFAULT NULL,\n"
                    + "`name` varchar(255),\n" + "`company_id` int\n" + ") ENGINE=InnoDB DEFAULT CHARSET=utf8";
            String insertPersons =
                "INSERT INTO `storage_engine_test_person` (`id`, `name`, `company_id`) VALUES " + "(1, 'Alice', 1), "
                    + "(2, 'Bob', 1), " + "(3, 'Charlie', 2), " + "(4, 'David', 3), " + "(5, 'Eve', 2)";
            statement.execute(dropPersonTable);
            statement.execute(createPersonTable);
            statement.execute(insertPersons);
        }
    }

    void prepareTablesContainsTime() throws SQLException {
        String url = "jdbc:mysql://" + HOST + ":" + PORT + "/" + DATABASE;
        try (Connection connection = DriverManager.getConnection(url, USER,
            PASSWORD); Statement statement = connection.createStatement()) {

            String dropCompanyTable = "DROP TABLE IF EXISTS `storage_engine_test_company`";
            String createCompanyTable =
                "CREATE TABLE IF NOT EXISTS `storage_engine_test_company` (\n" +
                    "`id` int DEFAULT NULL,\n" +
                    "`name` varchar(255),\n" +
                    "`create_time` DATETIME DEFAULT CURRENT_TIMESTAMP," +
                    "`update_time` DATE DEFAULT CURRENT_TIMESTAMP" +
                    ") ENGINE=InnoDB DEFAULT CHARSET=utf8";
            String insertCompanies =
                "INSERT INTO `storage_engine_test_company` (`id`, `name`,`create_time`,`update_time`) VALUES "
                    + "(1, 'Company A','2024-12-16 00:00:20','2024-12-15'), "
                    + "(2, 'Company B','2024-12-16 00:00:20','2024-12-16'), "
                    + "(3, 'Company C','2024-12-16 00:00:20','2024-12-13')";
            statement.execute(dropCompanyTable);
            statement.execute(createCompanyTable);
            statement.execute(insertCompanies);
        }
    }

    @Test
    @EnabledIf("isDatabaseAccessible")
    void executeQueryTest() throws SQLException, IOException {
        // 准备测试表
        prepareTables();

        MysqlConnectionParams params = new MysqlConnectionParams();
        params.setConnectionType(ConnectionType.MYSQL);
        params.setHost(HOST);
        params.setPort(PORT);
        params.setDatabase(DATABASE);
        params.setUsername(USER);
        params.setPassword(PASSWORD);

        final String PERSON_NAME = "person_name";
        final String COMPANY_NAME = "company_name";
        final String COMPANY_ID = "company_id";
        final String PERSON_ID = "person_id";
        try (MysqlConnection mysqlConnection = new MysqlConnection(params)) {
            String queryStatement =
                "SELECT " + "p.id AS person_id, " + "p.name AS person_name, " + "c.id AS company_id, "
                    + "c.name AS company_name " + "FROM storage_engine_test_person p "
                    + "LEFT JOIN storage_engine_test_company c ON p.company_id = c.id";
            List<Map<String, Object>> maps = mysqlConnection.executeQuery(queryStatement);
            List<Map<String, Object>> excepted = List.of(
                Map.of(COMPANY_ID, 1, COMPANY_NAME, "Company A", PERSON_ID, 1, PERSON_NAME, "Alice"),
                Map.of(COMPANY_ID, 1, COMPANY_NAME, "Company A", PERSON_ID, 2, PERSON_NAME, "Bob"),
                Map.of(COMPANY_ID, 2, COMPANY_NAME, "Company B", PERSON_ID, 3, PERSON_NAME, "Charlie"),
                Map.of(COMPANY_ID, 2, COMPANY_NAME, "Company B", PERSON_ID, 5, PERSON_NAME, "Eve"),
                Map.of(COMPANY_ID, 3, COMPANY_NAME, "Company C", PERSON_ID, 4, PERSON_NAME, "David"));
            assertThat(maps.size()).isEqualTo(excepted.size());
            assertThat(maps).containsAll(excepted);
        }
    }

    @Test
    @EnabledIf("isDatabaseAccessible")
    void executeQueryByParamsTest() throws SQLException, IOException {
        // 准备测试表
        prepareTables();

        MysqlConnectionParams params = new MysqlConnectionParams();
        params.setConnectionType(ConnectionType.MYSQL);
        params.setHost(HOST);
        params.setPort(PORT);
        params.setDatabase(DATABASE);
        params.setUsername(USER);
        params.setPassword(PASSWORD);

        final String COMPANY_ID = "id";
        final String COMPANY_NAME = "name";
        try (MysqlConnection mysqlConnection = new MysqlConnection(params)) {

            DataServiceField field = new DataServiceField();
            field.setEnName(COMPANY_NAME);
            List<ValueObject> values = new ArrayList<>();
            values.add(new ValueObject("Company A"));
            values.add(new ValueObject("Company B"));
            Condition condition = new Condition(DataServiceConditionType.EXPRESSION, field, values, "in");

            PageParams pageParams = new PageParams();
            pageParams.setPageNum(1);
            pageParams.setPageSize(10);

            ConditionSearchParams searchParams = new ConditionSearchParams();
            searchParams.setReturnFields(List.of(COMPANY_ID, COMPANY_NAME));
            searchParams.setConditions(List.of(condition));
            searchParams.setPageParams(pageParams);
            DataConnection dataConnection = new DataConnection();
            dataConnection.setConnectionType(ConnectionType.MYSQL);
            List<Map<String, Object>> maps = mysqlConnection.executeQueryByParams("storage_engine_test_company",
                searchParams, dataConnection);
            List<Map<String, Object>> excepted = List.of(
                Map.of(COMPANY_ID, 1, COMPANY_NAME, "Company A"),
                Map.of(COMPANY_ID, 2, COMPANY_NAME, "Company B"));
            assertThat(maps.size()).isEqualTo(2);
            assertThat(maps).containsAll(excepted);
        }
    }

    @Test
    @EnabledIf("isDatabaseAccessible")
    void returnFieldsTest() throws SQLException, IOException {
        // 准备测试表
        prepareTables();
        MysqlConnectionParams params = new MysqlConnectionParams();
        params.setConnectionType(ConnectionType.MYSQL);
        params.setHost(HOST);
        params.setPort(PORT);
        params.setDatabase(DATABASE);
        params.setUsername(USER);
        params.setPassword(PASSWORD);
        final String COMPANY_NAME = "name";
        try (MysqlConnection mysqlConnection = new MysqlConnection(params)) {
            DataServiceField field = new DataServiceField();
            field.setEnName(COMPANY_NAME);
            List<ValueObject> values = new ArrayList<>();
            values.add(new ValueObject("Company A"));
            values.add(new ValueObject("Company B"));
            values.add(new ValueObject("Company C"));
            Condition condition = new Condition(DataServiceConditionType.EXPRESSION, field, values, "in");
            PageParams pageParams = new PageParams();
            pageParams.setPageNum(1);
            pageParams.setPageSize(10);
            ConditionSearchParams searchParams = new ConditionSearchParams();
            searchParams.setReturnFields(List.of(COMPANY_NAME));
            searchParams.setConditions(List.of(condition));
            searchParams.setPageParams(pageParams);
            DataConnection dataConnection = new DataConnection();
            dataConnection.setConnectionType(ConnectionType.MYSQL);
            List<Map<String, Object>> maps = mysqlConnection.executeQueryByParams("storage_engine_test_company",
                searchParams, dataConnection);
            List<Map<String, Object>> excepted = List.of(
                Map.of(COMPANY_NAME, "Company A"),
                Map.of(COMPANY_NAME, "Company B"),
                Map.of(COMPANY_NAME, "Company C"));
            assertThat(maps.size()).isEqualTo(3);
            assertThat(maps).containsAll(excepted);
        }
    }

    @Test
    @EnabledIf("isDatabaseAccessible")
    void CountQueryTest() throws SQLException, IOException {
        // 准备测试表
        prepareTables();
        MysqlConnectionParams params = new MysqlConnectionParams();
        params.setConnectionType(ConnectionType.MYSQL);
        params.setHost(HOST);
        params.setPort(PORT);
        params.setDatabase(DATABASE);
        params.setUsername(USER);
        params.setPassword(PASSWORD);
        try (MysqlConnection mysqlConnection = new MysqlConnection(params)) {
            DataConnection dataConnection = new DataConnection();
            dataConnection.setConnectionType(ConnectionType.MYSQL);
            long result = mysqlConnection.executeCountQuery("select count(1) from storage_engine_test_company");
            assertThat(result).isEqualTo(3);
        }
    }

    @Test
    @EnabledIf("isDatabaseAccessible")
    void containTimeTest() throws SQLException, IOException {
        // 准备测试表
        prepareTablesContainsTime();

        MysqlConnectionParams params = new MysqlConnectionParams();
        params.setConnectionType(ConnectionType.MYSQL);
        params.setHost(HOST);
        params.setPort(PORT);
        params.setDatabase(DATABASE);
        params.setUsername(USER);
        params.setPassword(PASSWORD);

        final String PERSON_NAME = "person_name";
        final String COMPANY_NAME = "company_name";
        final String COMPANY_ID = "company_id";
        final String PERSON_ID = "person_id";
        final String CREATE_TIME = "create_time";
        final String UPDATE_TIME = "update_time";
        try (MysqlConnection mysqlConnection = new MysqlConnection(params)) {
            String queryStatement =
                "SELECT " + "p.id AS person_id, " + "p.name AS person_name, " + "c.id AS company_id, "
                    + "c.name AS company_name, c.create_time, c.update_time " + "FROM storage_engine_test_person p "
                    + "LEFT JOIN storage_engine_test_company c ON p.company_id = c.id where p.name != 'Alice'";
            List<Map<String, Object>> maps = mysqlConnection.executeQuery(queryStatement);
            List<Map<String, Object>> excepted = List.of(
                Map.of(COMPANY_ID, 1, COMPANY_NAME, "Company A", PERSON_ID, 2, PERSON_NAME, "Bob", CREATE_TIME,
                    "2024-12-16 00:00:20", UPDATE_TIME, "2024-12-15"),
                Map.of(COMPANY_ID, 2, COMPANY_NAME, "Company B", PERSON_ID, 3, PERSON_NAME, "Charlie", CREATE_TIME,
                    "2024-12-16 00:00:20", UPDATE_TIME, "2024-12-16"),
                Map.of(COMPANY_ID, 2, COMPANY_NAME, "Company B", PERSON_ID, 5, PERSON_NAME, "Eve", CREATE_TIME,
                    "2024-12-16 00:00:20", UPDATE_TIME, "2024-12-16"),
                Map.of(COMPANY_ID, 3, COMPANY_NAME, "Company C", PERSON_ID, 4, PERSON_NAME, "David", CREATE_TIME,
                    "2024-12-16 00:00:20", UPDATE_TIME, "2024-12-13"));
            assertThat(maps.size()).isEqualTo(excepted.size());
            assertThat(maps).containsAll(excepted);
        }
    }

    @Test
    void groupFieldsTest() {
        String groupStatement = SqlUtils.buildGroupStatement(List.of("name", "age"), ConnectionType.MYSQL);
        String expected = "GROUP BY name, age";
        assertThat(groupStatement).isEqualTo(expected);
    }

    @Test
    void groupFieldsTestPgOracle() {
        String groupStatement = SqlUtils.buildGroupStatement(List.of("name", "age"), ConnectionType.ORACLE);
        String expected = "GROUP BY \"name\", \"age\"";
        String groupStatementPg = SqlUtils.buildGroupStatement(List.of("name", "age"), ConnectionType.POSTGRESQL);
        assertThat(groupStatement).isEqualTo(expected);
        assertThat(groupStatementPg).isEqualTo(expected);
    }
}