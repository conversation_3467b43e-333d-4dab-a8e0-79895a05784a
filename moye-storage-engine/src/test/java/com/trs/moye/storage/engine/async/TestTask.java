package com.trs.moye.storage.engine.async;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.function.Supplier;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @since 2025-02-12 17:19
 */
@Slf4j
public class TestTask implements Runnable{

    private static final Runnable EMPTY_RUNNABLE = () -> {};

    private final String name;

    private final long time;

    /**
     * 业务
     */
    private Runnable business;

    private final AtomicBoolean running = new AtomicBoolean(false);

    private final AtomicBoolean alreadyRunning = new AtomicBoolean(false);

    private final AtomicBoolean alreadyExecuteBusiness = new AtomicBoolean(false);

    private final AtomicBoolean businessRunning = new AtomicBoolean(false);

    private final AtomicBoolean businessAlreadyRunning = new AtomicBoolean(false);

    public TestTask(String name, long time, Runnable runnable) {
        this.name = name;
        this.time = time;
        this.business = runnable;
    }

    public TestTask(String name, long time) {
        this(name, time, EMPTY_RUNNABLE);
    }

    @Override
    public void run() {
        running.set(true);
        alreadyRunning.set(true);
        try {
            log.info("{} 启动，任务时间：{}ms", name, time);
            business.run();
            alreadyExecuteBusiness.set(true);
            if (time > 0){
                sleep(time);
            }
            log.info("{} 结束", name);
        }catch (Exception e){
            alreadyExecuteBusiness.set(true);
            log.error("{} 异常", name, e);
        }finally {
            running.set(false);
            if (businessRunning.get()){
                businessRunning.set(false);
            }
        }
    }

    /**
     * 是否正在运行
     *
     * @return true/false
     */
    public boolean isRunning() {
        return running.get();
    }

    /**
     * 是否已经运行过
     *
     * @return true/false
     */
    public boolean isAlreadyRunning() {
        return alreadyRunning.get();
    }

    /**
     * 是否已经执行过Runnable
     *
     * @return true/false
     */
    public boolean getAlreadyExecuteBusiness() {
        return alreadyExecuteBusiness.get();
    }

    /**
     * 是否正在执行业务
     *
     * @return true/false
     */
    public boolean isBusinessRunning() {
        return businessRunning.get();
    }

    /**
     * 设置是否正在执行业务
     *
     * @param running true/false
     */
    public void setBusinessRunning(boolean running) {
        this.businessRunning.set(running);
    }

    /**
     * 是否已经执行业务
     *
     * @return true/false
     */
    public boolean isBusinessAlreadyRunning() {
        return businessAlreadyRunning.get();
    }

    /**
     * 设置已经执行业务
     */
    public void setBusinessAlreadyRunning() {
        this.businessAlreadyRunning.set(true);
    }

    /**
     * 设置业务
     *
     * @param business 业务
     */
    public void setBusiness(Runnable business) {
        this.business = business;
    }

    /**
     * 睡眠
     *
     * @param time 时间，单位毫秒
     */
    public static void sleep(long time) {
        try {
            Thread.sleep(time);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.error("睡眠发生中断异常", e);
        }
    }

    /**
     * 等待状态为true
     *
     * @param timeout 超时时间，单位毫秒
     * @param sleepTime 睡眠时间，单位毫秒
     * @param statusChecker 状态检查器
     * @return true/false
     */
    public static boolean waitTrueStatus(long timeout, long sleepTime, Supplier<Boolean> statusChecker){
        long startTime = System.currentTimeMillis();
        while (System.currentTimeMillis() - startTime <= timeout){
            if (statusChecker.get()){
                return true;
            }
            sleep(sleepTime);
        }
        log.info("等待状态为true超时，等待时间：{}ms", System.currentTimeMillis() -startTime);
        return false;
    }

    /**
     * 等待所有任务完成
     *
     * @param executorService 执行器服务
     * @param waitTime 等待时间，单位毫秒
     */
    public static void waitAllTaskFinish(ExecutorService executorService, long waitTime) {
        try {
            executorService.awaitTermination(waitTime, TimeUnit.MILLISECONDS);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.error("等待所有任务完成异常", e);
        }
    }
}
