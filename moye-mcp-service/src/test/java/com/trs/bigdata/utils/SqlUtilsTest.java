package com.trs.bigdata.utils;


import static org.assertj.core.api.Assertions.assertThat;

import java.util.List;
import org.junit.jupiter.api.Test;

/**
 * <AUTHOR>
 * @since 2025/4/22 16:46
 */
class SqlUtilsTest {


    @Test
    void splitSqlWithChinese() {
        String str = """
            正确的SQL查询如下：
                        
            SELECT gxdwdm AS 分局代码, COUNT(*) AS 总警情数量
            FROM ods_ds_gx_jjdb
            WHERE bjsj BETWEEN '2025-04-21 16:00:00' AND '2025-04-22 16:00:00'
            AND gxdwdm NOT LIKE '510109%'
            GROUP BY gxdwdm;
            """;
        List<String> sqlList = SqlUtils.splitSql(str);
        assertThat(sqlList.get(0)).isEqualTo("""
            SELECT gxdwdm AS 分局代码, COUNT(*) AS 总警情数量
            FROM ods_ds_gx_jjdb
            WHERE bjsj BETWEEN '2025-04-21 16:00:00' AND '2025-04-22 16:00:00'
            AND gxdwdm NOT LIKE '510109%'
            GROUP BY gxdwdm;""");
    }
}