spring.application.name=moye-mcp-service
# Nacos配置中心
spring.cloud.nacos.username=${TRS_MOYE_NACOS_USER_NAME}
spring.cloud.nacos.password=${TRS_MOYE_NACOS_PASSWORD}
spring.cloud.nacos.server-addr=nacos-svc:8848
spring.cloud.nacos.config.namespace=test
spring.cloud.nacos.discovery.namespace=${spring.cloud.nacos.config.namespace}
spring.config.import[0]=nacos:mysql-moye-v4.properties
spring.config.import[1]=nacos:mysql-moye-v4-dynamic.properties
spring.config.import[2]=nacos:moye-mcp-service.properties?refresh=true
spring.config.import[3]=nacos:redis.properties?refresh=true
spring.config.import[4]=nacos:minio.properties?refresh=true
logging.level.com.alibaba.cloud.nacos=debug