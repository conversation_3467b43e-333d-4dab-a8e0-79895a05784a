package com.trs.bigdata.model;

import lombok.AllArgsConstructor;
import lombok.Data;

/**
 * 模型返回结果
 *
 * <AUTHOR>
 * @since 2025/4/27 14:10
 */
@Data
@AllArgsConstructor
public class ModelResult {

    /**
     * 回答id
     */
    private String id;
    /**
     * 返回结果字符串
     */
    private String result;
    /**
     * 模型请求参数
     */
    private ModelRequest request;
    /**
     * 模型返回结果(去掉think)
     */
    private String resultWithoutThink;
    /**
     * 步骤简要说明
     */
    private String summary;

    private SearchStepResponse step;
}
