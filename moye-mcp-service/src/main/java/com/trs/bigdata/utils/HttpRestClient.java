package com.trs.bigdata.utils;

import com.trs.moye.base.common.utils.JsonUtils;
import java.io.IOException;
import java.time.Duration;
import java.time.temporal.ChronoUnit;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import javax.net.ssl.SSLContext;
import javax.net.ssl.SSLSocketFactory;
import javax.net.ssl.TrustManager;
import javax.net.ssl.X509TrustManager;
import lombok.extern.slf4j.Slf4j;
import okhttp3.ConnectionPool;
import okhttp3.Headers;
import okhttp3.Headers.Builder;
import okhttp3.MediaType;
import okhttp3.OkHttpClient;
import okhttp3.Protocol;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;
import okhttp3.logging.HttpLoggingInterceptor;
import okhttp3.logging.HttpLoggingInterceptor.Level;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.util.UriComponentsBuilder;

/**
 * Http Rest Client
 *
 * <AUTHOR>
 */
@Slf4j
public class HttpRestClient {

    private HttpRestClient() {
        throw new UnsupportedOperationException("This is a utility class and cannot be instantiated");
    }

    private static final HttpLoggingInterceptor INTERCEPTOR = new HttpLoggingInterceptor(s -> {
        try {
            log.info("OkHttp ----- {}", s);
        } catch (Exception e) {
            log.error("OkHttp ----- {}", s, e);
        }
    }).setLevel(Level.BASIC);


    //ssl
    private static final TrustManager[] TRUST_ALL_CERTS = createTrustManager();
    private static final SSLSocketFactory SSL_SOCKET_FACTORY = createSocketFactory();
    private static final OkHttpClient HTTP_CLIENT = new OkHttpClient.Builder()
        .callTimeout(Duration.of(5, ChronoUnit.MINUTES))
        .addNetworkInterceptor(INTERCEPTOR)
        .connectionPool(new ConnectionPool(20, 5, TimeUnit.SECONDS))
        .connectTimeout(5, TimeUnit.MINUTES)
        .readTimeout(5, TimeUnit.MINUTES)
        .writeTimeout(5, TimeUnit.MINUTES)
        .retryOnConnectionFailure(true)
        .hostnameVerifier((s, sslSession) -> Boolean.TRUE)
        .sslSocketFactory(SSL_SOCKET_FACTORY, (X509TrustManager) TRUST_ALL_CERTS[0])
        .protocols(List.of(Protocol.HTTP_1_1))
        .build();


    private static SSLSocketFactory createSocketFactory() {
        try {
            // Install the all-trusting trust manager
            final SSLContext sslContext = SSLContext.getInstance("SSL");
            sslContext.init(null, HttpRestClient.TRUST_ALL_CERTS, new java.security.SecureRandom());
            return sslContext.getSocketFactory();
        } catch (Exception exception) {
            return null;
        }
    }

    private static TrustManager[] createTrustManager() {
        // Create a trust manager that does not validate certificate chains
        return new TrustManager[]{
            new X509TrustManager() {
                @Override
                public void checkClientTrusted(java.security.cert.X509Certificate[] chain, String authType) {
                    //目前暂时无需使用该方法
                }

                @Override
                public void checkServerTrusted(java.security.cert.X509Certificate[] chain, String authType) {
                    //目前暂时无需使用该方法
                }

                @Override
                public java.security.cert.X509Certificate[] getAcceptedIssuers() {
                    return new java.security.cert.X509Certificate[]{};
                }
            }
        };
    }

    /**
     * Post请求
     *
     * @param uri          请求地址
     * @param body         请求体
     * @param params       请求参数
     * @param headersParam 请求头
     * @return 请求结果
     * @throws IOException 请求异常
     */
    public static Response post(String uri, Object body, Map<String, Object> params, Map<String, String> headersParam)
        throws IOException {
        String json = JsonUtils.toJsonString(body);
        RequestBody requestBody = RequestBody.create(MediaType.parse("application/json"), json);
        String url = buildUri(uri, params);
        Request.Builder requestBuilder = new Request.Builder().url(url).post(requestBody);
        Headers headers = buildHeaders(headersParam);
        Request request = requestBuilder.headers(headers).build();
        return HTTP_CLIENT.newCall(request).execute();
    }

    /**
     * Post请求
     *
     * @param uri  请求地址
     * @param body 请求体
     * @return 请求结果
     * @throws IOException 请求异常
     */
    public static Response post(String uri, Object body) throws IOException {
        return post(uri, body, null, null);
    }

    /**
     * Post请求
     *
     * @param uri    请求地址
     * @param body   请求体
     * @param params 请求参数
     * @return 请求结果
     * @throws IOException 请求异常
     */
    public static Response post(String uri, Object body, Map<String, Object> params) throws IOException {
        return post(uri, body, params, null);
    }

    /**
     * Post请求
     *
     * @param uri  请求地址
     * @param body 请求体
     * @return 请求结果
     * @throws IOException 请求异常
     */
    public static String postResponseString(String uri, Object body) throws IOException {
        Response response = post(uri, body);
        if (response.body() == null) {
            throw new IOException("http response is null!");
        }
        String s = response.body().string();
        if (StringUtils.isBlank(s)) {
            throw new IOException("http response body is empty!");
        }
        return s;
    }

    /**
     * Get请求
     *
     * @param uri    请求地址
     * @param params 请求参数
     * @param header 请求头
     * @return 请求结果
     * @throws IOException 请求异常
     */
    public static Response get(String uri, Map<String, Object> params, Map<String, String> header)
        throws IOException {
        String url = buildUri(uri, params);
        Request.Builder requestBuilder = new Request.Builder().url(url).get();
        Headers headers = buildHeaders(header);
        Request request = requestBuilder.headers(headers).build();
        return HTTP_CLIENT.newCall(request).execute();
    }

    /**
     * Get请求
     *
     * @param uri    请求地址
     * @param params 请求参数
     * @return 请求结果
     * @throws IOException 请求异常
     */
    public static Response get(String uri, Map<String, Object> params) throws IOException {
        return get(uri, params, null);
    }

    /**
     * Get请求
     *
     * @param uri 请求地址
     * @return 请求结果
     * @throws IOException 请求异常
     */
    public static Response get(String uri) throws IOException {
        return get(uri, null, null);
    }

    private static String buildUri(String uri, Map<String, Object> params) {
        UriComponentsBuilder builder = UriComponentsBuilder.fromHttpUrl(uri);
        if (Objects.nonNull(params)) {
            params.forEach(builder::queryParam);
        }
        return builder.toUriString();
    }

    private static Headers buildHeaders(Map<String, String> headers) {
        Builder headerBuilder = new Builder();
        if (Objects.nonNull(headers)) {
            headers.forEach(headerBuilder::add);
        }
        return headerBuilder.build();
    }
}
