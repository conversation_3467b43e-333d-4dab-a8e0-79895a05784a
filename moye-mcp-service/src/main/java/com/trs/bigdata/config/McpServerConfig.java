package com.trs.bigdata.config;

import static com.trs.bigdata.constants.McpConstants.AGENT_NAME;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.trs.bigdata.pojo.FileInfo;
import com.trs.bigdata.service.ResourceService;
import com.trs.bigdata.service.SearchService;
import com.trs.bigdata.utils.BeanUtil;
import com.trs.moye.base.common.utils.JsonUtils;
import io.modelcontextprotocol.server.McpAsyncServer;
import io.modelcontextprotocol.server.McpServer;
import io.modelcontextprotocol.server.McpServerFeatures.SyncResourceSpecification;
import io.modelcontextprotocol.server.McpServerFeatures.SyncToolSpecification;
import io.modelcontextprotocol.server.McpSyncServer;
import io.modelcontextprotocol.server.transport.WebFluxSseServerTransportProvider;
import io.modelcontextprotocol.spec.McpSchema;
import io.modelcontextprotocol.spec.McpSchema.CallToolResult;
import io.modelcontextprotocol.spec.McpSchema.JsonSchema;
import io.modelcontextprotocol.spec.McpSchema.Tool;
import io.modelcontextprotocol.spec.McpServerTransportProvider;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.tool.ToolCallback;
import org.springframework.ai.tool.definition.ToolDefinition;
import org.springframework.ai.tool.method.MethodToolCallbackProvider;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.reactive.function.server.RouterFunction;

/**
 * MCP Server Configuration
 */
@Configuration
@Slf4j
public class McpServerConfig {

    @Value("${mcp.tool.search.description}")
    private String mcpSearchToolDescription;


    /**
     * SSE transport
     *
     * @return {@link McpServerTransportProvider}
     */
    @Bean
    public WebFluxSseServerTransportProvider sseServerTransportProvider() {
        return new WebFluxSseServerTransportProvider(new ObjectMapper(), "/mcp/message");
    }

    /**
     * Router function for SSE transport used by Spring WebFlux to start an HTTP
     *
     * @param transportProvider {@link McpServerTransportProvider}
     * @return {@link RouterFunction}
     */
    @Bean
    public RouterFunction<?> mcpRouterFunction(WebFluxSseServerTransportProvider transportProvider) {
        return transportProvider.getRouterFunction();
    }

    /**
     * mcpAsyncServer
     *
     * @param transportProvider {@link McpServerTransportProvider}
     * @param searchService     {@link SearchService}
     * @param resourceService   {@link ResourceService}
     * @return {@link McpAsyncServer}
     */
    @Bean
    public McpSyncServer mcpSyncServer(McpServerTransportProvider transportProvider, SearchService searchService,
        ResourceService resourceService) {
        // Configure server capabilities with resource support
        var capabilities = McpSchema.ServerCapabilities.builder()
            .tools(true) // Tool support with list changes notifications
            .logging()
            .resources(false, true)// Logging support
            .build();

        SyncToolSpecification syncToolSpecification = getSyncToolSpecification(searchService);

        // Create the server with both tool and resource capabilities
        List<SyncResourceSpecification> resources = resourceService.getSyncResourceSpecifications();

        // Add @Tools
        return McpServer.sync(transportProvider)
            .serverInfo(AGENT_NAME, "4.3.0")
            .capabilities(capabilities)
            .tools(syncToolSpecification)
            .resources(resources)// Add @Tools
            .build(); // @format
    }

    private SyncToolSpecification getSyncToolSpecification(SearchService searchService) {
        ToolCallback[] toolCallbacks = MethodToolCallbackProvider.builder().toolObjects(searchService).build()
            .getToolCallbacks();
        ToolDefinition toolDefinition = toolCallbacks[0].getToolDefinition();
        JsonSchema inputSchema = JsonUtils.parseObject(toolDefinition.inputSchema(), JsonSchema.class);
        inputSchema.properties().remove("exchange");
        inputSchema.required().remove("exchange");
        Tool searchTool = new Tool(toolDefinition.name(),
            mcpSearchToolDescription, inputSchema);
        return new SyncToolSpecification(searchTool,
            (exchange, args) -> {
                List<String> resourceUris = args.get("resourceUris") != null ? JsonUtils.toList(
                    JsonUtils.toJsonString(args.get("resourceUris")), String.class) : null;
                String query = args.get("query") != null ? args.get("query").toString() : null;
                String callId = args.get("callId") != null ? args.get("callId").toString() : null;
                String messageId = args.get("messageId") != null ? args.get("messageId").toString() : null;
                Integer appId = args.get("appId") != null ? Integer.parseInt(args.get("appId").toString()) : null;
                List<FileInfo> excelInfos =
                    args.get("excelInfos") != null ? JsonUtils.toList(JsonUtils.toJsonString(args.get("excelInfos")),
                        FileInfo.class) : null;
                String result = BeanUtil.getBean(SearchService.class)
                    .searchData(query, resourceUris, callId, messageId, appId, excelInfos, exchange);
                return new CallToolResult(result, false);
            });
    }

}