package com.trs.bigdata;

import com.trs.bigdata.feign.StorageEngineFeign;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.scheduling.annotation.EnableScheduling;

/**
 * Moye MCP Service Application
 *
 * <AUTHOR>
 */
@MapperScan(basePackages = {"com.trs.**.dao"})
@EnableFeignClients(basePackageClasses = StorageEngineFeign.class)
@SpringBootApplication
@EnableConfigurationProperties
@EnableScheduling
@ComponentScan("com.trs.*")
public class MoyeMcpServiceApplication {

    /**
     * Main method to run the application
     *
     * @param args command line arguments
     */
    public static void main(String[] args) {
        SpringApplication.run(MoyeMcpServiceApplication.class, args);
    }
}