package com.trs.bigdata.service;

import static com.trs.bigdata.constants.McpConstants.PLANING_STAGE_NAME;

import com.fasterxml.jackson.databind.JsonNode;
import com.trs.bigdata.model.ModelRequest;
import com.trs.bigdata.model.ModelResponse;
import com.trs.bigdata.model.ModelResult;
import com.trs.bigdata.model.SearchStepResponse;
import com.trs.bigdata.pojo.TraceInfo;
import com.trs.bigdata.pojo.TraceInfo.LlmInfo;
import com.trs.bigdata.properties.LlmModelProperties;
import com.trs.moye.base.common.exception.BizException;
import com.trs.moye.base.common.utils.JsonUtils;
import io.modelcontextprotocol.server.McpSyncServerExchange;
import io.netty.channel.ChannelOption;
import jakarta.annotation.Resource;
import java.time.Duration;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import java.util.concurrent.atomic.AtomicReference;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.MediaType;
import org.springframework.http.client.reactive.ReactorClientHttpConnector;
import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;
import reactor.netty.http.client.HttpClient;

/**
 * <AUTHOR>
 * @since 2025/4/28 14:52
 */
@Service
@Slf4j
public class WebFluxService {

    @Resource
    private LlmModelProperties llmModelProperties;

    private static final WebClient CLIENT = WebClient.builder()
        .clientConnector(new ReactorClientHttpConnector(HttpClient.create()
            .responseTimeout(Duration.ofMinutes(10))
            .option(ChannelOption.CONNECT_TIMEOUT_MILLIS, 30000)))
        .build();

    private static final String DONE = "[DONE]";

    private static final String STEP_EXTRACT_ERROR = "解析大模型输出的下一步动作失败";


    /**
     * 去除大模型返回的think内容
     */
    private static final Pattern THINK_PATTERN = Pattern.compile("<think>.*?</think>", Pattern.DOTALL);

    private static final Pattern SUMMARY_PATTERN = Pattern.compile("<step_summary>(.*?)</step_summary>",
        Pattern.DOTALL);


    /**
     * 流式获取大模型返回的内容
     *
     * @param exchange     McpSyncServerExchange
     * @param callId       调用id
     * @param modelRequest 大模型请求参数
     * @return 处理后的内容
     */
    public ModelResult doModelStreamRequest(McpSyncServerExchange exchange, String callId, ModelRequest modelRequest) {
        return doModelStreamRequest(exchange, callId, modelRequest, PLANING_STAGE_NAME);
    }

    /**
     * 流式获取大模型返回的内容
     *
     * @param exchange     McpSyncServerExchange
     * @param callId       调用id
     * @param modelRequest 大模型请求参数
     * @param name         阶段名称
     * @return 处理后的内容
     */
    public ModelResult doModelStreamRequest(McpSyncServerExchange exchange, String callId, ModelRequest modelRequest,
        String name) {

        log.info("请求大模型 - url: {} 请求参数：\n{}", llmModelProperties.getUrl(),
            JsonUtils.toJsonString(modelRequest));

        List<String> dataCache = new ArrayList<>();

        String stageId = UUID.randomUUID().toString();
        Flux<String> flux = CLIENT.post()
            .uri(llmModelProperties.getUrl())
            .bodyValue(modelRequest)
            .accept(MediaType.TEXT_EVENT_STREAM)
            .exchangeToFlux(response -> {
                if (response.statusCode().is2xxSuccessful()) {
                    return response.bodyToFlux(String.class);
                } else {
                    // Read the error body before returning an error
                    return response.bodyToMono(String.class)
                        .flatMapMany(errorBody -> {
                            log.error("Error response from model API: Status={}, Body={}",
                                response.statusCode(), errorBody);
                            return Flux.error(new BizException(
                                "Error calling model API: " + response.statusCode() + ", Body: " + errorBody
                            ));
                        });
                }
            });

        AtomicReference<String> answerId = new AtomicReference<>("");
        AtomicReference<String> stageName = new AtomicReference<>(name);

        Mono<List<String>> responseMono = flux
            .doOnSubscribe(sub -> log.info("开始请求大模型"))
            .doOnNext(event -> log.debug("请求大模型返回数据: {}", getContent(event)))
            .doOnComplete(() -> log.info("请求大模型结束"))
            .doOnError(e -> log.error("请求大模型失败", e))
            .publishOn(Schedulers.boundedElastic())
            .flatMap(data -> {
                if (!noNeedPush(data)) {
                    //发送流式日志
                    ModelResponse modelResponse = JsonUtils.parseObject(data, ModelResponse.class);
                    if (modelResponse != null) {
                        answerId.set(modelResponse.getId());
                        String deltaData = modelResponse.getDeltaContent();

                        dataCache.add(deltaData);
                        String currentResult = String.join("", dataCache);
                        Matcher matcher = SUMMARY_PATTERN.matcher(currentResult);
                        if (matcher.find()) {
                            String summary = matcher.group(1);
                            if (StringUtils.isNotBlank(summary)) {
                                stageName.set(summary);
                            }
                        }

                        TraceInfo traceInfo = TraceInfo.createLlmStreamTrace(callId, stageId, stageName.get(),
                            deltaData, false);
                        SearchService.sendLog(exchange, traceInfo);
                    }
                }
                return Mono.just(data);
            })
            .collectList();

        List<String> responseList = responseMono.block();
        if (responseList == null || responseList.isEmpty()) {
            return new ModelResult(answerId.get(), "", modelRequest, null, null, null);
        }

        String response = responseList.stream().map(this::getContent).reduce("", (a, b) -> a + b);
        log.info("请求大模型返回结果：\n{}", response);

        //将模型返回结果添加到请求中
        String resultWithoutThink = replaceThink(response);
        modelRequest.addAssistantMessage(resultWithoutThink);
        SearchStepResponse step = extractStepResponse(exchange, callId, modelRequest, resultWithoutThink);
        String summary = step != null ? step.getSummary() : stageName.get();

        //发送结束日志
        TraceInfo traceInfo = TraceInfo.createLlmStreamTrace(callId, stageId, summary, "", true);
        traceInfo.setTool(new LlmInfo(llmModelProperties.getUrl(), answerId.get(), llmModelProperties.getName(),
                JsonUtils.toJsonString(modelRequest), response));
        SearchService.sendLog(exchange, traceInfo);

        log.info("去除think的返回结果：\n{}", resultWithoutThink);
        return new ModelResult(answerId.get(), response, modelRequest, resultWithoutThink, summary, step);
    }

    private String getContent(String data) {
        try {
            if (StringUtils.isBlank(data) || data.equals(DONE)) {
                return "";
            }
            ModelResponse modelResponse = JsonUtils.parseObject(data, ModelResponse.class);
            if (modelResponse == null) {
                return "";
            }
            return modelResponse.getDeltaContent();
        } catch (Exception e) {
            log.error("从模型返回结果中获取content失败，data：{}", data, e);
            return "";
        }
    }

    private boolean noNeedPush(String data) {
        return data.equals(DONE) || getContent(data).equals("<think>") || getContent(data).equals("</think>");
    }

    private String replaceThink(String messageContent) {
        if (messageContent != null) {
            int thinkStart = messageContent.indexOf("</think>");
            if (thinkStart != -1) {
                return messageContent.substring(thinkStart + 8);
            }
            return messageContent;
        }
        return "";
    }

    private SearchStepResponse extractStepResponse(McpSyncServerExchange exchange, String callId, ModelRequest request,
                                                   String resultWithoutThink) {
        if (StringUtils.isBlank(resultWithoutThink)) {
            log.error("解析大模型输出的下一步动作为空，模型输出：{}", resultWithoutThink);

            TraceInfo traceInfo = new TraceInfo(
                    callId, STEP_EXTRACT_ERROR, "从大模型输出中解析的下一步动作为空：" + resultWithoutThink, true);
            SearchService.sendLog(exchange, traceInfo);
            request.addUserMessage(
                    "解析出的下一步动作为空，请检查是否返回下一步动作，并更正下一步动作");
            return null;
        }
        try {
            JsonNode jsonNode = JsonUtils.parseJsonNode(resultWithoutThink);
            // 如果是json数组，则是选表结果，SearchStepResponse返回null
            if (jsonNode.isArray()) {
                return null;
            }
            return JsonUtils.parseObject(resultWithoutThink, SearchStepResponse.class);
        } catch (Exception ex) {
            log.error("解析大模型输出的下一步动作失败，json不合法：{}", resultWithoutThink, ex);
            request.addUserMessage("下一步动作的json不合法：" + ex.getMessage());

            // 发送中间步骤
            TraceInfo traceInfo = new TraceInfo(
                    callId, STEP_EXTRACT_ERROR, "不符合要求的下一步动作，未遵循json格式要求：" + resultWithoutThink,
                    true);
            SearchService.sendLog(exchange, traceInfo);
            return null;
        }
    }

}
