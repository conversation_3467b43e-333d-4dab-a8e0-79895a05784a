package com.trs.bigdata.service;

import com.trs.bigdata.dao.KnowledgeBaseDataMapper;
import com.trs.bigdata.exception.MoyeMcpServiceException;
import com.trs.moye.base.knowledgebase.dao.KnowledgeBaseFieldMapper;
import com.trs.moye.base.knowledgebase.dao.KnowledgeBaseMapper;
import com.trs.moye.base.knowledgebase.entity.KnowledgeBase;
import com.trs.moye.base.knowledgebase.entity.KnowledgeBaseField;
import com.trs.moye.base.knowledgebase.enums.KnowledgeBaseType;
import jakarta.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @since 2025/4/22 16:08
 */
@Service
public class KnowledgeBaseService {

    @Resource
    private KnowledgeBaseDataMapper knowledgeBaseDataMapper;

    @Resource
    private KnowledgeBaseFieldMapper knowledgeBaseFieldMapper;

    @Resource
    private KnowledgeBaseMapper knowledgeBaseMapper;


    /**
     * 根据实体名称，获取实体库表数据
     *
     * @param toolName 实体名称
     * @return 数据
     */
    public List<Map<String, Object>> getKnowledgeBaseData(String toolName) {
        String zhName = toolName.replace("获取", "");
        KnowledgeBase entity = knowledgeBaseMapper.getByTypeName(KnowledgeBaseType.ENTITY.name(), zhName, true);
        String entityName;
        if (entity != null) {
            entityName = entity.getEnName();
        } else {
            throw new MoyeMcpServiceException("工具不存在");
        }
        String tableName = KnowledgeBaseType.ENTITY.buildTableName(entityName);
        List<Map<String, Object>> rawData = knowledgeBaseDataMapper.getAllTableData(tableName);
        KnowledgeBase base = knowledgeBaseMapper.getByTypeName(KnowledgeBaseType.ENTITY.name(), entityName, false);
        Map<String, KnowledgeBaseField> fieldMap = knowledgeBaseFieldMapper.listByBaseId(base.getId())
            .stream().collect(Collectors.toMap(KnowledgeBaseField::getEnName, field -> field));
        List<Map<String, Object>> result = new ArrayList<>();
        for (Map<String, Object> data : rawData) {
            Map<String, Object> newData = new HashMap<>();
            for (Map.Entry<String, KnowledgeBaseField> entry : fieldMap.entrySet()) {
                String key = entry.getKey();
                KnowledgeBaseField field = entry.getValue();
                if (data.containsKey(key)) {
                    Object value = data.get(key);
                    newData.put(field.getZhName(), value);
                }
            }
            result.add(newData);
        }
        return result;
    }


}
