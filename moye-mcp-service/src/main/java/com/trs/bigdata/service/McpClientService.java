package com.trs.bigdata.service;

import com.fasterxml.jackson.databind.JsonNode;
import com.trs.bigdata.pojo.McpServerInfo;
import com.trs.bigdata.pojo.ToolInfo;
import com.trs.bigdata.pojo.TraceInfo;
import com.trs.bigdata.properties.LlmModelProperties;
import com.trs.bigdata.properties.UsingToolInfo;
import com.trs.bigdata.utils.HttpRestClient;
import com.trs.moye.base.common.utils.JsonUtils;
import io.modelcontextprotocol.client.McpClient;
import io.modelcontextprotocol.client.McpSyncClient;
import io.modelcontextprotocol.client.transport.WebFluxSseClientTransport;
import io.modelcontextprotocol.server.McpSyncServerExchange;
import io.modelcontextprotocol.spec.McpSchema;
import io.modelcontextprotocol.spec.McpSchema.CallToolRequest;
import io.modelcontextprotocol.spec.McpSchema.CallToolResult;
import io.modelcontextprotocol.spec.McpSchema.ListToolsResult;
import io.modelcontextprotocol.spec.McpSchema.TextContent;
import jakarta.annotation.Resource;
import java.time.Duration;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.UUID;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Response;
import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.client.WebClient;

/**
 * MCP Client Service
 *
 * <AUTHOR>
 * @since 2025/5/7 17:47
 */
@Service
@Slf4j
public class McpClientService {

    @Resource
    private LlmModelProperties properties;


    /**
     * MCP Client Initialization
     *
     * @param url MCP Server URL
     * @return MCP Client
     */
    public McpSyncClient init(String url) {
        // 默认去掉/sse
        String mcpUrl = url.replace("/sse", "");
        WebFluxSseClientTransport transport = new WebFluxSseClientTransport(WebClient.builder().baseUrl(mcpUrl));
        McpSyncClient mcpSyncClient = McpClient.sync(transport)
            .clientInfo(new McpSchema.Implementation("Moye Mcp Client", "4.3.0"))
            .requestTimeout(Duration.ofSeconds(30))
            .loggingConsumer((notification) -> log.info("请求mcp server: {} - {}", mcpUrl, notification.data()))
            .build();
        mcpSyncClient.initialize();
        return mcpSyncClient;
    }

    /**
     * MCP Client Shutdown
     *
     * @param mcpSyncClient MCP Client
     */
    public void destroy(McpSyncClient mcpSyncClient) {
        if (mcpSyncClient != null) {
            mcpSyncClient.closeGracefully();
        }
    }

    /**
     * 调用工具
     *
     * @param toolName 工具名称
     * @param input    输入参数
     * @param url      MCP Server URL
     * @return 工具调用结果
     */
    public String callTool(String url, String toolName, Map<String, Object> input) {
        log.info("开始请求工具：{}，url: {}, 参数：{}", toolName, url, JsonUtils.toJsonString(input));
        McpSyncClient mcpSyncClient = init(url);
        CallToolResult result = mcpSyncClient.callTool(new CallToolRequest(toolName, input));
        destroy(mcpSyncClient);
        log.info("工具请求完成：{}，结果：{}", toolName, JsonUtils.toJsonString(result));
        return result.content().stream().map(content -> {
            if (content instanceof TextContent textContent) {
                return textContent.text();
            }
            return null;
        }).collect(Collectors.joining(""));
    }

    /**
     * 获取工具列表，包括工具名称和参数配置等信息
     *
     * @param url MCP Server URL
     * @return 工具列表
     */
    public ListToolsResult listTools(String url) {
        log.info("开始获取工具列表 url:{}", url);
        McpSyncClient mcpSyncClient = init(url);
        ListToolsResult result = mcpSyncClient.listTools();
        destroy(mcpSyncClient);
        log.info("获取工具列表完成：{}", JsonUtils.toJsonString(result));
        return result;
    }

    /**
     * 获取MCP服务器列表
     *
     * @param url MCP服务器URL
     * @return MCP服务器信息列表
     */
    public List<McpServerInfo> getMcpServers(String url) {
        try {
            Response response = HttpRestClient.get(url);
            String responseStr = response.body().string();
            JsonNode responseNode = JsonUtils.toJsonNode(responseStr);
            return JsonUtils.toList(responseNode.get("data").toString(), McpServerInfo.class);
        } catch (Exception e) {
            log.error("获取MCP服务器失败，url: {}, 错误信息: {}", url, e.getMessage(), e);
            return new ArrayList<>();
        }

    }

    /**
     * 获取MCP工具列表
     *
     * @param appId    应用ID
     * @param exchange MCP同步服务器交换对象
     * @param callId   调用ID
     * @return MCP工具信息列表
     */
    public List<ToolInfo> getMcpTools(Integer appId, McpSyncServerExchange exchange, String callId) {
        String mcpToolUrl = properties.getMcpToolUrl().replace("{appId}", String.valueOf(appId));
        List<McpServerInfo> mcpServerInfos = getMcpServers(mcpToolUrl);
        List<ToolInfo> toolInfos = new ArrayList<>();
        TraceInfo traceInfo = new TraceInfo(callId, UUID.randomUUID().toString(), "获取可用工具");
        for (McpServerInfo mcpServerInfo : mcpServerInfos) {
            List<ToolInfo> serverToolInfos = new ArrayList<>();
            try {
                if (Objects.isNull(mcpServerInfo)
                    || Objects.isNull(mcpServerInfo.getName())
                    || mcpServerInfo.getConfig().isEmpty()) {
                    log.warn("存在MCP Server信息不完整，跳过处理: {}", mcpServerInfo);
                    continue;
                }
                // 如果MCP服务器名称包含配置中的工具名称，则获取对应的工具信息
                if (properties.getMcpToolName().contains(mcpServerInfo.getName())) {
                    // 如果是获取知识工具，工具描述需要额外拼接可查询的知识库
                    if (mcpServerInfo.getUrl().equals(properties.getMcpServerUrl())) {
                        McpSchema.Tool tool = getSearchKnowledgeTool(appId);
                        if (tool != null) {
                            ToolInfo knowledgeToolInfo = new ToolInfo(mcpServerInfo.getName(),
                                mcpServerInfo.getDescription(),
                                mcpServerInfo.getUrl(),
                                tool.name(),
                                tool.description(),
                                tool.inputSchema());
                            serverToolInfos.add(knowledgeToolInfo);
                        }
                    } else {
                        ListToolsResult listToolsResult = listTools(mcpServerInfo.getUrl());
                        List<String> usingToolName = getUsingTools(mcpServerInfo.getName());
                        for (McpSchema.Tool tool : listToolsResult.tools()) {
                            // 如果server配置了可用的工具名称，排除不需要的工具名称；无配置时返回全部
                            if (usingToolName.isEmpty() || usingToolName.contains(tool.name())) {
                                ToolInfo toolInfo = new ToolInfo(
                                        mcpServerInfo.getName(),
                                        mcpServerInfo.getDescription(),
                                        mcpServerInfo.getUrl(),
                                        tool.name(),
                                        tool.description(),
                                        tool.inputSchema());
                                serverToolInfos.add(toolInfo);
                            }
                        }
                    }

                    String logContent = "\n获取到[" + mcpServerInfo.getName() + "]的工具: "
                        + serverToolInfos.stream().map(ToolInfo::getToolName).collect(Collectors.joining(", "));
                    traceInfo.setContent(logContent);
                    SearchService.sendLog(exchange, traceInfo);
                }
            } catch (Exception e) {
                log.error("获取MCP工具列表失败，mcp server信息: {}, 错误信息: {}", mcpServerInfo, e.getMessage(), e);
                String logContent =
                    "\n从McpServer[" + mcpServerInfo.getName() + "]获取工具列表失败，错误信息: " + e.getMessage();
                traceInfo.setContent(logContent);
                traceInfo.setError(true);
                SearchService.sendLog(exchange, traceInfo);
            }
            toolInfos.addAll(serverToolInfos);

        }
        traceInfo.setContent("\n获取工具列表完成，共获取到 " + toolInfos.size() + " 个工具");
        traceInfo.setEnd(true);
        SearchService.sendLog(exchange, traceInfo);
        return toolInfos;
    }

    private List<String> getUsingTools(String serverName) {
        List<UsingToolInfo> usingToolInfos = properties.getMcpUsingToolName();
        if (usingToolInfos != null && !usingToolInfos.isEmpty()) {
            for (UsingToolInfo usingToolInfo : usingToolInfos) {
                if (usingToolInfo.getServerName().equals(serverName)) {
                    return usingToolInfo.getToolName();
                }
            }
        }
        return new ArrayList<>();
    }

    /**
     * 获取search_knowledge工具信息
     *
     * @param appId 知识列表
     * @return search_knowledge工具信息
     */
    public McpSchema.Tool getSearchKnowledgeTool(Integer appId) {
        ListToolsResult listToolsResult = listTools(properties.getMcpServerUrl());
        String knowledgeList = callToolListKnowledge(appId);
        Optional<McpSchema.Tool> knowledgeToolOptional = listToolsResult.tools().stream()
            .filter(tool -> properties.getSearchKnowledgeToolName().equals(tool.name()))
            .findFirst();
        if (knowledgeToolOptional.isPresent()) {
            McpSchema.Tool knowledgeTool = knowledgeToolOptional.get();
            String desc = knowledgeTool.description() + " 可供查询的知识范围如下：" + knowledgeList + "\nappId为：" + appId;
            return new McpSchema.Tool(knowledgeTool.name(), desc, knowledgeTool.inputSchema());
        }
        return null;
    }

    /**
     * 调用工具获取知识库列表
     *
     * @param appId 应用ID
     * @return 知识列表
     */
    public String callToolListKnowledge(Integer appId) {
        return callTool(properties.getMcpServerUrl(),
            properties.getListKnowledgeToolName(), Map.of("appId", appId == null ? 0 : appId));
    }

}
