package com.trs.bigdata.service;

import com.trs.bigdata.dao.PromptTemplateMapper;
import com.trs.bigdata.feign.StorageEngineFeign;
import com.trs.bigdata.pojo.ToolInfo;
import com.trs.moye.base.common.enums.FieldType;
import com.trs.moye.base.common.utils.JsonUtils;
import com.trs.moye.base.data.explore.FieldExplorationResult;
import com.trs.moye.base.data.explore.TableExplorationRequest;
import com.trs.moye.base.data.explore.TableExplorationResult;
import com.trs.moye.base.mcp.SearchableField;
import com.trs.moye.base.mcp.TableInfo;
import jakarta.annotation.Resource;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.util.StringUtil;
import org.springframework.stereotype.Component;

/**
 * 生成用于查询大模型的prompt
 *
 * <AUTHOR>
 * @since 2025/4/17 15:46
 */
@Slf4j
@Component
public class PromptCreator {

    @Resource
    private PromptTemplateMapper promptTemplateMapper;

    @Resource
    private StorageEngineFeign storageEngineFeign;


    /**
     * 生成选表提示信息
     *
     * @param tables         表信息列表
     * @param fileTableInfos 文件信息列表
     * @return 提示信息
     */
    public String createSelectTablePrompt(List<TableInfo> tables, List<TableInfo> fileTableInfos) {
        String template = promptTemplateMapper.selectByType("SELECT_TABLE");
        return template.replace("{file_info}", createAllTablePrompt(fileTableInfos))
            .replace("{table_info}", createAllTablePrompt(tables));
    }

    /**
     * 生成系统提示信息
     *
     * @param tableInfos    表信息
     * @param fileInfos     文件信息
     * @param toolInfos     工具列表
     * @param searchContext 搜索历史记录
     * @return 表名提示信息
     */
    public String createSystemPrompt(List<TableInfo> tableInfos, List<TableInfo> fileInfos, List<ToolInfo> toolInfos,
        String searchContext) {
        String filePrompt = createAllTablePrompt(fileInfos);
        String tablePrompt = createAllTablePrompt(tableInfos);
        String toolsPrompt = createToolsPrompt(toolInfos);

        String taskPrompt = promptTemplateMapper.selectByType("TASK");
        String systemPrompt = searchContext
            + "\n\n今天是" + DateTimeFormatter.ofPattern("yyyy年MM月dd日").format(LocalDateTime.now());
        return taskPrompt.replace("{file_info}", filePrompt)
            .replace("{table_info}", tablePrompt)
            .replace("{tool_info}", toolsPrompt)
            .replace("{other_context}", systemPrompt);
    }

    private String createAllTablePrompt(List<TableInfo> tableInfos) {
        return tableInfos.isEmpty() ? "" :
            "<tables>\n" + tableInfos.stream().map(TableInfo::getProbePrompt)
                .collect(Collectors.joining("\n")) + "\n</tables>";
    }

    /**
     * 创建表提示词信息，包括表的基本信息和字段探查信息
     *
     * @param table tableInfo
     * @return 提示词
     */
    public String createTablePrompt(TableInfo table) {
        return String.format(
            "<table connection_id=%s>\n表数据库类型：%s\n表中文名: %s\n表英文名: %s\n表描述: %s\n%s</table>",
            table.getConnectionId(), table.getConnectionType(), table.getZhName(), table.getEnName(),
            table.getDescription(), createFieldProbePrompt(table));
    }

    /**
     * 从文件数据中创建表提示词信息，包括表的基本信息和字段探查信息
     *
     * @param table tableInfo
     * @return 提示词
     */
    public String createFileTablePrompt(TableInfo table) {
        return String.format(
            "<table connection_id=%s>\n表数据库类型：%s\n表中文名: %s\n表英文名: %s\n表描述: %s\n%s</table>",
            table.getConnectionId(), table.getConnectionType(), table.getZhName(), table.getEnName(),
            table.getDescription(), createFieldProbePromptByRows(table));
    }

    private String createToolsPrompt(List<ToolInfo> tools) {
        return tools.stream()
            .map(tool -> String.format(
                "<mcp-tool>\nserver_name=%s\nserver_desc=%s\ntool_name=%s\ntool_desc=%s\ninputSchema=%s\n</mcp-tool>",
                tool.getServerName(), tool.getServerDesc(),
                tool.getToolName(), tool.getToolDesc(), JsonUtils.toJsonString(tool.getInputSchema())))
            .collect(Collectors.joining("\n"));
    }

    /**
     * 根据字段示例数据生成字段探查信息
     *
     * @param table 数据表信息
     * @return 字段探查信息
     */
    private String createFieldProbePrompt(TableInfo table) {
        // 1. 通过feign调用获取结构化的数据探查结果
        TableExplorationResult explorationResult = storageEngineFeign.getTableDataExplore(
            new TableExplorationRequest(table));

        if (Objects.isNull(explorationResult)) {
            return createFieldProbePromptByRows(table);
        }

        if (explorationResult.getFieldResults() == null || explorationResult.getFieldResults().isEmpty()) {
            if (explorationResult.getErrors() != null && !explorationResult.getErrors()
                .isEmpty()) {
                return "数据探查失败: " + String.join(", ", explorationResult.getErrors());
            }
            return "该数据表目前暂时没有数据或字段信息，无法进行探查。";
        }

        StringBuilder resultPrompt = new StringBuilder();

        resultPrompt.append(String.format("表的字段有：%s\n 字段详情如下：",
            table.getFields().stream().map(SearchableField::getEnName).collect(Collectors.joining(","))));

        // 获取采样数据用于内存分析
        List<Map<String, Object>> rows = table.getRows() == null ? Collections.emptyList() : table.getRows().getItems();

        for (FieldExplorationResult fieldResult : explorationResult.getFieldResults()) {
            String columnName = fieldResult.getFieldName();
            SearchableField field = table.getFields().stream()
                .filter(f -> f.getEnName().equals(columnName))
                .findFirst().orElse(null);

            if (field == null) {
                continue;
            }

            // 2. 基于结构化探查结果生成Prompt
            StringBuilder summary = new StringBuilder();
            summary.append(String.format("字段 %s 类型为 %s 中文名为 %s。",
                columnName, fieldResult.getFieldType(), fieldResult.getFieldZhName()));
            summary.append(String.format("在 %d 条记录中，有 %d 个 NULL 值。",
                fieldResult.getTotalRecords(), fieldResult.getNullCount()));
            summary.append(String.format("共有 %d 个唯一值。", fieldResult.getDistinctCount()));

            if (fieldResult.getMinValue() != null) {
                summary.append(String.format("最小值为 '%s'，最大值为 '%s'。",
                    fieldResult.getMinValue(), fieldResult.getMaxValue()));
            }

            if (fieldResult.getMostCommonValues() != null && !fieldResult.getMostCommonValues().isEmpty()) {
                summary.append(String.format("最常见的非 NULL 值为: %s。",
                    fieldResult.getMostCommonValues().stream().map(v -> "'" + v + "'")
                        .collect(Collectors.joining(", "))));
            }

            if (fieldResult.getMinLength() != null) {
                if (fieldResult.getMinLength().equals(fieldResult.getMaxLength())) {
                    summary.append(String.format("字段值长度统一为 %d。", fieldResult.getMinLength()));
                } else {
                    summary.append(String.format("字段值长度范围从 %d 到 %d。", fieldResult.getMinLength(),
                        fieldResult.getMaxLength()));
                }
            }

            if (Boolean.TRUE.equals(fieldResult.getAllNumeric())) {
                summary.append("所有非空字段值看起来都像数字。");
            }

            // 3. 对无法通过SQL探查的部分，使用采样数据在内存中分析
            performInMemoryAnalysis(rows, field, columnName, summary);

            resultPrompt.append(summary.toString().trim()).append("\n\n");
        }

        return resultPrompt.toString();
    }

    /**
     * 对采样数据进行内存分析，补充探查信息
     *
     * @param rows       采样数据行
     * @param field      字段信息
     * @param columnName 字段名称
     * @param summary    探查信息摘要
     */
    private void performInMemoryAnalysis(List<Map<String, Object>> rows, SearchableField field, String columnName,
        StringBuilder summary) {
        for (Map<String, Object> row : rows) {
            Object value = row.get(columnName);
            if (value instanceof String strValue) {
                // 猜测日期格式
                if (FieldType.DATE.equals(field.getType()) || FieldType.DATETIME.equals(field.getType())) {
                    String dateFormat = guessDateFormat(strValue);
                    if (dateFormat != null) {
                        summary.append(String.format("日期时间格式为 '%s'。 ", dateFormat));
                        return;
                    }
                }
            }
        }
    }

    /**
     * 根据字段示例数据生成字段探查信息
     *
     * @param table 数据表信息
     * @return 字段探查信息
     */
    public String createFieldProbePromptByRows(TableInfo table) {
        List<Map<String, Object>> rows = table.getRows() == null ? Collections.emptyList() : table.getRows().getItems();
        List<SearchableField> fields = table.getFields();
        long totalRecords = table.getRows() == null ? 0 : table.getRows().getTotal();

        if (fields == null || fields.isEmpty() || totalRecords == 0) {
            return "该数据表目前暂时没有数据或字段信息，无法进行探查。";
        }

        StringBuilder resultPrompt = new StringBuilder();
        resultPrompt.append(String.format("表的字段有：%s\n 字段详情如下：",
            table.getFields().stream().map(SearchableField::getEnName).collect(Collectors.joining(","))));

        for (SearchableField field : fields) {
            String columnName = field.getEnName();
            long nullCount = 0;
            Set<Object> distinctValues = new HashSet<>();
            Comparable<Object> minValue = null;
            Comparable<Object> maxValue = null;
            Integer minLen = null;
            Integer maxLen = null;
            Map<String, Long> charTypes = new HashMap<>();
            Map<Object, Long> valueCounts = new HashMap<>();
            boolean allLookLikeNumbers = true;
            String dateFormat = null;

            boolean isDateType = field.getType() != null
                && (FieldType.DATE.equals(field.getType())
                || FieldType.DATETIME.equals(field.getType()));

            if (rows != null && !rows.isEmpty()) {
                for (Map<String, Object> row : rows) {
                    Object value = row.get(columnName);

                    if (value == null || "NULL".equalsIgnoreCase(String.valueOf(value))) {
                        nullCount++;
                        continue;
                    }

                    distinctValues.add(value);
                    valueCounts.put(value, valueCounts.getOrDefault(value, 0L) + 1);

                    if (value instanceof Comparable) {
                        @SuppressWarnings("unchecked")
                        Comparable<Object> cValue = (Comparable<Object>) value;
                        if (minValue == null) {
                            minValue = cValue;
                        } else if (minValue.getClass().isInstance(cValue) && cValue.compareTo(minValue) < 0) {
                            minValue = cValue;
                        }

                        if (maxValue == null) {
                            maxValue = cValue;
                        } else if (maxValue.getClass().isInstance(cValue) && cValue.compareTo(maxValue) > 0) {
                            maxValue = cValue;
                        }
                    }

                    if (value instanceof String sValue) {
                        int len = sValue.length();
                        if (minLen == null || len < minLen) {
                            minLen = len;
                        }
                        if (maxLen == null || len > maxLen) {
                            maxLen = len;
                        }

                        if (isDateType && dateFormat == null && !sValue.trim().isEmpty()) {
                            dateFormat = guessDateFormat(sValue);
                        }

                        for (char c : sValue.toCharArray()) {
                            String type;
                            if (Character.isUpperCase(c)) {
                                type = "uppercase";
                            } else if (Character.isLowerCase(c)) {
                                type = "lowercase";
                            } else if (Character.isDigit(c)) {
                                type = "digit";
                            } else if (Character.isWhitespace(c)) {
                                type = "whitespace";
                            } else {
                                type = "punctuation";
                            }
                            charTypes.put(type, charTypes.getOrDefault(type, 0L) + 1L);
                        }
                        if (allLookLikeNumbers) {
                            try {
                                Double.parseDouble(sValue);
                            } catch (NumberFormatException e) {
                                allLookLikeNumbers = false;
                            }
                        }
                    } else {
                        allLookLikeNumbers = false;
                    }
                }
            } else {
                nullCount = totalRecords;
            }

            List<Map.Entry<Object, Long>> sortedValues = new ArrayList<>(valueCounts.entrySet());
            sortedValues.sort(Map.Entry.<Object, Long>comparingByValue().reversed());

            StringBuilder summary = new StringBuilder();
            summary.append(String.format("字段 %s 类型为 %s 中文名为 %s %s。", columnName, field.getType(),
                field.getZhName(),
                StringUtil.isBlank(field.getDescription()) ? "" : "描述为 " + field.getDescription()));
            summary.append(
                String.format("字段 %s 在 %d 条记录中，有 %d 个 NULL 值。 ", columnName, totalRecords, nullCount));
            summary.append(String.format("共有 %d 个唯一值。 ", distinctValues.size()));
            if (minValue != null) {
                summary.append(String.format("最小值为 '%s'，最大值为 '%s'。 ", minValue, maxValue));
            }
            int topK = Math.min(10, sortedValues.size());
            List<Map.Entry<Object, Long>> topkValues = sortedValues.subList(0, topK);
            List<String> topkStrings = topkValues.stream().map(e -> "'" + e.getKey().toString() + "'")
                .collect(Collectors.toList());
            if (!topkStrings.isEmpty()) {
                summary.append(String.format("最常见的非 NULL 值为: %s。 ", String.join(", ", topkStrings)));
            }

            if (dateFormat != null) {
                summary.append(String.format("日期时间格式为 '%s'。 ", dateFormat));
            }

            if (minLen != null) {
                if (minLen.equals(maxLen)) {
                    summary.append(String.format("字段值长度统一为 %d。 ", minLen));
                } else {
                    summary.append(String.format("字段值长度范围从 %d 到 %d。 ", minLen, maxLen));
                }
            }
            if (allLookLikeNumbers && !distinctValues.isEmpty()) {
                summary.append("所有字段值看起来都像数字。 ");
            }
            resultPrompt.append(summary.toString().trim()).append("\n\n");
        }
        return resultPrompt.toString();
    }

    private String guessDateFormat(String dateString) {
        // Order matters. More specific patterns should come first.
        List<String> patterns = Arrays.asList(
            "yyyy-MM-dd HH:mm:ss.SSS",
            "yyyy-MM-dd HH:mm:ss",
            "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'",
            "yyyy-MM-dd'T'HH:mm:ss'Z'",
            "yyyy-MM-dd'T'HH:mm:ss",
            "yyyy-MM-dd",
            "yyyy/MM/dd HH:mm:ss",
            "yyyy/MM/dd",
            "MM/dd/yyyy HH:mm:ss",
            "MM/dd/yyyy",
            "dd/MM/yyyy HH:mm:ss",
            "dd/MM/yyyy",
            "EEE MMM dd HH:mm:ss zzz yyyy" // e.g. Tue Jun 24 10:47:52 CST 2025
        );

        for (String pattern : patterns) {
            try {
                if (pattern.contains("MMM") || pattern.contains("zzz")) {
                    DateTimeFormatter.ofPattern(pattern, Locale.ENGLISH).parse(dateString);
                } else {
                    DateTimeFormatter.ofPattern(pattern).parse(dateString);
                }
                return pattern;
            } catch (DateTimeParseException e) {
                // try next
            }
        }
        return null;
    }
}
