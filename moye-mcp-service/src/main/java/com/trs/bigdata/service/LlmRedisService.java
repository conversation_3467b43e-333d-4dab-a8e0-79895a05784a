package com.trs.bigdata.service;

import com.trs.bigdata.pojo.StepResult;
import com.trs.moye.base.common.utils.StringUtils;
import jakarta.annotation.Resource;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @since 2025/6/20 11:39
 */
@Component
@Slf4j
public class LlmRedisService {

    @Resource
    private RedisTemplate<String, String> redisTemplate;

    /**
     * redis中存储搜索上下文的ttl，默认1小时
     */
    @Value("${mcp.search.context.redis.ttl:3600}")
    private Integer searchContextRedisTtl;

    private static final String AGENT_PREFIX = "mcp-data-agent";

    private static final String HISTORY_PREFIX = "history_step";

    /**
     * 保存步骤结果到Redis
     *
     * @param sessionId   会话ID
     * @param stepResults 已执行步骤
     */
    public void saveSearchContext(String sessionId, Map<Integer, StepResult> stepResults) {
        if (StringUtils.isEmpty(sessionId)) {
            return;
        }
        try {
            String stepMessageStr = stepResults.values().stream().map(step -> {
                String stepInfo = String.format("任务标题：%s\n任务描述：%s\n参数：%s\n%s\n",
                    step.getTaskTitle(), step.getSummary(), step.getStep().getParams(), step.getUserMsg());
                    return "已执行步骤：" + stepInfo;
                }
            ).collect(Collectors.joining("\n"));
            // 将步骤结果与之前的上下文合并
            String history = getSearchContext(sessionId);
            stepMessageStr = StringUtils.isNotEmpty(history) ? history + "\n" + stepMessageStr : stepMessageStr;
            // 保存到Redis
            redisTemplate.opsForValue().set(AGENT_PREFIX + ":" + sessionId + ":" + HISTORY_PREFIX,
                stepMessageStr, searchContextRedisTtl, TimeUnit.SECONDS);

        } catch (Exception e) {
            log.error("Failed to save search context for sessionId: {}", sessionId, e);
        }

    }

    /**
     * 获取搜索上下文
     *
     * @param sessionId 会话ID
     * @return 上下文字符串
     */
    public String getSearchContext(String sessionId) {
        if (StringUtils.isEmpty(sessionId)) {
            return "";
        }
        try {
            String value = redisTemplate.opsForValue().get(AGENT_PREFIX + ":" + sessionId + ":" + HISTORY_PREFIX);
            return value == null ? "" : value;
        } catch (Exception e) {
            log.error("Failed to get search context for sessionId: {}", sessionId, e);
            return "";
        }
    }

}
