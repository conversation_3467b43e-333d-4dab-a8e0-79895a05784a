package com.trs.bigdata.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.fasterxml.jackson.databind.JsonNode;
import com.trs.ai.moye.minio.starter.properties.MinioProperties;
import com.trs.bigdata.constants.enums.StepStatus;
import com.trs.bigdata.dao.McpFileTableMapper;
import com.trs.bigdata.exception.MoyeMcpServiceException;
import com.trs.bigdata.feign.StorageEngineFeign;
import com.trs.bigdata.file.FileParser;
import com.trs.bigdata.file.FileParser.FileParseResult;
import com.trs.bigdata.file.FileReader;
import com.trs.bigdata.model.ModelRequest;
import com.trs.bigdata.model.ModelResult;
import com.trs.bigdata.model.SearchStepResponse;
import com.trs.bigdata.model.ToolParams;
import com.trs.bigdata.pojo.CodeSearchParams;
import com.trs.bigdata.pojo.ConditionSearchParams;
import com.trs.bigdata.pojo.ConnectionInfo;
import com.trs.bigdata.pojo.FileInfo;
import com.trs.bigdata.pojo.FileTable;
import com.trs.bigdata.pojo.SearchResponse;
import com.trs.bigdata.pojo.StepResult;
import com.trs.bigdata.pojo.ToolInfo;
import com.trs.bigdata.pojo.TraceInfo;
import com.trs.bigdata.properties.LlmModelProperties;
import com.trs.moye.base.common.request.PageParams;
import com.trs.moye.base.common.response.PageResponse;
import com.trs.moye.base.common.utils.DateTimeUtils;
import com.trs.moye.base.common.utils.JsonUtils;
import com.trs.moye.base.common.utils.PinyinUtil;
import com.trs.moye.base.data.connection.dao.DataConnectionMapper;
import com.trs.moye.base.data.connection.entity.DataConnection;
import com.trs.moye.base.data.connection.enums.ConnectionType;
import com.trs.moye.base.data.storage.DataStorage;
import com.trs.moye.base.data.storage.dao.DataStorageMapper;
import com.trs.moye.base.mcp.SearchableField;
import com.trs.moye.base.mcp.TableInfo;
import io.modelcontextprotocol.server.McpSyncServerExchange;
import io.modelcontextprotocol.spec.McpSchema.LoggingLevel;
import io.modelcontextprotocol.spec.McpSchema.LoggingMessageNotification;
import jakarta.annotation.Resource;
import java.net.HttpURLConnection;
import java.net.URL;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Optional;
import java.util.UUID;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.FileNameUtils;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.bouncycastle.util.Strings;
import org.springframework.ai.tool.annotation.Tool;
import org.springframework.ai.tool.annotation.ToolParam;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

/**
 * 数据建模服务
 */
@Service
@Slf4j
public class SearchService {

    @Resource
    private StorageEngineFeign storageEngineFeign;

    @Resource
    private ResourceService resourceService;

    @Resource
    private PromptCreator promptCreator;

    @Resource
    private DataStorageMapper dataStorageMapper;

    @Resource
    private WebFluxService webFluxService;

    @Resource
    private LlmModelProperties llmModelProperties;

    @Resource
    private McpClientService mcpClientService;

    @Resource
    private FileReader fileReader;

    @Resource
    private FileParser fileParser;

    @Resource
    private DataConnectionMapper dataConnectionMapper;

    @Resource
    private McpFileTableMapper mcpFileTableMapper;

    @Resource
    private LlmRedisService llmRedisService;

    /**
     * 请求大模型生成sql的最大迭代次数
     */
    @Value("${mcp.search.sql.iterate.times:6}")
    private Integer sqlIterateTimes = 3;

    /**
     * 请求大模型的总最大次数限制，防止死循环
     */
    @Value("${mcp.search.llm.max.times:20}")
    private Integer maxSearchIterations = 10;
    /**
     * 限制提交给大模型的查询数据条数
     */
    @Value("${mcp.search.data.limit:10}")
    private Integer llmDataLimit = 10;

    /**
     * 查询结果的最大条数
     */
    @Value("${mcp.search.max.size:1000}")
    private Integer searchMaxSize = 1000;
    @Resource
    private MinioProperties minioProperties;

    private static final Pattern TOOL_USE_PATTERN = Pattern.compile("<tool_use>\\s*<name>(.*?)</name>.*?</tool_use>",
        Pattern.DOTALL);

    private static final Pattern MARKDOWN_JSON_PATTERN = Pattern.compile("<step_info>(.*?)</step_info>",
        Pattern.DOTALL);

    private static final String STEP_CREATE_SQL = "生成并执行SQL";

    private static final String STEP_CORRECT_SQL = "更正SQL";

    private static final String STEP_TOOL = "调用工具";

    private static final String STEP_CHECK = "验证结果";

    private static final String STEP_FINISH = "完成任务";

    private static final String STEP_STOP = "终止任务";


    /**
     * 执行数据表查询
     *
     * @param query        查询诉求，自然语言
     * @param resourceUris 查询资源
     * @param callId       用户请求id（每次请求的id）
     * @param messageId    用户问题id（每个会话的id）
     * @param exchange     用户会话
     * @param appId        应用id
     * @param excelInfos   文件信息
     * @return 查询结果
     */
    @Tool(name = "查询数据", description = "根据您的数据查询需求，可一次性地整合查询来自多个不同数据源（resources）的信息。该工具也支持对用户上传的excel、json或csv文件中的信息进行联合查询。")
    public String searchData(
        @ToolParam(description = "用自然语言描述的数据查询诉求，查询数据的输入和输出描述要详细、精确。") String query,
        @ToolParam(description = "用于执行查询的可选资源列表") List<String> resourceUris,
        @ToolParam(description = "用户请求id") String callId,
        @ToolParam(description = "用户问题id") String messageId,
        @ToolParam(description = "应用id") Integer appId,
        @ToolParam(description = "用户上传的文件信息，被用作查询的额外补充数据和关联数据，同已有数据一起进行数据分析和查询。如果用户上传了文件，则必须填写此参数", required = false) List<FileInfo> excelInfos,
        McpSyncServerExchange exchange) {
        log.info("开始请求 doSearch：用户问题：{} 资源列表：{} appId：{} callId：{} 文件信息：{}", query, resourceUris,
            appId, callId, excelInfos);
        sendLog(exchange, new TraceInfo(callId, "开始处理请求：" + query, "", false));
        String searchResult;
        try {
            //获取可查询的表信息
            List<TableInfo> dbTableInfos = getTableInfo(exchange, callId, resourceUris);
            //读取文件信息
            List<TableInfo> fileInfos = new ArrayList<>();
            if (excelInfos != null && !excelInfos.isEmpty()) {
                fileInfos = excelInfos.stream()
                    .map(fileInfo -> {
                        TableInfo tableInfo = createTableInfoFromNewFile(fileInfo);
                        fileInfo.setTableInfo(tableInfo);
                        return tableInfo;
                    })
                    .collect(Collectors.toList());
            }
            //大模型选表
            List<TableInfo> selectedTables = getUsingTableByLlm(exchange, callId, query, dbTableInfos, fileInfos);

            // 选完表之后再将文件数据写入表
            List<TableInfo> fileTableInfos = new ArrayList<>();
            if (ObjectUtils.isNotEmpty(excelInfos)) {
                List<ConnectionInfo> connections = getConnectionInfosFromExcelInfos(callId, exchange, selectedTables);
                if (connections.isEmpty()) {
                    return "未获取到可供查询的数据库连接，无法进行查询";
                }
                fileTableInfos = getTableInfosFromExcelInfos(exchange, callId, excelInfos, connections);
            }

            if (selectedTables.isEmpty() && fileTableInfos.isEmpty()) {
                String noResourceContent = "未获取到可供查询的数据资源，无法进行查询";
                sendLog(exchange, new TraceInfo(callId, "未获取到数据资源", noResourceContent, true));
                return noResourceContent;
            }

            //获取工具信息
            List<ToolInfo> toolInfos = mcpClientService.getMcpTools(appId, exchange, callId);
            // 加载redis中的搜索上下文
            String searchContext = llmRedisService.getSearchContext(messageId);
            // 创建请求大模型的初始请求
            ModelRequest request = createSearchRequest(selectedTables, fileTableInfos, toolInfos, query, searchContext);
            //进行查询
            Map<Integer, StepResult> stepResults = new HashMap<>();
            searchResult = searchIteration(request, toolInfos, callId, exchange, stepResults);
            //存储步骤到redis
            llmRedisService.saveSearchContext(messageId, stepResults);

        } catch (MoyeMcpServiceException mcpException) {
            log.info("请求 doSearch抛出业务异常", mcpException);
            //流程中主动抛出的异常，根据title和message生成步骤信息
            searchResult = mcpException.getMessage();
            sendLog(exchange, new TraceInfo(callId, mcpException.getTitle(), mcpException.getMessage(), true));
        } catch (Exception e) {
            log.error("请求 doSearch 失败：", e);
            searchResult = "查询失败，失败原因：" + e.getMessage();
            sendLog(exchange, new TraceInfo(callId, "查询流程发生异常", "失败原因：" + e.getMessage(), true));
        }
        log.info("结束请求 doSearch：用户问题：{} 结果：{}", query, searchResult);
        return searchResult;
    }

    private List<ConnectionInfo> getConnectionInfosFromExcelInfos(String callId, McpSyncServerExchange exchange,
        List<TableInfo> dbTableInfos) {
        List<ConnectionInfo> connections = dbTableInfos.stream()
            .map(tableInfo -> new ConnectionInfo(tableInfo.getConnectionId(), tableInfo.getConnectionType()))
            .distinct()
            .collect(Collectors.toList());
        // 如果没有连接信息，则默认获取Doris连接
        if (connections.isEmpty()) {
            List<DataConnection> dorisConnections = dataConnectionMapper.selectByConnectionType(
                ConnectionType.DORIS);
            Optional<Integer> dorisConnectionId = dorisConnections.stream()
                .filter(DataConnection::isTestSuccess).map(DataConnection::getId).findFirst();
            if (dorisConnectionId.isPresent()) {
                connections.add(new ConnectionInfo(dorisConnectionId.get(), ConnectionType.DORIS));
            } else {
                String noConnectionContent = "未获取到可供查询的数据库连接，无法进行查询";
                sendLog(exchange, new TraceInfo(callId, "未获取到数据库连接", noConnectionContent, true));
            }
        }
        return connections;
    }


    /**
     * 推送日志
     *
     * @param exchange  McpSyncServerExchange
     * @param traceInfo TraceInfo
     */
    public static void sendLog(McpSyncServerExchange exchange, TraceInfo traceInfo) {
        exchange.loggingNotification(
            LoggingMessageNotification.builder().level(LoggingLevel.EMERGENCY).logger("custom-logger")
                .data(JsonUtils.toJsonString(traceInfo)).build());
    }

    /**
     * 根据系统内已有表及其组织结构和查询诉求，请求大模型获取需要查询的表信息
     *
     * @param exchange  通道
     * @param callId    用户会话id
     * @param resources 查询资源
     * @return 表信息
     */
    private List<TableInfo> getTableInfo(McpSyncServerExchange exchange, String callId, List<String> resources) {
        try {
            List<TableInfo> tableInfos = new ArrayList<>();
            //若给定了资源列表，则根据此过滤表
            if (resources != null && !resources.isEmpty()) {
                List<TableInfo> allTableInfos = resourceService.getSearchableDataModel();
                List<String> resourceIds = resources.stream().map(request -> {
                    String[] path = request.split("/");
                    return path[path.length - 1];
                }).toList();
                tableInfos = allTableInfos.stream()
                    .filter(tableInfo -> resourceIds.contains(tableInfo.getId().toString()))
                    .collect(Collectors.toList());
            }
            if (tableInfos.isEmpty()) {
                return new ArrayList<>();
            }

            // 获取连接信息
            tableInfos.forEach(tableInfo -> {
                DataStorage dataStorage = dataStorageMapper.selectByDataModelIdWithConnection(tableInfo.getId()).get(0);
                tableInfo.setConnectionId(dataStorage.getConnectionId());
                tableInfo.setConnectionType(dataStorage.getConnection().getConnectionType());
                //查询示例数据（只查询提供数据服务的字段）
                PageResponse<Map<String, Object>> rows = storageEngineFeign.conditionQuery(
                    dataStorage.getConnectionId(),
                    dataStorage.getEnName(), new ConditionSearchParams(1, 10000,
                        tableInfo.getFields().stream().map(SearchableField::getEnName).toList()));
                tableInfo.setRows(rows);
                // 获取字段探查信息
                tableInfo.setProbePrompt(promptCreator.createTablePrompt(tableInfo));
            });

            String tableStr = tableInfos.stream()
                .map(tableInfo -> tableInfo.getZhName() + "(" + tableInfo.getEnName() + ")")
                .collect(Collectors.joining("、"));
            sendLog(exchange, new TraceInfo(callId, "获取数据资源：" + tableStr,
                "可使用的数据资源列表如下: " + tableStr, false));

            return tableInfos;
        } catch (Exception e) {
            throw new MoyeMcpServiceException("获取可用的数据资源失败", "失败原因：" + e.getMessage(), e);
        }
    }

    private List<TableInfo> getUsingTableByLlm(McpSyncServerExchange exchange, String callId, String query,
        List<TableInfo> dbTableInfos, List<TableInfo> fileTableInfos) {
        String prompt = promptCreator.createSelectTablePrompt(dbTableInfos, fileTableInfos);
        ModelRequest request = createSearchRequest(prompt, query);
        ModelResult modelResult = webFluxService.doModelStreamRequest(exchange, callId, request, "选择用于查询的表");
        String result = modelResult.getResultWithoutThink();
        try {
            List<String> selectTableNames = JsonUtils.toList(result, String.class);
            if (selectTableNames.isEmpty()) {
                sendLog(exchange, new TraceInfo(callId, "未选择出用于查询的表", "大模型未选择出可用于查询的表", true));
                return new ArrayList<>();
            } else {
                String tableNames = selectTableNames.stream()
                    .map(String::trim)
                    .filter(name -> !name.isEmpty())
                    .collect(Collectors.joining(", "));
                sendLog(exchange, new TraceInfo(callId, "已选择出用于查询的表：" + tableNames,
                    "已选择出用于查询的表：" + tableNames, false));
                return dbTableInfos.stream()
                    .filter(tableInfo -> selectTableNames.contains(tableInfo.getEnName()))
                    .collect(Collectors.toList());
            }
        } catch (Exception e) {
            throw new MoyeMcpServiceException("解析大模型选择的表信息失败", "失败原因：" + e.getMessage() + "\n模型返回结果：" + result, e);
        }
    }

    /**
     * 获取文件表信息
     *
     * @param exchange    通道
     * @param callId      用户会话id
     * @param excelInfos  文件信息
     * @param connections 连接信息列表
     * @return 表信息
     */
    private List<TableInfo> getTableInfosFromExcelInfos(McpSyncServerExchange exchange, String callId,
        List<FileInfo> excelInfos, List<ConnectionInfo> connections) {
        List<TableInfo> tableList = new ArrayList<>();
        TraceInfo traceInfo = new TraceInfo(callId, UUID.randomUUID().toString(), "从文件中解析数据资源");
        traceInfo.setContent(
            String.format("从%s中解析数据资源：",
                excelInfos.stream().map(FileInfo::getFileName).collect(Collectors.joining("、"))));
        sendLog(exchange, traceInfo);
        try {
            for (FileInfo excelInfo : excelInfos) {
                // 计算文件MD5
                FileReader.FileContent fileContent = fileReader.getFileContentWithMd5(excelInfo.getDownloadUri());
                String md5 = fileContent.md5();

                // 对于数据资源中每一个不同的connection，都要建一次表
                for (ConnectionInfo connection : connections) {
                    // 检查MD5是否存在
                    QueryWrapper<FileTable> queryWrapper = new QueryWrapper<>();
                    queryWrapper.eq("md5", md5).eq("connection_id", connection.getConnectionId());
                    FileTable existingFile = mcpFileTableMapper.selectOne(queryWrapper);

                    // 获取表结构和数据
                    TableInfo tableInfo = excelInfo.getTableInfo();
                    tableInfo.setConnectionId(connection.getConnectionId());
                    tableInfo.setConnectionType(connection.getConnectionType());

                    //如果MD5已经存在就直接从数据库中读取表字段和信息
                    if (existingFile != null) {
                        log.info("文件 {} (md5:{}) 已存在，直接使用表 {}", excelInfo.getFileName(), md5,
                            existingFile.getTableName());
                        traceInfo.setContent(String.format("\n文件 %s 的内容已处理过，将直接使用已有数据资源 %s",
                            excelInfo.getFileName(), existingFile.getTableName()));
                        sendLog(exchange, traceInfo);
                        tableInfo.setEnName(existingFile.getTableName());
                        // 获取表字段探查数据
                        tableInfo.setProbePrompt(promptCreator.createTablePrompt(tableInfo));
                        tableList.add(tableInfo);
                        continue;
                    }

                    // 如果MD5不存在，则创建新表
                    try {
                        boolean result = storageEngineFeign.saveFileToStorage(connection.getConnectionId(), tableInfo);
                        if (result) {
                            mcpFileTableMapper.insert(new FileTable(tableInfo.getEnName(), excelInfo.getFileName(),
                                connection.getConnectionId(), md5));
                        }
                        // 获取表字段探查数据
                        tableInfo.setProbePrompt(promptCreator.createTablePrompt(tableInfo));
                        tableList.add(tableInfo);
                        traceInfo.setContent(String.format("\n文件%s解析并存储完成，共解析到%d条数据，存储表名：%s",
                            excelInfo.getFileName(), tableInfo.getData().size(), tableInfo.getEnName()));
                        traceInfo.setError(false);
                        traceInfo.setEnd(true);
                        sendLog(exchange, traceInfo);
                    } catch (Exception e) {
                        String errContent = "文件：" + excelInfo.getFileName() + "保存到存储失败，请检查文件格式或存储配置。报错信息："
                            + e.getMessage();
                        throw new MoyeMcpServiceException("保存文件到存储失败", errContent, e);
                    }
                }
            }
        } catch (Exception e) {
            log.error("从文件中解析数据资源失败：", e);
            throw new MoyeMcpServiceException("从文件中解析数据资源失败", "文件解析失败，请检查文件格式 报错信息：" + e.getMessage(), e);
        }

        String tableStr = tableList.stream()
            .map(tableInfo -> String.format("%s(%s)", tableInfo.getZhName(), tableInfo.getEnName()))
            .collect(Collectors.joining(","));
        traceInfo.setStageName(traceInfo.getStageName() + ": " + tableStr);
        traceInfo.setContent("\n从文件中解析到可使用的数据资源列表如下: " + tableStr);
        traceInfo.setError(false);
        traceInfo.setEnd(true);
        sendLog(exchange, traceInfo);

        return tableList;
    }

    private TableInfo createTableInfoFromNewFile(FileInfo fileInfo) {

        String fileUrl = fileInfo.getDownloadUri();
        String fileName = fileInfo.getFileName();
        String enName = createTableName(fileInfo.getFileUri());
        try {
            log.info("获取文件输入流: {}", fileUrl);
            URL url = new URL(fileUrl);
            HttpURLConnection conn = (HttpURLConnection) url.openConnection();
            conn.setRequestMethod("GET");
            conn.setConnectTimeout(5000);
            conn.setReadTimeout(60000);
            // 读取输入流到字节数组
            byte[] fileBytes = IOUtils.toByteArray(conn.getInputStream());
            conn.disconnect(); // 关闭连接

            // 使用新的输入流保存到Minio
            String minioPath = fileReader.saveFileToMinio(fileBytes, FilenameUtils.getName(fileUrl));

            // 使用新的输入流解析文件内容到TableInfo
            FileParseResult fileParseResult = fileParser.parseFileToTableInfo(fileName, fileBytes);

            // 提取表基本信息
            TableInfo tableInfo = new TableInfo();
            tableInfo.setEnName(enName);
            tableInfo.setZhName(fileName);
            tableInfo.setDescription(fileName + "，用户提供的外部数据文件创建的表，用于和其他数据表连表查询。");
            tableInfo.setFields(fileParseResult.getHeaders());
            tableInfo.setData(fileParseResult.getDataRows());
            tableInfo.setS3Path(getS3Path(minioPath));
            tableInfo.setRows(fileParseResult.getPage());
            tableInfo.setProbePrompt(promptCreator.createFileTablePrompt(tableInfo));
            return tableInfo;
        } catch (Exception e) {
            log.error("从文件中解析数据资源失败", e);
            throw new MoyeMcpServiceException("从文件中解析数据资源失败：" + e.getMessage(), e);
        }
    }

    private String getS3Path(String minioPath) {
        return String.format("""
                 s3(
                   "uri" = "s3://%s/%s",
                   "s3.endpoint" = "%s",
                   "s3.access_key" = "%s",
                   "s3.secret_key" = "%s",
                   "format" = "%s",
                   "s3.region" = "us-east-1",
                   "strip_outer_array" = "true"
                 )
                """, minioProperties.getBucket().getMcp(),
            FilenameUtils.getName(minioPath),
            minioProperties.getExternalEndpoint(),
            minioProperties.getAccessKey(),
            minioProperties.getSecretKey(),
            FilenameUtils.getExtension(minioPath));
    }

    private String createTableName(String fileUri) {
        String[] fileNameSplit = Strings.split(fileUri, '/');
        String fileName = fileNameSplit[fileNameSplit.length - 1];
        String baseName = FileNameUtils.getBaseName(fileName);
        String tableName =
            "t_tmp_mcp_" + PinyinUtil.toPinyin(baseName) + "_" + DateTimeUtils.formatTime(LocalDateTime.now());
        // 确保表名不超过63个字符
        if (tableName.length() > 63) {
            tableName = tableName.substring(0, 63);
        }
        return tableName;
    }

    private ModelRequest createSearchRequest(List<TableInfo> tableInfos, List<TableInfo> fileInfos,
        List<ToolInfo> toolInfos, String query, String searchContext) {
        //生成请求大模型的初始prompt
        String prompt = promptCreator.createSystemPrompt(tableInfos, fileInfos, toolInfos, searchContext);
        return createSearchRequest(prompt, query);
    }

    private ModelRequest createSearchRequest(String prompt, String query) {
        log.info("请求大模型的初始prompt：\n{}", prompt);
        ModelRequest request = new ModelRequest(llmModelProperties);
        request.setModelName(llmModelProperties.getName());
        request.addSystemMessage("think");
        request.addSystemMessage(prompt);
        request.addUserMessage(query);
        return request;
    }

    /**
     * 提取 JSON 字符串中的```json xxx ``` 内的json
     *
     * @param jsonString JSON 字符串
     * @return 提取后的字符串
     */
    public static String extractJsonString(String jsonString) {
        if (StringUtils.isBlank(jsonString)) {
            return "";
        }
        // 使用正则表达式提取
        Matcher matcher = MARKDOWN_JSON_PATTERN.matcher(jsonString);
        if (matcher.find()) {
            String innerJsonString = matcher.group(1).strip();
            // 去除多余的空格和换行符
            innerJsonString = innerJsonString.replaceAll("\\s+", " ");
            return innerJsonString;
        } else {
            //大模型未输出markdown格式时的额外处理
            int start = jsonString.indexOf('{');
            int end = jsonString.lastIndexOf('}');
            if (start >= 0 && end > start) {
                String extracted = jsonString.substring(start, end + 1);
                // 替换所有空白序列为单个空格并去除换行
                return extracted.replaceAll("\\s+", " ");
            }
        }
        return "";
    }

    /**
     * 根据已知的表ddl等信息，请求大模型获取查询sql，并请求存储引擎执行查询
     *
     * @param request     请求大模型的请求体
     * @param toolInfos   工具信息
     * @param callId      用户会话id
     * @param exchange    用户会话
     * @param stepResults 步骤结果
     * @return 查询结果
     */
    private String searchIteration(ModelRequest request, List<ToolInfo> toolInfos, String callId,
        McpSyncServerExchange exchange, Map<Integer, StepResult> stepResults) {
        int createSqlTimes = 0;
        for (int i = 0; i <= maxSearchIterations + 1 && createSqlTimes <= sqlIterateTimes + 1; i++) {
            //达到最大迭代次数，直接要求模型返回结果
            if (createSqlTimes == sqlIterateTimes || i == maxSearchIterations) {
                String userMsg = "已经尝试过多次查询数据，现在请直接根据执行过的步骤生成结果，在完成任务和终止任务中选择一个步骤执行";
                request.addUserMessage(userMsg);
            }
            //请求大模型
            ModelResult modelResult = webFluxService.doModelStreamRequest(exchange, callId, request);
            SearchStepResponse step = modelResult.getStep();
            if (step == null) {
                continue;
            }

            try {
                //处理大模型返回结果
                ProcessResult processResult = processModelResult(exchange, createSqlTimes, step, callId, toolInfos,
                    stepResults);
                createSqlTimes = processResult.times();

                //执行结束步骤时，返回结束原因
                if (processResult.status().equals(StepStatus.STOP)) {
                    return processResult.results();
                } else if (processResult.status().equals(StepStatus.FINISH)) {
                    //执行完成步骤时，根据步骤id返回对应结果
                    List<Integer> resultStepIds = JsonUtils.toList(processResult.results(), Integer.class);
                    Map<String, String> finalResults = new HashMap<>();
                    for (Integer id : resultStepIds) {
                        StepResult stepResult = stepResults.get(id);
                        if (stepResult != null) {
                            finalResults.put(stepResult.getTaskTitle(), stepResult.getResults());
                        }
                    }
                    return JsonUtils.toJsonString(finalResults);
                } else {
                    request.addUserMessage(processResult.userMsg());
                    //非结束步骤，将步骤结果存入结果集
                    stepResults.put(step.getStepId(),
                        new StepResult(step.getStepId(), step.getTaskTitle(), step.getSummary(),
                            step, processResult.results(), processResult.userMsg()));
                }
            } catch (Exception e) {
                request.addUserMessage("处理下一步动作出错：" + e.getMessage());
                sendLog(exchange, new TraceInfo(callId, step.getStep() + "失败", "失败原因：" + e.getMessage(), true));
            }
        }

        throw new MoyeMcpServiceException("尝试多次都没有查询到可用的结果，流程终止",
            "请求大模型生成sql查询数据，重复" + sqlIterateTimes + "次后仍然未获取到可用的结果，流程中止");
    }

    private ProcessResult processModelResult(McpSyncServerExchange exchange, Integer times, SearchStepResponse step,
        String callId, List<ToolInfo> toolInfos, Map<Integer, StepResult> stepResults) {
        //步骤处理结果，提供给大模型用
        String processResult = "";
        //本步骤是否标志流程结束
        StepStatus isFinish = StepStatus.PROCESSING;
        //真实返回数据
        String results = "";
        //步骤名称
        String stepName = normalizeStep(step.getStep());
        //步骤标题
        String stepTitle = stepName + "：" + step.getTaskTitle();
        switch (stepName) {
            case STEP_CREATE_SQL, STEP_CORRECT_SQL -> {
                stepTitle = "执行sql：" + step.getTaskTitle();
                //只有执行生成sql并查询的步骤时，才更新createSqlTimes的值
                times += 1;

                TraceInfo traceInfo = new TraceInfo(callId, UUID.randomUUID().toString(), stepTitle);
                SqlResult sqlResult = executeSql(step, exchange, traceInfo);

                results = sqlResult.results();
                processResult = sqlResult.processResult();
            }
            case STEP_TOOL -> {
                stepTitle = "调用工具：" + step.getTaskTitle();
                List<ToolParams> toolParams = new ArrayList<>();
                try {
                    toolParams = JsonUtils.toList(JsonUtils.toJsonString(step.getParams()), ToolParams.class);

                } catch (Exception e) {
                    log.error("解析工具参数失败，参数不合法：{}", JsonUtils.toJsonString(step.getParams()), e);
                    processResult = "解析工具参数失败，参数不合法！参数：" + JsonUtils.toJsonString(step.getParams())
                        + "\n报错信息：" + e.getMessage();
                    TraceInfo traceInfo = new TraceInfo(callId, stepTitle, processResult, true);
                    sendLog(exchange, traceInfo);
                }

                if (!toolParams.isEmpty()) {
                    List<ToolResult> toolResults = new ArrayList<>();
                    for (ToolParams param : toolParams) {
                        ToolResult toolResult = getToolResult(param, toolInfos);
                        TraceInfo traceInfo = new TraceInfo(callId, toolResult.invokeTitle(), toolResult.logContent(),
                            toolResult.isError());
                        toolResults.add(toolResult);
                        sendLog(exchange, traceInfo);
                    }
                    processResult = "\n" + toolResults.stream()
                        .map(toolResult -> String.format("[%s]结果：%s", toolResult.invokeTitle(),
                            toolResult.processResult()))
                        .collect(Collectors.joining("\n"));
                    results = processResult;
                }
            }
            case STEP_FINISH -> {
                List<Integer> stepIds = null;
                try {
                    processResult = step.getParams().get("return_action_step").toString();
                    stepIds = JsonUtils.toList(processResult, Integer.class);
                } catch (Exception e) {
                    String logContent = "结果return_action_step中的步骤id列表解析失败，无法完成任务。返回步骤信息："
                            + JsonUtils.toJsonString(step) + "，错误信息：" + e.getMessage();
                    processResult = logContent;
                    TraceInfo traceInfo = new TraceInfo(callId, stepTitle, logContent, true);
                    sendLog(exchange, traceInfo);
                }

                if (stepIds != null) {
                    // 如果选择的id不在已完成步骤列表中
                    if (!stepResults.keySet().containsAll(stepIds)) {
                        String logContent = "步骤id不在已完成步骤列表中，无法完成任务。返回步骤信息："
                                + JsonUtils.toJsonString(step) + "已完成步骤id列表：" + stepResults.keySet();
                        processResult = logContent;
                        TraceInfo traceInfo = new TraceInfo(callId, stepTitle, logContent, true);
                        sendLog(exchange, traceInfo);
                    } else {
                        String logContent = "任务完成：" + step.getTaskTitle() + "，返回结果步骤："
                                + stepIds.stream().map(id -> stepResults.get(id).getTaskTitle())
                                .collect(Collectors.joining(", "));
                        isFinish = StepStatus.FINISH;
                        results = processResult;
                        TraceInfo traceInfo = new TraceInfo(callId, stepTitle, logContent, false);
                        sendLog(exchange, traceInfo);
                    }
                }

            }
            case STEP_STOP -> {
                processResult = step.getParams().get("stop_reason").asText();
                String logContent = "任务终止，终止原因：" + processResult;
                isFinish = StepStatus.STOP;
                results = logContent;
                TraceInfo traceInfo = new TraceInfo(callId, stepTitle, logContent, false);
                sendLog(exchange, traceInfo);
            }
            default -> throw new MoyeMcpServiceException("未知的下一步动作类型：" + step.getStep());
        }
        String userMsg = String.format("执行任务“%s”的结果：%s", step.getTaskTitle(), processResult);
        return new ProcessResult(times, userMsg, results, isFinish);
    }

    private ToolResult getToolResult(ToolParams toolParams, List<ToolInfo> toolInfos) {
        String toolName = toolParams.getToolName();
        String invokeTitle = toolParams.getToolInvokeTitle();
        JsonNode arguments = toolParams.getToolArguments();
        boolean isError = false;
        String logContent;
        String processResult = "";
        if (StringUtils.isBlank(toolName)) {
            isError = true;
            logContent = "选择工具名称为空，不进行调用";
        } else {
            try {
                Map<String, Object> argumentMap = JsonUtils.jsonNodeToMap(arguments);
                if (StringUtils.isBlank(toolParams.getServerName())) {
                    throw new MoyeMcpServiceException(
                        "调用工具[" + toolName + "]未提供server_name，无法调用，请检查调用参数");
                }
                String url = toolInfos.stream()
                    .filter(toolInfo -> toolInfo.getServerName().equals(toolParams.getServerName()))
                    .findFirst()
                    .map(ToolInfo::getUrl)
                    .orElseThrow(() -> new MoyeMcpServiceException("未找到工具[" + toolName + "]的URL"));
                log.info("请求工具：{} url: {}", toolName, url);
                String toolResult = mcpClientService.callTool(url, toolName, argumentMap);
                processResult = toolResult;
                logContent =
                    "调用工具[" + toolName + "]获取" + toolParams.getToolInvokeTitle()
                        + "成功，返回结果：\n" + toolResult;
                log.info("请求工具[{}]成功，返回结果：{}", toolName, toolResult);
            } catch (Exception e) {
                isError = true;
                log.error("请求工具失败！", e);
                logContent = "调用工具[" + toolName + "]失败，失败原因：" + e.getMessage();
                processResult = logContent;
            }
        }
        return new ToolResult("调用工具：" + invokeTitle, processResult, logContent, isError);
    }

    private record ToolResult(String invokeTitle, String processResult, String logContent, boolean isError) {

    }

    private SqlResult executeSql(SearchStepResponse step, McpSyncServerExchange exchange, TraceInfo stepTraceInfo) {
        boolean isError = false;
        StringBuilder logContent = new StringBuilder();
        StringBuilder processResult = new StringBuilder();
        StringBuilder results = new StringBuilder();
        try {
            String sql = step.getParams().get("sql").asText();

            // sql 可能包含多条查询语句，需要拆分，一次一次查询
            if (StringUtils.isBlank(sql)) {
                throw new MoyeMcpServiceException("执行查询的sql语句为空，请检查大模型输出的下一步动作是否正确");
            }

            String[] sqlList = sql.split(";");
            // 去掉空语句，并且去掉前后的空格
            List<String> validSqlList = Arrays.stream(sqlList).map(String::trim)
                .filter(StringUtils::isNotBlank).toList();

            for (String validSql : validSqlList) {
                log.info("执行查询sql：{}", validSql);
                stepTraceInfo.setContent(String.format("执行查询sql：%s", validSql));
                sendLog(exchange, stepTraceInfo);
                // 如果有excel参与查询，需要将表名替换为s3文件路径
                // validSql = processS3TableSql(validSql, tables);
                Integer connectionId = step.getParams().get("connection_id").asInt();
                SearchResponse response = storageEngineFeign.codeQueryByStorage(connectionId,
                    new CodeSearchParams(validSql, new PageParams(1, searchMaxSize)));
                log.info("请求存储引擎 - sql：\n{}\n查询结果：\n{}", validSql,
                    JsonUtils.toJsonString(response.getData()));

                if (!response.getSuccess()) {
                    isError = true;
                    processResult.append(response.getMessage());
                    logContent.append("\n出错：").append(processResult);
                    break;
                } else {
                    List<Map<String, Object>> responseData = response.getData();
                    //如果查询结果超过限制条数，则只返回前10条数据给大模型；并将此步骤标记为最终结果
                    if (responseData.size() > llmDataLimit) {
                        List<Map<String, Object>> limitData = getLimitData(responseData);
                        String resultPrefix = String.format(
                            "已获取到共计%d条记录%s，前几条实例如下：\n",
                            responseData.size(),
                            responseData.size() == searchMaxSize ? "（因限制，最多可以查询前" + searchMaxSize + "条）"
                                : "");
                        processResult.append(resultPrefix).append(JsonUtils.toJsonString(limitData));
                    } else {
                        processResult.append(JsonUtils.toJsonString(responseData));
                    }

                    logContent.append("\n结果如下：").append(processResult);
                    results.append(JsonUtils.toJsonString(responseData));
                }
            }
        } catch (Exception e) {
            isError = true;
            processResult = new StringBuilder(e.getMessage());
            logContent = new StringBuilder("执行查询报错：" + e.getMessage());
        }
        stepTraceInfo.setContent(logContent.toString());
        stepTraceInfo.setEnd(true);
        sendLog(exchange, stepTraceInfo);
        return new SqlResult(processResult.toString(), logContent.toString(), isError, results.toString());
    }

    private record SqlResult(String processResult, String logContent, boolean isError, String results) {

    }

    /**
     * 处理步骤名称
     *
     * @param originStep 原始名称
     * @return 步骤名称
     */
    public static String normalizeStep(String originStep) {
        List<String> steps = List.of(STEP_CREATE_SQL, STEP_CORRECT_SQL, STEP_TOOL, STEP_CHECK, STEP_STOP, STEP_FINISH);
        for (String step : steps) {
            if (originStep.toUpperCase(Locale.ROOT).contains(step)) {
                return step;
            }
        }
        return originStep;
    }

    private List<Map<String, Object>> getLimitData(List<Map<String, Object>> data) {
        return data.size() >= llmDataLimit ? data.subList(0, llmDataLimit) : data;
    }

    private record ProcessResult(Integer times, String userMsg, String results, StepStatus status) {

    }

    /**
     * <tool_use>
     * <name>fj</name>
     * <arguments>{"condition": "高新分局"}</arguments>
     * </tool_use>
     *
     * @param result 模型返回结果
     * @return 工具名称
     */
    public static String getUseToolName(String result) {
        Matcher matcher = TOOL_USE_PATTERN.matcher(result);
        if (matcher.find()) {
            return matcher.group(1);
        }
        return null;
    }
}

