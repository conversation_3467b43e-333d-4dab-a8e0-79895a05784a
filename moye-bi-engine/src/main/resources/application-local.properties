# 应用名称
spring.application.name=moye-bi-engine
# nacos服务配置
spring.cloud.nacos.server-addr=192.168.211.100:8849
spring.cloud.nacos.username=nacos
spring.cloud.nacos.password=nacos
spring.cloud.nacos.config.namespace=local-zal
spring.cloud.nacos.discovery.namespace=${spring.cloud.nacos.config.namespace}
logging.level.com.alibaba.cloud.nacos=debug
# nacos配置文件配置
moye.merger.config.enable=true
spring.config.import[0]=nacos:moye-bi-engine.properties
spring.config.import[1]=nacos:mysql-moye-v4.properties