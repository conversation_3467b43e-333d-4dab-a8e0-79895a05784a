package com.trs.moye.bi.engine.indicator.processor.completion;

import com.trs.moye.base.common.exception.BizException;
import com.trs.moye.base.common.utils.DateTimeUtils;
import com.trs.moye.base.data.indicator.entity.StatisticPeriod;
import com.trs.moye.base.data.indicator.enums.DataCompletionStrategy;
import com.trs.moye.base.data.indicator.enums.IndicatorPeriodType;
import com.trs.moye.bi.engine.indicator.entity.TimeValuePair;
import com.trs.moye.bi.engine.indicator.util.CalculationUtils;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.TreeSet;
import java.util.function.UnaryOperator;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 数据补齐服务
 * <p>
 * 该服务用于处理时间序列数据，能够根据当前周期、上一周期以及去年同期的数据， 自动填充缺失的时间点，确保当前周期数据在时间维度上的连续性
 * </p>
 *
 * <AUTHOR>
 * @since 2025/07/21
 */
@Slf4j
@Service
public class DataCompletionService {

    /**
     * 对外暴露的主方法，用于执行数据补齐操作
     * <p>
     * 该方法的核心逻辑是找出所有理论上应该存在但实际缺失的时间点， 并为这些时间点创建新的数据行，最终只返回这些被创建出来用于补齐的数据
     * </p>
     *
     * @param currentPeriodData 当前周期的数据列表
     * @param lastPeriodData    上一周期的数据列表
     * @param lastYearData      去年同期的数据列表
     * @param periodType        数据的周期类型 (用于计算同比环比的偏移量)
     * @param startTimeField    数据中表示开始时间的字段名
     * @param endTimeField      数据中表示结束时间的字段名
     * @param dimsFields        用于对数据进行分组的维度字段列表
     * @param strategy          数据补齐策略
     * @return 一个仅包含新增的补齐数据的列表
     */
    public List<Map<String, Object>> completeData(List<Map<String, Object>> currentPeriodData,
        List<Map<String, Object>> lastPeriodData,
        List<Map<String, Object>> lastYearData,
        IndicatorPeriodType periodType,
        String startTimeField,
        String endTimeField,
        List<String> dimsFields,
        DataCompletionStrategy strategy) {

        if (currentPeriodData == null || currentPeriodData.isEmpty()) {
            log.debug("当前周期数据为空，无法进行数据补齐");
            return new ArrayList<>();
        }
        if (Objects.nonNull(strategy)) {
            log.debug("开始数据补齐处理，策略: {}, 当前数据量: {}, 上期数据量: {}, {}: {}",
                strategy.getDescription(),
                currentPeriodData.size(),
                lastPeriodData != null ? lastPeriodData.size() : 0,
                strategy.getDataTypeDescription(),
                lastYearData != null ? lastYearData.size() : 0);
        }

        try {
            List<Map<String, Object>> result = executeDataCompletion(
                currentPeriodData,
                lastPeriodData,
                lastYearData,
                periodType,
                startTimeField,
                endTimeField,
                dimsFields
            );

            log.debug("数据补齐处理完成，策略: {}, 补齐数据量: {}",
                strategy.getDescription(), result.size());
            return result;

        } catch (Exception e) {
            log.error("数据补齐处理失败，策略: {}", strategy.getDescription(), e);
            throw new BizException("数据补齐处理失败，策略: " + strategy.getDescription() + ", 错误: " + e.getMessage(),
                e);
        }
    }

    /**
     * 执行数据补齐的核心逻辑
     *
     * @param currentPeriodData 当前周期的数据列表
     * @param lastPeriodData    上一周期的数据列表
     * @param lastYearData      去年同期的数据列表
     * @param periodType        数据的周期类型
     * @param startTimeField    数据中表示开始时间的字段名
     * @param endTimeField      数据中表示结束时间的字段名
     * @param dimsFields        用于对数据进行分组的维度字段列表
     * @return 补齐的数据列表
     */
    private List<Map<String, Object>> executeDataCompletion(
        List<Map<String, Object>> currentPeriodData,
        List<Map<String, Object>> lastPeriodData,
        List<Map<String, Object>> lastYearData,
        IndicatorPeriodType periodType,
        String startTimeField,
        String endTimeField,
        List<String> dimsFields) {

        // 1. 从数据源直接解析并缓存包含起止时间的 StatisticPeriod 对象
        Map<Map<String, Object>, StatisticPeriod> periodCache = new HashMap<>();
        preParsePeriods(currentPeriodData, startTimeField, endTimeField, periodCache);
        preParsePeriods(lastPeriodData, startTimeField, endTimeField, periodCache);
        preParsePeriods(lastYearData, startTimeField, endTimeField, periodCache);

        // 2. 根据维度字段对数据进行分组
        Map<String, List<Map<String, Object>>> currentGrouped = groupDataByDims(currentPeriodData, dimsFields);
        Map<String, List<Map<String, Object>>> lastPeriodGrouped = groupDataByDims(lastPeriodData, dimsFields);
        Map<String, List<Map<String, Object>>> lastYearGrouped = groupDataByDims(lastYearData, dimsFields);

        List<Map<String, Object>> paddedData = new ArrayList<>();

        // 3. 遍历每个维度组合，进行数据补齐
        for (Map.Entry<String, List<Map<String, Object>>> entry : currentGrouped.entrySet()) {
            String dimensionKey = entry.getKey();
            List<Map<String, Object>> currentGroup = entry.getValue();
            List<Map<String, Object>> lastPeriodGroup = lastPeriodGrouped.getOrDefault(dimensionKey, new ArrayList<>());
            List<Map<String, Object>> lastYearGroup = lastYearGrouped.getOrDefault(dimensionKey, new ArrayList<>());

            // 4. 获取该维度下所有需要展示的时间周期
            Set<StatisticPeriod> allPeriods = getAllPeriods(currentGroup, lastPeriodGroup, lastYearGroup,
                periodCache, periodType);

            // 5. 将当前周期数据按时间点存入Map，方便快速查找
            Map<LocalDateTime, Map<String, Object>> currentTimeMap = groupDataByTime(currentGroup, periodCache);
            Map<LocalDateTime, Map<String, Object>> lastPeriodTimeMap = groupDataByTime(lastPeriodGroup, periodCache);
            Map<LocalDateTime, Map<String, Object>> lastYearTimeMap = groupDataByTime(lastYearGroup, periodCache);

            // 6. 遍历所有时间周期，仅为缺失的数据创建补齐记录
            RecordCreationContext context = new RecordCreationContext(
                currentTimeMap, lastPeriodTimeMap, lastYearTimeMap, paddedData, startTimeField, endTimeField);

            for (StatisticPeriod period : allPeriods) {
                if (!currentTimeMap.containsKey(period.getStartTime())) {
                    // 当前周期数据缺失，创建一条补齐记录
                    Map<String, Object> completedRecord = createCompletedRecord(
                        period, dimensionKey, dimsFields, context);
                    paddedData.add(completedRecord);
                }
            }
        }

        return paddedData;
    }

    /**
     * 从数据源解析开始和结束时间，并缓存为 StatisticPeriod 对象
     *
     * @param data           数据列表
     * @param startTimeField 开始时间字段的名称
     * @param endTimeField   结束时间字段的名称
     * @param periodCache    用于存储解析结果的缓存
     */
    private void preParsePeriods(List<Map<String, Object>> data, String startTimeField, String endTimeField,
        Map<Map<String, Object>, StatisticPeriod> periodCache) {
        if (data == null || data.isEmpty()) {
            return;
        }
        for (Map<String, Object> row : data) {
            if (!periodCache.containsKey(row)) {
                Object startTimeValue = row.get(startTimeField);
                Object endTimeValue = row.get(endTimeField);
                LocalDateTime startTime = parseTime(startTimeValue);
                LocalDateTime endTime = parseTime(endTimeValue);
                // 必须同时成功解析出开始和结束时间
                if (startTime != null && endTime != null) {
                    periodCache.put(row, new StatisticPeriod(startTime, endTime));
                }
            }
        }
    }


    /**
     * 根据维度字段列表对数据进行分组
     *
     * @param data       需���分组的数据列表
     * @param dimsFields 维度字段列表
     * @return 以维度组合字符串为key，数据列表为value的Map
     */
    private Map<String, List<Map<String, Object>>> groupDataByDims(List<Map<String, Object>> data,
        List<String> dimsFields) {
        if (data == null || data.isEmpty()) {
            return new HashMap<>();
        }
        return data.stream().collect(Collectors.groupingBy(row -> {
            StringBuilder keyBuilder = new StringBuilder();
            for (String dimField : dimsFields) {
                Object value = row.get(dimField);
                keyBuilder.append(value != null ? value.toString() : "null").append("_");
            }
            return keyBuilder.toString();
        }));
    }

    /**
     * 将列表数据按开始时间点（Key）映射成Map，以便快速访问
     *
     * @param data        数据列表
     * @param periodCache 周期缓存
     * @return 以开始时间点为key，数据行为value的Map
     */
    private Map<LocalDateTime, Map<String, Object>> groupDataByTime(List<Map<String, Object>> data,
        Map<Map<String, Object>, StatisticPeriod> periodCache) {
        if (data == null || data.isEmpty()) {
            return new HashMap<>();
        }
        Map<LocalDateTime, Map<String, Object>> timeMap = new HashMap<>();
        for (Map<String, Object> row : data) {
            StatisticPeriod period = periodCache.get(row);
            if (period != null) {
                timeMap.put(period.getStartTime(), row);
            }
        }
        return timeMap;
    }

    /**
     * 汇总来自当前周期、上一周期和去年同期的数据，生成一个包含所有时间点的完整周期集合
     *
     * @param currentGroup    当前周期的数据
     * @param lastPeriodGroup 上一周期的数据
     * @param lastYearGroup   去年同期的数据
     * @param periodCache     周期缓存
     * @param periodType      周期类型 (用于计算同比环比的偏移量)
     * @return 一个包含所有时间周期的有序集
     */
    private Set<StatisticPeriod> getAllPeriods(List<Map<String, Object>> currentGroup,
        List<Map<String, Object>> lastPeriodGroup,
        List<Map<String, Object>> lastYearGroup,
        Map<Map<String, Object>, StatisticPeriod> periodCache,
        IndicatorPeriodType periodType) {

        Set<StatisticPeriod> allPeriods = new TreeSet<>(Comparator.comparing(StatisticPeriod::getStartTime));

        // 处理当前周期
        addTransformedPeriods(allPeriods, currentGroup, periodCache, p -> p); // 直接使用原始周期
        // 处理上一周期，将周期整体前推
        addTransformedPeriods(allPeriods, lastPeriodGroup, periodCache,
            p -> shiftPeriod(p, period -> StatisticPeriod.plusPeriod(periodType, 1L, period)));
        // 处理去年同期，将周期整体前推一年
        addTransformedPeriods(allPeriods, lastYearGroup, periodCache,
            p -> shiftPeriod(p, period -> period.plusYears(1)));

        return allPeriods;
    }

    /**
     * 通用的添加转换后时间周期到集合的方法
     *
     * @param periods           用于存放结果的集合
     * @param data              数据源
     * @param periodCache       周期缓存
     * @param periodTransformer 周期转换函数，用于将缓存中的周期转换为目标周期
     */
    private void addTransformedPeriods(Set<StatisticPeriod> periods,
        List<Map<String, Object>> data,
        Map<Map<String, Object>, StatisticPeriod> periodCache,
        UnaryOperator<StatisticPeriod> periodTransformer) {
        if (data == null) {
            return;
        }
        for (Map<String, Object> row : data) {
            StatisticPeriod originalPeriod = periodCache.get(row);
            if (originalPeriod != null) {
                // 应用周期转换逻辑
                periods.add(periodTransformer.apply(originalPeriod));
            }
        }
    }

    /**
     * 对一个 StatisticPeriod 进行整体平移
     *
     * @param originalPeriod 原始周期
     * @param timeShifter    用于平移起始时间的函数
     * @return 平移后的新周期
     */
    private StatisticPeriod shiftPeriod(StatisticPeriod originalPeriod,
        UnaryOperator<LocalDateTime> timeShifter) {
        LocalDateTime newStartTime = timeShifter.apply(originalPeriod.getStartTime());
        LocalDateTime newEndTime = timeShifter.apply(originalPeriod.getEndTime());
        return new StatisticPeriod(newStartTime, newEndTime);
    }

    //记录创建上下文，用于封装 createCompletedRecord 方法的参数
    private record RecordCreationContext(Map<LocalDateTime, Map<String, Object>> currentTimeMap,
                                         Map<LocalDateTime, Map<String, Object>> lastPeriodTimeMap,
                                         Map<LocalDateTime, Map<String, Object>> lastYearTimeMap,
                                         List<Map<String, Object>> paddedData, String startTimeField,
                                         String endTimeField) {

    }

    /**
     * 创建一条用于补齐的记录
     * <p>
     * 现在支持从历史数据中填充实际数据值，而不仅仅是创建空记录
     * </p>
     *
     * @param period       要补齐的时间周期
     * @param dimensionKey 维度组合的key
     * @param dimsFields   维度字段列表
     * @param context      记录创建上下文
     * @return 一条补齐的数据记录
     * <AUTHOR>
     * @since 2025/07/25 11:47:05
     */
    private Map<String, Object> createCompletedRecord(StatisticPeriod period,
        String dimensionKey,
        List<String> dimsFields,
        RecordCreationContext context) {
        Map<String, Object> newRecord = new HashMap<>();

        setTimeFields(newRecord, period, context);
        setDimensionFields(newRecord, dimensionKey, dimsFields);
        setOtherFields(newRecord, context, dimsFields);

        return newRecord;
    }

    /**
     * 设置时间字段
     *
     * @param data    记录
     * @param period  时期
     * @param context 上下文
     */
    private void setTimeFields(Map<String, Object> data, StatisticPeriod period, RecordCreationContext context) {
        data.put(context.startTimeField(), period.getStartTime());
        data.put(context.endTimeField(), period.getEndTime());
    }

    /**
     * 设置维度字段
     *
     * @param data         记录
     * @param dimensionKey 维度键
     * @param dimsFields   DIMS 字段
     */
    private void setDimensionFields(Map<String, Object> data, String dimensionKey, List<String> dimsFields) {
        String[] dimValues = dimensionKey.split("_");
        for (int i = 0; i < dimsFields.size() && i < dimValues.length; i++) {
            String value = dimValues[i];
            if (!"null".equals(value)) {
                data.put(dimsFields.get(i), value);
            }
        }
    }

    /**
     * 设置其他字段
     *
     * @param data       记录
     * @param context    上下文
     * @param dimsFields DIMS 字段
     */
    private void setOtherFields(Map<String, Object> data, RecordCreationContext context, List<String> dimsFields) {
        //TODO: 先注释，暂时不处理其他字段，后续需要再做处理
        Set<String> allFields = collectAllFields(context);
        for (String field : allFields) {
            if (shouldProcessField(field, context, dimsFields, data)) {
                Object fieldValue = determineFieldValue(field, context);
                data.put(field, fieldValue);
            }
        }
    }

    /**
     * 收集所有可能出现的字段
     *
     * @param context 上下文
     * @return {@link Set }<{@link String }>
     */
    private Set<String> collectAllFields(RecordCreationContext context) {
        Set<String> allFields = new HashSet<>();
        context.lastPeriodTimeMap().values().forEach(map -> allFields.addAll(map.keySet()));
        context.lastYearTimeMap().values().forEach(map -> allFields.addAll(map.keySet()));
        context.currentTimeMap().values().forEach(map -> allFields.addAll(map.keySet()));
        context.paddedData().forEach(map -> allFields.addAll(map.keySet()));
        return allFields;
    }

    /**
     * 判断是否应该处理该字段
     *
     * @param field      田
     * @param context    上下文
     * @param dimsFields DIMS 字段
     * @param data       记录
     * @return boolean
     */
    private boolean shouldProcessField(String field, RecordCreationContext context,
        List<String> dimsFields, Map<String, Object> data) {
        return !field.equals(context.startTimeField())
            && !field.equals(context.endTimeField())
            && !dimsFields.contains(field)
            && !data.containsKey(field);
    }

    /**
     * 确定字段值
     *
     * @param field   田
     * @param context 上下文
     * @return {@link Object }
     */
    private Object determineFieldValue(String field, RecordCreationContext context) {
        List<Object> values = collectFieldValues(field, context);
        return calculateFieldValue(values);
    }

    /**
     * 收集字段值，按时间顺序排序
     *
     * @param field   字段名
     * @param context 上下文
     * @return 按时间升序排列的字段值列表
     */
    private List<Object> collectFieldValues(String field, RecordCreationContext context) {
        // 创建时间-值对的列表
        List<TimeValuePair> timeValuePairs = new ArrayList<>();

        // 1. 从 currentTimeMap 收集值，按时间排序
        context.currentTimeMap().entrySet().stream()
            .sorted(Map.Entry.comparingByKey()) // 按 LocalDateTime 升序排序
            .forEach(entry -> {
                LocalDateTime time = entry.getKey();
                Map<String, Object> map = entry.getValue();
                if (map.containsKey(field)) {
                    timeValuePairs.add(new TimeValuePair(time, map.get(field)));
                }
            });

        // 2. 从 paddedData 收集值，按时间排序
        context.paddedData().stream()
            .filter(map -> map.containsKey(field) && map.containsKey(context.startTimeField()))
            .map(map -> {
                Object timeValue = map.get(context.startTimeField());
                LocalDateTime time = parseTime(timeValue);
                return time != null ? new TimeValuePair(time, map.get(field)) : null;
            })
            .filter(Objects::nonNull)
            .sorted(Comparator.comparing(TimeValuePair::time)) // 按时间升序排序
            .forEach(timeValuePairs::add);

        // 3. 对所有时间-值对按时间排序，并提取值
        return timeValuePairs.stream()
            .sorted(Comparator.comparing(TimeValuePair::time))
            .map(TimeValuePair::value)
            .collect(Collectors.toCollection(ArrayList::new));
    }


    /**
     * 计算字段值
     *
     * @param values 值
     * @return {@link Object }
     */
    private Object calculateFieldValue(List<Object> values) {
        if (values.isEmpty()) {
            return "/";
        }

        // 检查所有值是否都为 null 或空字符串
        if (values.stream().allMatch(v -> v == null || (v instanceof String s && s.isEmpty()))) {
            return "/";
        }

        if (values.stream().distinct().count() == 1) {
            // 如果所有值相等
            return values.get(0);
        }

        if (values.stream().allMatch(v -> v instanceof Number || v instanceof String)) {
            return handleNumericOrStringValues(values);
        }

        return "/";
    }

    /**
     * 处理数值型或字符串型的值
     *
     * @param values 值
     * @return {@link Object }
     */
    private Object handleNumericOrStringValues(List<Object> values) {
        List<Double> numericValues = values.stream().map(v -> {
            Number number = CalculationUtils.safeGetNumber(v);
            return number != null ? number.doubleValue() : null;
        }).filter(Objects::nonNull).toList();

        if (numericValues.size() == values.size() && isArithmeticSequence(numericValues)) {
            return calculateArithmeticSequenceNext(values, numericValues);
        }

        return "/";
    }

    /**
     * 计算等差数列的下一个值
     *
     * @param originalValues 原始值
     * @param numericValues  数值
     * @return {@link Object }
     */
    private Object calculateArithmeticSequenceNext(List<Object> originalValues, List<Double> numericValues) {
        double diff = numericValues.get(1) - numericValues.get(0);
        double nextValue = numericValues.get(numericValues.size() - 1) + diff;

        // 检查原始值是否为字符串格式，并还原为对应类型
        if (originalValues.get(0) instanceof String string && string.matches("\\d+")) {
            int originalLength = string.length();
            return originalLength > 1
                ? String.format("%0" + originalLength + "d", (int) nextValue)
                : String.valueOf((int) nextValue);
        }

        return nextValue;
    }

    /**
     * 将Object类型的时间值解析为LocalDateTime
     *
     * @param timeValue 时间值 (可以是LocalDateTime或String)
     * @return 解析后的LocalDateTime，如果解析失败则返回null
     */
    private LocalDateTime parseTime(Object timeValue) {
        try {
            if (timeValue instanceof LocalDateTime v) {
                return v;
            } else if (timeValue instanceof String s) {
                return DateTimeUtils.parse(s);
            }
        } catch (Exception e) {
            log.warn("时间解析失败: {}错误: {}", timeValue, e.getMessage());
        }
        return null;
    }

    private boolean isArithmeticSequence(List<Double> values) {
        if (values.size() < 2) {
            return false;
        }
        double diff = values.get(1) - values.get(0);
        for (int i = 2; i < values.size(); i++) {
            if (values.get(i) - values.get(i - 1) != diff) {
                return false;
            }
        }
        return true;
    }
}
