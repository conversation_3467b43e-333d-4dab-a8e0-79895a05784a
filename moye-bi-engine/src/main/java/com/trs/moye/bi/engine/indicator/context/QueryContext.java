package com.trs.moye.bi.engine.indicator.context;

import com.trs.moye.base.data.connection.entity.DataConnection;
import com.trs.moye.base.data.indicator.entity.IndicatorConfig;
import com.trs.moye.base.data.indicator.entity.IndicatorDataSearchParams;
import com.trs.moye.base.data.indicator.entity.IndicatorPeriodConfigEntity;
import com.trs.moye.base.data.model.entity.AbstractField;
import com.trs.moye.bi.engine.indicator.IndicatorServiceNew.FieldGroups;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import lombok.Builder;
import lombok.Getter;
import org.apache.commons.collections4.CollectionUtils;

/**
 * 查询上下文
 * <p>
 * 封装指标查询所需的所有配置信息，避免重复查询和传参
 *
 * <AUTHOR>
 * @since 2025/07/23
 */
@Getter
@Builder
public class QueryContext {

    /**
     * 数据连接信息
     */
    private final DataConnection dataConnection;

    /**
     * 指标配置
     */
    private final IndicatorConfig indicatorConfig;

    /**
     * 字段组信息
     */
    private final FieldGroups fieldGroups;

    /**
     * 查询参数
     */
    private final IndicatorDataSearchParams request;

    /**
     * 周期配置
     */
    private final IndicatorPeriodConfigEntity periodConfig;

    /**
     * 获取统计维度字段名列表.
     *
     * @return 统计维度字段名列表
     */
    public List<String> getStatisticDimensions() {
        return Optional.ofNullable(indicatorConfig)
            .map(IndicatorConfig::getStatisticStrategyInfo)
            .filter(info -> CollectionUtils.isNotEmpty(info.getStatisticDims()))
            .map(info -> info.getStatisticDims().stream()
                .map(AbstractField::getEnName)
                .toList())
            .orElse(Collections.emptyList());
    }

    /**
     * 检查是否需要历史数据
     *
     * @return 是否需要历史数据
     */
    public boolean needsHistoricalData() {
        return indicatorConfig != null
            && indicatorConfig.getStatisticStrategyInfo() != null
            && periodConfig != null;
    }

    /**
     * 检查是否需要数据补齐
     *
     * @return 是否需要数据补齐
     */
    public boolean needsDataCompletion() {
        return needsHistoricalData();
    }

    /**
     * 获取数据连接ID
     *
     * @return 数据连接ID
     */
    public Integer getConnectionId() {
        return dataConnection.getId();
    }

    /**
     * 获取数据模型ID
     *
     * @return 数据模型ID
     */
    public Integer getDataModelId() {
        return request.getDataModelId();
    }

    /**
     * 获取表名
     *
     * @return 表名
     */
    public String getTableName() {
        return request.getTableName();
    }

    /**
     * 验证上下文完整性
     *
     * @throws IllegalStateException 当上下文不完整时
     */
    public void validate() {
        if (dataConnection == null) {
            throw new IllegalStateException("数据连接不能为空");
        }
        if (request == null) {
            throw new IllegalStateException("查询参数不能为空");
        }
        if (fieldGroups == null) {
            throw new IllegalStateException("字段组不能为空");
        }
    }
}
