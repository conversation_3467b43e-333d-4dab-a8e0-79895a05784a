package com.trs.moye.bi.engine.indicator.calculation;

import com.trs.moye.base.common.utils.DateTimeUtils;
import com.trs.moye.bi.engine.indicator.context.IndicatorDataContext;
import com.trs.moye.bi.engine.indicator.entity.TimedRow;
import java.time.LocalDateTime;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

/**
 * 计算工具类
 *
 * <AUTHOR>
 * @since 2025/07/02
 */
@Slf4j
public final class CalculationUtils {

    private CalculationUtils() {
    }

    /**
     * 将Object类型的时间值安全地解析为LocalDateTime
     *
     * @param timeObj 时间对象
     * @return 解析后的LocalDateTime，如果解析失败则返回null
     */
    public static LocalDateTime parseLocalDateTime(Object timeObj) {
        if (Objects.isNull(timeObj)) {
            return null;
        }
        try {
            if (timeObj instanceof LocalDateTime localDateTime) {
                return localDateTime;
            } else {
                return DateTimeUtils.parse(String.valueOf(timeObj));
            }
        } catch (Exception e) {
            log.error("无法解析时间字段，值为: {}", timeObj, e);
            return null;
        }
    }

    /**
     * 将Object类型的值安全地转换为Number
     *
     * @param value 待转换的值
     * @return 转换后的Number，如果失败则返回null
     */
    public static Number safeGetNumber(Object value) {
        if (value instanceof Number number) {
            return number;
        }
        if (Objects.nonNull(value)) {
            try {
                String stringValue = value.toString().trim();
                if (StringUtils.isBlank(stringValue)) {
                    return null;
                }
                // 尝试解析为整数
                if (stringValue.matches("-?\\d+")) {
                    return Long.parseLong(stringValue);
                }
                // 否则解析为浮点数
                return Double.parseDouble(stringValue);
            } catch (NumberFormatException e) {
                log.warn("无法将'{}'解析为数字", value);
            }
        }
        return null;
    }

    /**
     * 安全地计算两个数值的增长率
     *
     * @param current 当前值
     * @param history 历史值（基准值）
     * @return 格式化为百分比的字符串，如果无法计算则返回空字符串
     */
    public static String calculateSafeRatio(Number current, Number history) {
        if (Objects.isNull(history) || history.doubleValue() == 0) {
            return "/";
        }
        if (Objects.isNull(current)) {
            return "/";
        }
        double ratio = (current.doubleValue() - history.doubleValue()) * 100.0 / history.doubleValue();
        return String.format("%.2f%%", ratio);
    }

    /**
     * 安全地将两个Number类型相加，并根据输入动态决定返回类型（Long或Double）
     *
     * @param a 第一个数字
     * @param b 第二个数字
     * @return 相加后的结果，可能为Long或Double
     */
    public static Number addNumbers(Number a, Number b) {
        if (Objects.isNull(a)) {
            return b;
        }
        if (Objects.isNull(b)) {
            return a;
        }

        // 如果任一操作数是浮点数（或包含小数部分），则结果为Double
        if (a instanceof Double || b instanceof Double || a.doubleValue() % 1 != 0 || b.doubleValue() % 1 != 0) {
            return a.doubleValue() + b.doubleValue();
        }

        // 否则，结果为Long
        return a.longValue() + b.longValue();
    }

    /**
     * 从数据行或已计算值中获取字段值。
     * <p>
     * 优先从 allCalculatedValues 中查找，如果找不到，则从数据行 row 中查找。
     *
     * @param row                 数据行
     * @param fieldName           字段名
     * @param allCalculatedValues 所有已计算值的缓存
     * @param key                 当前行的唯一键
     * @return 字段的值，可能为 null
     */
    public static Object getFieldValue(Map<String, Object> row, String fieldName,
        Map<String, Map<String, Object>> allCalculatedValues, String key) {
        if (allCalculatedValues.containsKey(fieldName)) {
            Object calculatedValue = allCalculatedValues.get(fieldName).get(key);
            // 如果在已计算值中找到了，无论是什么类型，都直接返回
            if (calculatedValue != null) {
                return calculatedValue;
            }
        }
        return row.get(fieldName);
    }

    /**
     * 根据维度对当前周期数据进行分组，并将每个维度的数据转换为按时间排序的列表
     *
     * @param currentPeriodData 本期数据
     * @return {@link Map }<{@link String }, {@link List }<{@link TimedRow }>>
     */
    public static Map<String, List<TimedRow>> getTimedDataForDimension(List<Map<String, Object>> currentPeriodData) {
        Function<Map<String, Object>, String> dimKeyGenerator = IndicatorDataContext.getDimKeyGenerator().orElseThrow();
        String timeField = IndicatorDataContext.getTimeField().orElseThrow();

        return currentPeriodData.stream()
            .collect(Collectors.groupingBy(dimKeyGenerator))
            .entrySet().stream()
            .collect(Collectors.toMap(
                Map.Entry::getKey,
                entry -> entry.getValue().stream()
                    .map(row -> new TimedRow(row, CalculationUtils.parseLocalDateTime(row.get(timeField))))
                    .filter(tr -> Objects.nonNull(tr.time()))
                    .sorted(Comparator.comparing(TimedRow::time))
                    .toList()
            ));
    }

    /**
     * 比较两个值的大小
     *
     * @param v1 第一个值
     * @param v2 第二个值
     * @return 比较结果：负数表示v1小于v2，0表示v1=v2，正数表示v1大于v2
     * @since 2025/07/23 16:18:36
     */
    public static int compareValues(Object v1, Object v2) {
        if (v1 == null && v2 == null) {
            return 0;
        }
        if (v1 == null) {
            return 1;
        }
        if (v2 == null) {
            return -1;
        }

        Integer result = comparePercentages(v1, v2);
        if (result != null) {
            return result;
        }

        result = compareNumbers(v1, v2);
        if (result != null) {
            return result;
        }

        result = compareComparable(v1, v2);
        if (result != null) {
            return result;
        }

        return String.valueOf(v1).compareTo(String.valueOf(v2));
    }

    /**
     * 判断两个排序值是否相同
     *
     * @param value1 第一个值
     * @param value2 第二个值
     * @return 是否相同
     */
    public static boolean isSameValue(Object value1, Object value2) {
        return compareValues(value1, value2) == 0;
    }

    /**
     * 比较百分比字符串
     *
     * @param v1 第一个值
     * @param v2 第二个值
     * @return 比较结果，如果不是百分比字符串则返回null
     */
    private static Integer comparePercentages(Object v1, Object v2) {
        if (v1 instanceof String s1 && s1.trim().endsWith("%") && v2 instanceof String s2 && s2.trim().endsWith("%")) {
            try {
                double d1 = Double.parseDouble(s1.trim().replace("%", ""));
                double d2 = Double.parseDouble(s2.trim().replace("%", ""));
                return Double.compare(d1, d2);
            } catch (NumberFormatException e) {
                // Fallback
            }
        }
        return null;
    }

    /**
     * 比较数字类型
     *
     * @param v1 第一个值
     * @param v2 第二个值
     * @return 比较结果，如果不是数字类型则返回null
     */
    private static Integer compareNumbers(Object v1, Object v2) {
        if (v1 instanceof Number n1 && v2 instanceof Number n2) {
            return Double.compare(n1.doubleValue(), n2.doubleValue());
        }
        return null;
    }

    /**
     * 比较可比较类型
     *
     * @param v1 第一个值
     * @param v2 第二个值
     * @return 比较结果，如果不是可比较类型则返回null
     */
    private static Integer compareComparable(Object v1, Object v2) {
        if (v1 instanceof Comparable value && v1.getClass() == v2.getClass()) {
            try {
                return value.compareTo(v2);
            } catch (ClassCastException e) {
                log.warn("无法比较对象 {} 类型为 {} 和 {} 类型为 {}", v1, v1.getClass(), v2, v2.getClass(), e);
            }
        }
        return null;
    }

}
