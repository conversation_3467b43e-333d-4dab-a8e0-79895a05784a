package com.trs.moye.bi.engine.indicator.calculation;

import com.trs.moye.base.data.service.enums.EvaluateOperator;
import com.trs.moye.bi.engine.indicator.context.IndicatorDataContext;
import com.trs.moye.bi.engine.indicator.context.IndicatorDataContext.CalculationContext;
import com.trs.moye.bi.engine.indicator.entity.TimedRow;
import com.trs.moye.bi.engine.indicator.util.CalculationUtils;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

/**
 * 趋势分析
 *
 * <AUTHOR>
 * @since 2025/07/02
 */
public final class TrendAnalysis {

    private TrendAnalysis() {
    }

    /**
     * 检查一个字段是否连续N个周期呈某种趋势（上升、下降、持平）。
     * <p>
     * <b>处理流程:</b>
     * <ol>
     *   <li>获取当前周期的数据。</li>
     *   <li>按维度对数据进行分组并按时间排序。</li>
     *   <li>对每个维度，从最新的数据点开始，向前比较 `consecutivePeriods` 次。</li>
     *   <li>例如，要判断连续3期增长，需要比较 (T4 vs T3), (T3 vs T2), (T2 vs T1)。</li>
     *   <li>如果所有比较都满足指定的趋势操作符（如 GT 代表上升），则判定为真。</li>
     *   <li>将最终的判断结果（trueText或falseText）赋给该维度下的所有行。</li>
     * </ol>
     *
     * @param targetField        目标字段。
     * @param operator           趋势操作符 (GT: 上升, LT: 下降, EQ: 持平)。
     * @param consecutivePeriods 连续周期数。
     * @param trueText           如果满足趋势，返回此文本。
     * @param falseText          如果不满足趋势，返回此文本。
     * @return 包含结果的Map，值为trueText或falseText。
     */
    public static Map<String, Object> getConsecutiveTrendData(String targetField, EvaluateOperator operator,
        int consecutivePeriods, String trueText, String falseText) {
        return IndicatorDataContext.getCalculationContext().map(context -> {
            if (consecutivePeriods <= 0) {
                return Collections.<String, Object>emptyMap();
            }

            // 步骤 2: 按维度对数据进行分组和时间排序
            Map<String, List<TimedRow>> timedDataByDim = CalculationUtils.getTimedDataForDimension(
                context.currentPeriodData());

            // 步骤 3: 遍历每个维度的数据，计算连续趋势
            Map<String, Object> resultMap = new HashMap<>();
            timedDataByDim.forEach((dimKey, timedRows) ->
                calculateAndRecordTrend(resultMap, timedRows, targetField, operator, consecutivePeriods, trueText,
                    falseText, context)
            );

            return resultMap;
        }).orElse(Collections.emptyMap());
    }

    /**
     * 为单个维度的数据计算并记录连续趋势
     *
     * @param resultMap          用于存储最终结果的Map
     * @param timedRows          按时间排序的单个维度的数据行
     * @param targetField        目标字段
     * @param operator           趋势操作符
     * @param consecutivePeriods 连续周期数
     * @param trueText           趋势满足时返回的文本
     * @param falseText          趋势不满足时返回的文本
     * @param context            计算上下文
     */
    private static void calculateAndRecordTrend(Map<String, Object> resultMap, List<TimedRow> timedRows,
        String targetField, EvaluateOperator operator, int consecutivePeriods, String trueText, String falseText,
        CalculationContext context) {

        // 检查最新数据是否满足连续趋势
        boolean trendMet = false;
        // 需要 (consecutivePeriods + 1) 个数据点来判断 N 个连续周期
        if (timedRows.size() > consecutivePeriods) {
            trendMet = true;
            for (int j = 0; j < consecutivePeriods; j++) {
                int currentIndex = timedRows.size() - 1 - j;
                Map<String, Object> currentRow = timedRows.get(currentIndex).row();
                String currentKey = context.keyGenerator().apply(currentRow);
                Object currentValueObj = CalculationUtils.getFieldValue(currentRow, targetField,
                    context.allCalculatedValues(),
                    currentKey);
                Number currentValue = CalculationUtils.safeGetNumber(currentValueObj);

                Map<String, Object> previousRow = timedRows.get(currentIndex - 1).row();
                String previousKey = context.keyGenerator().apply(previousRow);
                Object previousValueObj = CalculationUtils.getFieldValue(previousRow, targetField,
                    context.allCalculatedValues(), previousKey);
                Number previousValue = CalculationUtils.safeGetNumber(previousValueObj);

                if (previousValue == null || !ConditionalCalculations.evaluate(currentValue, operator,
                    previousValue)) {
                    trendMet = false;
                    break;
                }
            }
        }

        // 将结果应用于此维度的所有行
        String resultText = trendMet ? trueText : falseText;
        for (TimedRow timedRow : timedRows) {
            String rowKey = context.keyGenerator().apply(timedRow.row());
            resultMap.put(rowKey, resultText);
        }
    }

    /**
     * 计算字段在当前周期内的趋势，使用默认符号（↑、↓、—）。
     *
     * @param targetField 目标字段的名称
     * @return 包含趋势字符串的Map，键为组合键，值为趋势字符串
     */
    public static Map<String, Object> getTrendData(String targetField) {
        return getTrendData(targetField, "↑", "↓", "—", "");
    }

    /**
     * 计算字段在当前周期内的趋势，允许自定义符号但无分隔符。
     *
     * @param targetField 目标字段的名称
     * @param upText      上升趋势文本
     * @param downText    下降趋势文本
     * @param equalText   平稳趋势文本
     * @return 包含趋势字符串的Map
     */
    public static Map<String, Object> getTrendData(String targetField, String upText, String downText,
        String equalText) {
        return getTrendData(targetField, upText, downText, equalText, "");
    }

    /**
     * 计算字段在当前周期内的趋势，并使用完整自定义选项。
     *
     * @param targetField 目标字段的名称
     * @param upText      上升趋势文本
     * @param downText    下降趋势文本
     * @param equalText   平稳趋势文本
     * @param separator   趋势符号之间的分隔符
     * @return 包含趋势字符串的Map
     */
    public static Map<String, Object> getTrendData(String targetField, String upText, String downText,
        String equalText, String separator) {
        return IndicatorDataContext.getCalculationContext().map(context -> {
            Map<String, List<TimedRow>> timedDataByDim = CalculationUtils.getTimedDataForDimension(
                context.currentPeriodData());

            Map<String, Object> resultMap = new HashMap<>();

            timedDataByDim.forEach((dimKey, timedRows) -> {
                String trend = calculateTrend(timedRows, targetField, upText, downText, equalText, separator,
                    context);
                for (TimedRow timedRow : timedRows) {
                    String rowKey = context.keyGenerator().apply(timedRow.row());
                    resultMap.put(rowKey, trend);
                }
            });

            return resultMap;
        }).orElse(Collections.emptyMap());
    }


    private static String calculateTrend(List<TimedRow> timedData, String targetField, String upText, String downText,
        String equalText, String separator, CalculationContext context) {
        if (timedData.size() <= 1) {
            return "";
        }

        return IntStream.range(1, timedData.size())
            .mapToObj(i -> {
                Map<String, Object> previousRow = timedData.get(i - 1).row();
                String previousKey = context.keyGenerator().apply(previousRow);
                Object previousValueObj = CalculationUtils.getFieldValue(previousRow, targetField,
                    context.allCalculatedValues(), previousKey);
                Number previousValue = CalculationUtils.safeGetNumber(previousValueObj);

                Map<String, Object> currentRow = timedData.get(i).row();
                String currentKey = context.keyGenerator().apply(currentRow);
                Object currentValueObj = CalculationUtils.getFieldValue(currentRow, targetField,
                    context.allCalculatedValues(),
                    currentKey);
                Number currentValue = CalculationUtils.safeGetNumber(currentValueObj);

                if (previousValue == null || currentValue == null) {
                    return "";
                }

                int comparison = Double.compare(currentValue.doubleValue(), previousValue.doubleValue());
                if (comparison > 0) {
                    return upText;
                } else if (comparison < 0) {
                    return downText;
                } else {
                    return equalText;
                }
            })
            .collect(Collectors.joining(separator));
    }
}
