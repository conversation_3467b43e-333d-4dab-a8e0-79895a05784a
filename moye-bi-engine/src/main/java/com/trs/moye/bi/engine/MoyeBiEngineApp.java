package com.trs.moye.bi.engine;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.EnableMBeanExport;
import org.springframework.jmx.support.RegistrationPolicy;
import org.springframework.scheduling.annotation.EnableScheduling;


/**
 * BI 引擎
 *
 * <AUTHOR>
 * @since 2025/07/09 11:20:46
 */
@EnableMBeanExport(registration = RegistrationPolicy.IGNORE_EXISTING)
@SpringBootApplication
@EnableScheduling
@EnableFeignClients
@MapperScan(basePackages = {"com.trs.**.dao"})
public class MoyeBiEngineApp {

    /**
     * Main
     *
     * @param args args
     */
    public static void main(String[] args) {
        SpringApplication.run(MoyeBiEngineApp.class, args);
    }
}
