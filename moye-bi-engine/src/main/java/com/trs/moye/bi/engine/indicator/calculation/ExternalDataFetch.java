package com.trs.moye.bi.engine.indicator.calculation;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.trs.moye.bi.engine.common.request.CodeSearchParams;
import com.trs.moye.bi.engine.indicator.context.IndicatorDataContext;
import com.trs.moye.bi.engine.indicator.context.IndicatorDataContext.CalculationContext;
import com.trs.moye.bi.engine.indicator.util.CalculationUtils;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;

/**
 * 外部数据获取
 *
 * <AUTHOR>
 * @since 2025/07/02
 */
@Slf4j
public final class ExternalDataFetch {

    private ExternalDataFetch() {
    }

    /**
     * 通过SQL查询从其他表获取数据，并将结果映射回当前数据行
     * <p>
     * <b>处理流程:</b>
     * <ol>
     *   <li>从上下文获取必要的服务（SearchFeign）和数据（当前周期数据）</li>
     *   <li>解析字段映射关系的JSON字符串</li>
     *   <li>根据当前数据行的维度值，构建一个大的`WHERE`子句（例如 `(dim1='a' AND dim2='b') OR (dim1='c' AND dim2='d')`）</li>
     *   <li>使用构建的SQL查询外部表，获取关联数据</li>
     *   <li>将查询到的外部数据（以维度值为键）存入一个临时的Map</li>
     *   <li>遍历当前数据行，根据其维度值从临时Map中查找并匹配外部数据</li>
     * </ol>
     *
     * @param tableName        要查询的外部表名
     * @param fieldMappingJson 字段映射关系的JSON字符串，定义了当前数据维度与外部表维度的对应关系 例如：`{"current_dim_1":"external_dim_a",
     *                         "current_dim_2":"external_dim_b"}`
     * @param targetField      要从外部表获取的目标字段名
     * @return 包含查询结果的Map，键为行的唯一标识，值为从外部查询到的目标字段值
     */
    public static Map<String, Object> getDataBySql(String tableName, String fieldMappingJson, String targetField) {
        return IndicatorDataContext.getCalculationContext().map(context -> {
            if (context.searchFeign() == null || context.connectionId() == null) {
                log.warn("SearchFeign或ConnectionId为空，无法执行外部数据查询");
                return Collections.<String, Object>emptyMap();
            }

            Map<String, String> fieldMapping;
            try {
                fieldMapping = new ObjectMapper().readValue(fieldMappingJson, new TypeReference<>() {
                });
            } catch (Exception e) {
                log.error("解析字段映射关系失败: {}", fieldMappingJson, e);
                return Collections.<String, Object>emptyMap();
            }

            // 步骤 2: 根据维度值构建并执行SQL查询，从外部表获取数据
            Map<String, Object> externalDataMap = queryExternalData(context, tableName, fieldMapping, targetField);

            if (externalDataMap.isEmpty()) {
                return Collections.<String, Object>emptyMap();
            }

            // 步骤 3: 将查询到的外部数据映射到当前数据行
            return mapExternalDataToCurrentRows(context, externalDataMap);
        }).orElse(Collections.emptyMap());
    }

    /**
     * 查询外部数据表
     *
     * @param context      计算上下文
     * @param tableName    外部表名
     * @param fieldMapping 字段映射关系
     * @param targetField  要查询的目标字段
     * @return 一个Map，键是组合的维度值，值是目标字段的值
     */
    private static Map<String, Object> queryExternalData(CalculationContext context, String tableName,
        Map<String, String> fieldMapping, String targetField) {

        String whereClause = buildWhereClause(context, fieldMapping);
        if (whereClause.isEmpty()) {
            return Collections.emptyMap();
        }

        List<String> externalDims = new ArrayList<>(fieldMapping.values());
        String selectFields = String.join(", ", externalDims) + ", " + targetField;
        String sql = String.format("SELECT %s FROM %s WHERE %s", selectFields, tableName, whereClause);

        try {
            CodeSearchParams codeSearchParams = new CodeSearchParams();
            codeSearchParams.setCode(sql);
            List<Map<String, Object>> resultData = context.searchFeign()
                .indicatorQuery(context.connectionId(), codeSearchParams);

            // 使用外部维度字段名从结果中构建key
            Function<Map<String, Object>, String> externalKeyGenerator = row -> {
                Map<String, Object> keyMap = new HashMap<>();
                for (Map.Entry<String, String> entry : fieldMapping.entrySet()) {
                    keyMap.put(entry.getKey(), row.get(entry.getValue()));
                }
                return context.keyGenerator().apply(keyMap);
            };

            return resultData.stream()
                .collect(Collectors.toMap(
                    externalKeyGenerator,
                    row -> row.get(targetField),
                    (existing, replacement) -> existing, // 如果有重复的维度值，保留第一个
                    HashMap::new
                ));
        } catch (Exception e) {
            log.error("通过SQL查询数据失败. SQL: '{}'", sql, e);
            return Collections.emptyMap();
        }
    }

    private static String buildWhereClause(CalculationContext context, Map<String, String> fieldMapping) {
        return context.currentPeriodData().stream()
            .map(row -> {
                String key = context.keyGenerator().apply(row);
                List<String> conditions = new ArrayList<>();
                for (Map.Entry<String, String> entry : fieldMapping.entrySet()) {
                    Object value = CalculationUtils.getFieldValue(row, entry.getKey(), context.allCalculatedValues(),
                        key);
                    if (value != null) {
                        String condition = String.format("%s = '%s'", entry.getValue(),
                            value.toString().replace("'", "''"));
                        conditions.add(condition);
                    } else {
                        // 如果任何一个维度的值为null，则无法为该行构建完整的条件
                        return null;
                    }
                }
                return "(" + String.join(" AND ", conditions) + ")";
            })
            .filter(Objects::nonNull)
            .distinct()
            .collect(Collectors.joining(" OR "));
    }


    /**
     * 将从外部查询到的数据映射回当前数据行
     *
     * @param context         计算上下文
     * @param externalDataMap 从外部查询到的数据
     * @return 一个Map，键是行的唯一标识，值是匹配到的外部数据
     */
    private static Map<String, Object> mapExternalDataToCurrentRows(CalculationContext context,
        Map<String, Object> externalDataMap) {

        Map<String, Object> resultMap = new HashMap<>();

        for (Map<String, Object> row : context.currentPeriodData()) {
            String key = context.keyGenerator().apply(row);
            if (externalDataMap.containsKey(key)) {
                resultMap.put(key, externalDataMap.get(key));
            }
        }
        return resultMap;
    }
}
