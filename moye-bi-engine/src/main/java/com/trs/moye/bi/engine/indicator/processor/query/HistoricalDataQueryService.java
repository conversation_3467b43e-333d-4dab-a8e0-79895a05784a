package com.trs.moye.bi.engine.indicator.processor.query;

import com.trs.moye.base.common.utils.DateTimeUtils;
import com.trs.moye.base.data.indicator.dao.IndicatorPeriodConfigMapper;
import com.trs.moye.base.data.indicator.entity.IndicatorPeriodConfig;
import com.trs.moye.base.data.indicator.entity.IndicatorPeriodConfigEntity;
import com.trs.moye.base.data.indicator.entity.StatisticPeriod;
import com.trs.moye.base.data.indicator.enums.IndicatorPeriodType;
import com.trs.moye.base.data.indicator.utils.IndicatorPeriodConverter;
import com.trs.moye.bi.engine.indicator.config.IndicatorQueryProperties;
import com.trs.moye.bi.engine.indicator.context.IndicatorDataContext;
import com.trs.moye.bi.engine.indicator.context.QueryContext;
import com.trs.moye.bi.engine.indicator.exception.IndicatorQueryException;
import com.trs.moye.bi.engine.indicator.util.IndicatorQueryBuilder;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.ExecutorService;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 历史数据查询服务
 * <p>
 * 负责查询各种历史数据，包括上一周期数据、去年同期数据、去年整年数据等。 支持并行查询以提高性能，并根据配置决定是否启用去年整年数据查询功能。
 * </p>
 *
 * <AUTHOR>
 * @since 2025/07/28
 */
@Slf4j
@Service
public class HistoricalDataQueryService {

    @Resource
    private DataQueryService dataQueryService;
    @Resource
    private ExecutorService indicatorQueryExecutor;
    @Resource
    private IndicatorQueryProperties indicatorQueryProperties;
    @Resource
    private IndicatorPeriodConfigMapper indicatorPeriodConfigMapper;

    /**
     * 并行执行历史数据查询
     * <p>
     * 根据配置决定是否查询去年全年数据，并将不同类型的历史数据分别存储到数据上下文中。 查询包括：
     * <ul>
     *   <li>上一周期数据：用于环比计算</li>
     *   <li>去年同期数据：用于同比计算</li>
     *   <li>去年整年数据：用于年度数据补齐（可选）</li>
     * </ul>
     * </p>
     *
     * @param context 查询上下文，包含当前周期和查询配置
     * @throws IndicatorQueryException 当历史数据查询失败时抛出
     */
    public void executeHistoricalDataQuery(QueryContext context) {
        try {
            log.debug("开始并行获取历史数据, dataModelId: {}", context.getDataModelId());

            IndicatorQueryBuilder queryBuilder = new IndicatorQueryBuilder(
                context.getRequest(), context.getFieldGroups(), context.getDataConnection());

            StatisticPeriod currentPeriod = context.getRequest().getStatisticPeriod();

            // 并行获取历史数据
            CompletableFuture<List<Map<String, Object>>> lastPeriodFuture =
                CompletableFuture.supplyAsync(() -> getLastPeriodData(context, queryBuilder, currentPeriod),
                    indicatorQueryExecutor);

            CompletableFuture<List<Map<String, Object>>> lastYearFuture =
                CompletableFuture.supplyAsync(() -> getLastYearData(context, queryBuilder, currentPeriod),
                    indicatorQueryExecutor);

            // 根据配置决定是否并行获取去年整年数据
            CompletableFuture<List<Map<String, Object>>> lastYearFullFuture = null;
            boolean enableFullYearData = indicatorQueryProperties.isEnableLastYearFullData();

            if (enableFullYearData) {
                log.debug("配置启用去年整年数据获取功能, 开始并行查询去年全年数据, dataModelId: {}",
                    context.getDataModelId());
                lastYearFullFuture = CompletableFuture.supplyAsync(
                    () -> getLastYearFullData(context, queryBuilder, currentPeriod),
                    indicatorQueryExecutor);
            } else {
                log.debug("配置未启用去年整年数据获取功能, 跳过去年全年数据查询, dataModelId: {}",
                    context.getDataModelId());
            }

            // 等待所有历史数据查询完成
            if (lastYearFullFuture != null) {
                CompletableFuture.allOf(lastPeriodFuture, lastYearFuture, lastYearFullFuture).join();
            } else {
                CompletableFuture.allOf(lastPeriodFuture, lastYearFuture).join();
            }

            // 分别存储不同类型的历史数据
            List<Map<String, Object>> lastPeriodData = lastPeriodFuture.get();
            List<Map<String, Object>> lastYearData = lastYearFuture.get();

            // 添加到基础数据上下文
            IndicatorDataContext.addData(lastPeriodData);
            IndicatorDataContext.addData(lastYearData);

            log.debug("历史数据存储完成 - 上期数据: {}, 去年同期数据: {}, dataModelId: {}",
                lastPeriodData.size(), lastYearData.size(), context.getDataModelId());

            // 如果获取了去年整年数据，专门存储到去年全年数据上下文中
            if (lastYearFullFuture != null) {
                List<Map<String, Object>> lastYearFullData = lastYearFullFuture.get();

                // 专门存储去年全年数据，与去年同期数据分开管理
                IndicatorDataContext.setLastYearFullData(lastYearFullData);

                // 同时也添加到基础数据上下文中
                IndicatorDataContext.addData(lastYearFullData);

                log.info("去年整年数据获取并存储完成, 数据量: {}, dataModelId: {}",
                    lastYearFullData.size(), context.getDataModelId());
            }
            log.debug("历史数据查询和存储全部完成, dataModelId: {}", context.getDataModelId());
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            throw IndicatorQueryException.dataQueryError("历史数据查询", e);
        } catch (ExecutionException e) {
            log.error("获取历史数据失败, dataModelId: {}", context.getDataModelId(), e);
            throw IndicatorQueryException.dataQueryError("历史数据查询", e);
        }
    }

    /**
     * 获取上一周期数据
     * <p>
     * 根据当前统计周期和周期配置，计算上一个统计周期的时间范围，并查询对应的历史数据
     * </p>
     *
     * @param context       查询上下文
     * @param queryBuilder  查询构建器
     * @param currentPeriod 当前统计周期
     * @return 上一周期的数据列表，查询失败时返回空列表
     */
    private List<Map<String, Object>> getLastPeriodData(QueryContext context,
        IndicatorQueryBuilder queryBuilder,
        StatisticPeriod currentPeriod) {
        try {
            StatisticPeriod lastPeriod = new StatisticPeriod(
                StatisticPeriod.minusPeriod(context.getPeriodConfig().getPeriodType(), 1L,
                    currentPeriod.getStartTime()),
                currentPeriod.getStartTime());

            return getDataByPeriod(context.getConnectionId(), context.getTableName(),
                lastPeriod, queryBuilder);
        } catch (Exception e) {
            log.warn("获取上一周期数据失败, 将返回空数据", e);
            return Collections.emptyList();
        }
    }

    /**
     * 获取去年同期数据
     * <p>
     * 根据当前统计周期，计算去年对应的同期时间范围，并查询对应的历史数据
     * </p>
     *
     * @param context       查询上下文
     * @param queryBuilder  查询构建器
     * @param currentPeriod 当前统计周期
     * @return 去年同期的数据列表，查询失败时返回空列表
     */
    private List<Map<String, Object>> getLastYearData(QueryContext context,
        IndicatorQueryBuilder queryBuilder,
        StatisticPeriod currentPeriod) {
        try {
            StatisticPeriod lastYear = new StatisticPeriod(
                currentPeriod.getStartTime().minusYears(1),
                currentPeriod.getEndTime().minusYears(1));

            return getDataByPeriod(context.getConnectionId(), context.getTableName(),
                lastYear, queryBuilder);
        } catch (Exception e) {
            log.warn("获取去年同期数据失败, 将返回空数据", e);
            return Collections.emptyList();
        }
    }

    /**
     * 获取去年整年的完整数据
     * <p>
     * 基于年度周期配置，获取相对于当前统计周期的去年一整年的完整历史数据。 该方法用于支持年度数据补齐功能，需要系统配置启用才会被调用。
     * </p>
     *
     * @param context       查询上下文
     * @param queryBuilder  查询构建器
     * @param currentPeriod 当前统计周期
     * @return 去年整年的数据列表，查询失败时返回空列表
     */
    private List<Map<String, Object>> getLastYearFullData(QueryContext context,
        IndicatorQueryBuilder queryBuilder,
        StatisticPeriod currentPeriod) {
        try {
            log.debug("开始获取去年整年数据, 当前周期: {} - {}",
                DateTimeUtils.formatStr(currentPeriod.getStartTime()),
                DateTimeUtils.formatStr(currentPeriod.getEndTime()));

            // 获取年度周期配置
            IndicatorPeriodConfigEntity yearPeriodEntity = indicatorPeriodConfigMapper.selectByPeriodType(
                IndicatorPeriodType.YEARLY.name());
            if (yearPeriodEntity == null) {
                log.warn("未找到年度周期配置，使用默认配置");
                yearPeriodEntity = new IndicatorPeriodConfigEntity(
                    IndicatorPeriodType.YEARLY,
                    IndicatorPeriodType.YEARLY.getDefaultConfig(),
                    null);
            }

            IndicatorPeriodConfig<?> yearConfig = yearPeriodEntity.getConfig();

            // 使用工具类和年度配置计算去年整年的时间范围
            StatisticPeriod lastYearFull = IndicatorPeriodConverter.calculateLastYearFullPeriod(
                currentPeriod, yearConfig);

            log.debug("去年整年时间范围: {} - {}",
                DateTimeUtils.formatStr(lastYearFull.getStartTime()),
                DateTimeUtils.formatStr(lastYearFull.getEndTime()));

            List<Map<String, Object>> result = getDataByPeriod(context.getConnectionId(),
                context.getTableName(), lastYearFull, queryBuilder);

            log.debug("去年整年数据获取成功, 数据量: {}", result.size());
            return result;

        } catch (Exception e) {
            log.warn("获取去年整年数据失败, 将返回空数据", e);
            return Collections.emptyList();
        }
    }

    /**
     * 根据周期获取数据
     * <p>
     * 使用查询构建器生成指定时间周期的SQL查询语句，并执行查询获取数据
     * </p>
     *
     * @param connectionId 数据库连接ID
     * @param tableName    数据表名
     * @param period       查询的时间周期
     * @param queryBuilder 查询构建器
     * @return 指定周期的数据列表
     * @throws IndicatorQueryException 当周期数据查询失败时抛出
     */
    private List<Map<String, Object>> getDataByPeriod(Integer connectionId, String tableName,
        StatisticPeriod period, IndicatorQueryBuilder queryBuilder) {
        try {
            String sql = queryBuilder.buildHistoricalQuerySql(tableName, period);
            return dataQueryService.executeQuery(connectionId, sql);
        } catch (Exception e) {
            log.error("查询周期数据失败, 表名: {}, 周期: {} - {}", tableName,
                DateTimeUtils.formatStr(period.getStartTime()), DateTimeUtils.formatStr(period.getEndTime()), e);
            throw IndicatorQueryException.dataQueryError("周期数据查询", e);
        }
    }
}
