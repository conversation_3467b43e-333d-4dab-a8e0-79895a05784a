package com.trs.moye.bi.engine.indicator.util;

import com.trs.moye.base.data.indicator.entity.IndicatorSortField;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;

/**
 * 指标数据排序器
 *
 * <AUTHOR>
 * @since 2025/07/13 14:27:39
 */
@Slf4j
public class IndicatorDataSorter {

    private IndicatorDataSorter() {
    }

    /**
     * 排序
     *
     * @param data      数据
     * @param sortField 排序字段
     * @return {@link List }<{@link Map }<{@link String }, {@link Object }>>
     * <AUTHOR>
     * @since 2025/07/13 14:27:43
     */
    public static List<Map<String, Object>> sort(List<Map<String, Object>> data, IndicatorSortField sortField) {
        if (sortField == null || sortField.getField() == null || sortField.getOrder() == null) {
            return data;
        }

        List<Map<String, Object>> sortedData = new ArrayList<>(data);

        Comparator<Map<String, Object>> comparator = switch (sortField.getOrder()) {
            case ASC -> createComparator(sortField.getField(), false);
            case DESC -> createComparator(sortField.getField(), true);
            case FIXED -> createFixedOrderComparator(sortField.getField(), sortField.getFixedOrder());
        };

        sortedData.sort(comparator);

        return sortedData;
    }

    private static Comparator<Map<String, Object>> createComparator(String fieldName, boolean descending) {
        return (m1, m2) -> {
            Object v1 = m1.get(fieldName);
            Object v2 = m2.get(fieldName);
            int comparison = CalculationUtils.compareValues(v1, v2);
            return descending ? -comparison : comparison;
        };
    }

    private static Comparator<Map<String, Object>> createFixedOrderComparator(String fieldName,
        List<String> fixedOrder) {
        if (fixedOrder == null || fixedOrder.isEmpty()) {
            return (m1, m2) -> 0;
        }
        Map<String, Integer> orderMap = new HashMap<>();
        for (int i = 0; i < fixedOrder.size(); i++) {
            orderMap.put(fixedOrder.get(i), i);
        }
        return (m1, m2) -> {
            Object v1 = m1.get(fieldName);
            Object v2 = m2.get(fieldName);
            int index1 = orderMap.getOrDefault(String.valueOf(v1), Integer.MAX_VALUE);
            int index2 = orderMap.getOrDefault(String.valueOf(v2), Integer.MAX_VALUE);
            return Integer.compare(index1, index2);
        };
    }

}
