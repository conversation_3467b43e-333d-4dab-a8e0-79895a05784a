package com.trs.moye.bi.engine.indicator.calculation;

import com.trs.moye.base.data.indicator.entity.StatisticPeriod;
import com.trs.moye.base.data.indicator.enums.IndicatorSearchRange;
import com.trs.moye.bi.engine.indicator.context.IndicatorDataContext;
import com.trs.moye.bi.engine.indicator.entity.TimedRow;
import com.trs.moye.bi.engine.indicator.util.CalculationUtils;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 数据聚合
 *
 * <AUTHOR>
 * @since 2025/07/02
 */
public final class DataAggregation {

    private static final Pattern TEMPLATE_PATTERN = Pattern.compile("#\\{([^}]+)}");

    private DataAggregation() {
    }

    /**
     * 计算字段在当前周期内的占比
     * <p>
     * <b>处理流程:</b>
     * <ol>
     *   <li>获取当前周期的数据</li>
     *   <li>根据时间字段对数据进行分组</li>
     *   <li>在每个时间分组内计算目标字段的总和</li>
     *   <li>遍历每一行，用当前行的值除以同一时间分组的总和，计算百分比</li>
     *   <li>将结果格式化为 "xx.xx%" 的字符串</li>
     * </ol>
     * <p>
     *
     * @param targetField 目标字段的名称
     * @return 包含占比的Map，值为格式化后的百分比字符串
     */
    public static Map<String, Object> getRatioData(String targetField) {
        return IndicatorDataContext.getCalculationContext()
            .map(context -> calculateRatioWithTimeGrouping(context, targetField))
            .orElse(Collections.emptyMap());
    }

    /**
     * 按时间分组计算占比的核心逻辑
     *
     * @param context     上下文
     * @param targetField 目标字段
     * @return {@link Map }<{@link String }, {@link Object }>
     */
    private static Map<String, Object> calculateRatioWithTimeGrouping(
        IndicatorDataContext.CalculationContext context, String targetField) {

        String timeField = context.timeField();
        if (timeField == null || context.currentPeriodData().isEmpty()) {
            return Collections.emptyMap();
        }

        // 预计算所有行数据
        List<RowData> rowDataList = preprocessRowData(context, targetField, timeField);

        // 按时间分组并计算总和
        Map<String, TimeGroupData> timeGroups = groupByTimeAndCalculateTotal(rowDataList);

        // 计算占比结果
        return calculateRatioResults(timeGroups, rowDataList.size());
    }

    /**
     * 计算占比结果
     *
     * @param timeGroups   时间组
     * @param expectedSize 预期尺寸
     * @return {@link Map }<{@link String }, {@link Object }>
     */
    private static Map<String, Object> calculateRatioResults(
        Map<String, TimeGroupData> timeGroups, int expectedSize) {

        Map<String, Object> resultMap = new HashMap<>(expectedSize);

        timeGroups.forEach((timeValue, groupData) -> {
            double total = groupData.total;
            groupData.rows.forEach(rowData -> {
                String ratioValue = calculateSingleRatio(rowData.value, total);
                resultMap.put(rowData.key, ratioValue);
            });
        });

        return resultMap;
    }

    /**
     * 计算单个记录的占比
     *
     * @param value 值
     * @param total 总数
     * @return {@link String }
     */
    private static String calculateSingleRatio(Number value, double total) {
        if (value == null || total == 0.0) {
            return "0.00%";
        }
        double percentage = (value.doubleValue() / total) * 100;
        return String.format("%.2f%%", percentage);
    }

    /**
     * 计算字段在当前周期内的排名
     * <p>
     * <b>处理流程:</b>
     * <ol>
     *   <li>获取当前周期的数据</li>
     *   <li>根据时间字段对数据进行分组</li>
     *   <li>在每个时间分组内将所有行的目标字段值提取出来，并按降序排序</li>
     *   <li>遍历排序后的列表，根据值和位置确定排名（处理并列情况）</li>
     *   <li>将排名结果存入Map</li>
     * </ol>
     *
     * @param targetField 目标字段的名称
     * @return 包含排名的Map，值为排名整数
     */
    public static Map<String, Object> getRankData(String targetField) {
        return IndicatorDataContext.getCalculationContext()
            .map(context -> calculateRankWithTimeGrouping(context, targetField))
            .orElse(Collections.emptyMap());
    }

    /**
     * 按时间分组计算排名的核心逻辑
     *
     * @param context     上下文
     * @param targetField 目标字段
     * @return {@link Map }<{@link String }, {@link Object }>
     */
    private static Map<String, Object> calculateRankWithTimeGrouping(
        IndicatorDataContext.CalculationContext context, String targetField) {

        String timeField = context.timeField();
        if (timeField == null || context.currentPeriodData().isEmpty()) {
            return Collections.emptyMap();
        }

        // 预计算所有行数据
        List<RowData> rowDataList = preprocessRowData(context, targetField, timeField);

        // 按时间分组
        Map<String, List<RowData>> timeGroups = groupByTime(rowDataList);

        // 计算排名结果
        return calculateRankResults(timeGroups, rowDataList.size());
    }

    /**
     * 计算排名结果
     *
     * @param timeGroups   时间组
     * @param expectedSize 预期
     * @return {@link Map }<{@link String }, {@link Object }>
     */
    private static Map<String, Object> calculateRankResults(
        Map<String, List<RowData>> timeGroups, int expectedSize) {

        Map<String, Object> resultMap = new HashMap<>(expectedSize);

        timeGroups.forEach((timeValue, groupRows) -> {
            // 排序：降序，null值排在最后
            groupRows.sort(DataAggregation::compareRowDataForRanking);

            // 计算排名
            assignRanksToGroup(groupRows, resultMap);
        });

        return resultMap;
    }

    /**
     * 为分组内的行分配排名
     *
     * @param groupRows 分组行
     * @param resultMap 结果
     */
    private static void assignRanksToGroup(List<RowData> groupRows, Map<String, Object> resultMap) {
        int rank = 0;
        Number previousValue = null;

        for (int i = 0; i < groupRows.size(); i++) {
            RowData rowData = groupRows.get(i);
            Number currentValue = rowData.value;

            if (currentValue == null) {
                resultMap.put(rowData.key, null);
                continue;
            }

            if (!currentValue.equals(previousValue)) {
                rank = i + 1; // 排名从1开始
            }

            resultMap.put(rowData.key, rank);
            previousValue = currentValue;
        }
    }

    /**
     * 比较两个RowData用于排名排序
     *
     * @param r1 r1
     * @param r2 R2
     * @return int
     */
    private static int compareRowDataForRanking(RowData r1, RowData r2) {
        // null值排在最后
        if (r1.value == null && r2.value == null) {
            return 0;
        }
        if (r1.value == null) {
            return 1;
        }
        if (r2.value == null) {
            return -1;
        }
        // 降序排序
        return Double.compare(r2.value.doubleValue(), r1.value.doubleValue());
    }

    /**
     * 横向合计，对当前周期内每个维度分组的数据进行指定字段的求和
     * <p>
     * <b>处理流程:</b>
     * <ol>
     *   <li>获取当前周期的数据</li>
     *   <li>按维度对数据进行分组，并对每个分组内的目标字段求和</li>
     *   <li>将每个维度的合计结果赋给该维度下的所有行</li>
     * </ol>
     *
     * @param targetField 要合计的目标字段
     * @return 包含合计值的Map
     */
    public static Map<String, Object> horizontalSum(String targetField) {
        return IndicatorDataContext.getCalculationContext().map(context -> {
            if (context.dimKeyGenerator() == null) {
                return Collections.<String, Object>emptyMap();
            }

            // 步骤 1: 一次遍历计算每个维度的总和
            Map<String, Number> sumByDim = context.currentPeriodData().stream()
                .collect(Collectors.groupingBy(
                    context.dimKeyGenerator(),
                    Collectors.reducing(null,
                        row -> {
                            String key = context.keyGenerator().apply(row);
                            Object value = CalculationUtils.getFieldValue(row, targetField,
                                context.allCalculatedValues(),
                                key);
                            return CalculationUtils.safeGetNumber(value);
                        },
                        CalculationUtils::addNumbers)
                ));

            // 步骤 2: 一次遍历构建最终结果
            return context.currentPeriodData().stream()
                .collect(Collectors.toMap(
                    context.keyGenerator(),
                    row -> (Object) sumByDim.get(context.dimKeyGenerator().apply(row)),
                    (existing, replacement) -> existing,
                    HashMap::new
                ));
        }).orElse(Collections.emptyMap());
    }

    /**
     * 纵向合计，根据维度对目标字段进行求和，并生成新的合计行
     *
     * @param targetField     要求和的目标字段
     * @param dimensionFields 用于分组的维度字段
     * @param newFieldName    新合计行中用于标识的字段名
     * @param newFieldValue   新合计行中标识字段的值
     * @return 因为此方法直接修改了上下文数据，所以返回一个空Map
     */
    public static Map<String, Object> verticalSum(String targetField, List<String> dimensionFields,
        String newFieldName, String newFieldValue) {
        // 步骤 1: 将单键值对的标识字段转换为Map，以便复用核心逻辑
        Map<String, String> newFieldsAndValues = new HashMap<>();
        newFieldsAndValues.put(newFieldName, newFieldValue);

        // 步骤 2: 调用扩展的纵向合计方法
        return verticalSumEx(targetField, dimensionFields, newFieldsAndValues);
    }

    /**
     * 纵向合计，根据维度对目标字段进行求和，并生成新的合计行
     * <p>
     * 默认情况下，此方法不会为只包含一行的分组创建合计行
     *
     * @param targetField        要求和的目标字段
     * @param dimensionFields    用于分组的维度字段
     * @param newFieldsAndValues 一个Map，定义了合计行中标识字段的名称及其值（支持字段引用模板）
     * @return 返回一个空Map，因为此方法通过修改计算上下文来直接更新数据集
     */
    public static Map<String, Object> verticalSumEx(String targetField, List<String> dimensionFields,
        Map<String, String> newFieldsAndValues) {
        // 调用完整版方法，并为 summarizeSingleRowGroups 参数传入默认值 false
        return verticalSumEx(targetField, dimensionFields, newFieldsAndValues, false);
    }

    /**
     * 纵向合计，根据维度对目标字段进行求和，生成新的合计行，支持多个自定义标识字段和单行合计配置
     * <p>
     * <b>核心流程:</b>
     * <ol>
     *   <li><b>数据分组:</b> 使用维度字段对当前周期的数据进行分组</li>
     *   <li><b>创建合计行:</b> 遍历每个分组，根据 {@code summarizeSingleRowGroups} 参数的配置决定是否为其生成合计行如果分组内数据多于一行或配置为合计单行，则调用 {@code createSummaryRow} 生成合计行</li>
     *   <li><b>合并数据:</b> 将所有新生成的合计行通过 {@code mergeOrAddRows} 方法，高效地合并回主数据集（包括当前周期和全周期数据）</li>
     * </ol>
     *
     * @param targetField              要求和的目标字段
     * @param dimensionFields          用于分组的维度字段
     * @param newFieldsAndValues       一个Map，定义了合计行中标识字段的名称及其值（支持字段引用模板）
     * @param summarizeSingleRowGroups 如果为 true，则即使分组只有一个成员也会为其生成合计行默认为 false
     * @return 返回一个空Map，因为此方法通过修改计算上下文来直接更新数据集
     */
    public static Map<String, Object> verticalSumEx(String targetField, List<String> dimensionFields,
        Map<String, String> newFieldsAndValues, boolean summarizeSingleRowGroups) {
        return IndicatorDataContext.getCalculationContext().map(context -> {
            List<Map<String, Object>> currentPeriodData = context.currentPeriodData();
            if (currentPeriodData.isEmpty()) {
                return Collections.<String, Object>emptyMap();
            }

            // 1. 数据分组
            Function<Map<String, Object>, String> groupingKeyGenerator = row -> dimensionFields.stream()
                .map(field -> String.valueOf(row.get(field)))
                .collect(Collectors.joining("_"));

            Map<String, List<Map<String, Object>>> groupedData = currentPeriodData.stream()
                .collect(Collectors.groupingBy(groupingKeyGenerator));

            // 2. 创建合计行
            List<Map<String, Object>> newOrUpdatedRows = new ArrayList<>();
            for (List<Map<String, Object>> group : groupedData.values()) {
                // 根据配置决定是否处理该分组
                if (group.size() > 1 || summarizeSingleRowGroups) {
                    newOrUpdatedRows.add(createSummaryRow(group, targetField, newFieldsAndValues, context));
                }
            }

            // 3. 合并数据
            if (!newOrUpdatedRows.isEmpty()) {
                mergeOrAddRows(context, newOrUpdatedRows, targetField, dimensionFields, newFieldsAndValues.keySet());
            }

            return Collections.<String, Object>emptyMap();
        }).orElse(Collections.emptyMap());
    }

    /**
     * 根据分组数据创建单条合计行
     * <p>
     * <b>核心流程:</b>
     * <ol>
     *   <li><b>查找共通字段:</b> 通过一次遍历高效地找出在分组内所有行都具有相同值的字段这些共通字段的值将被复制到合计行中，以保留分组的上下文信息</li>
     *   <li><b>计算总和:</b> 对分组内所有行的目标字段值进行求和</li>
     *   <li><b>填充合计值:</b> 将计算出的总和赋给合计行中的目标字段</li>
     *   <li><b>解析标识字段:</b> 处理自定义标识字段它会解析模板字符串（如 "#{field}_合计"），将 `#{...}` 占位符替换为分组内第一行对应字段的实际值，然后将最终生成的字符串填入合计行</li>
     * </ol>
     *
     * @param groupRows          一个分组的数据行列表
     * @param targetField        要求和的目标字段
     * @param newFieldsAndValues 定义标识字段及其模板的Map
     * @param context            计算上下文，用于获取已计算的值
     * @return 一个新创建的、代表合计的Map对象
     */
    private static Map<String, Object> createSummaryRow(List<Map<String, Object>> groupRows,
        String targetField, Map<String, String> newFieldsAndValues,
        IndicatorDataContext.CalculationContext context) {

        if (groupRows.isEmpty()) {
            return new HashMap<>();
        }

        // 1. 计算合计值
        Number sum = groupRows.stream()
            .map(row -> CalculationUtils.safeGetNumber(
                CalculationUtils.getFieldValue(row, targetField, context.allCalculatedValues(),
                    context.keyGenerator().apply(row))))
            .reduce(null, CalculationUtils::addNumbers);

        // 2. 高效查找共通字段
        Map<String, Object> firstRow = groupRows.get(0);
        Set<String> commonKeys = new HashSet<>(firstRow.keySet());
        commonKeys.remove(targetField); // 目标字段特殊处理，不参与共通性检查

        for (int i = 1; i < groupRows.size(); i++) {
            Map<String, Object> currentRow = groupRows.get(i);
            // 使用Iterator避免在循环中修改Set时的ConcurrentModificationException
            commonKeys.removeIf(key -> !Objects.equals(firstRow.get(key), currentRow.get(key)));
        }
        Map<String, Object> newRow = new HashMap<>();
        // 3. 填充合计值
        newRow.put(targetField, sum);
        for (String key : commonKeys) {
            newRow.put(key, firstRow.get(key));
        }
        // 4. 填充自定义标识字段，支持模板
        newFieldsAndValues.forEach((newField, valueTemplate) -> {
            Matcher matcher = TEMPLATE_PATTERN.matcher(valueTemplate);
            StringBuilder finalValue = new StringBuilder();
            while (matcher.find()) {
                String sourceField = matcher.group(1);
                Object fieldValue = firstRow.get(sourceField);
                String replacement = fieldValue != null ? fieldValue.toString() : "";
                matcher.appendReplacement(finalValue, Matcher.quoteReplacement(replacement));
            }
            matcher.appendTail(finalValue);
            newRow.put(newField, finalValue.toString());
        });

        return newRow;
    }

    /**
     * 将新生成的合计行合并或添加到数据集中
     * <p>
     * 此方法旨在避免在大型数据集中进行昂贵的重复扫描
     * <p>
     * <b>核心流程:</b>
     * <ol>
     *   <li><b>构建查找Map:</b> 对当前周期和全周期数据集进行一次预处理，创建一个基于唯一键的查找Map这个唯一键由维度字段和已解析的标识字段的值组合而成，可以快速定位到任何已存在的合计行</li>
     *   <li><b>遍历并合并:</b> 遍历每一个新生成的合计行：
     *     <ul>
     *       <li>为新行计算其唯一键</li>
     *       <li>使用该键在查找Map中进行O(1)复杂度的查找，判断是否存在匹配的旧合计行</li>
     *       <li>如果存在，则只更新旧行中的目标字段值</li>
     *       <li>如果不存在，则将新行作为一个全新的合计行添加到数据集中，并同步更新查找Map</li>
     *     </ul>
     *   </li>
     * </ol>
     *
     * @param context                 计算上下文，包含需要更新的数据列表
     * @param newOrUpdatedRows        本次计算生成的所有新合计行
     * @param targetField             被合计的目标字段名
     * @param dimensionFields         用于分组的维度字段列表
     * @param summaryIdentifierFields 用于唯一标识合计行的字段名集合
     */
    private static void mergeOrAddRows(IndicatorDataContext.CalculationContext context,
        List<Map<String, Object>> newOrUpdatedRows, String targetField, List<String> dimensionFields,
        Set<String> summaryIdentifierFields) {

        // Key: 维度字段和标识字段组合的唯一键, Value: 对应的行
        Function<Map<String, Object>, String> summaryKeyGenerator = row -> {
            String dimensionKey = dimensionFields.stream()
                .map(field -> String.valueOf(row.get(field)))
                .collect(Collectors.joining("_"));
            String identifierKey = summaryIdentifierFields.stream()
                .map(field -> String.valueOf(row.get(field)))
                .collect(Collectors.joining("_"));
            return dimensionKey + "||" + identifierKey;
        };

        // 1. 构建现有合计行的快速查找Map
        Map<String, Map<String, Object>> existingCurrentSummaries = context.currentPeriodData().stream()
            .filter(row -> summaryIdentifierFields.stream().anyMatch(row::containsKey)) // 粗略过滤，提高效率
            .collect(Collectors.toMap(summaryKeyGenerator, Function.identity(), (a, b) -> a));

        Map<String, Map<String, Object>> existingAllSummaries = context.allData().stream()
            .filter(row -> summaryIdentifierFields.stream().anyMatch(row::containsKey))
            .collect(Collectors.toMap(summaryKeyGenerator, Function.identity(), (a, b) -> a));

        // 2. 遍历新行并合并
        for (Map<String, Object> newRow : newOrUpdatedRows) {
            String key = summaryKeyGenerator.apply(newRow);

            // 合并到当前周期数据
            Map<String, Object> existingCurrent = existingCurrentSummaries.get(key);
            if (existingCurrent != null) {
                existingCurrent.put(targetField, newRow.get(targetField));
            } else {
                context.currentPeriodData().add(newRow);
                existingCurrentSummaries.put(key, newRow); // 更新map以处理后续可能重复的新行
            }

            // 合并到所有数据
            Map<String, Object> existingAll = existingAllSummaries.get(key);
            if (existingAll != null) {
                existingAll.put(targetField, newRow.get(targetField));
            } else {
                context.allData().add(newRow);
                existingAllSummaries.put(key, newRow);
            }
        }
    }

    /**
     * 用于排序的辅助内部类，保存了原始索引和值
     *
     * @param index index
     * @param value 值
     * <AUTHOR>
     * @since 2025/06/26 17:51:17
     */
    private record IndexedValue(int index, Number value) {

    }


    /**
     * 计算字段在当前周期内的日均值
     *
     * @param targetField 目标字段的名称
     * @return 包含日均值的Map，键为组合键，值为日均值
     */
    public static Map<String, Object> dailyAverage(String targetField) {
        return dailyAverage(targetField, null, 2);
    }

    /**
     * 计算字段在指定周期内的日均值
     *
     * @param targetField 目标字段的名称
     * @param periodType  周期类型 (e.g., "LAST_YEAR")
     * @return 包含日均值的Map，键为组合键，值为日均值
     */
    public static Map<String, Object> dailyAverage(String targetField, String periodType) {
        return dailyAverage(targetField, periodType, 2);
    }


    /**
     * 计算字段在指定周期内的日均值
     *
     * @param targetField 目标字段的名称
     * @param periodType  周期类型 (e.g., "LAST_YEAR")
     * @param precision   精度
     * @return 包含日均值的Map，键为组合键，值为日均值
     */
    public static Map<String, Object> dailyAverage(String targetField, String periodType, int precision) {
        return IndicatorDataContext.getCalculationContext().map(context -> {
            StatisticPeriod statisticPeriod = context.statisticPeriod();

            StatisticPeriod targetPeriod = statisticPeriod;
            if (IndicatorSearchRange.LAST_YEAR.name().equalsIgnoreCase(periodType)) {
                targetPeriod = new StatisticPeriod(statisticPeriod.getStartTime().minusYears(1),
                    statisticPeriod.getEndTime().minusYears(1));
            }
            long days = targetPeriod.getEndTime().toLocalDate().toEpochDay() - targetPeriod.getStartTime()
                .toLocalDate().toEpochDay();
            if (days <= 0) {
                days = 1; // 避免除以零
            }
            final long finalDays = days;

            Map<String, Object> resultMap = new HashMap<>();
            for (Map<String, Object> row : context.currentPeriodData()) {
                String key = context.keyGenerator().apply(row);
                Object valueObj = CalculationUtils.getFieldValue(row, targetField, context.allCalculatedValues(),
                    key);
                Number value = CalculationUtils.safeGetNumber(valueObj);
                if (value != null) {
                    double average = value.doubleValue() / finalDays;
                    BigDecimal bd = new BigDecimal(Double.toString(average));
                    bd = bd.setScale(precision, RoundingMode.HALF_UP);
                    resultMap.put(key, bd.doubleValue());
                } else {
                    resultMap.put(key, null);
                }
            }
            return resultMap;
        }).orElse(Collections.emptyMap());
    }

    /**
     * 计算字段的累计和
     * <p>
     * <b>处理逻辑:</b>
     * <ol>
     *   <li>如果当前行的 value 值为 null 或空值，该行对应的累计值设置为 null</li>
     *   <li>如果当前行的 value 值不为空，则正常进行累计求和计算</li>
     *   <li>确保后续非空值能够基于之前的累计值继续计算</li>
     * </ol>
     *
     * @param targetField 目标字段
     * @return 包含累计和的Map
     */
    public static Map<String, Object> cumulativeSum(String targetField) {
        return IndicatorDataContext.getCalculationContext().map(context -> {
            Map<String, List<TimedRow>> timedDataByDim = CalculationUtils.getTimedDataForDimension(
                context.currentPeriodData());
            Map<String, Object> resultMap = new HashMap<>();

            timedDataByDim.forEach((dimKey, timedRows) -> {
                Number cumulativeSum = 0;
                for (TimedRow timedRow : timedRows) {
                    Map<String, Object> row = timedRow.row();
                    String key = context.keyGenerator().apply(row);
                    Object valueObj = CalculationUtils.getFieldValue(row, targetField, context.allCalculatedValues(),
                        key);
                    Number value = CalculationUtils.safeGetNumber(valueObj);

                    if (value == null) {
                        // 如果当前值为 null，则当前行的累计值也为 null
                        resultMap.put(key, "/");
                    } else {
                        // 如果当前值不为 null，则正常进行累计求和计算
                        cumulativeSum = CalculationUtils.addNumbers(cumulativeSum, value);
                        resultMap.put(key, cumulativeSum);
                    }
                }
            });

            return resultMap;
        }).orElse(Collections.emptyMap());
    }

    /**
     * 预处理行数据：提取键、时间值和目标字段值
     *
     * @param context     上下文
     * @param targetField 目标字段
     * @param timeField   时间字段
     * @return {@link List }<{@link RowData }>
     */
    private static List<RowData> preprocessRowData(
        IndicatorDataContext.CalculationContext context, String targetField, String timeField) {

        List<Map<String, Object>> currentPeriodData = context.currentPeriodData();
        List<RowData> rowDataList = new ArrayList<>(currentPeriodData.size());

        for (Map<String, Object> row : currentPeriodData) {
            Object timeValue = row.get(timeField);
            if (timeValue != null) {
                String key = context.keyGenerator().apply(row);
                Object fieldValue = CalculationUtils.getFieldValue(row, targetField,
                    context.allCalculatedValues(), key);
                Number numericValue = CalculationUtils.safeGetNumber(fieldValue);
                rowDataList.add(new RowData(key, String.valueOf(timeValue), numericValue));
            }
        }

        return rowDataList;
    }

    /**
     * 按时间分组并计算总和
     *
     * @param rowDataList 行数据列表
     * @return {@link Map }<{@link String }, {@link TimeGroupData }>
     */
    private static Map<String, TimeGroupData> groupByTimeAndCalculateTotal(List<RowData> rowDataList) {
        Map<String, TimeGroupData> timeGroups = new HashMap<>();
        for (RowData rowData : rowDataList) {
            TimeGroupData groupData = timeGroups.computeIfAbsent(rowData.timeValue,
                k -> new TimeGroupData());
            groupData.addRow(rowData);
        }
        return timeGroups;
    }

    /**
     * 按时间分组（用于排名计算）
     *
     * @param rowDataList 行数据列表
     * @return {@link Map }<{@link String }, {@link List }<{@link RowData }>>
     */
    private static Map<String, List<RowData>> groupByTime(List<RowData> rowDataList) {
        Map<String, List<RowData>> timeGroups = new HashMap<>();
        for (RowData rowData : rowDataList) {
            timeGroups.computeIfAbsent(rowData.timeValue, k -> new ArrayList<>()).add(rowData);
        }
        return timeGroups;
    }

    /**
     * 存储行数据的预计算结果 避免重复计算键、时间值和字段值
     *
     * @param key       行的唯一键
     * @param timeValue 时间值
     * @param value     字段值
     * <AUTHOR>
     * @since 2025/07/23 19:33:20
     */
    private record RowData(String key, String timeValue, Number value) {

    }

    /**
     * 存储时间分组的数据和总和 避免重复计算总和
     */
    private static class TimeGroupData {

        final List<RowData> rows = new ArrayList<>();
        double total = 0.0;

        void addRow(RowData rowData) {
            rows.add(rowData);
            if (rowData.value != null) {
                total += rowData.value.doubleValue();
            }
        }
    }
}
