package com.trs.moye.bi.engine.indicator.calculation;

import com.trs.moye.bi.engine.indicator.context.IndicatorDataContext;
import com.trs.moye.bi.engine.indicator.util.CalculationUtils;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.ToDoubleFunction;

/**
 * 算术运算
 *
 * <AUTHOR>
 * @since 2025/07/02
 */
public final class ArithmeticOperations {

    private ArithmeticOperations() {
    }

    /**
     * 对多个字段执行加法运算
     * <p>
     * <b>处理流程:</b>
     * <ol>
     *   <li>从上下文中获取当前周期的数据行</li>
     *   <li>遍历每一行，提取所有指定字段的数值</li>
     *   <li>将这些数值相加</li>
     *   <li>如果提供了精度参数，则对结果进行格式化</li>
     *   <li>将最终结果存入Map，键为行的唯一标识</li>
     * </ol>
     *
     * @param fields 要相加的字段列表最后一个元素可以是可选的精度值（整数）
     * @return 包含加法结果的Map，键为行的唯一标识
     */
    public static Map<String, Object> add(List<String> fields) {
        return performArithmeticOperation(fields, values -> values.stream().mapToDouble(Double::doubleValue).sum());
    }

    /**
     * 对多个字段执行减法运算
     * <p>
     * <b>处理流程:</b>
     * <ol>
     *   <li>从上下文中获取当前周期的数据行</li>
     *   <li>遍历每一行，提取所有指定字段的数值</li>
     *   <li>用第一个值减去后续所有值</li>
     *   <li>如果提供了精度参数，则对结果进行格式化</li>
     *   <li>将最终结果存入Map，键为行的唯一标识</li>
     * </ol>
     *
     * @param fields 要进行减法运算的字段列表，第一个为被减数，其余为减数最后一个元素可以是可选的精度值
     * @return 包含减法结果的Map
     */
    public static Map<String, Object> subtract(List<String> fields) {
        return performArithmeticOperation(fields, values -> {
            if (values.isEmpty()) {
                return Double.NaN;
            }
            double result = values.get(0);
            for (int i = 1; i < values.size(); i++) {
                result -= values.get(i);
            }
            return result;
        });
    }

    /**
     * 对多个字段执行乘法运算
     * <p>
     * <b>处理流程:</b>
     * <ol>
     *   <li>从上下文中获取当前周期的数据行</li>
     *   <li>遍历每一行，提取所有指定字段的数值</li>
     *   <li>将这些数值相乘</li>
     *   <li>如果提供了精度参数，则对结果进行格式化</li>
     *   <li>将最终结果存入Map，键为行的唯一标识</li>
     * </ol>
     *
     * @param fields 要相乘的字段列表最后一个元素可以是可选的精度值
     * @return 包含乘法结果的Map
     */
    public static Map<String, Object> multiply(List<String> fields) {
        return performArithmeticOperation(fields, values -> {
            if (values.isEmpty()) {
                return Double.NaN;
            }
            return values.stream().mapToDouble(Double::doubleValue).reduce(1.0, (a, b) -> a * b);
        });
    }

    /**
     * 对多个字段执行除法运算
     * <p>
     * <b>处理流程:</b>
     * <ol>
     *   <li>从上下文中获取当前周期的数据行</li>
     *   <li>遍历每一行，提取所有指定字段的数值</li>
     *   <li>用第一个值除以后续所有值</li>
     *   <li>如果任何除数为0，该行的结果为null</li>
     *   <li>如果提供了精度参数，则对结果进行格式化</li>
     *   <li>将最终结果存入Map，键为行的唯一标识</li>
     * </ol>
     *
     * @param fields 要进行除法运算的字段列表，第一个为被除数，其余为除数最后一个元素可以是可选的精度值
     * @return 包含除法结果的Map
     */
    public static Map<String, Object> divide(List<String> fields) {
        return performArithmeticOperation(fields, values -> {
            if (values.size() < 2) {
                return Double.NaN;
            }
            double result = values.get(0);
            for (int i = 1; i < values.size(); i++) {
                if (values.get(i) == 0) {
                    return Double.NaN; // 除数为0
                }
                result /= values.get(i);
            }
            return result;
        });
    }

    /**
     * 执行四则运算的通用方法
     *
     * @param inputs    参与运算的字段列表,最后一个可能为精度
     * @param operation 对字段值列表执行的具体运算
     * @return 包含运算结果的Map
     */
    private static Map<String, Object> performArithmeticOperation(List<String> inputs,
        ToDoubleFunction<List<Double>> operation) {
        return IndicatorDataContext.getCalculationContext().map(context -> {
            if (Objects.isNull(inputs) || inputs.isEmpty()) {
                return Collections.<String, Object>emptyMap();
            }

            // 提取字段和精度
            List<String> fields = new ArrayList<>(inputs);
            Integer precision = extractPrecision(fields);

            if (fields.isEmpty()) {
                return Collections.<String, Object>emptyMap();
            }

            Map<String, Object> resultMap = new HashMap<>();

            for (Map<String, Object> row : context.currentPeriodData()) {
                String key = context.keyGenerator().apply(row);
                List<Number> numbers = getNumbersFromFields(fields, row, context.allCalculatedValues(), key);

                if (numbers.stream().anyMatch(Objects::isNull)) {
                    resultMap.put(key, null);
                } else {
                    List<Double> doubleValues = numbers.stream().map(Number::doubleValue).toList();
                    double result = operation.applyAsDouble(doubleValues);
                    resultMap.put(key, formatResult(result, precision));
                }
            }
            return resultMap;
        }).orElse(Collections.emptyMap());
    }

    /**
     * 从输入列表中提取精度值如果最后一个元素是数字则将其视为精度，并从列表中移除
     *
     * @param fields 字段列表，可能会被修改
     * @return 提取出的精度值，如果不存在则为null
     */
    private static Integer extractPrecision(List<String> fields) {
        if (fields.isEmpty()) {
            return null;
        }
        String lastInput = fields.get(fields.size() - 1);
        try {
            Integer precision = Integer.parseInt(lastInput);
            fields.remove(fields.size() - 1); // 从字段列表中移除精度值
            return precision;
        } catch (NumberFormatException e) {
            // 不是数字，视为字段名
            return null;
        }
    }

    /**
     * 从给定的字段列表中，为当前行数据检索数值
     *
     * @param fields              要检索的字段列表
     * @param currentRow          当前数据行
     * @param allCalculatedValues 缓存的已计算值
     * @param key                 当前数据行的唯一键
     * @return 从字段中提取的数值列表
     */
    private static List<Number> getNumbersFromFields(List<String> fields, Map<String, Object> currentRow,
        Map<String, Map<String, Object>> allCalculatedValues, String key) {
        return fields.stream()
            .map(field -> {
                Object value = CalculationUtils.getFieldValue(currentRow, field, allCalculatedValues, key);
                return CalculationUtils.safeGetNumber(value);
            }).toList();
    }

    /**
     * 根据精度格式化结果
     *
     * @param result    计算结果
     * @param precision 精度，如果为null则返回整数
     * @return 格式化后的结果
     */
    private static Object formatResult(double result, Integer precision) {
        if (Double.isNaN(result)) {
            return null;
        }
        if (Objects.isNull(precision) || precision == 0) {
            return Math.round(result);
        }
        if (precision > 0) {
            String format = "%." + precision + "f";
            return Double.parseDouble(String.format(format, result));
        }
        return result;
    }
}
