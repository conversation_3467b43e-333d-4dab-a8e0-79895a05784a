package com.trs.moye.bi.engine.indicator.context;

import com.trs.moye.base.common.utils.DateTimeUtils;
import com.trs.moye.base.data.indicator.entity.StatisticPeriod;
import com.trs.moye.base.data.indicator.enums.IndicatorPeriodType;
import com.trs.moye.bi.engine.feign.SearchFeign;
import com.trs.moye.bi.engine.indicator.util.CalculationUtils;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;

/**
 * 指标数据上下文
 *
 * <AUTHOR>
 * @since 2025/06/26
 */
public class IndicatorDataContext {

    private static final ThreadLocal<DataContext> CONTEXT = new ThreadLocal<>();
    private static final ThreadLocal<Map<String, Map<String, Object>>> CALCULATED_VALUES = ThreadLocal.withInitial(
        HashMap::new);

    /**
     * 存储去年全年数据的 ThreadLocal
     * <p>
     * 与去年同期数据分开存储，用于支持全年补齐策略
     * </p>
     */
    private static final ThreadLocal<List<Map<String, Object>>> LAST_YEAR_FULL_DATA = new ThreadLocal<>();

    /**
     * 存放数据
     *
     * @param dataContext 数据上下文
     */
    public static void setContext(DataContext dataContext) {
        CONTEXT.set(dataContext);
    }

    /**
     * 获取数据
     *
     * @return {@link DataContext}
     */
    public static Optional<DataContext> getContext() {
        return Optional.ofNullable(CONTEXT.get());
    }

    /**
     * 获取基础数据
     *
     * @return {@link BaseData}
     */
    private static Optional<BaseData> getBaseData() {
        return getContext().map(DataContext::baseData);
    }

    /**
     * 获取数据
     *
     * @return {@link List}<{@link Map}<{@link String}, {@link Object}>>
     */
    public static Optional<List<Map<String, Object>>> getData() {
        return getBaseData().map(BaseData::data);
    }

    /**
     * 获取时间字段
     *
     * @return {@link String}
     */
    public static Optional<String> getTimeField() {
        return getContext().map(DataContext::timeField);
    }

    /**
     * 获取维度字段
     *
     * @return {@link List}<{@link String}>
     */
    public static Optional<List<String>> getDimsFields() {
        return getContext().map(DataContext::dimsFields);
    }

    /**
     * 获取时间范围
     *
     * @return {@link StatisticPeriod}
     */
    public static Optional<StatisticPeriod> getStatisticPeriod() {
        return getContext().map(DataContext::statisticPeriod);
    }

    /**
     * 获取周期类型
     *
     * @return {@link IndicatorPeriodType}
     */
    public static Optional<IndicatorPeriodType> getPeriodType() {
        return getContext().map(DataContext::periodType);
    }

    /**
     * 获取当前周期数据
     *
     * @return {@link List}<{@link Map}<{@link String}, {@link Object}>>
     */
    public static Optional<List<Map<String, Object>>> getCurrentPeriodData() {
        return getContext().map(DataContext::currentPeriodData);
    }

    /**
     * 获取键生成器
     *
     * @return {@link Function}<{@link Map}<{@link String}, {@link Object}>, {@link String}>
     */
    public static Optional<Function<Map<String, Object>, String>> getKeyGenerator() {
        return getContext().map(DataContext::keyGenerator);
    }

    /**
     * 获取维度键生成器
     *
     * @return {@link Function}<{@link Map}<{@link String}, {@link Object}>, {@link String}>
     */
    public static Optional<Function<Map<String, Object>, String>> getDimKeyGenerator() {
        return Optional.ofNullable(CONTEXT.get()).map(DataContext::dimKeyGenerator);
    }

    /**
     * 获取SearchFeign
     *
     * @return {@link SearchFeign}
     */
    public static Optional<SearchFeign> getSearchFeign() {
        return getContext().map(DataContext::searchFeign);
    }

    /**
     * 获取ConnectionId
     *
     * @return {@link Integer}
     */
    public static Optional<Integer> getConnectionId() {
        return getContext().map(DataContext::connectionId);
    }

    /**
     * 获取所有计算出的值
     *
     * @return {@link Map}<{@link String}, {@link Map}<{@link String}, {@link Object}>>
     */
    public static Map<String, Map<String, Object>> getAllCalculatedValues() {
        return CALCULATED_VALUES.get();
    }

    /**
     * 添加计算值
     *
     * @param fieldName 字段名
     * @param values    值
     */
    public static void addCalculatedValue(String fieldName, Map<String, Object> values) {
        CALCULATED_VALUES.get().put(fieldName, values);
    }

    /**
     * 添加数据
     *
     * @param data 数据
     */
    public static void addData(List<Map<String, Object>> data) {
        getData().ifPresent(currentData -> currentData.addAll(data));
    }

    /**
     * 添加当前周期数据
     *
     * @param data 数据
     * <AUTHOR>
     * @since 2025/07/08 15:17:52
     */
    public static void addCurrentPeriodData(List<Map<String, Object>> data) {
        getCurrentPeriodData().ifPresent(currentData -> currentData.addAll(data));
    }

    /**
     * 清除数据
     * <p>
     * 清理所有 ThreadLocal 数据，包括上下文、计算值和去年全年数据
     * </p>
     */
    public static void clear() {
        CONTEXT.remove();
        CALCULATED_VALUES.remove();
        LAST_YEAR_FULL_DATA.remove();
    }

    /**
     * 获取指定时间范围内的数据
     *
     * @param timeRangeCalculator 时间范围计算器
     * @return 符合时间范围的数据列表
     */
    private static Optional<List<Map<String, Object>>> getDataByTimeRange(
        Function<DataContext, StatisticPeriod> timeRangeCalculator) {

        return getContext().map(ctx -> {
            if (ctx.statisticPeriod() == null || ctx.timeField() == null) {
                return Collections.emptyList();
            }

            StatisticPeriod targetPeriod = timeRangeCalculator.apply(new DataContext(
                ctx.baseData(),
                ctx.currentPeriodData(),
                ctx.timeField(),
                ctx.dimsFields(),
                ctx.statisticPeriod(),
                ctx.periodType(),
                ctx.keyGenerator(),
                ctx.dimKeyGenerator(),
                ctx.searchFeign(),
                ctx.connectionId()
            ));

            return ctx.baseData().data().stream()
                .filter(row -> {
                    Object timeValue = row.get(ctx.timeField());
                    if (timeValue == null) {
                        return false;
                    }
                    LocalDateTime time = DateTimeUtils.parse(timeValue.toString());

                    return !time.isBefore(targetPeriod.getStartTime()) && time.isBefore(targetPeriod.getEndTime());
                })
                .toList();
        });
    }


    /**
     * 获取上一周期的数据
     *
     * @return {@link Optional }<{@link List }<{@link Map }<{@link String }, {@link Object }>>>
     * <AUTHOR>
     * @since 2025/07/21 16:30:00
     */
    public static Optional<List<Map<String, Object>>> getLastPeriodData() {
        return getDataByTimeRange(ctx -> {
            if (ctx.statisticPeriod() == null || ctx.periodType() == null) {
                return null;
            }
            return new StatisticPeriod(
                StatisticPeriod.minusPeriod(ctx.periodType(), 1L, ctx.statisticPeriod().getStartTime()),
                ctx.statisticPeriod().getStartTime());
        });
    }

    /**
     * 获取去年同期的数据
     *
     * @return {@link Optional }<{@link List }<{@link Map }<{@link String }, {@link Object }>>>
     * <AUTHOR>
     * @since 2025/07/21 16:30:00
     */
    public static Optional<List<Map<String, Object>>> getLastYearData() {
        return getDataByTimeRange(ctx -> {
            if (ctx.statisticPeriod() == null) {
                return null;
            }
            return new StatisticPeriod(
                ctx.statisticPeriod().getStartTime().minusYears(1),
                ctx.statisticPeriod().getEndTime().minusYears(1));
        });
    }

    /**
     * 设置去年全年数据
     * <p>
     * 专门用于存储去年一整年的完整历史数据，与去年同期数据分开管理
     * </p>
     *
     * @param lastYearFullData 去年全年数据
     * <AUTHOR>
     * @since 2025/07/24
     */
    public static void setLastYearFullData(List<Map<String, Object>> lastYearFullData) {
        LAST_YEAR_FULL_DATA.set(lastYearFullData);
    }

    /**
     * 获取去年全年数据
     * <p>
     * 获取专门存储的去年一整年完整历史数据，用于全年补齐策略
     * </p>
     *
     * @return 去年全年数据，如果未设置则返回空的 Optional
     * <AUTHOR>
     * @since 2025/07/24
     */
    public static Optional<List<Map<String, Object>>> getLastYearFullData() {
        List<Map<String, Object>> data = LAST_YEAR_FULL_DATA.get();
        return Optional.ofNullable(data);
    }

    /**
     * 取一个包含通用计算所需上下文的对象
     *
     * @return 如果当前周期数据为空，则返回Optional.empty()，否则返回包含上下文的Optional
     */
    public static Optional<CalculationContext> getCalculationContext() {
        Optional<List<Map<String, Object>>> currentPeriodDataOpt = getCurrentPeriodData();
        if (currentPeriodDataOpt.isEmpty() || currentPeriodDataOpt.get().isEmpty()) {
            return Optional.empty();
        }

        return getKeyGenerator().map(keyGen -> {
            Map<String, Map<String, Object>> allCalculatedValues = getAllCalculatedValues();
            Function<Map<String, Object>, String> dimKeyGen = getDimKeyGenerator().orElse(null);
            SearchFeign searchFeign = getSearchFeign().orElse(null);
            Integer connectionId = getConnectionId().orElse(null);
            List<Map<String, Object>> allData = getData().orElse(Collections.emptyList());
            String timeField = getTimeField().orElse(null);
            StatisticPeriod statisticPeriod = getStatisticPeriod().orElse(null);

            return new CalculationContext(
                currentPeriodDataOpt.get(),
                allData,
                allCalculatedValues,
                keyGen,
                dimKeyGen,
                searchFeign,
                connectionId,
                timeField,
                statisticPeriod
            );
        });
    }

    /**
     * 通用计算上下文，封装了计算函数中常用的数据和工具
     *
     * @param currentPeriodData   当前周期的数据列表
     * @param allData             所有周期的数据列表（包括历史数据）
     * @param allCalculatedValues 所有已计算指标的缓存
     * @param keyGenerator        行的唯一键生成器
     * @param dimKeyGenerator     维度的键生成器
     * @param searchFeign         用于执行外部SQL查询的Feign客户端
     * @param connectionId        当前数据连接的ID
     * @param timeField           时间维度字段的名称
     * @param statisticPeriod     统计周期
     */
    public record CalculationContext(
        List<Map<String, Object>> currentPeriodData,
        List<Map<String, Object>> allData,
        Map<String, Map<String, Object>> allCalculatedValues,
        Function<Map<String, Object>, String> keyGenerator,
        Function<Map<String, Object>, String> dimKeyGenerator,
        SearchFeign searchFeign,
        Integer connectionId,
        String timeField,
        StatisticPeriod statisticPeriod
    ) {

    }

    /**
     * 数据上下文
     *
     * @param baseData          基础数据
     * @param currentPeriodData 当前周期数据
     * @param timeField         时间字段
     * @param dimsFields        维度字段
     * @param statisticPeriod   统计周期
     * @param periodType        周期类型
     * @param keyGenerator      键生成器
     * @param dimKeyGenerator   维度键生成器
     * @param searchFeign       searchFeign
     * @param connectionId      connectionId
     * <AUTHOR>
     * @since 2025/06/26 10:42:21
     */
    public record DataContext(BaseData baseData, List<Map<String, Object>> currentPeriodData, String timeField,
                              List<String> dimsFields,
                              StatisticPeriod statisticPeriod, IndicatorPeriodType periodType,
                              Function<Map<String, Object>, String> keyGenerator,
                              Function<Map<String, Object>, String> dimKeyGenerator,
                              SearchFeign searchFeign, Integer connectionId) {

    }

    /**
     * 基础数据
     *
     * @param data 数据
     * <AUTHOR>
     * @since 2025/06/26 10:42:23
     */
    public record BaseData(List<Map<String, Object>> data) {

    }

    /**
     * 从当前周期数据中提取时间范围
     * <p>
     * 性能优化：先收集唯一的时间字段值，然后批量解析，避免重复解析相同的时间值
     * </p>
     *
     * @param context 计算上下文
     * @return 当前周期数据的时间范围，如果无法提取则返回空
     */
    public static Optional<StatisticPeriod> getCurrentPeriodTimeRange(CalculationContext context) {
        return getCurrentPeriodData()
            .filter(data -> !data.isEmpty() && context.timeField() != null)
            .map(data -> extractTimeRangeFromData(data, context.timeField()));
    }

    /**
     * 从数据中提取时间范围
     *
     * @param currentData 当前周期数据
     * @param timeField   时间字段名
     * @return 时间范围，如果无法提取则返回null
     */
    private static StatisticPeriod extractTimeRangeFromData(List<Map<String, Object>> currentData, String timeField) {
        Set<Object> uniqueTimeValues = collectUniqueTimeValues(currentData, timeField);
        if (uniqueTimeValues.isEmpty()) {
            return null;
        }
        return calculateTimeRangeFromValues(uniqueTimeValues);
    }

    /**
     * 收集唯一的时间字段值
     *
     * @param currentData 当前周期数据
     * @param timeField   时间字段名
     * @return 唯一时间值集合
     */
    private static Set<Object> collectUniqueTimeValues(List<Map<String, Object>> currentData, String timeField) {
        Set<Object> uniqueTimeValues = new HashSet<>();
        for (Map<String, Object> row : currentData) {
            Object timeValue = row.get(timeField);
            if (timeValue != null) {
                uniqueTimeValues.add(timeValue);
            }
        }
        return uniqueTimeValues;
    }

    /**
     * 从时间值集合中计算时间范围
     *
     * @param uniqueTimeValues 唯一时间值集合
     * @return 时间范围，如果无法计算则返回null
     */
    private static StatisticPeriod calculateTimeRangeFromValues(Set<Object> uniqueTimeValues) {
        LocalDateTime minTime = null;
        LocalDateTime maxTime = null;

        for (Object timeValue : uniqueTimeValues) {
            LocalDateTime time = CalculationUtils.parseLocalDateTime(timeValue);
            if (time != null) {
                minTime = updateMinTime(minTime, time);
                maxTime = updateMaxTime(maxTime, time);
            }
        }

        return createTimeRange(minTime, maxTime);
    }

    /**
     * 更新最小时间
     *
     * @param currentMin 当前最小时间
     * @param newTime    新时间
     * @return 更新后的最小时间
     */
    private static LocalDateTime updateMinTime(LocalDateTime currentMin, LocalDateTime newTime) {
        return currentMin == null || newTime.isBefore(currentMin) ? newTime : currentMin;
    }

    /**
     * 更新最大时间
     *
     * @param currentMax 当前最大时间
     * @param newTime    新时间
     * @return 更新后的最大时间
     */
    private static LocalDateTime updateMaxTime(LocalDateTime currentMax, LocalDateTime newTime) {
        return currentMax == null || newTime.isAfter(currentMax) ? newTime : currentMax;
    }

    /**
     * 创建时间范围
     *
     * @param minTime 最小时间
     * @param maxTime 最大时间
     * @return 时间范围，如果时间无效则返回null
     */
    private static StatisticPeriod createTimeRange(LocalDateTime minTime, LocalDateTime maxTime) {
        if (minTime == null || maxTime == null) {
            return null;
        }
        // 结束时间加1秒以确保包含最大时间点
        return new StatisticPeriod(minTime, maxTime.plusSeconds(1));
    }
}
