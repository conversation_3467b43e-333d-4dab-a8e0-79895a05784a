package com.trs.moye.bi.engine.controller;

import com.trs.moye.base.common.response.PageResponse;
import com.trs.moye.base.data.indicator.entity.IndicatorDataSearchParams;
import com.trs.moye.bi.engine.indicator.IndicatorServiceNew;
import com.trs.moye.bi.engine.indicator.v1.IndicatorService;
import java.util.List;
import java.util.Map;
import javax.annotation.Resource;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 指标控制器
 *
 * <AUTHOR>
 * @since 2025/07/09
 */
@RestController
@RequestMapping("/indicator")
public class IndicatorController {

    @Resource
    private IndicatorServiceNew indicatorServiceNew;

    @Resource
    private IndicatorService indicatorService;

    /**
     * 指标查询
     *
     * @param connectionId 连接id
     * @param request      请求
     * @return {@link List}<{@link Map}<{@link String}, {@link Object}>>
     */
    @PostMapping("/query/{connectionId}/v1")
    public PageResponse<Map<String, Object>> indicatorQueryV1(@PathVariable("connectionId") Integer connectionId,
        @RequestBody IndicatorDataSearchParams request) {
        return indicatorService.indicatorQuery(connectionId, request);
    }

    /**
     * 指标查询
     *
     * @param connectionId 连接id
     * @param request      请求
     * @return {@link List}<{@link Map}<{@link String}, {@link Object}>>
     */
    @PostMapping("/query/{connectionId}")
    public PageResponse<Map<String, Object>> indicatorQuery(@PathVariable("connectionId") Integer connectionId,
        @RequestBody IndicatorDataSearchParams request) {
        return indicatorServiceNew.indicatorQuery(connectionId, request);
    }

    /**
     * 指标查询（带分组）
     *
     * @param connectionId 连接id
     * @param request      请求
     * @return {@link List}<{@link Map}<{@link String}, {@link Object}>>
     */
    @PostMapping("/query-with-grouping/{connectionId}")
    public PageResponse<Map<String, Object>> indicatorQueryWithGrouping(
        @PathVariable("connectionId") Integer connectionId,
        @RequestBody IndicatorDataSearchParams request) {
        return indicatorServiceNew.indicatorQueryWithGrouping(connectionId, request);
    }
}
