package com.trs.moye.bi.engine.feign;

import com.trs.moye.bi.engine.common.config.OpenFeignConfig;
import com.trs.moye.bi.engine.common.request.CodeSearchParams;
import java.util.List;
import java.util.Map;
import javax.validation.constraints.NotNull;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * 存储引擎搜索feign
 */
@FeignClient(name = "${data.service.application.name:moye-storage-engine}", path = "/storage/search", contextId = "search", configuration = OpenFeignConfig.class)
public interface SearchFeign {

    /**
     * 执行sql查询，不分页，提供给指标查询使用
     *
     * @param connectionId 连接id
     * @param request      sql
     * @return 查询结果
     */
    @PostMapping(value = "/indicator-query")
    List<Map<String, Object>> indicatorQuery(
        @RequestParam("connectionId") @NotNull(message = "连接id不能为空") Integer connectionId,
        @RequestBody CodeSearchParams request);

}
