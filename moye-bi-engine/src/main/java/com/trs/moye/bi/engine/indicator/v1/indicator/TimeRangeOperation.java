package com.trs.moye.bi.engine.indicator.v1.indicator;

import com.trs.moye.base.data.indicator.enums.IndicatorStatOperation;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

/**
 * 时间范围操作 基础代码实现
 */
@Slf4j
@Component
public class TimeRangeOperation extends AbstractStatOperation {

    @Override
    public void calculate(List<Map<String, Object>> currentData, List<Map<String, Object>> historyData,
        IndicatorStatParams params) {
        if (CollectionUtils.isEmpty(currentData)) {
            return;
        }
        String resultField = getResultField(params);
        if (Objects.isNull(resultField)) {
            return;
        }

        // 从第一条记录中查找字段名
        Map<String, Object> firstRecord = currentData.get(0);
        String startTimeField = findTimeField(firstRecord, "start_time");
        String endTimeField = findTimeField(firstRecord, "end_time");

        if (Objects.isNull(startTimeField) || Objects.isNull(endTimeField)) {
            log.warn("无法找到 start_time 或 end_time 字段");
            return;
        }

        for (Map<String, Object> data : currentData) {
            Object startTime = data.get(startTimeField);
            Object endTime = data.get(endTimeField);

            if (Objects.nonNull(startTime) && Objects.nonNull(endTime)) {
                String timeRange = startTime + " - " + endTime;
                data.put(resultField, timeRange);
            } else {
                data.put(resultField, null);
            }
        }
    }

    private String findTimeField(Map<String, Object> data, String fieldNamePart) {
        return data.keySet().stream()
            .filter(e -> e.contains(fieldNamePart))
            .findFirst()
            .orElse(null);
    }

    @Override
    public boolean isSupport(IndicatorStatOperation operation) {
        return IndicatorStatOperation.TIME_RANGE.equals(operation);
    }
}
