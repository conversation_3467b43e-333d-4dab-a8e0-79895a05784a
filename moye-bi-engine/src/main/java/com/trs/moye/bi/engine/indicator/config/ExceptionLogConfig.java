package com.trs.moye.bi.engine.indicator.config;

import javax.annotation.PostConstruct;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 异常日志配置类.
 * <p>
 * 提供异常日志优化器的配置参数，支持通过配置文件动态调整异常日志记录行为。 主要配置项：
 * <ul>
 *   <li>采样率：控制异常日志的记录频率</li>
 *   <li>详细日志开关：控制是否记录详细的异常日志</li>
 *   <li>统计摘要开关：控制是否输出异常统计摘要</li>
 * </ul>
 * </p>
 * <p>
 * 配置示例（application.yml）：
 * <pre>
 * moye:
 *   bi:
 *     exception-log:
 *       sample-rate: 100          # 采样率：每100个异常记录1个详细日志
 *       detailed-logging: true    # 是否启用详细异常日志记录
 *       summary-enabled: true     # 是否启用异常统计摘要
 * </pre>
 * </p>
 *
 * <AUTHOR>
 * @since 2025/07/28
 */
@Slf4j
@Data
@Component
@ConfigurationProperties(prefix = "moye.bi.exception-log")
public class ExceptionLogConfig {

    /**
     * 采样率：每多少个异常记录1个详细日志.
     * <p>
     * 默认值为100，表示每100个异常记录1个详细日志。 值越大，记录的日志越少，性能越好，但异常信息越少。 值越小，记录的日志越多，异常信息越详细，但性能影响越大。
     * </p>
     */
    private int sampleRate = 100;

    /**
     * 是否启用详细异常日志记录.
     * <p>
     * 默认值为true，启用详细异常日志记录。 在高负载场景下可以设置为false以完全禁用详细日志记录，提升性能。 即使禁用详细日志，异常统计信息仍会被收集。
     * </p>
     */
    private boolean detailedLogging = true;

    /**
     * 是否启用异常统计摘要.
     * <p>
     * 默认值为true，启用异常统计摘要输出。 统计摘要提供异常类型和数量的汇总信息，用于监控和问题排查。
     * </p>
     */
    private boolean summaryEnabled = true;

    /**
     * 初始化配置.
     * <p>
     * 在Spring容器初始化完成后，将配置参数应用到异常日志优化器中。
     * </p>
     */
    @PostConstruct
    public void initConfig() {
        log.info("初始化异常日志配置: sampleRate={}, detailedLogging={}, summaryEnabled={}",
            sampleRate, detailedLogging, summaryEnabled);

        // 应用配置到异常日志优化器
        ExceptionLogOptimizer.setSampleRate(sampleRate);
        ExceptionLogOptimizer.setDetailedLoggingEnabled(detailedLogging);

        log.info("异常日志配置初始化完成");
    }

    /**
     * 动态更新采样率.
     * <p>
     * 支持运行时动态调整采样率，无需重启应用。
     * </p>
     *
     * @param sampleRate 新的采样率
     */
    public void updateSampleRate(int sampleRate) {
        if (sampleRate <= 0) {
            log.warn("采样率必须大于0，忽略无效配置: {}", sampleRate);
            return;
        }

        this.sampleRate = sampleRate;
        ExceptionLogOptimizer.setSampleRate(sampleRate);
        log.info("异常日志采样率已动态更新为: {}", sampleRate);
    }

    /**
     * 动态更新详细日志开关.
     * <p>
     * 支持运行时动态开启或关闭详细异常日志记录，无需重启应用。
     * </p>
     *
     * @param enabled 是否启用详细日志记录
     */
    public void updateDetailedLogging(boolean enabled) {
        this.detailedLogging = enabled;
        ExceptionLogOptimizer.setDetailedLoggingEnabled(enabled);
        log.info("详细异常日志记录已动态{}为: {}", enabled ? "启用" : "禁用", enabled);
    }

    /**
     * 获取当前异常统计信息.
     * <p>
     * 提供运行时查询异常统计信息的接口，用于监控和诊断。
     * </p>
     *
     * @return 异常统计信息的描述字符串
     */
    public String getExceptionStats() {
        long globalCount = ExceptionLogOptimizer.getGlobalExceptionCount();
        return String.format("全局异常总数: %d, 采样率: %d, 详细日志: %s, 统计摘要: %s",
            globalCount, sampleRate, detailedLogging ? "启用" : "禁用", summaryEnabled ? "启用" : "禁用");
    }
}
