package com.trs.moye.bi.engine.indicator.processor;

import com.trs.moye.base.common.response.PageResponse;
import com.trs.moye.bi.engine.indicator.context.IndicatorDataContext;
import com.trs.moye.bi.engine.indicator.context.QueryContext;
import com.trs.moye.bi.engine.indicator.exception.IndicatorQueryException;
import com.trs.moye.bi.engine.indicator.processor.completion.DataCompletionProcessor;
import com.trs.moye.bi.engine.indicator.processor.query.DataQueryService;
import com.trs.moye.bi.engine.indicator.processor.query.HistoricalDataQueryService;
import com.trs.moye.bi.engine.indicator.util.ExceptionLogOptimizer;
import java.util.List;
import java.util.Map;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 数据查询处理器
 * <p>
 * 负责协调整个指标查询和计算流程 将具体的业务逻辑委托给专门的服务类处理
 * </p>
 * <p>
 * 主要职责：
 * <ul>
 *   <li>流程编排：协调各个服务的执行顺序</li>
 *   <li>异常处理：统一处理各个步骤的异常</li>
 *   <li>上下文管理：管理数据上下文的生命周期</li>
 *   <li>结果封装：将处理结果封装为统一的响应格式</li>
 * </ul>
 * </p>
 *
 * <AUTHOR>
 * @since 2025/07/23
 */
@Slf4j
@Component
public class DataQueryProcessor {

    @Resource
    private DataQueryService dataQueryService;
    @Resource
    private HistoricalDataQueryService historicalDataQueryService;
    @Resource
    private DataCompletionProcessor dataCompletionProcessor;
    @Resource
    private IndicatorCalculationService indicatorCalculationService;
    @Resource
    private DataProcessingService dataProcessingService;

    /**
     * 处理数据查询的主要流程.
     * <p>
     * 作为整个查询流程的协调器，按照以下步骤执行：
     * <ol>
     *   <li>执行当前周期数据查询</li>
     *   <li>初始化数据上下文</li>
     *   <li>并行获取历史数据</li>
     *   <li>执行数据补齐处理</li>
     *   <li>执行指标计算</li>
     *   <li>合并计算结果</li>
     *   <li>应用排序</li>
     *   <li>按目标周期筛选</li>
     *   <li>过滤返回字段</li>
     * </ol>
     * </p>
     *
     * @param context 查询上下文，包含查询条件和配置信息
     * @return 处理后的分页结果
     * @throws IndicatorQueryException 当查询处理过程中发生错误时抛出
     */
    public PageResponse<Map<String, Object>> processDataQuery(final QueryContext context) {
        log.info("开始处理数据查询, dataModelId: {}, tableName: {}",
            context.getDataModelId(), context.getTableName());

        final long startTime = System.currentTimeMillis();

        try {
            // 1. 执行当前周期数据查询
            List<Map<String, Object>> currentData = dataQueryService.executeCurrentDataQuery(context);

            // 2. 初始化数据上下文
            dataQueryService.initializeDataContext(currentData, context);

            // 3. 并行获取历史数据
            if (context.needsHistoricalData()) {
                historicalDataQueryService.executeHistoricalDataQuery(context);
            }

            // 4. 获取当前周期数据
            List<Map<String, Object>> resultData = dataQueryService.getCurrentPeriodData();
            if (resultData.isEmpty()) {
                log.info("当前周期数据为空, 返回空结果");
                return new PageResponse<>();
            }

            // 5. 数据补齐处理
            if (context.needsDataCompletion()) {
                resultData = dataCompletionProcessor.performDataCompletion(resultData, context);
            }

            if (resultData.isEmpty()) {
                log.info("数据补齐后结果为空, 返回空结果");
                return new PageResponse<>();
            }

            // 6. 执行指标计算
            Map<String, Map<String, Object>> calculatedValues = indicatorCalculationService.performCalculations(
                context);

            // 7. 合并计算结果
            indicatorCalculationService.mergeCalculationResults(resultData, calculatedValues);

            // 8. 处理排序
            resultData = dataProcessingService.applySorting(resultData, context);

            // 9. 按目标周期筛选
            resultData = dataProcessingService.filterByTargetPeriods(resultData, context);

            // 10. 过滤返回字段
            resultData = dataProcessingService.filterReturnFields(resultData, context.getRequest().getReturnFields());

            final long duration = System.currentTimeMillis() - startTime;
            log.info("数据查询处理完成, dataModelId: {}, 结果数量: {}, 耗时: {}ms",
                context.getDataModelId(), resultData.size(), duration);

            return new PageResponse<>(resultData, resultData.size());
        } catch (IndicatorQueryException e) {
            log.error("指标查询处理失败: {}", e.getMessage(), e);
            throw e;
        } catch (Exception e) {
            log.error("数据查询处理发生错误, dataModelId: {}", context.getDataModelId(), e);
            throw IndicatorQueryException.dataProcessingError("数据查询处理", e);
        } finally {
            // 输出异常统计摘要（用于性能监控和问题排查）
            ExceptionLogOptimizer.logExceptionSummary();

            // 确保清理上下文
            IndicatorDataContext.clear();

            // 清理异常统计信息，为下次处理做准备
            ExceptionLogOptimizer.clearStats();

            final long totalTime = System.currentTimeMillis() - startTime;
            log.debug("数据查询处理总耗时: {}ms", totalTime);
        }
    }
}
