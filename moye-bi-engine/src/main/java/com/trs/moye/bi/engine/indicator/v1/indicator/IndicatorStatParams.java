package com.trs.moye.bi.engine.indicator.v1.indicator;

import com.trs.moye.base.data.indicator.entity.DataModelIndicatorField;
import com.trs.moye.base.data.indicator.entity.IndicatorApplicationFieldConfig;
import com.trs.moye.base.data.indicator.enums.IndicatorPeriodType;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * stat 配置
 *
 * <AUTHOR>
 * @since 2025/05/27 17:01:53
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
public class IndicatorStatParams {

    /**
     * 数据型id
     */
    private Integer dataModelId;

    /**
     * 计算目标字段
     */
    private DataModelIndicatorField targetField;

    /**
     * 统计维度
     */
    private List<String> statisticDims;

    /**
     * 配置
     */
    IndicatorApplicationFieldConfig fieldConfig;

    /**
     * 结果字段
     */
    private DataModelIndicatorField resultField;

    /**
     * 周期类型
     */
    private IndicatorPeriodType periodType;

    /**
     * 差异周期数
     */
    private long diffPeriodNum;

    /**
     * 时间字段
     */
    private String startTimeField;

    /**
     * 相邻周期计算时使用的差异周期数
     */
    private long neighborDiffPeriodNum;
}

