package com.trs.moye.bi.engine.indicator.calculation;

import com.trs.moye.base.data.service.enums.EvaluateOperator;
import com.trs.moye.bi.engine.indicator.context.IndicatorDataContext;
import com.trs.moye.bi.engine.indicator.entity.TimedRow;
import com.trs.moye.bi.engine.indicator.util.CalculationUtils;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import lombok.extern.slf4j.Slf4j;

/**
 * 条件计算
 *
 * <AUTHOR>
 * @since 2025/07/02
 */
@Slf4j
public final class ConditionalCalculations {

    private ConditionalCalculations() {
    }

    /**
     * 检查单个数据点是否满足条件
     * <p>
     * <b>处理流程:</b>
     * <ol>
     *   <li>获取当前周期的数据</li>
     *   <li>遍历每一行数据</li>
     *   <li>获取目标字段的实际值</li>
     *   <li>调用 {@link #evaluate} 方法进行比较</li>
     *   <li>将布尔结果存入Map中</li>
     * </ol>
     *
     * @param targetField     目标字段
     * @param operator        比较运算符
     * @param comparisonValue 用于比较的值
     * @return 包含布尔结果的Map
     */
    public static Map<String, Object> isConditionMet(String targetField, EvaluateOperator operator,
        Object comparisonValue) {
        return IndicatorDataContext.getCalculationContext().map(context -> {
            Map<String, Object> resultMap = new HashMap<>();

            for (Map<String, Object> row : context.currentPeriodData()) {
                String key = context.keyGenerator().apply(row);

                Object actualValue = CalculationUtils.getFieldValue(row, targetField, context.allCalculatedValues(),
                    key);

                resultMap.put(key, evaluate(actualValue, operator, comparisonValue));
            }
            return resultMap;
        }).orElse(Collections.emptyMap());
    }

    /**
     * 计算一个字段的值在所有周期内满足指定条件的次数
     * <p>
     * <b>处理流程:</b>
     * <ol>
     *   <li>获取所有历史和当前周期的数据</li>
     *   <li>按维度对数据进行分组</li>
     *   <li>对每个维度分组内的数据进行遍历</li>
     *   <li>检查每一行是否满足条件，并进行计数</li>
     *   <li>将每个维度的总计数值赋给该维度下的所有行</li>
     * </ol>
     *
     * @param targetField     要判断的目标字段
     * @param operator        比较操作符
     * @param comparisonValue 要比较的值
     * @return 包含次数的Map，键为行的唯一标识，值为满足条件的次数
     */
    public static Map<String, Object> countConditionMet(String targetField, EvaluateOperator operator,
        Object comparisonValue) {
        return IndicatorDataContext.getCalculationContext().map(context -> {
            // 步骤 2: 按维度对数据进行分组
            Map<String, List<TimedRow>> timedDataByDim = CalculationUtils.getTimedDataForDimension(context.allData());

            // 步骤 3: 遍历每个维度，计算满足条件的次数
            Map<String, Object> resultMap = new HashMap<>();

            timedDataByDim.forEach((dimKey, timedRows) -> {
                int count = 0;
                for (TimedRow timedRow : timedRows) {
                    Map<String, Object> row = timedRow.row();
                    String key = context.keyGenerator().apply(row);

                    // 优先从已计算结果中获取值，否则从原始行数据中获取
                    Object actualValue = CalculationUtils.getFieldValue(row, targetField,
                        context.allCalculatedValues(), key);

                    if (evaluate(actualValue, operator, comparisonValue)) {
                        count++;
                    }
                }

                // 将每个维度的总数赋给该维度的所有行
                for (TimedRow timedRow : timedRows) {
                    String rowKey = context.keyGenerator().apply(timedRow.row());
                    resultMap.put(rowKey, count);
                }
            });

            return resultMap;
        }).orElse(Collections.emptyMap());
    }

    /**
     * 判断一个字段的值是否连续N个周期满足指定条件
     * <p>
     * <b>处理流程:</b>
     * <ol>
     *   <li>获取当前周期的数据</li>
     *   <li>按维度对数据进行分组并按时间排序</li>
     *   <li>对每个维度，从最新的数据点开始，向前检查N个周期</li>
     *   <li>如果所有N个周期都满足条件，则该维度结果为true</li>
     *   <li>将最终的判断结果（trueText或falseText）赋给该维度下的所有行</li>
     * </ol>
     *
     * @param targetField        要判断的目标字段
     * @param operator           比较操作符
     * @param comparisonValue    要比较的值
     * @param consecutivePeriods 需要连续满足的周期数
     * @param trueText           满足条件时返回的文本
     * @param falseText          不满足条件时返回的文本
     * @return 包含判断结果的Map
     */
    public static Map<String, Object> getConditionalData(String targetField, EvaluateOperator operator,
        Object comparisonValue, int consecutivePeriods, String trueText, String falseText) {

        return IndicatorDataContext.getCalculationContext().map(context -> {
            // 步骤 2: 按维度对数据进行分组和时间排序
            Map<String, List<TimedRow>> timedDataByDim = CalculationUtils.getTimedDataForDimension(
                context.currentPeriodData());

            // 步骤 3: 遍历每个维度，进行条件判断
            Map<String, Object> resultMap = new HashMap<>();
            ConditionEvaluationContext evalContext = new ConditionEvaluationContext(targetField, operator,
                comparisonValue,
                consecutivePeriods, trueText, falseText, context.keyGenerator(), context.allCalculatedValues());

            timedDataByDim.forEach((dimKey, timedRows) ->
                evaluateAndRecordCondition(resultMap, timedRows, evalContext)
            );

            return resultMap;
        }).orElse(Collections.emptyMap());
    }

    /**
     * 为单个维度的数据计算并记录是否满足连续条件
     *
     * @param resultMap 结果集
     * @param timedRows 定时行
     * @param context   条件评估上下文
     * <AUTHOR>
     * @since 2025/06/30 19:58:58
     */
    private static void evaluateAndRecordCondition(Map<String, Object> resultMap, List<TimedRow> timedRows,
        ConditionEvaluationContext context) {

        // 检查最新数据是否满足连续条件
        boolean isConsecutive = checkConsecutiveCondition(timedRows, timedRows.size() - 1, context);

        // 将结果应用于此维度的所有行
        String resultText = isConsecutive ? context.trueText : context.falseText;
        for (TimedRow timedRow : timedRows) {
            String rowKey = context.keyGenerator.apply(timedRow.row());
            resultMap.put(rowKey, resultText);
        }
    }

    /**
     * 检查从指定索引开始，往前N个周期是否都满足条件
     *
     * @param timedRows    按时间排序的数据行
     * @param currentIndex 当前行的索引
     * @param context      条件评估上下文
     * @return 如果连续满足条件则为true，否则为false
     */
    private static boolean checkConsecutiveCondition(List<TimedRow> timedRows, int currentIndex,
        ConditionEvaluationContext context) {

        // 如果数据不足以判断，则直接返回false
        if (currentIndex < 0 || currentIndex + 1 < context.consecutivePeriods) {
            return false;
        }

        for (int i = 0; i < context.consecutivePeriods; i++) {
            int checkIndex = currentIndex - i;
            Map<String, Object> rowToCheck = timedRows.get(checkIndex).row();
            String keyToCheck = context.keyGenerator.apply(rowToCheck);

            // 优先从已计算结果中获取值，否则从原始行数据中获取
            Object actualValue = CalculationUtils.getFieldValue(rowToCheck, context.targetField,
                context.allCalculatedValues, keyToCheck);

            if (!evaluate(actualValue, context.operator, context.comparisonValue)) {
                return false; // 只要有一个周期不满足，立即返回false
            }
        }

        return true; // 所有周期都满足条件
    }

    /**
     * 执行具体的比较操作
     *
     * @param actualValue     实际值
     * @param operator        操作符
     * @param comparisonValue 要比较的值
     * @return 是否满足条件
     */
    public static boolean evaluate(Object actualValue, EvaluateOperator operator, Object comparisonValue) {
        if (actualValue == null || comparisonValue == null) {
            return false;
        }

        // 尝试将两个值都作为数字进行比较
        Number actualNumber = CalculationUtils.safeGetNumber(actualValue);
        Number comparisonNumber = CalculationUtils.safeGetNumber(comparisonValue);

        if (actualNumber != null && comparisonNumber != null) {
            double actual = actualNumber.doubleValue();
            double expected = comparisonNumber.doubleValue();
            return switch (operator) {
                case GT -> actual > expected;
                case GTE -> actual >= expected;
                case LT -> actual < expected;
                case LTE -> actual <= expected;
                case EQ -> Double.compare(actual, expected) == 0;
                case NEQ -> Double.compare(actual, expected) != 0;
                default -> {
                    // 对于数字比较，不支持CONTAIN等操作
                    log.warn("Unsupported numeric comparison operator: {}", operator);
                    yield false;
                }
            };
        }

        // 如果无法进行数字比较，则回退到字符串比较
        String actualString = actualValue.toString();
        String comparisonString = comparisonValue.toString();

        return switch (operator) {
            case EQ -> actualString.equals(comparisonString);
            case NEQ -> !actualString.equals(comparisonString);
            case CONTAIN -> actualString.contains(comparisonString);
            default -> {
                log.warn("Unsupported string comparison operator: {}", operator);
                yield false;
            }
        };
    }

    private record ConditionEvaluationContext(
        String targetField, EvaluateOperator operator, Object comparisonValue, int consecutivePeriods,
        String trueText, String falseText, Function<Map<String, Object>, String> keyGenerator,
        Map<String, Map<String, Object>> allCalculatedValues) {

    }
}
