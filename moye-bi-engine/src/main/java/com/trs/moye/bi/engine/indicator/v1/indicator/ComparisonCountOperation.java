package com.trs.moye.bi.engine.indicator.v1.indicator;

import com.trs.moye.base.data.indicator.entity.IndicatorApplicationFieldConfig;
import com.trs.moye.base.data.indicator.enums.IndicatorStatOperation;
import com.trs.moye.base.data.indicator.enums.IndicatorStatType;
import com.trs.moye.base.data.service.enums.EvaluateOperator;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import org.springframework.stereotype.Component;

/**
 * 总数操作 基础代码实现
 */
@Component
public class ComparisonCountOperation extends AbstractStatOperation {

    @Override
    public void calculate(List<Map<String, Object>> currentData, List<Map<String, Object>> historyData,
        IndicatorStatParams params) {
        String targetField = getTargetField(params);
        String resultField = getResultField(params);
        String timeField = params.getStartTimeField();
        List<String> dims = getStatisticDims(params);
        Integer compNum = params.getFieldConfig().getComparisonNumber();
        EvaluateOperator op = params.getFieldConfig().getOperator();
        IndicatorApplicationFieldConfig config = params.getFieldConfig();
        IndicatorStatType indicatorStatType = config.getIndicatorStatType();
        for (Map<String, Object> data : currentData) {
            List<Entry<LocalDateTime, Map<String, Object>>> timed = buildTimedEntries(historyData, data, dims,
                timeField);
            int count = 0;
            if (IndicatorStatType.CONTINUOUS == indicatorStatType) {
                for (int i = timed.size() - 1; i >= 0; i--) {
                    Entry<LocalDateTime, Map<String, Object>> e = timed.get(i);
                    Number num = safeGetNumber(e.getValue(), targetField);
                    double val = num != null ? num.doubleValue() : 0d;
                    if (evaluateCondition(op, val, compNum)) {
                        count++;
                    } else {
                        break;
                    }
                }
                String resultString = "无连续上升趋势";
                if (count > 1) {
                    String direction = getDirection(op);
                    resultString = String.format("连续两日%s阈值", direction);
                }
                data.put(resultField, resultString);
            } else {
                for (Entry<LocalDateTime, Map<String, Object>> e : timed) {
                    Number num = safeGetNumber(e.getValue(), targetField);
                    double val = num != null ? num.doubleValue() : 0d;
                    if (evaluateCondition(op, val, compNum)) {
                        count++;
                    }
                }
                data.put(resultField, count);
            }
        }
    }

    private String getDirection(EvaluateOperator op) {
        return switch (op) {
            case GT, GTE -> "超过";
            case LT, LTE -> "低于";
            case EQ -> "等于";
            case NEQ -> "不等于";
            default -> "";
        };
    }

    private boolean evaluateCondition(EvaluateOperator op, double val, Integer compNum) {
        return switch (op) {
            case GT -> val > compNum;
            case GTE -> val >= compNum;
            case LT -> val < compNum;
            case LTE -> val <= compNum;
            case EQ -> val == compNum;
            case NEQ -> val != compNum;
            default -> false;
        };
    }

    @Override
    public boolean isSupport(IndicatorStatOperation operation) {
        return IndicatorStatOperation.COMPARISON_COUNT.equals(operation);
    }

}
