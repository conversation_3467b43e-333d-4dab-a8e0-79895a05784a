package com.trs.moye.bi.engine.indicator.processor.completion;

import com.trs.moye.base.data.indicator.enums.DataCompletionStrategy;
import com.trs.moye.bi.engine.indicator.config.IndicatorQueryProperties;
import com.trs.moye.bi.engine.indicator.context.IndicatorDataContext;
import com.trs.moye.bi.engine.indicator.context.QueryContext;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

/**
 * 数据补齐处理器
 * <p>
 * 负责处理指标数据的补齐逻辑，支持多种补齐策略：
 * <ul>
 *   <li>去年同期补齐：根据去年同期数据进行补齐</li>
 *   <li>去年全年补齐：基于去年全年数据进行补齐</li>
 * </ul>
 * 根据系统配置和策略设置，自动选择合适的补齐方式和数据源
 * </p>
 *
 * <AUTHOR>
 * @since 2025/07/28
 */
@Slf4j
@Service
public class DataCompletionProcessor {

    @Resource
    private DataCompletionService dataCompletionService;
    @Resource
    private IndicatorQueryProperties indicatorQueryProperties;

    /**
     * 执行数据补齐
     * <p>
     * 根据配置的数据补齐策略，选择合适的补齐模式：
     * <ul>
     *   <li>去年同期补齐：根据当前数据周期确定去年对应的同期范围进行补齐</li>
     *   <li>去年全年补齐：基于去年全年数据进行补齐，补齐到年底</li>
     * </ul>
     * 如果数据补齐失败，将返回原始数据以确保流程不中断
     * </p>
     *
     * @param currentData 当前周期的原始数据
     * @param context     查询上下文，包含补齐策略和配置信息
     * @return 补齐后的数据列表，补齐失败时返回原始数据
     */
    public List<Map<String, Object>> performDataCompletion(List<Map<String, Object>> currentData,
        QueryContext context) {
        try {
            log.debug("开始数据补齐处理, dataModelId: {}", context.getDataModelId());

            // 获取数据补齐策略
            DataCompletionStrategy strategy = getDataCompletionStrategy(context);

            // 策略空值检查：如果策略为 null，则不进行数据补齐处理
            if (strategy == null) {
                log.info("数据补齐策略为空，跳过数据补齐处理, dataModelId: {}", context.getDataModelId());
                return currentData;
            }

            log.debug("使用数据补齐策略: {}, dataModelId: {}", strategy.getDescription(), context.getDataModelId());

            // 验证全年补齐条件
            DataCompletionStrategy finalStrategy = validateAndAdjustStrategy(strategy, context);
            if (finalStrategy != strategy) {
                log.info("数据补齐策略已调整: {} -> {}, dataModelId: {}",
                    strategy.getDescription(), finalStrategy.getDescription(), context.getDataModelId());
            }

            // 根据最终策略获取相应的历史数据
            List<Map<String, Object>> lastPeriodData = getLastPeriodDataForStrategy(finalStrategy, context);
            List<Map<String, Object>> lastYearData = getLastYearDataForStrategy(finalStrategy, context);
            List<String> dimsFields = context.getStatisticDimensions();

            // 执行数据补齐
            List<Map<String, Object>> paddedData = dataCompletionService.completeData(
                currentData,
                lastPeriodData,
                lastYearData,
                context.getIndicatorConfig().getStatisticStrategyInfo().getPeriod(),
                context.getFieldGroups().startTimeField(),
                context.getFieldGroups().endTimeField(),
                dimsFields,
                finalStrategy
            );

            if (CollectionUtils.isNotEmpty(paddedData)) {
                IndicatorDataContext.addCurrentPeriodData(paddedData);
                IndicatorDataContext.addData(paddedData);
                log.info("数据补齐成功, dataModelId: {}, 策略: {}, 补齐数据量: {}",
                    context.getDataModelId(), finalStrategy.getDescription(), paddedData.size());
            } else {
                log.debug("无需补齐数据, dataModelId: {}, 策略: {}",
                    context.getDataModelId(), finalStrategy.getDescription());
            }

            return currentData;

        } catch (Exception e) {
            log.error("数据补齐处理失败, dataModelId: {}", context.getDataModelId(), e);
            // 数据补齐失败时返回原始数据，不中断流程
            log.warn("数据补齐失败，使用原始数据继续处理, dataModelId: {}", context.getDataModelId());
            return currentData;
        }
    }

    /**
     * 验证并调整数据补齐策略
     * <p>
     * 只有当同时满足以下两个条件时才执行去年全年补齐：
     * <ul>
     *   <li>数据补齐策略为 FULL_YEAR_LAST_YEAR</li>
     *   <li>系统配置启用了去年整年数据获取功能 (isEnableLastYearFullData = true)</li>
     * </ul>
     * 如果上述条件不完全满足，则默认使用去年同期补齐策略
     * </p>
     *
     * @param strategy 原始补齐策略
     * @param context  查询上下文
     * @return 验证后的最终策略
     */
    private DataCompletionStrategy validateAndAdjustStrategy(DataCompletionStrategy strategy, QueryContext context) {
        try {
            // 如果不是全年补齐策略，直接返回原策略
            if (!strategy.isFullYearLastYear()) {
                log.debug("策略验证通过: {}, dataModelId: {}",
                    strategy.getDescription(), context.getDataModelId());
                return strategy;
            }

            // 检查系统配置是否启用了去年整年数据获取功能
            boolean isFullDataEnabled = indicatorQueryProperties.isEnableLastYearFullData();

            if (isFullDataEnabled) {
                log.debug("全年补齐策略验证通过: 策略={}, 系统配置启用全年数据, dataModelId: {}",
                    strategy.getDescription(), context.getDataModelId());
                return strategy;
            } else {
                log.warn("全年补齐策略条件不满足，降级为去年同期补齐: 策略={}, 系统配置未启用全年数据, dataModelId: {}",
                    strategy.getDescription(), context.getDataModelId());
                return DataCompletionStrategy.SAME_PERIOD_LAST_YEAR;
            }

        } catch (Exception e) {
            log.error("策略验证过程中发生异常, dataModelId: {}, 使用默认策略",
                context.getDataModelId(), e);
            return DataCompletionStrategy.SAME_PERIOD_LAST_YEAR;
        }
    }

    /**
     * 获取数据补齐策略
     * <p>
     * 从指标配置中获取数据补齐策略，支持以下情况的处理：
     * <ul>
     *   <li>如果配置中明确设置了策略，则返回配置的策略</li>
     *   <li>如果配置为空或获取失败，则返回 null（表示不进行数据补齐）</li>
     *   <li>如果配置中的策略为 null，则返回 null（表示不进行数据补齐）</li>
     * </ul>
     * </p>
     *
     * @param context 查询上下文
     * @return 数据补齐策略，可能为 null（表示不进行数据补齐）
     */
    private DataCompletionStrategy getDataCompletionStrategy(QueryContext context) {
        try {
            // 检查上下文和配置的完整性
            if (context == null) {
                log.warn("查询上下文为空，无法获取数据补齐策略");
                return null;
            }

            // 获取配置中的策略
            DataCompletionStrategy strategy = context.getIndicatorConfig()
                .getStatisticStrategyInfo()
                .getDataCompletionStrategy();
            if (strategy == null) {
                log.debug("配置中的数据补齐策略为空, dataModelId: {}", context.getDataModelId());
                return null;
            }

            log.debug("从配置中获取数据补齐策略: {}, dataModelId: {}",
                strategy.getDescription(), context.getDataModelId());
            return strategy;

        } catch (Exception e) {
            log.warn("获取数据补齐策略失败, 使用默认策略", e);
            return null;
        }
    }

    /**
     * 根据策略获取上一周期数据
     * <p>
     * 获取上一个统计周期的历史数据，用于数据补齐时的参考 上一周期数据的获取逻辑对于不同策略基本相同，主要用于补齐算法中的参考数据
     * </p>
     *
     * @param strategy 数据补齐策略
     * @param context  查询上下文
     * @return 上一周期数据列表，获取失败时返回空列表
     */
    private List<Map<String, Object>> getLastPeriodDataForStrategy(DataCompletionStrategy strategy,
        QueryContext context) {
        try {
            if (strategy == null) {
                log.warn("数据补齐策略为空，无法获取上一周期数据, dataModelId: {}",
                    context != null ? context.getDataModelId() : "");
                return Collections.emptyList();
            }

            List<Map<String, Object>> lastPeriodData = IndicatorDataContext.getLastPeriodData()
                .orElse(Collections.emptyList());

            String dataModelId = context != null ? context.getDataModelId().toString() : "";
            log.debug("获取上一周期数据成功, 策略: {}, dataModelId: {}, 数据量: {}",
                strategy.getDescription(), dataModelId, lastPeriodData.size());
            return lastPeriodData;

        } catch (Exception e) {
            String dataModelId = context != null ? context.getDataModelId().toString() : "";
            log.warn("获取上一周期数据失败, 策略: {}, dataModelId: {}",
                strategy != null ? strategy.getDescription() : "null", dataModelId, e);
            return Collections.emptyList();
        }
    }

    /**
     * 根据策略获取去年数据
     * <p>
     * 明确区分去年同期数据与去年全年数据的获取逻辑：
     * <ul>
     *   <li>去年同期补齐策略：获取去年同期数据（与当前周期对应的历史数据）</li>
     *   <li>去年全年补齐策略：获取去年全年数据（需要系统配置支持）</li>
     * </ul>
     * <strong>重要说明</strong>：此方法调用前应已通过 validateAndAdjustStrategy 验证策略的有效性
     * </p>
     *
     * @param strategy 数据补齐策略
     * @param context  查询上下文
     * @return 去年数据列表，获取失败时返回空列表
     */
    private List<Map<String, Object>> getLastYearDataForStrategy(DataCompletionStrategy strategy,
        QueryContext context) {
        try {
            if (strategy == null) {
                log.warn("数据补齐策略为空，无法获取去年数据, dataModelId: {}",
                    context != null ? context.getDataModelId() : "");
                return Collections.emptyList();
            }

            List<Map<String, Object>> lastYearData;
            String dataModelId = context != null ? context.getDataModelId().toString() : "";

            if (strategy.isFullYearLastYear()) {
                // 全年补齐策略：获取去年全年数据
                lastYearData = IndicatorDataContext.getLastYearFullData().orElse(Collections.emptyList());
                log.debug("全年补齐策略获取去年全年数据, dataModelId: {}, 数据量: {}",
                    dataModelId, lastYearData.size());
            } else {
                // 去年同期补齐策略：获取去年同期数据
                lastYearData = IndicatorDataContext.getLastYearData().orElse(Collections.emptyList());
                log.debug("同期补齐策略获取去年同期数据, dataModelId: {}, 数据量: {}",
                    dataModelId, lastYearData.size());
            }
            return lastYearData;
        } catch (Exception e) {
            String dataModelId = context != null ? context.getDataModelId().toString() : "";
            log.error("获取去年数据失败, 策略: {}, dataModelId: {}",
                strategy != null ? strategy.getDescription() : "null", dataModelId, e);
            return Collections.emptyList();
        }
    }
}
