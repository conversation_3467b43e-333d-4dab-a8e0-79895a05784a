package com.trs.moye.stream.engine.service.impl;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.when;

import com.fasterxml.jackson.databind.JsonNode;
import com.trs.ai.ty.base.service.MonitorBufferService;
import com.trs.moye.ability.utils.BeanUtil;
import com.trs.moye.base.common.utils.JsonUtils;
import com.trs.moye.stream.engine.cache.CacheService;
import com.trs.moye.stream.engine.domain.entity.ExecuteOperatorBaseInfo;
import com.trs.moye.stream.engine.properties.AbilityCenterGatewayProperties;
import com.trs.moye.stream.engine.properties.LogProperties;
import com.trs.moye.stream.engine.service.MsgProcessService;
import com.trs.moye.stream.engine.service.MsgProcessServiceImpl;
import com.trs.moye.stream.engine.utils.AddressHelper;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.MockedStatic.Verification;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class MsgProcessServiceImplTest {

    @Mock
    private CacheService cacheService;

    @Mock
    private MonitorBufferService monitorBufferService;

    @Mock
    private AbilityCenterGatewayProperties abilityCenterGatewayProperties;

    @Mock
    private LogProperties logProperties;

    @Mock
    private AddressHelper addressHelper;

    @Test
    void testProcessMessage() {
        // 创建基础信息，包含 fieldMapping 和 inputFields
        ExecuteOperatorBaseInfo baseInfo = JsonUtils.parseObject(
            """
                {
                  "dataModelId": 8686,
                  "dataModelName": "测试",
                  "dataSourceFields": [
                    {
                      "id": 117052,
                      "dataModelId": 8686,
                      "zhName": "年龄",
                      "enName": "age",
                      "type": "INT",
                      "title": false,
                      "multiValue": false
                    },
                    {
                      "id": 117053,
                      "dataModelId": 8686,
                      "zhName": "姓名",
                      "enName": "name",
                      "type": "STRING",
                      "title": false,
                      "multiValue": false
                    },
                    {
                      "id": 117054,
                      "dataModelId": 8686,
                      "zhName": "中文名",
                      "enName": "zhName",
                      "type": "STRING",
                      "title": false,
                      "multiValue": false
                    }
                  ],
                  "titleEnName": "zhName",
                  "operators": [
                    {
                      "pipelineId": 123,
                      "conditions": [],
                      "name": "测试",
                      "inputFields": {
                        "name": {
                          "enName": "name",
                          "zhName": "姓名",
                          "type": "STRING",
                          "description": "姓名",
                          "required": false
                        },
                        "age": {
                          "enName": "age",
                          "zhName": "年龄",
                          "type": "INT",
                          "description": "年龄",
                          "required": false
                        }
                      },
                      "outputFields": {
                        "name": {
                          "enName": "name",
                          "zhName": "姓名",
                          "type": "STRING",
                          "description": "姓名",
                          "required": false
                        },
                        "currentDateTime": {
                          "enName": "currentDateTime",
                          "zhName": "当前时间",
                          "type": "STRING",
                          "description": "当前日期时间",
                          "required": false
                        },
                        "age": {
                          "enName": "age",
                          "zhName": "年龄",
                          "type": "INT",
                          "description": "年龄",
                          "required": false
                        }
                      },
                      "abilityId": 1,
                      "ability": {
                        "enName": "getCurrentDateTime",
                        "type": "LOCAL",
                        "path": "com.trs.moye.ability.base.DateTimeAbility.getCurrentDateTime",
                        "inputSchema": {
                          "type": "OBJECT",
                          "required": false,
                          "properties": {
                            "format": {
                              "type": "STRING",
                              "description": "格式字符串",
                              "required": false
                            }
                          }
                        },
                        "outputSchema": {
                          "type": "STRING",
                          "description": "当前日期时间",
                          "required": false
                        }
                      },
                      "inputBind": {
                        "bindings": {
                          "format": {
                            "fixedValue": "YYYY-MM-DD hh:mm:ss"
                          }
                        }
                      },
                      "outputBind": {
                        "bindings": {
                          "currentDateTime": {
                            "method": "APPEND",
                            "fieldName": "currentDateTime",
                            "jsonPath": ""
                          }
                        }
                      },
                      "config": {
                        "beginTime": "12:00:00",
                        "endTime": "18:00:00",
                        "overTime": 1
                      },
                      "enabled": true
                    }
                  ],
                  "inputFields": {
                    "name": {
                      "enName": "name",
                      "zhName": "姓名",
                      "type": "STRING",
                      "description": "姓名",
                      "required": false
                    },
                    "age": {
                      "enName": "age",
                      "zhName": "年龄",
                      "type": "INT",
                      "description": "年龄",
                      "required": false
                    },
                    "zhName": {
                      "enName": "zhName",
                      "zhName": "中文名",
                      "type": "STRING",
                      "description": "中文名",
                      "required": false
                    }
                  },
                  "fieldMapping": {
                    "firstName": ["name"],
                    "userAge": ["age"]
                  }
                }""",
            ExecuteOperatorBaseInfo.class);

        when(cacheService.getBaseInfo(8686)).thenReturn(baseInfo);
        MockedStatic<BeanUtil> mockedStatic = mockStatic(BeanUtil.class);
        mockedStatic.when((Verification) BeanUtil.getBean(AddressHelper.class))
            .thenReturn(addressHelper);

        // 构造输入消息，包含映射前的字段名
        String inputJson = """
            {
              "firstName": "John Doe",
              "userAge": 25,
              "zhName": "约翰"
            }""";
        JsonNode inputMessage = JsonUtils.parseJsonNode(inputJson);

        MsgProcessService msgProcessService = new MsgProcessServiceImpl(cacheService, monitorBufferService,
            abilityCenterGatewayProperties, logProperties);
        JsonNode result = msgProcessService.processMessage(8686, inputMessage);
        mockedStatic.close();

        // 验证结果，包括字段映射和算子执行结果
        assertThat(result).isNotNull();
        // 验证字段映射：userAge -> age
        assertThat(result.has("age")).isTrue();
        assertThat(result.get("age").asInt()).isEqualTo(25);
        // 验证字段映射：firstName -> name
        assertThat(result.has("name")).isTrue();
        assertThat(result.get("name").asText()).isEqualTo("John Doe");
        // 验证字段保留
        assertThat(result.has("zhName")).isTrue();
        assertThat(result.get("zhName").asText()).isEqualTo("约翰");
        // 验证算子执行添加了新字段
        assertThat(result.has("currentDateTime")).isTrue();
    }

    @Test
    void testMsgProcessWithoutFieldMapping() {
        // 创建基础信息，没有 fieldMapping
        ExecuteOperatorBaseInfo baseInfo = JsonUtils.parseObject(
            """
                {
                  "dataModelId": 8686,
                  "dataModelName": "测试",
                  "titleEnName": "title",
                  "operators": [
                    {
                      "pipelineId": 123,
                      "conditions": [],
                      "name": "测试",
                      "abilityId": 1,
                      "ability": {
                        "enName": "getCurrentDateTime",
                        "type": "LOCAL",
                        "path": "com.trs.moye.ability.base.DateTimeAbility.getCurrentDateTime",
                        "inputSchema": {
                          "type": "OBJECT",
                          "required": false,
                          "properties": {
                            "format": {
                              "type": "STRING",
                              "description": "格式字符串",
                              "required": false
                            }
                          }
                        },
                        "outputSchema": {
                          "type": "STRING",
                          "description": "当前日期时间",
                          "required": false
                        }
                      },
                      "inputBind": {
                        "bindings": {
                          "format": {
                            "fixedValue": "YYYY-MM-DD hh:mm:ss"
                          }
                        }
                      },
                      "outputBind": {
                        "bindings": {
                          "currentDateTime": {
                            "method": "APPEND",
                            "fieldName": "currentDateTime",
                            "jsonPath": ""
                          }
                        }
                      },
                      "enabled": true
                    }
                  ],
                  "inputFields": {
                    "title": {
                      "enName": "title",
                      "zhName": "标题",
                      "type": "STRING"
                    },
                    "content": {
                      "enName": "content",
                      "zhName": "内容",
                      "type": "STRING"
                    },
                    "zhName": {
                      "enName": "zhName",
                      "zhName": "中文名",
                      "type": "STRING"
                    }
                  }
                }""",
            ExecuteOperatorBaseInfo.class);

        when(cacheService.getBaseInfo(8686)).thenReturn(baseInfo);
        MockedStatic<BeanUtil> mockedStatic = mockStatic(BeanUtil.class);
        mockedStatic.when((Verification) BeanUtil.getBean(AddressHelper.class))
            .thenReturn(addressHelper);

        // 构造输入消息
        String inputJson = """
            {
              "title": "测试标题",
              "content": "测试内容"
            }""";
        JsonNode inputMessage = JsonUtils.parseJsonNode(inputJson);

        MsgProcessService msgProcessService = new MsgProcessServiceImpl(cacheService, monitorBufferService,
            abilityCenterGatewayProperties, logProperties);
        JsonNode result = msgProcessService.processMessage(8686, inputMessage);
        mockedStatic.close();

        // 验证结果
        assertThat(result).isNotNull();
        assertThat(result.has("title")).isTrue();
        assertThat(result.get("title").asText()).isEqualTo("测试标题");
        assertThat(result.has("content")).isTrue();
        assertThat(result.get("content").asText()).isEqualTo("测试内容");
        assertThat(result.has("currentDateTime")).isTrue();
        assertThat(result.has("zhName")).isTrue();
    }
}