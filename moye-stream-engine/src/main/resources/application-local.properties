spring.application.name=moye-stream-engine
# Nacos\u914D\u7F6E\u4E2D\u5FC3
spring.cloud.nacos.username=nacos
spring.cloud.nacos.password=nacos
spring.cloud.nacos.server-addr=192.168.211.100:8849
spring.cloud.nacos.config.namespace=local
spring.cloud.nacos.discovery.namespace=${spring.cloud.nacos.config.namespace}
spring.config.import[0]=nacos:${spring.application.name}.properties?refresh=true
spring.config.import[1]=nacos:mysql-moye-v4.properties
spring.config.import[2]=nacos:mysql-moye-v4-dynamic.properties
spring.config.import[3]=nacos:redis.properties
spring.config.import[4]=nacos:ability-center.properties
spring.config.import[5]=nacos:kafka.properties

logging.level.com.alibaba.cloud.nacos=debug