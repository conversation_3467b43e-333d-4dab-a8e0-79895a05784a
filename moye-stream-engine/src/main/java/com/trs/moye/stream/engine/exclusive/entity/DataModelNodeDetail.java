package com.trs.moye.stream.engine.exclusive.entity;

import java.util.List;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 数据建模节点详情列表：数据建模所在引擎节点
 *
 * <AUTHOR>
 * @since 2025-01-10 13:44
 */
@Data
@NoArgsConstructor
public class DataModelNodeDetail {

    /**
     * 数据建模id
     */
    private int id;

    /**
     * 数据建模名称
     */
    private String name;

    /**
     * 期望节点数
     */
    private int expectNodeCount;


    /**
     * 节点列表
     */
    private List<String> actualNodes;

    /**
     * 实际节点数
     *
     * @return 真实节点数量
     */
    public int getActualNodeCount() {
        return actualNodes == null ? 0 : actualNodes.size();
    }
}
