package com.trs.moye.stream.engine.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.trs.moye.ability.entity.Ability;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * Mapper接口，用于操作Ability实体。 该接口扩展了BaseMapper接口，提供了对Ability表的基本CRUD操作。
 */
@Mapper
public interface AbilityMapper extends BaseMapper<Ability> {

    /**
     * 根据ID查询能力
     *
     * @param id 能力ID
     * @return 能力实体
     */
    Ability selectById(@Param("id") Integer id);

    /**
     * 根据能力路径查询能力
     *
     * @param path 能力路径
     * @return 能力实体
     */
    Ability selectByPath(@Param("path") String path);

    /**
     * 根据能力路径列表查询能力
     *
     * @param paths 路径
     * @return {@link List }<{@link Ability }>
     */
    List<Ability> selectByPaths(@Param("paths") List<String> paths);

    /**
     * 根据enName查询能力
     *
     * @param enName enName
     * @return 能力实体
     */
    Ability selectByEnName(@Param("id") String enName);

}
