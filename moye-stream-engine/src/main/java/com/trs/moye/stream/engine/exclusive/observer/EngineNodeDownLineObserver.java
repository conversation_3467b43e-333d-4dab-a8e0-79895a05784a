package com.trs.moye.stream.engine.exclusive.observer;

import com.trs.ai.moye.redis.starter.service.RedisService;
import com.trs.ai.ty.base.annotation.EtcdListener;
import com.trs.ai.ty.base.annotation.EventScope;
import com.trs.ai.ty.base.event.EventType;
import com.trs.ai.ty.base.utils.AppUtil;
import com.trs.moye.stream.engine.common.IpHelper;
import com.trs.moye.stream.engine.exclusive.EngineNodeAllocationService;
import com.trs.moye.stream.engine.exclusive.EngineNodeRegister;
import com.trs.moye.stream.engine.exclusive.constant.Constants;
import com.trs.moye.stream.engine.service.EtcdService;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;
import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @since 2025-01-10 14:21
 */
@Order(Constants.APPLICATION_CONFIG_OBSERVER_ORDER + 1)
@Slf4j
@EventScope
@Component
public class EngineNodeDownLineObserver {

    private static final String SERVER_DELETE_LOCK_PREFIX = "node-control-lock-";

    // 单位秒
    private static final int REDIS_LOCK_TTL_SECONDS = 20;

    @Resource
    private RedisService redisService;

    @Resource
    private EngineNodeAllocationService engineNodeAllocationService;

    @Resource
    private IpHelper ipHelper;

    private AutoRegisterUtil autoRegisterUtil;

    @PostConstruct
    private void init() {
        autoRegisterUtil = new AutoRegisterUtil();
    }

    @Resource
    private EngineNodeRegister register;

    /**
     * 缩容回调
     *
     * @param deleteNode 删除的节点
     * @param value 节点信息
     */
    @EtcdListener(eventType = EventType.DELETE, watchCatalog = "", watchKey = EtcdService.ETCD_REGISTER_DIRECTORY, isPrefix = true)
    public synchronized void shrinkageCapacityCallback(String deleteNode, String value) {
        String currentServerNode = ipHelper.getCurrentServerNode();
        if (addLock(deleteNode)) {
            if (currentServerNode.equals(deleteNode)) {
                autoRegisterUtil.serverOfflineAutoRegisterServerNode();
            } else {
                engineNodeAllocationService.allocateServerNode();
            }
        } else {
            if (currentServerNode.equals(deleteNode)) {
                autoRegisterUtil.serverOfflineAutoRegisterServerNode();
            }
        }
    }

    private boolean addLock(String deleteNode) {
        String redisLockKey = SERVER_DELETE_LOCK_PREFIX + deleteNode;
        String currentServerNode = ipHelper.getCurrentServerNode();
        return redisService.setIfNotExist(redisLockKey, currentServerNode, REDIS_LOCK_TTL_SECONDS);
    }

    private class AutoRegisterUtil {

        private final Lock autoRegisterServerLock = new ReentrantLock();

        // 为避免创建多个自动注册服务的线程，用这个状态记录是否已启动自动注册服务的线程
        private final AtomicBoolean autoRegisterServerTaskEnableStatus = new AtomicBoolean(false);

        /**
         * 当前服务异常掉线后自动注册服务节点，如果注册失败，则启动一个后台线程定时重试。
         */
        public void serverOfflineAutoRegisterServerNode() {
            try {
                log.warn("当前服务掉线，准备重新注册。。。");
                long startTime = System.currentTimeMillis();
                register.registerAndAllocationServerNode("服务异常下线首次尝试注册");
                log.warn("服务掉线后重新注册成功，耗时：{}ms。。。", System.currentTimeMillis() - startTime);
            } catch (Throwable e) {
                log.error("注册专属节点服务失败", e);
                enableAutoRegisterServerTask();
            }
        }

        private void enableAutoRegisterServerTask() {
            try {
                autoRegisterServerLock.lock();
                if (autoRegisterServerTaskEnableStatus.get()) {
                    log.warn("自动注册服务任务已经存在");
                    return;
                }
                setAutoRegisterServerTaskEnableStatus(true);
                AppUtil.THREAD_POOL.execute(() -> {
                    log.error("启动自动注册服务任务。。。");
                    long startTime = System.currentTimeMillis();
                    long sleepTimeMillis = 15000;
                    int tryCount = 0;
                    while (true) {
                        try {
                            tryCount++;
                            register.registerAndAllocationServerNode("服务异常下线第" + tryCount + "次尝试注册");
                            log.error("服务注册成功，重试次数：{}，耗时：{}ms", tryCount, System.currentTimeMillis() - startTime);
                            break;
                        } catch (Throwable ee) {
                            log.error("第[" + tryCount + "]次注册专属节点服务失败，" + sleepTimeMillis + "毫秒后重试", ee);
                            try {
                                Thread.sleep(sleepTimeMillis);
                            } catch (InterruptedException eee) {
                                Thread.currentThread().interrupt();
                                log.error("线程睡眠发生中断异常", eee);
                            } catch (Exception eee) {
                                log.error("线程睡眠发生异常", eee);
                            }
                        }
                    }
                    setAutoRegisterServerTaskEnableStatus(false);
                });
            } finally {
                autoRegisterServerLock.unlock();
            }
        }

        private void setAutoRegisterServerTaskEnableStatus(boolean enableStatus) {
            try {
                autoRegisterServerLock.lock();
                autoRegisterServerTaskEnableStatus.set(enableStatus);
            } finally {
                autoRegisterServerLock.unlock();
            }
        }
    }

}
