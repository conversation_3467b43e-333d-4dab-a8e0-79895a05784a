package com.trs.moye.stream.engine.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.trs.moye.stream.engine.exclusive.entity.StreamExclusiveNodeConfig;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 * @since 2025-01-10 16:04
 */

@Mapper
public interface StreamExclusiveNodeConfigMapper extends BaseMapper<StreamExclusiveNodeConfig> {

    /**
     * id集合查询
     *
     * @return 专属节点配置列表
     */
    List<StreamExclusiveNodeConfig> selectAll();

    /**
     * 更新并发线程数情况
     *
     * @param concurrentThreads 并发线程数
     */
    void updateConcurrentThreadsWhenNull(@Param("concurrentThreads") Integer concurrentThreads);

}
