package com.trs.moye.stream.engine.common;

import com.trs.ai.ty.grpc.etcd.EtcdUtil;
import com.trs.ai.ty.grpc.util.IPHelper;
import com.trs.moye.base.common.exception.BizException;
import java.net.Inet4Address;
import java.util.Optional;
import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @since 2025-01-15 17:26
 */
@Component
public class IpHelper {

    @Resource
    private EtcdUtil etcdUtil;

    private String currentServerIp;

    @PostConstruct
    private void init() {
        currentServerIp = getServerNode();
    }

    private String getServerNode() {
        Optional<Inet4Address> inet4AddressOptional = IPHelper.getIpByTcpSocket(etcdUtil.getEtcdClusterNodes());
        if (inet4AddressOptional.isEmpty()) {
            throw new BizException("未发现本机ip地址");
        }
        return inet4AddressOptional.get().getHostAddress();
    }

    /**
     * 获取当前服务节点ip
     *
     * @return ip地址
     */
    public String getIp() {
        return currentServerIp;
    }

    /**
     * 获取当前服务节点ip
     *
     * @return ip地址
     */
    public String getCurrentServerNode() {
        return getIp();
    }
}
