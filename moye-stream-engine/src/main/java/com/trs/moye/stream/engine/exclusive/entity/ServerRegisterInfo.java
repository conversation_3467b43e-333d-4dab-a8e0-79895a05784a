package com.trs.moye.stream.engine.exclusive.entity;

import com.alibaba.fastjson.annotation.JSONField;
import java.time.LocalDateTime;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2025-01-17 14:22
 */
@Data
@NoArgsConstructor
public class ServerRegisterInfo {

    private String node;

    private String remark;

    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime registerTime;

    public ServerRegisterInfo(String node, String remark) {
        this.node = node;
        this.remark = remark;
        this.registerTime = LocalDateTime.now();
    }
}
