package com.trs.moye.stream.engine.exclusive.observer;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.trs.ai.ty.base.annotation.EtcdListener;
import com.trs.ai.ty.base.annotation.EventScope;
import com.trs.ai.ty.grpc.etcd.EtcdUtil;
import com.trs.moye.base.common.utils.ValidationUtils;
import com.trs.moye.base.data.model.task.entity.StreamProcessTask;
import com.trs.moye.stream.engine.common.IpHelper;
import com.trs.moye.stream.engine.exclusive.entity.EngineNodeAllocationDetail;
import com.trs.moye.stream.engine.exclusive.entity.EngineSubscribeTable;
import com.trs.moye.stream.engine.process.DataProcessConsumerManager;
import com.trs.moye.stream.engine.process.ThreadPoolMessageProcessor;
import com.trs.moye.stream.engine.service.EtcdService;
import java.util.List;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @since 2025-01-17 18:01
 */
@Slf4j
@EventScope
@Component
public class NodeAllocationDetailObserver {

    @Resource
    private EtcdUtil etcdUtil;

    @Resource
    private DataProcessConsumerManager consumerManager;

    @Resource
    private IpHelper ipHelper;

    @Resource
    private EtcdService etcdService;

    @Resource
    private ThreadPoolMessageProcessor threadPoolMessageProcessor;

    /**
     * 监听节点分配详情变化
     *
     * @param nodeAllocationDetailJson 节点分配详情
     */
    @EtcdListener(watchCatalog = "", watchKey = EtcdService.ETCD_NODE_ALLOCATION_DETAIL)
    public void watchNodeAllocationDetailChange(String nodeAllocationDetailJson) {
        if (ObjectUtils.isEmpty(nodeAllocationDetailJson)) {
            return;
        }
        EngineNodeAllocationDetail detail = JSON.parseObject(nodeAllocationDetailJson,
            EngineNodeAllocationDetail.class);
        ValidationUtils.validate(detail);
        EngineSubscribeTable subscribeTable = detail.buildSubscribeTable(
            ipHelper.getCurrentServerNode());
        // 设置并发线程数
        threadPoolMessageProcessor.setExecutorPoolSize(subscribeTable.getConcurrentThreads());
        List<StreamProcessTask> needStartTasks = etcdService.getStartedTasks().stream()
            .filter(task -> subscribeTable.isIn(task.getId()))
            .toList();
        if (log.isDebugEnabled()) {
            log.debug("服务节点【{}】：节点分配详情：\n{}\n节点订阅信息：\n{}"
                , ipHelper.getCurrentServerNode()
                , JSON.toJSONString(detail, SerializerFeature.PrettyFormat)
                , JSON.toJSONString(subscribeTable, SerializerFeature.PrettyFormat));
        }
        consumerManager.refreshSubscribeTableAndConsumer(subscribeTable, needStartTasks);
    }


}
