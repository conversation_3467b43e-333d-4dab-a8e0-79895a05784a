package com.trs.moye.stream.engine;

import com.trs.moye.stream.engine.properties.AbilityCenterGatewayProperties;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.cloud.openfeign.EnableFeignClients;

/**
 * Stream engine application
 *
 * <AUTHOR>
 * @since 2024/12/30
 */
@EnableCaching
@EnableFeignClients
@SpringBootApplication(scanBasePackages = "com.trs")
@MapperScan(basePackages = {"com.trs.**.dao", "com.trs.**.mapper"})
@EnableConfigurationProperties({AbilityCenterGatewayProperties.class})
public class StreamEngineApplication {

    /**
     * Main method
     *
     * @param args command line arguments
     */
    public static void main(String[] args) {
        SpringApplication.run(StreamEngineApplication.class, args);
    }
}
