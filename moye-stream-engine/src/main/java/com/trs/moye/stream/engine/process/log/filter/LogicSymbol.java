package com.trs.moye.stream.engine.process.log.filter;

import com.trs.moye.stream.engine.utils.StreamEngineStopWatch.TaskInfo;
import java.util.function.Predicate;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 逻辑运算符
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-12-20 21:17
 */
@AllArgsConstructor
@Getter
public enum LogicSymbol {

    ADN("并且") {
        @Override
        public Predicate<TaskInfo> combinationPredicate(Predicate<TaskInfo> p1, Predicate<TaskInfo> p2) {
            return p1.and(p2);
        }
    },
    OR("或者") {
        @Override
        public Predicate<TaskInfo> combinationPredicate(Predicate<TaskInfo> p1, Predicate<TaskInfo> p2) {
            return p1.or(p2);
        }
    },
    ;

    private final String value;

    /**
     * 逻辑运算符
     *
     * @param value 逻辑运算符字符串
     * @return 逻辑运算符
     */
    public static LogicSymbol fromValue(String value) {
        for (LogicSymbol symbol : values()) {
            if (symbol.value.equals(value)) {
                return symbol;
            }
        }
        throw new IllegalArgumentException("不支持的逻辑符号：" + value);
    }

    /**
     * 组合过滤断言
     *
     * @param p1 断言1
     * @param p2 断言2
     * @return 新断言
     */
    public abstract Predicate<TaskInfo> combinationPredicate(Predicate<TaskInfo> p1, Predicate<TaskInfo> p2);
}
