package com.trs.moye.stream.engine.exclusive.observer;

import com.trs.ai.ty.base.annotation.EtcdListener;
import com.trs.ai.ty.base.annotation.EventScope;
import com.trs.moye.stream.engine.exclusive.EngineNodeAllocationService;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @since 2025-01-17 18:01
 */
@Slf4j
@EventScope
@Component
public class NodeAllocationTriggerObserver {

    private static final String ETCD_NODE_ALLOCATION_TRIGGER = "node-control/node-allocation-trigger";

    @Resource
    private EngineNodeAllocationService engineNodeAllocationService;

    /**
     * 触发节点分配
     *
     * @param triggerNode 触发的节点
     */
    @EtcdListener(watchCatalog = "", watchKey = ETCD_NODE_ALLOCATION_TRIGGER)
    public void triggerNodeAllocate(String triggerNode) {
        engineNodeAllocationService.executeNodeAllocationStrategy(triggerNode, () -> {
        });
    }

}
