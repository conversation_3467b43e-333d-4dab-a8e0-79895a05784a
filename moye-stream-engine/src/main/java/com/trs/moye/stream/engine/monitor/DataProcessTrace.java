package com.trs.moye.stream.engine.monitor;

import com.trs.ai.ty.base.entity.po.TracerData;
import com.trs.moye.base.common.utils.SnowflakeIdUtil;
import com.trs.moye.stream.engine.domain.entity.MessageContext;
import com.trs.moye.stream.engine.utils.StreamEngineStopWatch.TaskInfo;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * stream-engine 算子链路监控实体
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2021/5/12 10:12
 **/
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@ToString
public class DataProcessTrace extends DataProcessLog {

    /**
     * 流程唯一id-必填
     **/
    private long processId;
    /**
     * 算子id
     **/
    private Integer operatorId;
    /**
     * 能力id
     */
    private Integer abilityId;
    /**
     * 存储点id
     */
    private Integer storageId;
    /**
     * 算子名称
     **/
    private String operatorName;
    /**
     * 环节内处理顺序
     **/
    private Integer processingOrder;


    /**
     * 创建算子链路追踪数据
     *
     * @param messageContext 消息上下文
     * @param taskInfo       任务信息
     */
    public DataProcessTrace(MessageContext messageContext, TaskInfo taskInfo, Integer order) {
        super(messageContext);
        this.setProcessId(SnowflakeIdUtil.newId());
        this.setStartTime(taskInfo.getStartTime());
        this.setEndTime(taskInfo.getEndTime());
        this.setProcessingTime(taskInfo.getTimeMillis());
        this.setOperatorId(taskInfo.getOperatorId());
        this.setOperatorName(taskInfo.getOperatorName());
        //TODO 环节内处理顺序是否要保存？
        this.setProcessingOrder(order);
        this.setIsError(taskInfo.getIsError());
        this.setErrorMsg(taskInfo.getErrorMessage());
        this.setInput(taskInfo.getInput());
        this.setOutput(taskInfo.getOutput());
        this.setAbilityId(taskInfo.getAbilityId());
        this.setStorageId(taskInfo.getStorageId());
    }

    /**
     * 转换成TracerData
     *
     * @return {@link TracerData}
     */
    public TracerData toTracerData() {
        TracerData tracerData = new TracerData();
        tracerData.setRecordId(recordId);
        tracerData.setProcessId(processId);
        tracerData.setDataSourceId(dataModelId);
        tracerData.setDataSourceName(dataModelName);
        tracerData.setMsgTitle(msgTitle);
        tracerData.setMsgContent(input);
        tracerData.setResults(output);
        tracerData.setStartTime(startTime);
        tracerData.setEndTime(endTime);
        tracerData.setStorageTime(storageTime);
        tracerData.setProcessingTime(processingTime);
        tracerData.setOperatorId(operatorId);
        tracerData.setProcessingName(operatorName);
        tracerData.setIsError(isError);
        tracerData.setErrorMsg(errorMsg);
        tracerData.setPodIp(podIp);
        tracerData.setProcessingOrder(processingOrder);
        tracerData.setMasterNode("DATA_PROCESSING");
        tracerData.setProcessingType("数据处理");
        tracerData.setAbilityId(abilityId);
        tracerData.setStorageId(storageId);
        return tracerData;
    }
}
