package com.trs.moye.stream.engine.exclusive;

import com.trs.moye.base.common.response.IdNameResponse;
import com.trs.moye.stream.engine.exclusive.entity.EngineNodeAllocationDetail;
import com.trs.moye.stream.engine.exclusive.entity.ExclusiveConfigDetail;
import com.trs.moye.stream.engine.exclusive.entity.StreamExclusiveNodeConfig;
import com.trs.moye.stream.engine.exclusive.entity.Summary;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Comparator;
import java.util.LinkedList;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;
import javax.annotation.Nullable;

/**
 * 引擎节点分配辅助类，用于处理引擎配置的节点分配问题。
 *
 * <AUTHOR>
 * @since 2025-01-10 14:19
 */
public class EngineNodeAllocationHelper {

    /**
     * 引擎配置列表：触发节点分配时的引擎配置及实际分配给其的实际节点列表初始的
     */
    private final List<ExclusiveConfigDetail> originalExclusiveConfigDetailList;

    /**
     * 引擎配置列表：触发节点分配时的引擎配置及实际分配给其的实际节点列表初始的
     */
    private final List<ExclusiveConfigDetail> exclusiveConfigDetailList;

    /**
     * 所有实际节点列表
     */
    private final Set<String> allActualNodes;

    /**
     * 可分配的节点列表：初始为所有实际节点，在分配过程中会被逐步移除。
     */
    private final LinkedList<String> delayAllocationNodes;

    public EngineNodeAllocationHelper(List<StreamExclusiveNodeConfig> exclusiveNodeConfigs,
        Set<String> allActualNodes) {
        exclusiveNodeConfigs.sort(Comparator.comparing(StreamExclusiveNodeConfig::getPriority));
        this.originalExclusiveConfigDetailList = exclusiveNodeConfigs.stream()
            .map(ExclusiveConfigDetail::new)
            .collect(Collectors.toList());
        this.exclusiveConfigDetailList = new ArrayList<>(this.originalExclusiveConfigDetailList);
        this.allActualNodes = allActualNodes;
        this.delayAllocationNodes = new LinkedList<>(this.allActualNodes);
    }

    /**
     * 排除期望节点数等于0的专属配置
     */
    public void excludeExpectNodeCountEqualsZeroExclusiveConfig() {
        exclusiveConfigDetailList.removeIf(exclusiveConfigDetail -> {
            if (exclusiveConfigDetail.getExpectNodeCount() <= 0) {
                exclusiveConfigDetail.setRemark("期望节点数小于等于0，不分配专属节点");
                return true;
            }
            return false;
        });
    }

    /**
     * 排除所有数据建模已关闭的引擎配置
     *
     * @param startedDataModelIds 已开启的数据模型ID列表
     */
    public void excludeStopExclusiveConfig(Collection<Integer> startedDataModelIds) {
        exclusiveConfigDetailList.removeIf(exclusiveConfigDetail -> {
            for (IdNameResponse dataModel : exclusiveConfigDetail.getDataModels()) {
                if (startedDataModelIds.contains(dataModel.getId())) {
                    return false;
                }
            }
            exclusiveConfigDetail.setRemark("没有启动的数据建模，不分配专属节点");
            return true;
        });
    }


    /**
     * 同步旧引擎配置详情：引擎配置尽量与上一次保持一致。在这里引擎配置的多余的节点会被抽到可分配的节点列表中，节点不足的不会分配节点
     *
     * @param oldExclusiveConfigDetailList 旧引擎配置详情列表
     */
    public void syncOldExclusiveConfigDetail(List<ExclusiveConfigDetail> oldExclusiveConfigDetailList) {
        // 设置旧引擎配置详情中的数据模型ID集合，便于后续匹配
        for (ExclusiveConfigDetail oldExclusiveConfigDetail : originalExclusiveConfigDetailList) {
            oldExclusiveConfigDetail.setDataModelIds(oldExclusiveConfigDetail.getDataModels().stream()
                .map(IdNameResponse::getId).collect(Collectors.toSet()));
        }
        for (ExclusiveConfigDetail exclusiveConfigDetail : exclusiveConfigDetailList) {
            for (ExclusiveConfigDetail oldExclusiveConfigDetail : oldExclusiveConfigDetailList) {
                if (exclusiveConfigDetail.getDataModelIds().equals(oldExclusiveConfigDetail.getDataModelIds())) {
                    syncOldExclusiveConfig(exclusiveConfigDetail, oldExclusiveConfigDetail);
                    break;
                }
            }
        }
    }

    private void syncOldExclusiveConfig(ExclusiveConfigDetail exclusiveConfigDetail,
        ExclusiveConfigDetail oldExclusiveConfigDetail) {
        LinkedList<String> nodes = exclusiveConfigDetail.getActualNodes();
        LinkedList<String> oldNodes = oldExclusiveConfigDetail.getActualNodes();
        for (String oldNode : oldNodes) {
            if (delayAllocationNodes.remove(oldNode)) {
                nodes.add(oldNode);
            }
            if (nodes.size() == exclusiveConfigDetail.getExpectNodeCount()) {
                break;
            }
        }
    }

    /**
     * 执行分配规则
     *
     * @return 分配详情
     */
    public EngineNodeAllocationDetail executeAllocationRule() {
        // 分配通用节点
        allocationCommonNode();
        // 尽量保证每个专属配置至少分配一个节点
        tryEnsureOneExclusiveNode();
        // 分配完所有可分配的节点（清空delayAllocationNodes）
        allocationAllDelayAllocationNodes();
        // 按优先级调整节点：按低优先级、次低优先级的顺序转移节点满足高优先级配置
        priorityAdjustNode();
        // 构造返回结果
        return buildEngineNodeAllocationDetail();
    }

    private void allocationCommonNode() {
        ExclusiveConfigDetail commonConfigDetail = getCommonConfigDetail();
        // 先从可分配的节点中分配
        while (!commonConfigDetail.isSatisfyExpectNodeCount()
            && !delayAllocationNodes.isEmpty()) {
            String node = delayAllocationNodes.removeLast();
            commonConfigDetail.getActualNodes().add(node);
        }
        // 再从其他引擎配置中转移节点
        String delayTransferNode;
        while (!commonConfigDetail.isSatisfyExpectNodeCount()
            && (delayTransferNode = findDelayTransferNode(0, false)) != null) {
            commonConfigDetail.getActualNodes().add(delayTransferNode);
        }
    }

    private ExclusiveConfigDetail getCommonConfigDetail() {
        return exclusiveConfigDetailList.get(0);
    }

    /**
     * 尽量保证每个专属配置至少分配一个节点，如果有可分配的节点则优先满足高优先级节点；否则低优先级、次低优先级的顺序转移节点满足高优先级节点
     */
    private void tryEnsureOneExclusiveNode() {
        for (int i = 0; i < exclusiveConfigDetailList.size(); i++) {
            if (exclusiveConfigDetailList.get(i).getActualNodeCount() > 0) {
                continue;
            }
            allocationOneNode(i);
        }
    }

    private void allocationOneNode(int currentNodeIndex) {
        ExclusiveConfigDetail detail = exclusiveConfigDetailList.get(currentNodeIndex);
        String delayAllocationNode;
        if (delayAllocationNodes.isEmpty()) {
            delayAllocationNode = findDelayTransferNode(currentNodeIndex, true);
        } else {
            delayAllocationNode = delayAllocationNodes.removeLast();
        }
        if (delayAllocationNode != null) {
            detail.getActualNodes().add(delayAllocationNode);
        }
    }

    @Nullable
    private String findDelayTransferNode(int currentNodeIndex, boolean allowExceedCurrentNodePriority) {
        // 第1个是通用节点
        int lowerLimitIndex = allowExceedCurrentNodePriority ? 1 : currentNodeIndex;
        for (int i = exclusiveConfigDetailList.size() - 1; i >= lowerLimitIndex; i--) {
            ExclusiveConfigDetail detail = exclusiveConfigDetailList.get(i);
            if (i == currentNodeIndex || detail.getActualNodeCount() <= 1) {
                continue;
            }
            return detail.getActualNodes().removeLast();
        }
        return null;
    }

    private void allocationAllDelayAllocationNodes() {
        // 可分配的节点先分配给专属配置
        for (int i = 1; i < exclusiveConfigDetailList.size(); i++) {
            ExclusiveConfigDetail detail = exclusiveConfigDetailList.get(i);
            while (!delayAllocationNodes.isEmpty() && !detail.isSatisfyExpectNodeCount()) {
                String delayAllocationNode = delayAllocationNodes.removeLast();
                detail.getActualNodes().add(delayAllocationNode);
            }
            if (delayAllocationNodes.isEmpty()) {
                break;
            }
        }
        // 还有剩余节点，则分配给通用配置
        if (!delayAllocationNodes.isEmpty()) {
            getCommonConfigDetail().getActualNodes().addAll(delayAllocationNodes);
            delayAllocationNodes.clear();
        }
    }

    private void priorityAdjustNode() {
        String delayTransferNode;
        for (int i = 1; i < exclusiveConfigDetailList.size(); i++) {
            ExclusiveConfigDetail detail = exclusiveConfigDetailList.get(i);
            while (!detail.isSatisfyExpectNodeCount()
                && (delayTransferNode = findDelayTransferNode(i, false)) != null) {
                detail.getActualNodes().add(delayTransferNode);
            }
            // 当前专属配置节点还未满足，说明已经没有可转移的节点了，跳出循环
            if (!detail.isSatisfyExpectNodeCount()) {
                break;
            }
        }
    }

    private EngineNodeAllocationDetail buildEngineNodeAllocationDetail() {
        EngineNodeAllocationDetail detail = new EngineNodeAllocationDetail();
        detail.setExclusiveConfigDetailList(originalExclusiveConfigDetailList);
        detail.setAllActualNodes(allActualNodes);
        detail.setSummary(buildSummary());
        return detail;
    }

    private Summary buildSummary() {
        ExclusiveConfigDetail commonConfigDetail = getCommonConfigDetail();
        int expectExclusiveNodeCount = 0;
        int actualExclusiveNodeCount = 0;
        int expectCommonNodeCount = commonConfigDetail.getExpectNodeCount();
        int actualCommonNodeCount = commonConfigDetail.getActualNodeCount();
        for (int i = 1; i < exclusiveConfigDetailList.size(); i++) {
            ExclusiveConfigDetail detail = exclusiveConfigDetailList.get(i);
            expectExclusiveNodeCount += detail.getExpectNodeCount();
            actualExclusiveNodeCount += detail.getActualNodeCount();
        }
        Summary summary = new Summary();
        summary.setExpectCommonNodeCount(expectCommonNodeCount);
        summary.setActualCommonNodeCount(actualCommonNodeCount);
        summary.setExpectExclusiveNodeCount(expectExclusiveNodeCount);
        summary.setActualExclusiveNodeCount(actualExclusiveNodeCount);
        return summary;
    }
}
