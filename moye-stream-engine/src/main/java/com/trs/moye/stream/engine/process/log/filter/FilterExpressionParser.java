package com.trs.moye.stream.engine.process.log.filter;

import com.trs.ai.ty.base.utils.AppUtil;
import com.trs.moye.stream.engine.utils.StreamEngineStopWatch.TaskInfo;
import java.util.Objects;
import java.util.function.Predicate;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 2024-12-16 22:19
 */
@Slf4j
public class FilterExpressionParser {

    private FilterExpressionParser() {
    }

    /**
     * 解析过滤表达式
     *
     * @param expression 表达式
     * @return 过滤断言
     */
    public static Predicate<TaskInfo> parseExpression(String expression) {
        if (Objects.isNull(expression) || ObjectUtils.isEmpty(expression = expression.trim())) {
            return taskInfo -> true;
        }
        String[] expressionItems = expression.split("\\s+");
        if (expressionItems.length % 4 != 3) {
            throw new IllegalArgumentException(String.format("表达式【%s】有误，通过空格分割后，分割后字符串数组长度模4不等于3", expression));
        }
        Predicate<TaskInfo> predicate = buildCompareSymbolExpressionPredicate(expressionItems[0], expressionItems[1],
            expressionItems[2]);
        for (int i = 3; i < expressionItems.length; i += 4) {
            LogicSymbol logicSymbol = LogicSymbol.fromValue(expressionItems[i]);
            Predicate<TaskInfo> tempPredicate = buildCompareSymbolExpressionPredicate(expressionItems[i + 1],
                expressionItems[i + 2], expressionItems[i + 3]);
            predicate = logicSymbol.combinationPredicate(predicate, tempPredicate);
        }
        return predicate;
    }

    private static Predicate<TaskInfo> buildCompareSymbolExpressionPredicate(String fieldName,
        String compareSymbolValue, String value) {
        AppUtil.assertNotEmpty(value, "比较值不能为空");
        FilterField filterField = FilterField.fromValue(fieldName);
        CompareSymbol compareSymbol = CompareSymbol.fromValue(compareSymbolValue);
        return filterField.buildPredicate(compareSymbol, value);
    }


}
