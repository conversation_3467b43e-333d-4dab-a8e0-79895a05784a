package com.trs.ai.moye.data.ability.service;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.when;

import com.trs.ai.moye.common.response.TreeBaseResponse;
import com.trs.ai.moye.common.utils.BeanUtil;
import com.trs.ai.moye.data.ability.dao.AbilityMapper;
import com.trs.ai.moye.data.ability.request.AbilityRequest;
import com.trs.ai.moye.data.ability.request.SearchTypyArrayAbilityTreeRequest;
import com.trs.ai.moye.data.ability.response.AbilityResponse;
import com.trs.ai.moye.data.model.dao.batch.BatchOperatorMapper;
import com.trs.ai.moye.data.model.dao.operator.OperatorNewMapper;
import com.trs.ai.moye.data.operator.domain.response.AbilityArrangementTreeResponse;
import com.trs.ai.moye.data.operator.service.OperatorCategoryService;
import com.trs.ai.moye.permission.service.DynamicUserNameService;
import com.trs.moye.ability.entity.Ability;
import com.trs.moye.ability.enums.AbilityType;
import com.trs.moye.base.data.model.dao.OperatorBusinessCategoryMapper;
import com.trs.moye.base.data.model.entity.OperatorBusinessCategory;
import java.util.Collections;
import java.util.List;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.MockedStatic.Verification;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class AbilityServiceImplTest {

    @Mock
    private AbilityMapper abilityMapper;

    @Mock
    private OperatorCategoryService operatorCategoryService;

    @Mock
    private OperatorBusinessCategoryMapper operatorCategoryMapper;

    @Mock
    private OperatorNewMapper operatorNewMapper;

    @Mock
    private BatchOperatorMapper batchOperatorMapper;

    @Mock
    private DynamicUserNameService dynamicUserNameService;

    @InjectMocks
    private AbilityServiceImpl abilityService;

    @Test
    void selectById_ShouldReturnAbility() {
        // Mock
        Ability mockAbility = new Ability();
        mockAbility.setId(1);
        mockAbility.setType(AbilityType.LOCAL);
        when(operatorCategoryMapper.selectById(any())).thenReturn(new OperatorBusinessCategory());
        when(abilityMapper.selectById(1)).thenReturn(mockAbility);

        MockedStatic<BeanUtil> mockedStatic = mockStatic(BeanUtil.class);
        mockedStatic.when((Verification) BeanUtil.getBean(DynamicUserNameService.class))
            .thenReturn(dynamicUserNameService);

        // Test
        AbilityResponse result = abilityService.selectById(1);

        // Verify
        assertThat(result.getId()).isEqualTo(1);

        mockedStatic.close();
    }

    @Test
    void updateAbility_WhenNotExist_ShouldThrowException() {
        // Mock
        when(abilityMapper.selectById(anyInt())).thenReturn(null);

        // Test & Verify
        AbilityRequest request = new AbilityRequest();
        assertThatThrownBy(() -> abilityService.updateAbility(1, request))
            .isInstanceOf(RuntimeException.class)
            .hasMessageContaining("不存在");
    }

    @Test
    void deleteAbility_ShouldReturnTrueWhenSuccess() {
        // Mock
        Ability mockAbility = new Ability();
        mockAbility.setId(1);
        mockAbility.setZhName("Test Ability");

        // Mock selectById for checkAbilityExist and getAbilityUsageInfo methods
        when(abilityMapper.existById(1)).thenReturn(true);
        when(abilityMapper.selectById(1)).thenReturn(mockAbility);
        when(operatorNewMapper.selectUsingDataModel(1)).thenReturn(Collections.emptyList());
        when(batchOperatorMapper.selectUsingDataModel(1)).thenReturn(Collections.emptyList());
        when(abilityMapper.deleteById(1)).thenReturn(1);

        // Test
        boolean result = abilityService.deleteAbility(1);

        // Verify
        assertThat(result).isTrue();
    }

    @Test
    void getAllAbilityTree_ShouldReturnTree() {
        TreeBaseResponse categoryNode = new TreeBaseResponse();
        categoryNode.setId(1);
        categoryNode.setName("Category 1");

        Ability ability = new Ability();
        ability.setId(1);
        ability.setType(AbilityType.BATCH);
        ability.setOperatorCategoryId(1);
        ability.setInputSize(1);
        ability.setZhName("Ability 1");

        // Mock dependencies
        when(operatorCategoryService.getAllOperatorCategoryList()).thenReturn(List.of(categoryNode));
        when(abilityMapper.selectAllByRequest(any(SearchTypyArrayAbilityTreeRequest.class))).thenReturn(List.of(ability));

        // Call the method under test
        List<TreeBaseResponse> result = abilityService.getAvailableBatchAbilityTree();

        // Verify the results
        assertThat(result).isNotEmpty();
        List<?> children = result.get(0).getChildren();
        assertThat(children).isNotEmpty();
        assertEquals(1, ((AbilityArrangementTreeResponse) children.get(0)).getInputSize());

    }
}
