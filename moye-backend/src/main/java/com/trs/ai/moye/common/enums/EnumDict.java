package com.trs.ai.moye.common.enums;

import com.trs.ai.moye.backstage.enums.NoticeSendConfInsideCreateType;
import com.trs.ai.moye.backstage.enums.UserType;
import com.trs.ai.moye.common.response.DictResponse;
import com.trs.ai.moye.data.model.enums.ComparisonOperatorType;
import com.trs.ai.moye.data.model.enums.DwdKafkaOffsetResetType;
import com.trs.ai.moye.data.model.enums.ScheduleStatus;
import com.trs.ai.moye.data.model.enums.StreamProcessMonitorType;
import com.trs.moye.base.common.entity.ModuleEnum;
import com.trs.moye.base.common.enums.ModelLayer;
import com.trs.moye.base.common.enums.TimeUnit;
import com.trs.moye.base.common.log.operate.OperateType;
import com.trs.moye.base.data.connection.enums.ConnectionType;
import com.trs.moye.base.data.connection.enums.FTPStartTimeType;
import com.trs.moye.base.data.connection.enums.FileChosenType;
import com.trs.moye.base.data.connection.enums.FileExtension;
import com.trs.moye.base.data.connection.enums.KafkaSaslMechanism;
import com.trs.moye.base.data.connection.enums.OracleServiceType;
import com.trs.moye.base.data.execute.ExecuteModeEnum;
import com.trs.moye.base.data.model.enums.CreateModeEnum;
import com.trs.moye.base.data.model.enums.CreateTableStatus;
import com.trs.moye.base.data.model.enums.ModelExecuteStatus;
import com.trs.moye.base.data.source.enums.FileMatchMode;
import com.trs.moye.base.data.source.enums.KafkaOffsetResetType;
import com.trs.moye.base.data.source.enums.RocketMQOffsetResetType;
import com.trs.moye.base.monitor.enums.MonitorConfigType;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Stream;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 枚举字典
 *
 * <AUTHOR>
 * @since 2024-09-24 11:48
 */
@Getter
@AllArgsConstructor
public enum EnumDict {

    KAFKA_SASL_MECHANISM("kafka加密方式") {
        @Override
        public List<DictResponse> dictList() {
            return Arrays.stream(KafkaSaslMechanism.values())
                .map(obj -> new DictResponse(obj.name(), obj.getLabel())).toList();
        }
    },

    ORACLE_SERVICE_TYPE("Oracle服务类型") {
        @Override
        public List<DictResponse> dictList() {
            return Arrays.stream(OracleServiceType.values())
                .map(obj -> new DictResponse(obj.name(), obj.getLabel())).toList();
        }
    },

    USER_TYPE("用户类型") {
        @Override
        public List<DictResponse> dictList() {
            return Arrays.stream(UserType.values())
                .map(obj -> new DictResponse(obj.name(), obj.getLabel())).toList();
        }
    },

    MODEL_CREATE_MODE("数据建模-创建模式") {
        @Override
        public List<DictResponse> dictList() {
            return Arrays.stream(CreateModeEnum.values())
                .map(obj -> new DictResponse(obj.name(), obj.getLabel())).toList();
        }
    },

    MODEL_EXECUTE_STATUS("数据建模-执行状态") {
        @Override
        public List<DictResponse> dictList() {
            return Arrays.stream(ModelExecuteStatus.values())
                .map(obj -> new DictResponse(obj.name(), obj.getLabel())).toList();
        }
    },

    MODEL_LAYER("数据建模-层级") {
        @Override
        public List<DictResponse> dictList() {
            return Arrays.stream(ModelLayer.values())
                .map(obj -> new DictResponse(obj.name(), obj.getLabel())).toList();
        }
    },

    MODEL_MONITOR_CONFIG_TYPE("数据建模-监控配置类型") {
        @Override
        public List<DictResponse> dictList() {
            return Arrays.stream(MonitorConfigType.values())
                .map(obj -> new DictResponse(obj.name(), obj.getLabel())).toList();
        }
    },

    MODEL_MONITOR_CONFIG_PERIOD_UNIT("数据建模-监控配置周期单位") {
        @Override
        public List<DictResponse> dictList() {
            return Stream.of(TimeUnit.DAY, TimeUnit.HOUR, TimeUnit.MINUTE)
                .map(obj -> new DictResponse(obj.name(), obj.getLabel())).toList();
        }
    },

    MODEL_EXECUTE_MODE("数据建模-执行模式") {
        @Override
        public List<DictResponse> dictList() {
            return Arrays.stream(ExecuteModeEnum.values())
                .map(obj -> new DictResponse(obj.name(), obj.getLabel())).toList();
        }
    },

    MODEL_FTP_SUPPORT_FILE_TYPE("数据建模-ftp支持的文件类型") {
        @Override
        public List<DictResponse> dictList() {
            return Stream.of(FileExtension.CSV, FileExtension.JSON, FileExtension.XLSX, FileExtension.TRS,
                    FileExtension.BCP, FileExtension.TEXT)
                .map(obj -> new DictResponse(obj.name(), obj.getLabel())).toList();
        }
    },

    MODEL_FTP_FILE_CHOSEN_TYPE("数据建模-ftp文件选择类型") {
        @Override
        public List<DictResponse> dictList() {
            return Arrays.stream(FileChosenType.values())
                .map(obj -> new DictResponse(obj.name(), obj.getLabel())).toList();
        }
    },

    MODEL_FTP_FILE_MATCH_MODE("数据建模-ftp文件匹配模式") {
        @Override
        public List<DictResponse> dictList() {
            return Arrays.stream(FileMatchMode.values())
                .map(obj -> new DictResponse(obj.name(), obj.getZhName())).toList();
        }
    },

    MODEL_SCHEDULE_CONFIG_MORE_EXECUTE_PERIOD_UNIT("数据建模-调度配置-多次执行周期单位") {
        @Override
        public List<DictResponse> dictList() {
            return Stream.of(TimeUnit.WEEK, TimeUnit.MONTH, TimeUnit.DAY, TimeUnit.HOUR)
                .map(obj -> new DictResponse(obj.name(), obj.getLabel())).toList();
        }
    },

    MODEL_SCHEDULE_CONFIG_FIXED_INTERVAL_PERIOD_UNIT("数据建模-调度配置-固定间隔周期单位") {
        @Override
        public List<DictResponse> dictList() {
            return Stream.of(TimeUnit.YEAR, TimeUnit.MONTH, TimeUnit.DAY, TimeUnit.HOUR, TimeUnit.MINUTE,
                    TimeUnit.SECONDS)
                .map(obj -> new DictResponse(obj.name(), obj.getLabel())).toList();
        }
    },

    MODEL_CREATE_TABLE_STATUS("数据建模-建表状态") {
        @Override
        public List<DictResponse> dictList() {
            return Arrays.stream(CreateTableStatus.values())
                .map(obj -> new DictResponse(obj.name(), obj.getLabel())).toList();
        }
    },
    MODEL_CREATE_SCHEDULE_STATUS("数据建模-调度状态") {
        @Override
        public List<DictResponse> dictList() {
            return Arrays.stream(ScheduleStatus.values()).map(obj -> new DictResponse(obj.name(), obj.getLabel()))
                .toList();
        }
    },

    FTP_START_TIME_TYPE("FTP开始时间类型") {
        @Override
        public List<DictResponse> dictList() {
            return Arrays.stream(FTPStartTimeType.values()).map(obj -> new DictResponse(obj.name(), obj.getDesc()))
                .toList();
        }
    },

    KAFKA_OFFSET_RESET_TYPE("kafka offset重置类型") {
        @Override
        public List<DictResponse> dictList() {
            return Arrays.stream(KafkaOffsetResetType.values()).map(obj -> new DictResponse(obj.name(), obj.getName()))
                .toList();
        }
    },

    ROCKET_MQ_OFFSET_RESET_TYPE("rocketMQ offset重置类型") {
        @Override
        public List<DictResponse> dictList() {
            return Arrays.stream(RocketMQOffsetResetType.values())
                .map(obj -> new DictResponse(obj.name(), obj.getName()))
                .toList();
        }
    },

    USAGE_INFO_MODULE("使用信息-模块") {
        @Override
        public List<DictResponse> dictList() {
            return Arrays.stream(ModuleEnum.values()).map(obj -> new DictResponse(obj.name(), obj.getObjectName()))
                .toList();
        }
    },

    SUPPORT_DDL_IMPORT_DB_TYPE("支持DDL导入的数据库类型") {
        @Override
        public List<DictResponse> dictList() {
            return Stream.of(ConnectionType.ELASTIC_SEARCH, ConnectionType.MYSQL, ConnectionType.HIVE,
                    ConnectionType.CLICK_HOUSE, ConnectionType.POSTGRESQL)
                .map(obj -> new DictResponse(obj.name(), obj.getLabel()))
                .toList();
        }
    },
    DWD_KAFKA_OFFSET_RESET_TYPE("要素库kafka offset重置类型") {
        @Override
        public List<DictResponse> dictList() {
            return Arrays.stream(DwdKafkaOffsetResetType.values())
                .map(obj -> new DictResponse(obj.name(), obj.getName()))
                .toList();
        }
    },

    STREAM_PROCESS_MONITOR_TYPE("流处理监控过滤条件类型") {
        @Override
        public List<DictResponse> dictList() {
            return Arrays.stream(StreamProcessMonitorType.values())
                .map(obj -> new DictResponse(obj.name(), obj.getLabel()))
                .toList();
        }
    },
    COMPARISON_OPERATOR_TYPE("对比运算符枚举类型") {
        @Override
        public List<DictResponse> dictList() {
            return Arrays.stream(ComparisonOperatorType.values())
                .map(obj -> new DictResponse(obj.name(), obj.getData()))
                .toList();
        }
    },

    NOTICE_CONF_INSIDE_CREATE_TYPE("站内消息推送配置类型") {
        @Override
        public List<DictResponse> dictList() {
            return Arrays.stream(NoticeSendConfInsideCreateType.values())
                .map(obj -> new DictResponse(obj.name(), obj.getLabel()))
                .toList();
        }
    },

    OPERATE_LOG_OPERATE_TYPE("操作日志-操作类型") {
        @Override
        public List<DictResponse> dictList() {
            return Arrays.stream(OperateType.values())
                .map(obj -> new DictResponse(obj.name(), obj.getLabel()))
                .toList();
        }
    },

    SYSTEM_MODULE("系统模块") {
        @Override
        public List<DictResponse> dictList() {
            return Arrays.stream(ModuleEnum.values())
                .map(obj -> new DictResponse(obj.name(), obj.getModuleName()))
                .toList();
        }
    },
    ;


    private final String label;

    /**
     * 字典列表
     *
     * @return 字典对象列表
     */
    public abstract List<DictResponse> dictList();
}
