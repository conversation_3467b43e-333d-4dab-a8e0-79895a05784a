package com.trs.ai.moye.data.model.service;

import com.trs.moye.base.data.model.entity.DataModel;
import com.trs.moye.base.data.schedule.ScheduleInfo;
import com.trs.ai.moye.data.model.response.ScheduleConfigResponse;

/**
 * 数据建模-调度信息
 *
 * <AUTHOR>
 * @since 2025/1/3 16:11
 */
public interface DataModelScheduleService {

    /**
     * 创建调度配置
     *
     * @param dataModelId  建模id
     * @param scheduleInfo 调度信息
     */
    void createScheduleConfig(Integer dataModelId, ScheduleInfo scheduleInfo);

    /**
     * 创建调度配置
     *
     * @param dataModel  建模对象
     * @param scheduleInfo 调度信息
     */
    void createScheduleConfig(DataModel dataModel, ScheduleInfo scheduleInfo);

    /**
     * 数据建模的调度配置信息
     *
     * @param id 建模id
     * @return 调度配置信息
     */
    ScheduleConfigResponse getModelScheduleInfo(Integer id);

    /**
     * 更新数据建模的调度配置
     *
     * @param dataModelId  建模id
     * @param scheduleInfo 调度配置信息
     */
    void updateModelScheduleConfig(Integer dataModelId, ScheduleInfo scheduleInfo);

    /**
     * 删除调度配置
     *
     * @param id 建模ID
     */
    void deleteScheduleConfig(Integer id);
}
