package com.trs.ai.moye.data.operator.domain.request;

import javax.validation.constraints.NotBlank;
import lombok.Data;

/**
 * 算子业务分类新增/修改
 *
 * <AUTHOR>
 */
@Data
public class OperatorCategoryRequest {

    /**
     * 父分类ID
     */
    private Integer pid;

    /**
     * 英文缩写
     */
    @NotBlank(message = "英文缩写不能为空")
    private String enAbbr;

    /**
     * 中文名
     */
    @NotBlank(message = "中文名不能为空")
    private String zhName;

    /**
     * 描述
     */
    private String description;

}
