package com.trs.ai.moye.bi.controller;

import com.trs.ai.moye.bi.domain.entity.ChartPosition;
import com.trs.ai.moye.bi.domain.request.PublishStatusRequest;
import com.trs.ai.moye.bi.domain.request.SubjectRequest;
import com.trs.ai.moye.bi.domain.response.CategoryResponse;
import com.trs.ai.moye.bi.domain.response.SubjectPublishStatusResponse;
import com.trs.ai.moye.bi.domain.response.SubjectResponse;
import com.trs.ai.moye.bi.enums.SubjectPublishLocation;
import com.trs.ai.moye.bi.service.SubjectService;
import com.trs.ai.moye.bi.service.impl.SubjectPublishService;
import com.trs.ai.moye.common.response.TreeAddResponse;
import com.trs.ai.moye.common.response.TreeBaseResponse;
import com.trs.ai.moye.data.model.request.MoveRequest;
import com.trs.ai.moye.data.model.response.BatchDeleteResponse;
import java.util.List;
import javax.annotation.Resource;
import javax.validation.constraints.NotBlank;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * 可视化分析-主题
 *
 * <AUTHOR>
 * @since 2024/12/11 15:15
 */
@RestController
@RequestMapping("/visual-analysis")
public class SubjectController {

    @Resource
    private SubjectService subjectService;

    @Resource
    private SubjectPublishService subjectPublishService;


    /**
     * 新增分类
     *
     * @param subjectRequest 请求
     * @return {@link TreeAddResponse}
     */
    @PostMapping("/category")
    public TreeAddResponse addCategory(@RequestBody @Validated SubjectRequest subjectRequest) {
        return subjectService.addCategory(subjectRequest);
    }

    /**
     * 修改分类
     *
     * @param id             分类id
     * @param subjectRequest 请求
     */
    @PutMapping("/category/{id}")
    public void modifyCategory(@PathVariable Integer id, @RequestBody @Validated SubjectRequest subjectRequest) {
        subjectService.modifyCategory(id, subjectRequest);
    }

    /**
     * 删除分类
     *
     * @param id 分类id
     */
    @DeleteMapping("/category/{id}")
    public void deleteCategory(@PathVariable Integer id) {
        subjectService.deleteCategory(id);
    }

    /**
     * 查询名称是否重复
     *
     * @param name 名称
     * @return 是否重复
     */
    @GetMapping("/category/check-name")
    public boolean categoryCheckName(@NotBlank @RequestParam("name") String name) {
        return subjectService.categoryCheckName(name);
    }

    /**
     * 分类详情
     *
     * @param id 分类id
     * @return 详情
     */
    @GetMapping("/category/{id}")
    public CategoryResponse categoryDetail(@PathVariable Integer id) {
        return subjectService.categoryDetail(id);
    }

    /**
     * 新增主题<a href="http://192.168.210.40:3001/project/5419/interface/api/166183">...</a>
     *
     * @param subjectRequest 请求
     * @return {@link TreeAddResponse}
     */
    @PostMapping("/subject")
    public TreeAddResponse addSubject(@RequestBody @Validated SubjectRequest subjectRequest) {
        return subjectService.addSubject(subjectRequest);
    }

    /**
     * 修改主题
     *
     * @param id             主题id
     * @param subjectRequest 请求
     */
    @PutMapping("/subject/{id}")
    public void modifySubject(@PathVariable Integer id, @RequestBody @Validated SubjectRequest subjectRequest) {
        subjectService.modifySubject(id, subjectRequest);
    }

    /**
     * 删除主题
     *
     * @param id 主题id
     */
    @DeleteMapping("/subject/{id}")
    public void deleteSubject(@PathVariable Integer id) {
        subjectService.deleteSubject(id);
    }

    /**
     * 查询名称是否重复
     *
     * @param name 名称
     * @return 是否重复
     */
    @GetMapping("/subject/check-name")
    public boolean subjectCheckName(@NotBlank @RequestParam("name") String name) {
        return subjectService.subjectCheckName(name);
    }

    /**
     * 主题详情
     *
     * @param id 主题id
     * @return 详情
     */
    @GetMapping("/subject/{id}")
    public SubjectResponse subjectDetail(@PathVariable Integer id) {
        return subjectService.subjectDetail(id);
    }

    /**
     * 已发布主题详情
     *
     * @param id       主题id
     * @param location 发布位置
     * @return 详情
     */
    @GetMapping("/subject/published/{id}/{location}")
    public SubjectResponse publishedSubjectDetail(@PathVariable Integer id,
        @PathVariable SubjectPublishLocation location) {
        return subjectService.publishedSubjectDetail(id, location);
    }

    /**
     * 目录树
     *
     * @param isPublish 是否发布
     * @param location  发布位置
     * @return 目录树
     */
    @GetMapping("/category/tree")
    public List<TreeBaseResponse> categoryTree(@RequestParam(value = "isPublish", required = false) boolean isPublish,
        @RequestParam(value = "location", defaultValue = "VISUAL_ANALYSIS") SubjectPublishLocation location) {
        return subjectService.categoryTree(isPublish, location);
    }

    /**
     * 发布/取消发布主题
     * <a href="http://192.168.210.40:3001/project/5419/interface/api/166183">yapi</a>
     *
     * @param request 状态
     */
    @PutMapping("/subject/publish")
    public void publishSubject(@RequestBody @Validated PublishStatusRequest request) {
        subjectService.publishSubject(request);
    }

    /**
     * 获取主题的发布状态信息
     * <a href="http://192.168.210.40:3001/project/5419/interface/api/168538> 【主题】查询当前主题的发布状态 </a>
     *
     * @param id 主题id
     * @return 主题发布状态
     */
    @GetMapping("/subject/publish/{id}")
    public SubjectPublishStatusResponse getPublishSubjectStatus(@PathVariable @Validated Integer id) {
        return subjectPublishService.getSubjectPublishStatus(id);
    }


    /**
     * 更新看板位置信息
     *
     * @param id       主题id
     * @param position 位置
     */
    @PutMapping("/subject/{id}/dashboard/position")
    public void updatePosition(@PathVariable Integer id, @RequestBody List<ChartPosition> position) {
        subjectService.updatePosition(id, position);
    }

    /**
     * 批量移动主题<a href="http://192.168.210.40:3001/project/5419/interface/api/166879">...</a>
     *
     * @param request 移动参数
     */
    @PostMapping("/subject/move")
    public void moveSubject(@RequestBody @Validated MoveRequest request) {
        subjectService.moveSubject(request);
    }

    /**
     * 批量删除仪表盘<a href="http://192.168.210.40:3001/project/5419/interface/api/166939">...</a>
     *
     * @param ids 仪表盘id
     * @return 结果
     */
    @DeleteMapping("/subject/batch-delete")
    public List<BatchDeleteResponse> batchDeleteSubject(@RequestBody List<Integer> ids) {
        return subjectService.batchDeleteSubject(ids);
    }

    /**
     * 复制主题<a href="http://192.168.210.40:3001/project/5419/interface/api/166885">...</a>
     *
     * @param request 移动参数
     */
    @PostMapping("/subject/copy")
    public void copySubject(@RequestBody @Validated MoveRequest request) {
        subjectService.copySubject(request);
    }
}
