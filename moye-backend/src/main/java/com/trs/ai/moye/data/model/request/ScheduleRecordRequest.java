package com.trs.ai.moye.data.model.request;

import com.trs.ai.moye.data.model.entity.StorageTask.SeaTunnelJobStatus;
import com.trs.moye.base.common.request.BaseRequestParams;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 调度监控分页查询请求
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ScheduleRecordRequest extends BaseRequestParams {

    private SeaTunnelJobStatus executeStatus;

    /**
     * 仅显示有数据的记录
     */
    private Boolean showOnlyHasData;
}
