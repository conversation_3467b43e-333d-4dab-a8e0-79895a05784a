package com.trs.ai.moye.data.model.entity.imports;

import com.trs.ai.moye.common.constants.BoolConstants;
import com.trs.ai.moye.common.utils.BizUtils;
import com.trs.ai.moye.data.model.entity.BaseModelField;
import com.trs.ai.moye.data.model.enums.FieldParseResultType;
import com.trs.moye.base.common.utils.FieldTypeMappingUtils;
import com.trs.moye.base.common.enums.FieldType;
import com.trs.moye.base.common.utils.JsonUtils;
import com.trs.moye.base.data.connection.enums.ConnectionType;
import com.trs.moye.base.data.standard.entity.TagEdgeField;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.ObjectUtils;

/**
 * <AUTHOR>
 * @since 2024-10-28 17:49
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
public class FieldImportParseResult extends BaseModelField {

    private Integer dataModelId;

    /**
     * 解析结果类型
     */
    private FieldParseResultType resultType;

    /**
     * 失败或重复原因
     */
    private StringBuilder reasons = new StringBuilder();

    /**
     * 追加原因
     *
     * @param reason 原因
     */
    public void appendReason(String reason) {
        this.reasons.append(reason);
    }

    public FieldImportParseResult(Integer dataModelId, FieldImportExportTemplate request) {
        this.dataModelId = dataModelId;
        this.zhName = request.getZhName();
        this.enName = request.getEnName();
        this.type = FieldType.valueOf(request.getType());
        this.typeName = request.getTypeName();
        this.isMultiValue = BoolConstants.ZH_TRUE.equals(request.getMultiValue());
        this.isNullable = BoolConstants.ZH_TRUE.equals(request.getNullable());
        this.description = request.getDescription();
        this.advanceConfig = BizUtils.readFieldAdvanceConfig(request);
        this.defaultValue = request.getDefaultValue();
        this.isUseStandard = false;
        this.isStatistic = BoolConstants.ZH_TRUE.equals(request.getStatistic());
        this.isBuiltIn = false;
        this.isPrimaryKey = BoolConstants.ZH_TRUE.equals(request.getPrimaryKey());
        this.isIncrement = BoolConstants.ZH_TRUE.equals(request.getIncrement());
        this.isPartition = BoolConstants.ZH_TRUE.equals(request.getPartition());
        this.isProvideService = BoolConstants.ZH_TRUE.equals(request.getProvideService());
        this.isTitle = BoolConstants.ZH_TRUE.equals(request.getTitle());
        this.fieldsPrimaryKey = request.getFieldsPrimaryKey();
        if (ObjectUtils.isNotEmpty(request.getTagEdgeFields())) {
            this.fields = JsonUtils.toList(request.getTagEdgeFields(), TagEdgeField.class);
        }
    }

    public FieldImportParseResult(Integer dataModelId, JsonStructureParseResult parseResult) {
        this.dataModelId = dataModelId;
        this.zhName = parseResult.getFieldName();
        this.enName = parseResult.getFieldName();
        this.type = parseResult.getFieldType();
        this.typeName = parseResult.getFieldType().getLabel();
        this.isMultiValue = parseResult.isMultiValue();
    }

    public FieldImportParseResult(Integer dataModelId, ConnectionType connectionType, DdlColumn column) {
        this.dataModelId = dataModelId;
        this.enName = column.getName();
        this.zhName = ObjectUtils.isEmpty(column.getComment()) ? column.getName() : column.getComment();
        this.type = FieldTypeMappingUtils.getFieldType(connectionType, column.getType());
        this.typeName = this.type.getLabel();
    }
}
