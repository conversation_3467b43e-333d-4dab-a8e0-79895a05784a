package com.trs.ai.moye.data.service.processer.process;


import com.trs.ai.moye.data.service.entity.DataServiceField;
import com.trs.ai.moye.data.service.entity.params.DataServiceConfigParams;
import com.trs.ai.moye.data.service.enums.ServiceConfigType;
import com.trs.ai.moye.data.service.enums.ServiceCreateMode;
import com.trs.ai.moye.data.service.request.ability.ReqBody;
import com.trs.ai.moye.storageengine.request.StorageEngineSearchRequest;
import com.trs.ai.moye.storageengine.response.StorageSearchResponse;
import com.trs.moye.base.common.exception.BizException;
import com.trs.moye.base.common.request.PageParams;
import com.trs.moye.base.data.storage.DataStorage;
import com.trs.moye.base.data.storage.dao.DataStorageMapper;
import java.io.IOException;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @since 2024/9/27
 */
@Component
@Slf4j
public class DataServiceProcessorContext {

    @Resource
    private List<DataServiceProcessor> dataServiceProcessors;

    @Resource
    private DataStorageMapper dataStorageMapper;

    /**
     * 获取数据服务处理器
     *
     * @param type {@link ServiceConfigType}
     * @return {@link DataServiceProcessor}
     */
    private Optional<DataServiceProcessor> getDataServiceProcessor(ServiceConfigType type) {
        for (DataServiceProcessor processor : dataServiceProcessors) {
            if (processor.isSupport(type)) {
                return Optional.of(processor);
            }
        }
        return Optional.empty();
    }

    /**
     * 根据创建模式获取数据服务处理器
     *
     * @param createMode 创建模式
     * @return {@link Optional }<{@link DataServiceProcessor }>
     * <AUTHOR>
     * @since 2024/09/30 18:02:44
     */
    private Optional<DataServiceProcessor> getDataServiceProcessor(ServiceCreateMode createMode) {
        for (DataServiceProcessor processor : dataServiceProcessors) {
            if (processor.isSupportByCreateMode(createMode)) {
                return Optional.of(processor);
            }
        }
        return Optional.empty();
    }

    /**
     * 构建搜索请求
     *
     * @param pageParams    页面参数
     * @param params        预览参数
     * @param dataStorageId 数据存储id
     * @return {@link StorageSearchResponse}
     * <AUTHOR>
     * @since 2024/10/10 18:31:35
     */
    public StorageEngineSearchRequest buildSearchRequest(PageParams pageParams, DataServiceConfigParams params,
        Integer dataStorageId) {
        DataServiceProcessor dataServiceProcessor = getDataServiceProcessor(params.getType()).orElseThrow(
            () -> new BizException(String.format("不支持的数据服务类型：%s", params.getType())));
        StorageEngineSearchRequest searchParams = dataServiceProcessor.buildSearchRequest(pageParams,
            params, dataStorageId);
        log.info("存储点id: {}", dataStorageId);
        DataStorage dataStorage = dataStorageMapper.selectById(dataStorageId);
        log.info("查询存储信息: {}", dataStorage);
        searchParams.setDataModelIds(
            Objects.isNull(dataStorage) ? Collections.emptyList() : List.of(dataStorage.getDataModelId()));
        return searchParams;
    }

    /**
     * 生成能力中心请求体
     *
     * @param params     参数
     * @param createMode 创建模式
     * @return {@link ReqBody }
     * <AUTHOR>
     * @since 2024/09/30 18:03:13
     */
    public ReqBody generateAbilityCenterReqBody(DataServiceConfigParams params, ServiceCreateMode createMode) {
        DataServiceProcessor dataServiceProcessor = getDataServiceProcessor(createMode).orElseThrow(
            () -> new BizException(String.format("不支持的数据服务创建模式：%s", createMode)));
        return dataServiceProcessor.generateAbilityCenterReqBody(params);
    }

    /**
     * 查询数据
     *
     * @param createMode   创建模式
     * @param connectionId 连接id
     * @param tableName    表名称
     * @param request      请求
     * @return {@link StorageSearchResponse }
     * <AUTHOR>
     * @since 2024/10/18 11:20:15
     */
    public StorageSearchResponse queryData(ServiceCreateMode createMode, Integer connectionId, String tableName,
        StorageEngineSearchRequest request) {
        DataServiceProcessor dataServiceProcessor = getDataServiceProcessor(createMode).orElseThrow(
            () -> new BizException(String.format("不支持的数据服务创建模式：%s", createMode)));
        return dataServiceProcessor.queryData(connectionId, tableName, request);
    }


    /**
     * 执行导出数据
     *
     * @param serviceName       服务名称
     * @param serviceConfigType 服务配置类型
     * @param data              数据
     * @param response          响应
     * @param returnFields      返回字段
     * @throws IOException io异常
     * <AUTHOR>
     * @since 2024/11/07 16:19:58
     */
    public void doExportData(String serviceName, ServiceConfigType serviceConfigType, List<Map<String, Object>> data,
        List<DataServiceField> returnFields, HttpServletResponse response) throws IOException {
        DataServiceProcessor dataServiceProcessor = getDataServiceProcessor(serviceConfigType).orElseThrow(
            () -> new BizException(String.format("不支持的数据服务类型：%s", serviceConfigType)));
        dataServiceProcessor.doExportData(serviceName, data, returnFields, response);
    }
}
