package com.trs.ai.moye.common.jackson.deserializer;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.BeanProperty;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JavaType;
import com.fasterxml.jackson.databind.JsonDeserializer;
import com.fasterxml.jackson.databind.JsonMappingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.deser.ContextualDeserializer;
import com.trs.ai.moye.data.service.entity.params.DataServiceConfigParams;
import com.trs.ai.moye.data.service.enums.ServiceConfigType;
import com.trs.moye.base.common.utils.JsonUtils;
import java.io.IOException;


/**
 * Service Config Params 反序列化器
 *
 * <AUTHOR>
 * @since 2024/09/26 10:50:13
 */
public class ServiceConfigParamsDeserializer extends JsonDeserializer<DataServiceConfigParams> implements
    ContextualDeserializer {

    private JavaType valueType;

    @Override
    public DataServiceConfigParams deserialize(JsonParser p, DeserializationContext ctxt)
        throws IOException {
        if (DataServiceConfigParams.class == valueType.getRawClass()) {
            // 读取整个JSON对象为JsonNode
            JsonNode rootNode = p.getCodec().readTree(p);
            String paramsType = rootNode.get(DataServiceConfigParams.PARAMS_TYPE_FIELD).asText();
            String connectionParamsJson = JsonUtils.OBJECT_MAPPER.writeValueAsString(rootNode);
            return ServiceConfigType.getConfigParams(paramsType, connectionParamsJson);
        } else {
            return new DataServiceConfigParams();
        }

    }

    @Override
    public JsonDeserializer<?> createContextual(DeserializationContext ctxt, BeanProperty property)
        throws JsonMappingException {
        JavaType javaType = property.getType();
        ServiceConfigParamsDeserializer deserializer = new ServiceConfigParamsDeserializer();
        deserializer.valueType = javaType;
        return deserializer;
    }
}
