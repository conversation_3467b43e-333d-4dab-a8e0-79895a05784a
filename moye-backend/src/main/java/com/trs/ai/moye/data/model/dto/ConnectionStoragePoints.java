package com.trs.ai.moye.data.model.dto;

import java.util.ArrayList;
import java.util.List;
import javax.validation.constraints.NotNull;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.ObjectUtils;

/**
 * 连接存储点
 *
 * <AUTHOR>
 * @since 2024/12/11 10:59:40
 */
@Data
@NoArgsConstructor
public class ConnectionStoragePoints {

    /**
     * 连接id
     */
    @NotNull(message = "连接id不能为空")
    private Integer connectionId;

    /**
     * 存储表列表
     */
    private List<StorageTable> storageTables;

    public ConnectionStoragePoints(StoragePointParams storagePointParams) {
        this.connectionId = storagePointParams.getConnectionId();
        this.storageTables = new ArrayList<>();
        if (ObjectUtils.isNotEmpty(storagePointParams.getTableNameList())) {
            for (String tableName : storagePointParams.getTableNameList()) {
                this.storageTables.add(new StorageTable(tableName, null));
            }
        }
    }
}
