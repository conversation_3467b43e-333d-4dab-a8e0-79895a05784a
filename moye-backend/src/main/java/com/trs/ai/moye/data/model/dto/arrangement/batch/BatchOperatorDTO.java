package com.trs.ai.moye.data.model.dto.arrangement.batch;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.trs.moye.ability.deserializer.InputBindDeserializer;
import com.trs.moye.ability.deserializer.OperatorRowTypeDeserializer;
import com.trs.moye.ability.deserializer.OutputBindDeserializer;
import com.trs.moye.ability.entity.Ability;
import com.trs.moye.ability.entity.InputBind;
import com.trs.moye.ability.entity.OperatorStyleInfo;
import com.trs.moye.ability.entity.OutputBind;
import com.trs.moye.ability.entity.operator.BatchOperator;
import com.trs.moye.ability.entity.operator.OperatorRowType;
import com.trs.moye.ability.enums.ArrangeNodeType;
import com.trs.moye.base.common.enums.ModelLayer;
import com.trs.moye.base.data.service.entity.Condition;
import java.util.List;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 批处理编排中算子dto
 *
 * <AUTHOR>
 * @since 2025/3/21 15:46
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class BatchOperatorDTO {

    /**
     * 算子位置（前端生成和使用）
     */
    private OperatorStyleInfo canvas;

    /**
     * 算子id（前端生成）
     */
    @NotNull(message = "算子id[displayId]缺失!")
    private Long displayId;

    /**
     * 算子连接节点的算子id（前端生成）
     */
    private List<Long> targetDisplayIds;

    /**
     * 节点类型 (TABLE/OPERATOR)，区分内置的输入输出算子和处理数据的算子
     */
    @NotNull(message = "节点类型[type]缺失!")
    private ArrangeNodeType type;

    /**
     * 算子中文名
     */
    @NotBlank(message = "算子名称[name]缺失!")
    private String name;

    /**
     * 节点描述
     */
    private String desc;

    /**
     * 是否启用
     */
    private Boolean enabled = Boolean.TRUE;

    /**
     * 条件
     */
    private List<Condition> conditions;

    /**
     * 输入字段
     */
    @JsonDeserialize(using = OperatorRowTypeDeserializer.class)
    private OperatorRowType inputFields;

    /**
     * 输出字段
     */
    @JsonDeserialize(using = OperatorRowTypeDeserializer.class)
    private OperatorRowType outputFields;

    /**
     * 算子类型是 OPERATOR 时, 能力id
     */
    private Integer abilityId;

    /**
     * 算子类型是 OPERATOR 时, 能力
     */
    private Ability ability;

    /**
     * 输入绑定
     */
    @JsonDeserialize(using = InputBindDeserializer.class)
    private InputBind inputBind;

    /**
     * 输出绑定
     */
    @JsonDeserialize(using = OutputBindDeserializer.class)
    private OutputBind outputBind;

    /**
     * 算子类型是 TABLE 时, 输入算子所选择的数据建模所处的层级
     */
    private ModelLayer tableType;

    /**
     * 算子类型是 TABLE 时, 输入算子所选择的数据建模
     */
    private Integer dataModelId;

    /**
     * 算子类型是 TABLE 时, 输入算子选择的数据建模中的存储点
     */
    private Integer storageId;

    /**
     * 算子类型是 TABLE 时, 数据接入算子——是否启用增量
     */
    private Boolean isIncrement;

    /**
     * 构造算子
     *
     * @param arrangementId 编排id
     * @param dataModelId   数据建模id
     * @return {@link BatchOperator }
     */
    public BatchOperator toBatchOperator(Integer arrangementId, Integer dataModelId) {
        BatchOperator operator = BatchOperator.builder().arrangementId(arrangementId).dataModelId(dataModelId)
            .canvas(canvas).displayId(displayId)
            .targetDisplayIds(targetDisplayIds).type(type).name(name).desc(desc)
            .enabled(enabled).conditions(conditions).inputFields(inputFields).outputFields(outputFields)
            .abilityId(abilityId).inputBind(inputBind).outputBind(outputBind).tableType(tableType)
            .tableDataModelId(this.dataModelId).tableStorageId(storageId).tableIsIncrement(isIncrement)
            .build();
        operator.setOutputTableName(type, displayId, name);
        return operator;
    }

    /**
     * 从算子构造dto
     *
     * @param operator 算子
     * @return {@link BatchOperatorDTO }
     */
    public static BatchOperatorDTO fromBatchOperator(BatchOperator operator) {
        return BatchOperatorDTO.builder()
            .canvas(operator.getCanvas())
            .displayId(operator.getDisplayId())
            .targetDisplayIds(operator.getTargetDisplayIds())
            .type(operator.getType())
            .name(operator.getName())
            .desc(operator.getDesc())
            .enabled(operator.getEnabled())
            .conditions(operator.getConditions())
            .inputFields(operator.getInputFields())
            .outputFields(operator.getOutputFields())
            .abilityId(operator.getAbilityId())
            .ability(operator.getAbility())
            .inputBind(operator.getInputBind())
            .outputBind(operator.getOutputBind())
            .tableType(operator.getTableType())
            .dataModelId(operator.getTableDataModelId())
            .storageId(operator.getTableStorageId())
            .isIncrement(operator.getTableIsIncrement())
            .build();
    }
}
