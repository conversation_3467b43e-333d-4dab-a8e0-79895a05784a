package com.trs.ai.moye.data.connection.controller;

import com.trs.ai.moye.data.connection.response.KerberosCertificateResponse;
import com.trs.ai.moye.data.connection.service.AuthCertificateService;
import java.util.List;
import javax.annotation.Resource;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 认证证书控制器
 *
 * <AUTHOR>
 * @since 2024-09-24 13:53
 */
@RestController
@RequestMapping("/auth-certificate")
public class AuthCertificateController {

    @Resource
    private AuthCertificateService service;

    /**
     * 当前用户证书列表 <a href="http://192.168.210.40:3001/project/5419/interface/api/164535">...</a>
     * <p>
     *
     * @return 证书列表
     */
    @GetMapping("/user-certificates")
    public List<KerberosCertificateResponse> getCurrentUserAuthCertificates() {
        return service.getCurrentUserAuthCertificates();
    }
}
