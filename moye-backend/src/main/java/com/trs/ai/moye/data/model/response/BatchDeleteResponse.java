package com.trs.ai.moye.data.model.response;


import com.trs.ai.moye.data.service.entity.DataService;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;


/**
 * 功能模块: <b>数据建模</b><br> 批量删除 数据建模 贴源库/要素库... 结果
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class BatchDeleteResponse {

    /**
     * 贴源表/要素表/数据服务 id
     */
    private Integer id;
    /**
     * 贴源表/要素表/数据服务 英文名
     */
    private String enName;
    /**
     * 贴源表/要素表/数据服务 中文名
     */
    private String zhName;
    /**
     * 错误信息
     */
    private String errorMsg;
    /**
     * 是否成功删除
     */
    private Boolean isSuccess;

    /**
     * 执行删除操作前，即还没有 errorMsg和 isSuccess 的情况下 使用 的 构造方法
     *
     * @param id     id
     * @param enName 英文名
     * @param zhName 中文名
     */
    public BatchDeleteResponse(Integer id, String enName, String zhName) {
        this.id = id;
        this.enName = enName;
        this.zhName = zhName;
    }


    /**
     * 数据服务批量删除结果
     *
     * @param dataService 数据服务
     * <AUTHOR>
     * @since 2024/12/5 16:32
     */
    public BatchDeleteResponse(DataService dataService, String errorMsg, Boolean isSuccess) {
        this.id = dataService.getId();
        this.enName = "";
        this.zhName = dataService.getName();
        this.errorMsg = errorMsg;
        this.isSuccess = isSuccess;

    }

    /**
     * 默认失败返回
     *
     * @param id       删除对象id
     * @param zhName   删除对象名称
     * @param errorMsg 报错信息
     * @return {@link BatchDeleteResponse}
     */
    public static BatchDeleteResponse toFailResponse(Integer id, String zhName, String errorMsg) {
        return new BatchDeleteResponse(id, "", zhName, errorMsg, false);
    }
}
