package com.trs.ai.moye.out.entity;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.Pattern;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * out 指标时间范围参数
 *
 * <AUTHOR>
 * @since 2025/05/30 15:24:52
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class OutIndicatorTimeRangeParams {

    @Pattern(regexp = "^\\d{4}-\\d{2}-\\d{2}$", message = "日期格式不正确，应为yyyy-MM-dd格式")
    private String date;

    @Min(value = 1900, message = "年份不能小于1900")
    @Max(value = 2100, message = "年份不能大于2100")
    private Integer year;

    @Min(value = 1, message = "周数不能小于1")
    @Max(value = 53, message = "周数不能大于53")
    private Integer week;

    @Min(value = 1, message = "月份必须在1-12之间")
    @Max(value = 12, message = "月份必须在1-12之间")
    private Integer month;

    @Min(value = 1, message = "季度必须在1-4之间")
    @Max(value = 4, message = "季度必须在1-4之间")
    private Integer quarter;

    @Min(value = 1, message = "半年值必须为1或2")
    @Max(value = 2, message = "半年值必须为1或2")
    private Integer semiannual;
}
