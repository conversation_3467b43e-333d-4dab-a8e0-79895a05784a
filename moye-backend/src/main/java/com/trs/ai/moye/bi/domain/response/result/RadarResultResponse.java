package com.trs.ai.moye.bi.domain.response.result;

import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 雷达图
 *
 * <AUTHOR>
 * @since 2024/12/17 16:50
 */
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
public class RadarResultResponse extends ResultResponse {

    private List<RadarResult> radarResult;

    /**
     * 雷达图结果
     */
    @Data
    public static class RadarResult {
        /**
         * 坐标轴
         */
        private ResultData groupField;
        /**
         * 值
         */
        private List<ResultData> statisticField;
    }
}
