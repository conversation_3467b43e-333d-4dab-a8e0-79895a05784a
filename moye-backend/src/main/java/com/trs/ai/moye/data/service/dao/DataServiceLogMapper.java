package com.trs.ai.moye.data.service.dao;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.trs.moye.base.common.enums.ResultType;
import com.trs.moye.base.common.log.data.service.DataServiceLog;
import com.trs.moye.base.common.request.SearchParams;
import java.time.LocalDateTime;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 * @since 2025/4/17
 **/

@DS("clickhouse")
@Mapper
public interface DataServiceLogMapper {

    /**
     * 分页列表
     *
     * @param serviceId      数据服务id
     * @param responseStatus 响应状态
     * @param startTime      开始时间
     * @param endTime        结束时间
     * @param searchParams   搜索参数
     * @param page           分页参数
     * @return 分页列表
     */
    Page<DataServiceLog> getPageList(@Param("serviceId") Integer serviceId,
        @Param("responseStatus") ResultType responseStatus,
        @Param("startTime") String startTime,
        @Param("endTime") String endTime,
        @Param("searchParams") SearchParams searchParams,
        Page<DataServiceLog> page);

    /**
     * 根据主键查询日志
     *
     * @param logId 主键
     * @return ApiLog
     */
    DataServiceLog selectByLogId(@Param("logId") Long logId);


    /**
     * 根据时间范围筛选出请求数量，（一段时间内的增量）
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 数量
     */
    Long getIncrement(@Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime);
}
