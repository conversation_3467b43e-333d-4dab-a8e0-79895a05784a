package com.trs.ai.moye.monitor.entity;

import com.trs.ai.moye.monitor.enums.DetectionType;
import java.time.LocalDateTime;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 基础组件监控
 *
 * <AUTHOR>
 * @since 2025/6/10
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class BasicComponentDetection {

    /**
     * 组件名称
     */
    private String componentName;

    /**
     * 异常信息
     */
    private String errorMessage;

    /**
     * 检测状态 1-正常 0-异常
     */
    private int status;

    /**
     * 检测时间
     */
    private LocalDateTime detectionTime;

    /**
     * 检测方式
     */
    private String detectionType;

    /**
     * 检测人员
     */
    private String detectionUserName;

    /**
     * 正常检测结果
     *
     * @param componentName 组件名称
     * @param detectionType 检测类型
     * @param userName      用户名称
     * @return 正常检测结果
     */
    public static BasicComponentDetection success(String componentName, DetectionType detectionType,
        String userName) {
        BasicComponentDetection basicComponentDetection = new BasicComponentDetection();
        basicComponentDetection.setComponentName(componentName);
        basicComponentDetection.setDetectionTime(LocalDateTime.now());
        basicComponentDetection.setStatus(1);
        basicComponentDetection.setDetectionType(detectionType.getDescription());
        basicComponentDetection.setDetectionUserName(userName);
        return basicComponentDetection;
    }

    /**
     * 异常检测结果
     *
     * @param componentName 组件名称
     * @param detectionType 检测类型
     * @param userName      用户名称
     * @param errorMessage  错误信息
     * @return 异常检测结果
     */
    public static BasicComponentDetection error(String componentName, DetectionType detectionType,
        String userName, String errorMessage) {
        BasicComponentDetection basicComponentDetection = new BasicComponentDetection();
        basicComponentDetection.setComponentName(componentName);
        basicComponentDetection.setDetectionTime(LocalDateTime.now());
        basicComponentDetection.setStatus(0);
        basicComponentDetection.setErrorMessage(errorMessage);
        basicComponentDetection.setDetectionType(detectionType.getDescription());
        basicComponentDetection.setDetectionUserName(userName);
        return basicComponentDetection;
    }

    /**
     * 异常检测结果
     *
     * @param errorMessage 错误信息
     */
    public void fail(String errorMessage) {
        this.setStatus(0);
        this.setErrorMessage(errorMessage);
    }
}
