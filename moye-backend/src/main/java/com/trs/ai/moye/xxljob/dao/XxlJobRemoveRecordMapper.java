package com.trs.ai.moye.xxljob.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.trs.ai.moye.xxljob.entity.XxlJobRemoveRecord;
import java.util.Collection;
import java.util.List;
import java.util.Set;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * XxlJobRemoveRecord数据访问层
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/08/26 15:12
 */
@Mapper
public interface XxlJobRemoveRecordMapper extends BaseMapper<XxlJobRemoveRecord> {

    /**
     * 主键查询
     *
     * @param id 主键
     * @return XxlJobRemoveRecord
     */
    XxlJobRemoveRecord selectByPrimaryKey(@Param("id") Integer id);

    /**
     * id集合查询
     *
     * @param idCollection id集合
     * @return 数据集合
     */
    List<XxlJobRemoveRecord> selectByIdCollection(@Param("idCollection") Collection<Integer> idCollection);

    /**
     * 查询所有xxlJobId
     *
     * @return xxlJobId集合
     */
    Set<Integer> selectAllXxlJobId();

}