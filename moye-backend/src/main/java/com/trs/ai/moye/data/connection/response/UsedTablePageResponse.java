package com.trs.ai.moye.data.connection.response;

import com.trs.ai.moye.data.model.dto.DataStorageWithModelAndCategoryDTO;
import com.trs.moye.base.common.enums.ModelLayer;
import java.time.LocalDateTime;
import java.util.Optional;
import lombok.Data;

/**
 * 查询数据存储中某数据库 被使用的表，即在数据中台建立的表 的 响应体
 *
 * <AUTHOR>
 * @since 2024/9/27 17:30
 */
@Data
public class UsedTablePageResponse {

    /**
     * 表id
     */
    private Integer storageId;
    /**
     * 表英文名
     */
    private String storageEnName;
    /**
     * 表中文名
     */
    private String storageZhName;

    /**
     * 业务分类名称
     */
    private String businessCategoryName;
    /**
     * 分层
     */
    private ModelLayer layer;
    /**
     * 分层名称
     */
    private String layerName;

    /**
     * 数据建模id
     */
    private Integer dataModelId;
    /**
     * 数据建模名称
     */
    private String dataModelName;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 从 数据存储-数据建模-业务分类 信息 构造 response
     *
     * @param dto {@link DataStorageWithModelAndCategoryDTO} 数据存储-数据建模-业务分类 信息
     */
    public UsedTablePageResponse(DataStorageWithModelAndCategoryDTO dto) {
        this.storageId = dto.getDataStorageId();
        this.storageEnName = dto.getDataStorageName();
        this.storageZhName = dto.getDataStorageComment();
        this.businessCategoryName = dto.getBusinessCategoryZhName();
        this.layer = dto.getLayer();
        this.layerName = Optional.ofNullable(dto.getLayer()).map(ModelLayer::getLabel).orElse(null);
        this.dataModelId = dto.getDataModelId();
        this.dataModelName = dto.getDataModelZhName();
        this.createTime = dto.getDataStorageCreateTime();
    }
}
