package com.trs.ai.moye.data.model.entity.imports;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Ddl字段，字段导入功能使用
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class DdlColumn {

    /**
     * 列名
     */
    private String name;

    /**
     * 类型
     */
    private String type;

    /**
     * 注释
     */
    private String comment;

    public DdlColumn(String name, String type) {
        this.name = name;
        this.type = type;
    }
}
