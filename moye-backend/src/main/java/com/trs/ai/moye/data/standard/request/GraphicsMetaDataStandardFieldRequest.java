package com.trs.ai.moye.data.standard.request;

import com.trs.moye.base.data.standard.entity.MetaDataStandardField;
import com.trs.moye.base.data.standard.entity.TagEdgeField;
import com.trs.moye.base.common.enums.FieldType;
import java.util.List;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 用于图形化元数据标准字添加，修改请求
 *
 * <AUTHOR>
 * @since 2024/10/10
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
public class GraphicsMetaDataStandardFieldRequest {

    /**
     * 主键
     */
    private Integer id;

    /**
     * 元数据标准id
     */
    @NotNull(message = "元数据标准id不能为空")
    private Integer metaDataStandardId;

    /**
     * 中文名
     */
    @NotEmpty(message = "中文名不能为空")
    @Size(max = 50, message = "中文名长度不能超过50")
    private String zhName;

    /**
     * 英文名
     */
    @NotEmpty(message = "英文名不能为空")
    @Size(max = 50, message = "英文名长度不能超过50")
    private String enName;

    /**
     * 描述
     */
    private String description;

    /**
     * 节点类型
     */
    @NotNull(message = "节点类型不能为空")
    private FieldType type;

    /**
     * 属性列表
     */
    private List<TagEdgeField> fields;

    /**
     * 主键
     */
    @NotEmpty(message = "主键不能为空")
    @Size(max = 50, message = "主键长度不能超过50")
    private String primaryKey;

    /**
     * 从请求对象转换为实体对象
     *
     * @return {@link MetaDataStandardField} 实体对象
     */
    public MetaDataStandardField toMetaDataStandardField() {
        MetaDataStandardField field = new MetaDataStandardField();
        field.setId(id);
        field.setMetaDataStandardId(metaDataStandardId);
        field.setZhName(zhName);
        field.setEnName(enName);
        field.setDescription(description);
        field.setType(type);
        field.setFields(fields);
        field.setPrimaryKey(primaryKey);
        return field;
    }
}
