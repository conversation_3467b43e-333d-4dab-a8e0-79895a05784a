package com.trs.ai.moye.out.controller;

import com.trs.ai.moye.common.utils.WatchUtils;
import com.trs.ai.moye.data.standard.service.MetaDataStandardService;
import com.trs.ai.moye.out.request.AddTagEdgeFieldsRequest;
import com.trs.ai.moye.out.request.DeleteTagEdgeFieldsRequest;
import com.trs.ai.moye.out.request.UpdateTagEdgeFieldsRequest;
import com.trs.moye.base.common.exception.BizException;
import com.trs.moye.base.common.exception.GlobalFeignException;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 提供给一体化平台的接口 修改图形化数据标准，增删改tag/edge及其下的属性，同步到指定数据建模和存储点物理库
 *
 * <AUTHOR>
 * @since 2025/01/17
 **/
@Slf4j
@RequestMapping("/out/metadata-standard/tag-edge")
@RestController
public class MetaDataStandardTagEdgeFieldController {

    @Resource
    private MetaDataStandardService metaDataStandardService;

    /**
     * 增加tag/edge到数据标准、数据建模、物理库
     * <a href="https://yapi-192.trscd.com.cn/project/5419/interface/api/167125">YApi</a>9
     *
     * @param request 请求参数
     */
    @PostMapping("/fields")
    public void addTagEdgeFields(@RequestBody @Validated AddTagEdgeFieldsRequest request) {
        try {
            WatchUtils.create("元数据标准-对外接口-新增标签或边字段");
            metaDataStandardService.addTagEdgeFields(request);
            if (log.isDebugEnabled()) {
                log.debug(WatchUtils.getWatchLogInfo());
            }
        } catch (Exception e) {
            String exceptionMessage = buildExceptionMessage(e);
            throw new BizException(e, exceptionMessage);
        } finally {
            WatchUtils.remove();
        }
    }

    private String buildExceptionMessage(Exception e) {
        return parseExceptionMessage(e) + "\n===执行日志信息===\n" + WatchUtils.getWatchLogInfo();
    }

    private String parseExceptionMessage(Exception e) {
        if (e instanceof GlobalFeignException ee) {
            return ObjectUtils.isEmpty(ee.getDetails()) ? e.getMessage() : ee.getDetails();
        }
        return e.getMessage();
    }

    /**
     * 修改tag/edge到数据标准、数据建模、物理库
     * <a href="https://yapi-192.trscd.com.cn/project/5419/interface/api/167161">YApi</a>9
     *
     * @param request 请求参数
     */
    @PutMapping("/fields")
    public void updateTagEdgeFields(@RequestBody @Validated UpdateTagEdgeFieldsRequest request) {
        try {
            WatchUtils.create("元数据标准-对外接口-修改标签或边字段");
            metaDataStandardService.updateTagEdgeFields(request);
            if (log.isDebugEnabled()) {
                log.debug(WatchUtils.getWatchLogInfo());
            }
        } catch (Exception e) {
            String exceptionMessage = buildExceptionMessage(e);
            throw new BizException(e, exceptionMessage);
        } finally {
            WatchUtils.remove();
        }
    }

    /**
     * 删除tag/edge到数据标准、数据建模、物理库
     * <a href="https://yapi-192.trscd.com.cn/project/5419/interface/api/167167">YApi</a>9
     *
     * @param request 请求参数
     */
    @DeleteMapping("/fields")
    public void deleteTagEdgeFields(@RequestBody @Validated DeleteTagEdgeFieldsRequest request) {
        try {
            WatchUtils.create("元数据标准-对外接口-删除标签或边字段");
            metaDataStandardService.deleteTagEdgeFields(request);
            if (log.isDebugEnabled()) {
                log.debug(WatchUtils.getWatchLogInfo());
            }
        } catch (Exception e) {
            String exceptionMessage = buildExceptionMessage(e);
            throw new BizException(e, exceptionMessage);
        } finally {
            WatchUtils.remove();
        }
    }
}
