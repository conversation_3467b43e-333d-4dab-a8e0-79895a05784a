package com.trs.ai.moye.data.service.request;

import com.trs.moye.base.common.enums.ResultType;
import com.trs.moye.base.common.request.BaseRequestParams;
import javax.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2025/4/17
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class DataServiceLogRequest extends BaseRequestParams {

    @NotNull(message = "数据服务ID不能为空")
    private Integer serviceId;
    private ResultType responseStatus;
}
