package com.trs.ai.moye.data.service.entity;

import com.trs.ai.moye.data.service.enums.ServiceCategoryTreeNode;
import com.trs.ai.moye.data.service.enums.ServiceCreateMode;
import com.trs.ai.moye.data.service.enums.ServiceHealthStatus;
import com.trs.ai.moye.data.service.enums.ServicePublishStatus;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * 数据服务类别树
 *
 * <AUTHOR>
 * @since 2024/09/24 15:02:14
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class DataServiceCategoryTree extends DataServiceCategory {

    /**
     * 子树
     */
    private List<DataServiceCategoryTree> children;
    /**
     * 服务发布状态
     */
    private ServicePublishStatus publishStatus;
    /**
     * 服务健康状态
     */
    private ServiceHealthStatus healthStatus;
    /**
     * 节点类型
     */
    private ServiceCategoryTreeNode type = ServiceCategoryTreeNode.SERVICE_CATEGORY;
    /**
     * 创建模式
     */
    private ServiceCreateMode createMode;

    /**
     * 转换
     *
     * @param dataService 数据服务
     * @return {@link DataServiceCategoryTree }
     * <AUTHOR>
     * @since 2024/09/25 17:42:12
     */
    public static DataServiceCategoryTree to(DataService dataService) {
        DataServiceCategoryTree dataServiceCategoryTree = new DataServiceCategoryTree();
        dataServiceCategoryTree.setId(dataService.getId());
        dataServiceCategoryTree.setPid(dataService.getCategoryId());
        dataServiceCategoryTree.setName(dataService.getName());
        dataServiceCategoryTree.setDescription(dataService.getDescription());
        dataServiceCategoryTree.setCreateTime(dataService.getCreateTime());
        dataServiceCategoryTree.setUpdateTime(dataService.getUpdateTime());
        dataServiceCategoryTree.setCreateBy(dataService.getCreateBy());
        dataServiceCategoryTree.setUpdateBy(dataService.getUpdateBy());
        dataServiceCategoryTree.setPublishStatus(dataService.getPublishStatus());
        dataServiceCategoryTree.setHealthStatus(dataService.getHealthStatus());
        dataServiceCategoryTree.setType(ServiceCategoryTreeNode.SERVICE);
        dataServiceCategoryTree.setCreateMode(dataService.getCreateMode());
        return dataServiceCategoryTree;
    }

}
