package com.trs.ai.moye.data.model.dto.arrangement.stream;

import com.trs.moye.ability.entity.HttpRequestConfig;
import com.trs.moye.ability.entity.InputBind;
import com.trs.moye.ability.entity.OutputBind;
import com.trs.moye.ability.entity.Schema;
import com.trs.moye.ability.entity.operator.Operator;
import com.trs.moye.ability.entity.operator.OperatorRowType;
import com.trs.moye.base.data.service.entity.Condition;
import java.util.Map;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 算子dto
 *
 * <AUTHOR>
 * @since 2025/03/11 15:27:29
 */
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
public class OperatorDto extends Operator {

    /**
     * 用于前端绘制算子编排的id，同时用于创建时与回显画布信息绑定
     */
    private Long displayId;

    public OperatorDto(Operator operator) {
        this.setPipelineId(operator.getPipelineId());
        this.setName(operator.getName());
        this.setConditions(operator.getConditions());
        this.setInputFields(operator.getInputFields());
        this.setOutputFields(operator.getOutputFields());
        this.setAbilityId(operator.getAbilityId());
        this.setAbility(operator.getAbility());
        this.setOutputBind(operator.getOutputBind());
        this.setEnabled(operator.getEnabled());
        this.setConfig(operator.getConfig());
        this.setId(operator.getId());
        this.setCreateTime(operator.getCreateTime());
        this.setUpdateTime(operator.getUpdateTime());
        this.setCreateBy(operator.getCreateBy());
        this.setUpdateBy(operator.getUpdateBy());
        this.setInputBind(parseInputBind(operator.getInputBind(), operator.getAbility().getHttpRequestConfig()));
    }

    /**
     * 老数据兼容：输入绑定的query和header参数没有前缀的，在此时补上前缀，以防止回显失败
     *
     * @param inputBind         输入绑定
     * @param httpRequestConfig HTTP请求配置
     * @return 处理后的输入绑定
     */
    private InputBind parseInputBind(InputBind inputBind, HttpRequestConfig httpRequestConfig) {
        // 如果没有HTTP请求配置，则不需要处理
        if (httpRequestConfig == null) {
            return inputBind;
        } else {
            // 如果HTTP请求配置中没有query和header参数，则不需要处理
            Map<String, Schema> queryProperties = httpRequestConfig.getQueryParams().getProperties();
            Map<String, Schema> headerProperties = httpRequestConfig.getHeaders().getProperties();
            if (queryProperties.isEmpty() && headerProperties.isEmpty()) {
                return inputBind;
            }
            // 如果输入绑定中已经有了前缀，则不需要处理
            if (inputBind.getBindings().keySet().stream().anyMatch(key -> key.startsWith("query#") || key.startsWith("header#"))) {
                return inputBind;
            }

            InputBind newInputBind = new InputBind();
            for (Map.Entry<String, InputBind.Binding> entry : inputBind.getBindings().entrySet()) {
                String key = entry.getKey();
                InputBind.Binding binding = entry.getValue();
                if (queryProperties.containsKey(key)) {
                    key = "query#" + key;
                } else if (headerProperties.containsKey(key)) {
                    key = "header#" + key;
                }
                newInputBind.getBindings().put(key, binding);
            }
            return newInputBind;
        }
    }

    public OperatorDto(
        Integer abilityId,
        String name,
        InputBind inputBind,
        OutputBind outputBind,
        Integer storageId,
        Integer dataModelId,
        Condition[] conditions) {
        this.setAbilityId(abilityId);
        this.setName(name);
        this.setEnabled(true);
        this.setInputBind(inputBind);
        this.setOutputBind(outputBind);
        this.setInputFields(new OperatorRowType());
        this.setOutputFields(new OperatorRowType());
        this.setStorageId(storageId);
        this.setDataModelId(dataModelId);
        this.setConditions(conditions);
    }

}
