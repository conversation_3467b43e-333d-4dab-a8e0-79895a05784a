package com.trs.ai.moye.data.model.request.fields;

import com.trs.ai.moye.data.model.dto.StoragePointParams;
import java.util.List;
import javax.validation.constraints.NotEmpty;
import lombok.Data;

/**
 * 检查已存在表字段请求
 *
 * <AUTHOR>
 * @since 2024/12/11 16:15:30
 */
@Data
public class CheckExistedTableFieldsRequest {

    /**
     * 存储点列表
     */
    @NotEmpty
    private List<StoragePointParams> storagePointList;

}
