package com.trs.ai.moye.monitor.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 2021/5/18 11:32
 **/
@AllArgsConstructor
public enum NacosInstanceEnum {


    MOYE("moye"),
    MOYE_STORAGE_ENGINE("moye-storage-engine"),
    MOYE_MONITOR_CENTER("moye-monitor-center"),
    MOYE_BATCH_ENGINE("moye-batch-engine"),
    MOYE_SCHEDULE_CENTER("moye-schedule-center"),
    TY_ENGINE("ty-engine"),
    TY_MSG_CPU("ty-msg-cpu");

    @Getter
    private String name;
}
