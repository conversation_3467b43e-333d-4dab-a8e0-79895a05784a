package com.trs.ai.moye.data.service.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 服务健康状态
 *
 * <AUTHOR>
 * @since 2024/09/24 18:19:44
 */
@Getter
@AllArgsConstructor
public enum ServiceHealthStatus {

    /**
     * 未检测
     */
    UNCHECKED("未检测"),
    /**
     * 健康
     */
    HEALTHY("健康"),
    /**
     * 不可用
     */
    UNAVAILABLE("不可用"),
    /**
     * 稳定
     */
    UNSTABLE("不稳定"),
    /**
     * 超时
     */
    TIMEOUT("超时");

    /**
     * 名称
     */
    private final String name;
}
