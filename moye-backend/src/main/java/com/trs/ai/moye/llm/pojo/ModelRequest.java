package com.trs.ai.moye.llm.pojo;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import lombok.Data;

/**
 * 大模型请求参数
 *
 * <AUTHOR>
 * @since 2025/4/15 15:49
 */
@Data
public class ModelRequest {

    /**
     * 生成控制参数
     */
    @JsonProperty("max_tokens")
    private Integer maxTokens = 4096;

    @JsonProperty("top_p")
    private Double topP = 0.85;

    @JsonProperty("stream")
    private Boolean stream = true;

    @JsonProperty("top_k")
    private Integer topK = 20;

    @JsonProperty("temperature")
    private Double temperature = 0.3;

    @JsonProperty("add_generation_prompt")
    private Boolean addGenerationPrompt = true;

    @JsonProperty("model")
    private String modelName;

    /**
     * 消息内容集合
     */
    private List<Message> messages = new ArrayList<>();

    /**
     * 重复抑制参数
     */
    @JsonProperty("repetition_penalty")
    private Double repetitionPenalty = 1.05;

    /**
     * 消息体嵌套类
     */
    @Data
    public static class Message {

        /**
         * 自然语言内容
         */
        private String content;

        /**
         * 角色标识（system/user/assistant）
         */
        private String role;
        /**
         * 工具名称
         */
        private String toolName;
    }

    /**
     * 工具列表嵌套类
     */
    @Data
    public static class Tool {

        /**
         * 工具名称
         */
        private Function function;

        /**
         * 工具描述
         */
        private String type = "function";

    }

    /**
     * 工具函数嵌套类
     */
    @Data
    public static class Function {

        /**
         * 工具名称
         */
        private String name;

        /**
         * 工具描述
         */
        private String description;

        /**
         * "parameters": { "condition": { "description": "需要获取哪些分局代码的条件", "type": "string" }, "required": [ "condition"
         * ] } },
         */
        private FunctionCallParameters parameters = new FunctionCallParameters();

        @Data
        private static class FunctionCallParameters {

            private ParameterField condition = new ParameterField();

            private List<String> required = new ArrayList<>();

            public FunctionCallParameters() {
                this.required.add("condition");
            }

            @Data
            private static class ParameterField {
                private String description = "需要获取数据的条件";
                private String type = "string";
            }
        }
    }

    /**
     * 添加用户消息体方法
     *
     * @param content 消息内容
     */
    public void addUserMessage(String content) {
        addMessage(content, "user");
    }

    private void addMessage(String content, String role) {
        Message message = new Message();
        message.setRole(role);
        message.setContent(content);
        this.messages.add(message);
    }

    /**
     * 添加用户消息体方法
     *
     * @param result   工具结果
     * @param toolName 工具名称
     */
    public void addToolMessage(String result, String toolName) {
        Message message = new Message();
        message.setRole("user");
        message.setToolName(toolName);
        String content = String.format("<tool_use_result><name>%s</name><result>%s</result></tool_use_result>",
            toolName, result);
        content = content + "\n请根据tool返回结果，继续进行前面的sql生成任务，如果当前信息不充分，需要调用给出的工具，则选择合适的工具，停止输出sql。";
        message.setContent(content);
        this.messages.add(message);
    }

    /**
     * 添加助手消息体方法
     *
     * @param content 消息内容
     */
    public void addAssistantMessage(String content) {
        addMessage(content, "assistant");
    }

    /**
     * 添加系统消息体方法
     *
     * @param content 消息内容
     */
    public void addSystemMessage(String content) {
        addMessage(content, "system");
    }

    /**
     * 获取所有上下文中调用过的工具名称
     *
     * @return 工具名称列表
     */
    public List<String> getCalledTools() {
        return messages.stream().map(Message::getToolName).filter(Objects::nonNull).collect(Collectors.toList());
    }
}


