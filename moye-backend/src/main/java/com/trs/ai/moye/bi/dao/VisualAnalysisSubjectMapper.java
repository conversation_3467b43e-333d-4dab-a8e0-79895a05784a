package com.trs.ai.moye.bi.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.trs.ai.moye.bi.domain.entity.ChartPosition;
import com.trs.ai.moye.bi.domain.entity.VisualAnalysisIdName;
import com.trs.ai.moye.bi.domain.entity.VisualAnalysisSubject;
import java.util.List;
import java.util.Set;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

/**
 * 可视化分析-主题(VisualAnalysisSubject)持久层
 *
 * <AUTHOR>
 * @since 2024-12-11 15:42:04
 */
@Mapper
public interface VisualAnalysisSubjectMapper extends BaseMapper<VisualAnalysisSubject> {

    /**
     * 判断名称是否存在
     *
     * @param name 名称
     * @return 是否存在
     */
    @Select("select count(*) from visual_analysis_subject where name = #{name}")
    boolean existByName(@Param("name") String name);

    /**
     * 判断id是否存在
     *
     * @param id id
     * @return 是否存在
     */
    @Select("select count(*) from visual_analysis_subject where id = #{id}")
    boolean existById(@Param("id") Integer id);

    /**
     * 根据是否发布查询主题
     *
     * @param isPublish 是否发布
     * @return 列表
     */
    List<VisualAnalysisSubject> selectByPublishStatus(@Param("isPublish") Boolean isPublish);

    /**
     * 查询分类下的主题数量
     *
     * @param categoryId 分类id
     * @return 数量
     */
    @Select("select count(*) from visual_analysis_subject where category_id = #{categoryId}")
    Integer countByCategoryId(@Param("categoryId") Integer categoryId);

    /**
     * 发布/取消发布主题
     *
     * @param id            主题id
     * @param publishStatus 状态
     */
    @Update("update visual_analysis_subject set is_publish = #{publishStatus} where id = #{id}")
    void updatePublishStatusById(@Param("id") Integer id, @Param("publishStatus") Boolean publishStatus);

    /**
     * 修改图表位置信息
     *
     * @param id       主题id
     * @param position 位置信息
     */
    void updatePositionById(@Param("id") Integer id, @Param("position") List<ChartPosition> position);

    /**
     * 批量移动
     *
     * @param categoryId 分类id
     * @param ids        主题id
     */
    void moveByCategoryId(@Param("categoryId") Integer categoryId, @Param("ids") Set<Integer> ids);

    /**
     * 根据数据建模id查询数据分析
     *
     * @param dataModelId 数据建模id
     * @return 数据分析
     */
    List<VisualAnalysisIdName> selectByDataModelId(@Param("dataModelId") Integer dataModelId);

    /**
     * 根据数据建模id查询已发布的可视化分析
     *
     * @param dataModelId 数据建模id
     * @return 已发布的可视化分析
     */
    String selectPublishedByDataModelId(@Param("dataModelId") Integer dataModelId);
}