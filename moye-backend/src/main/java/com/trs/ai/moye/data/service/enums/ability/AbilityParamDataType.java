package com.trs.ai.moye.data.service.enums.ability;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * ability param 数据类型enum 当parameterLocation为3:Form时,dataType 1代表text 2代表file(file暂时屏蔽,网关不路由文件) 其他情况都为null 参数类型：1-String
 * 2-Number 3-Integer 4-File 5-Array
 *
 * <AUTHOR>
 * @since 2024/09/30 10:08:56
 */
@AllArgsConstructor
public enum AbilityParamDataType {

    /**
     * String
     */
    STRING(1, "String"),
    /**
     * Number
     */
    NUMBER(2, "Number"),
    /**
     * Integer
     */
    INTEGER(3, "Integer"),
    /**
     * File
     */
    FILE(4, "File"),
    /**
     * Array
     */
    ARRAY(5, "Array");

    /**
     * code
     */
    @Getter
    private final Integer code;
    /**
     * 名称
     */
    private final String name;
}
