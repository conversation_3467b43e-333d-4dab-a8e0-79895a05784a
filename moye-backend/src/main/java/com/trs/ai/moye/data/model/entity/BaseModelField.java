package com.trs.ai.moye.data.model.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.trs.moye.base.common.entity.field.FieldAdvanceConfig;
import com.trs.moye.base.common.enums.FieldType;
import com.trs.moye.base.common.exception.BizException;
import com.trs.moye.base.common.help.RepeatChecker;
import com.trs.moye.base.common.typehandler.PolymorphismTypeHandler;
import com.trs.moye.base.data.model.entity.FieldForeignKeyConfig;
import com.trs.moye.base.data.standard.entity.TagEdgeField;
import java.util.List;
import java.util.Objects;
import javax.validation.constraints.NotNull;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.lang.Nullable;

/**
 * <AUTHOR>
 * @since 2024-10-11 16:20
 */
@Data
@NoArgsConstructor
public class BaseModelField {

    /**
     * 中文名：系统属性中文名
     */
    protected String zhName;

    /**
     * 系统属性英文名：系统属性英文名
     */
    protected String enName;

    /**
     * 系统属性类型：系统属性类型
     */
    protected FieldType type;

    /**
     * 类型中文名称
     */
    protected String typeName;

    /**
     * 允许多值。1：是否允许多值。1：是；0：否；默认否
     */
    protected boolean isMultiValue;

    /**
     * 允许空
     */
    protected boolean isNullable = true;

    /**
     * 系统属性描述：系统属性描述
     */
    protected String description;

    /**
     * 高级属性：高级属性
     */
    @TableField(typeHandler = PolymorphismTypeHandler.class)
    protected FieldAdvanceConfig advanceConfig;

    /**
     * 默认值
     */
    protected String defaultValue;

    /**
     * 类别：类别：1：标准属性；0：自定义属性
     */
    @NotNull(message = "类别不允许空")
    protected boolean isUseStandard;

    /**
     * 是否为统计字段。1：是否为统计字段。1：是；0：否；默认否
     */
    @NotNull(message = "是否为统计字段。1不允许空")
    protected boolean isStatistic;

    /**
     * 内置类型：内置类型：1普通类型，2系统内置
     */
    @NotNull(message = "内置类型不允许空")
    protected boolean isBuiltIn;

    /**
     * 是否为主键：是否为主键
     */
    @NotNull(message = "是否为主键不允许空")
    protected boolean isPrimaryKey;

    /**
     * 是否增量字段：是否增量字段
     */
    @NotNull(message = "是否增量字段不允许空")
    protected boolean isIncrement;

    /**
     * 是否分区字段：是否分区字段
     */
    @NotNull(message = "是否分区字段不允许空")
    protected boolean isPartition;

    /**
     * 是否提供服务字段：是否作为服务提供字段
     */
    protected boolean isProvideService = true;

    /**
     * 是标题字段
     */
    protected boolean isTitle;

    /**
     * 字段列表
     */
    @Nullable
    protected List<TagEdgeField> fields;

    /**
     * 字段列表中的主键字段
     */
    @Nullable
    protected String fieldsPrimaryKey;

    /**
     * 是否为外键
     */
    protected boolean isForeignKey = false;

    /**
     * 外键配置
     */
    protected FieldForeignKeyConfig foreignKeyConfig;

    /**
     * 检查 字段列表 和 主键，图形化字段才需要检查
     */
    public void checkFields() {
        boolean needCheckFields = FieldType.GRAPHICS_TAG.equals(this.type) || FieldType.GRAPHICS_EDGE.equals(this.type);
        if (!needCheckFields) {
            return;
        }
        // 消息前缀 e.g. 标签[people]中
        String tagEdgeMsg = String.format("%s[%s]中", this.type.getLabel(), this.zhName);

        // 检查字段列表: 列表不允许为空，名称类型不允许为空，名称不允许重复
        if (Objects.requireNonNull(fields, tagEdgeMsg + "没有字段").isEmpty()) {
            throw new BizException(tagEdgeMsg + "没有字段");
        }
        fields.forEach(field -> {
            Objects.requireNonNull(field.getEnName(), tagEdgeMsg + "有字段的英文名为空");
            Objects.requireNonNull(field.getZhName(), tagEdgeMsg + "有字段的中文名为空");
            Objects.requireNonNull(field.getType(), tagEdgeMsg + "有字段的类型为空");
        });
        RepeatChecker.checkCollection(this.fields, tagEdgeMsg + "字段英文名存在重复,", TagEdgeField::getEnName);

        // 检查主键: 主键不允许为空，主键需要是字段列表中的字段
        Objects.requireNonNull(fieldsPrimaryKey, tagEdgeMsg + "未选择主键");
        boolean isPrimaryKeyFromFields = fields.stream().anyMatch(field -> fieldsPrimaryKey.equals(field.getEnName()));
        if (!isPrimaryKeyFromFields) {
            throw new BizException(tagEdgeMsg + "主键不在字段列表中");
        }
    }

}
