package com.trs.ai.moye.data.model.response;

import com.trs.moye.base.data.connection.enums.ConnectionType;
import com.trs.moye.base.data.connection.enums.DataSourceCategory;
import com.trs.ai.moye.data.model.enums.RealtimeDwdRecordQuality;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 实时监控DWD层实体（对应表 t_monitor_dwd_realtime）
 *
 * <AUTHOR>
 * @since 2025/02/17 16:28:50
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class DwdRealtimeMonitorResponse {

    /**
     * id
     */
    private Long id;

    /**
     * 数据建模id
     */
    private Integer dataModelId;

    /**
     * 数据建模名称
     */
    private String dataModelName;

    /**
     * 消息主题
     */
    private String topic;

    // ==================== 监控指标 ====================
    /**
     * 监控时间
     */
    private LocalDateTime monitorTime;

    /**
     * 当前Offset
     */
    private Long currentOffset;

    /**
     * 结束Offset
     */
    private Long endOffset;

    /**
     * 积压量
     */
    private Long lag;

    /**
     * 平均TPS
     */
    private BigDecimal avgTps;

    // ==================== 数据来源 ====================
    /**
     * 来源大类,MQ
     */
    private DataSourceCategory sourceType;

    /**
     * 来源子类型表；mysql、kafka、sftp
     */
    private ConnectionType sourceSubType;

    // ==================== 系统字段 ====================
    /**
     * 入库时间
     */
    private LocalDateTime storageTime;

    /**
     * 数据质量标记
     */
    private RealtimeDwdRecordQuality dataQuality;

    /**
     * 异常备注
     */
    private String remark;

}
