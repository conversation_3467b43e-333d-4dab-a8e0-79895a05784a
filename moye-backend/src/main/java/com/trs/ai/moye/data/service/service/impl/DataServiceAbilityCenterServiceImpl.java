package com.trs.ai.moye.data.service.service.impl;

import com.fasterxml.jackson.core.type.TypeReference;
import com.trs.ai.moye.common.http.HttpClient;
import com.trs.ai.moye.common.http.HttpRequestEntity;
import com.trs.ai.moye.data.service.config.AbilityCenterProperties;
import com.trs.ai.moye.data.service.request.ability.DataServicePublishRequest;
import com.trs.ai.moye.data.service.response.ability.AbilityCheckDeleteResponse;
import com.trs.ai.moye.data.service.response.ability.AbilityCheckUnpublishResponse;
import com.trs.ai.moye.data.service.response.ability.AbilityGetByAppKeyResponse;
import com.trs.ai.moye.data.service.response.ability.AbilityMsgResponse;
import com.trs.ai.moye.data.service.service.DataServiceAbilityCenterService;
import com.trs.moye.base.common.exception.BizException;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

/**
 * 能力中心服务实现类
 *
 * <AUTHOR>
 * @since 2024/09/29 15:16:40
 */
@Service
@Slf4j
public class DataServiceAbilityCenterServiceImpl implements DataServiceAbilityCenterService {

    public static final String AUTHORIZATION_STR = "Authorization";
    public static final String BASIC = "Basic ";
    public static final String POWER_IDS = "powerIds";

    @Resource
    private AbilityCenterProperties abilityCenterProperties;
    @Resource
    private HttpClient httpClient;

    private Map<String, Object> buildAuthorizationHeader() {
        return Map.of(AUTHORIZATION_STR, BASIC + abilityCenterProperties.getAuthorization());
    }

    @Override
    public AbilityMsgResponse publish(DataServicePublishRequest request) {
        String baseUrl = abilityCenterProperties.getBaseUrl();
        String publishPath = abilityCenterProperties.getPublishPath();
        log.info("调用能力中心接口-注册能力，请求路径：{}，请求参数：{}", publishPath, request);
        HttpRequestEntity requestEntity = HttpRequestEntity.post(baseUrl + publishPath)
            .headers(buildAuthorizationHeader()).body(request).build();
        // 发送请求
        List<AbilityMsgResponse> responses = httpClient.doRequest(requestEntity, new TypeReference<>() {
        });

        return checkResponse(responses, publishPath);
    }

    @Override
    public AbilityMsgResponse republish(DataServicePublishRequest request) {
        String baseUrl = abilityCenterProperties.getBaseUrl();
        String republishPath = abilityCenterProperties.getRepublishPath();
        log.info("调用能力中心接口-重新注册能力，请求路径：{}，请求参数：{}", republishPath, request);
        HttpRequestEntity requestEntity = HttpRequestEntity.post(baseUrl + republishPath)
            .headers(buildAuthorizationHeader()).body(request).build();
        // 发送请求
        List<AbilityMsgResponse> responses = httpClient.doRequest(requestEntity, new TypeReference<>() {
        });

        return checkResponse(responses, republishPath);
    }

    @Override
    public AbilityCheckUnpublishResponse checkUnpublish(Integer abilityCenterId, String abilityName) {
        String baseUrl = abilityCenterProperties.getBaseUrl();
        String checkDeletePath = abilityCenterProperties.getCheckDeletePath();
        log.info("调用能力中心接口-查看能力调用情况，请求路径：{}，请求参数：{}", checkDeletePath,
            Collections.singletonList(abilityCenterId));
        HttpRequestEntity requestEntity = HttpRequestEntity.post(baseUrl + checkDeletePath)
            .headers(buildAuthorizationHeader()).body(Map.of(POWER_IDS, Collections.singletonList(abilityCenterId)))
            .build();
        // 发送请求
        List<AbilityCheckDeleteResponse> responses = httpClient.doRequest(requestEntity, new TypeReference<>() {
        });
        AbilityCheckDeleteResponse data = checkResponse(responses, checkDeletePath);
        AbilityCheckUnpublishResponse checkCancelVO = new AbilityCheckUnpublishResponse();

        Integer calleeCount = data.getCalleeCount();
        if (calleeCount == 0) {
            checkCancelVO.setCheckDelete(true);
        } else {
            checkCancelVO.setCheckDelete(false);
            String abilityAddress = baseUrl + abilityCenterProperties.getSearchByAbilityName();
            checkCancelVO.setAbilityAddress(abilityAddress + abilityName);
        }
        return checkCancelVO;
    }

    @Override
    public AbilityMsgResponse unpublish(Integer abilityCenterId) {
        String baseUrl = abilityCenterProperties.getBaseUrl();
        String deletePath = abilityCenterProperties.getUnpublishPath();
        log.info("调用能力中心接口-撤销发布，请求路径：{}，请求参数：{}", deletePath,
            Collections.singletonList(abilityCenterId));
        HttpRequestEntity requestEntity = HttpRequestEntity.post(baseUrl + deletePath)
            .headers(buildAuthorizationHeader()).body(Map.of(POWER_IDS, Collections.singletonList(abilityCenterId)))
            .build();
        // 发送请求
        List<AbilityMsgResponse> responses = httpClient.doRequest(requestEntity, new TypeReference<>() {
        });

        return checkResponse(responses, deletePath);
    }

    @Override
    public Map<Integer, Integer> checkPublishStatus(List<Integer> abilityCenterIds) {
        if (CollectionUtils.isEmpty(abilityCenterIds)) {
            return new HashMap<>();
        }
        String baseUrl = abilityCenterProperties.getBaseUrl();
        String checkPublishStatus = abilityCenterProperties.getPublishStatusPath();
        log.info("调用能力中心接口-查询能力发布状态，请求路径：{}，请求参数：{}", checkPublishStatus, abilityCenterIds);
        HttpRequestEntity requestEntity = HttpRequestEntity.post(baseUrl + checkPublishStatus)
            .headers(buildAuthorizationHeader()).body(Map.of(POWER_IDS, abilityCenterIds)).build();
        // 发送请求
        List<Map<Integer, Integer>> responses = httpClient.doRequest(requestEntity, new TypeReference<>() {
        });

        return checkResponse(responses, checkPublishStatus);
    }

    @Override
    public boolean checkAbilityExist(Integer abilityCenterId) {
        String baseUrl = abilityCenterProperties.getBaseUrl();
        String checkExistPath = abilityCenterProperties.getCheckExistPath();
        log.info("调用能力中心接口-查询能力是否存在，请求路径：{}，请求参数：{}", checkExistPath, abilityCenterId);
        HttpRequestEntity requestEntity = HttpRequestEntity.get(baseUrl + checkExistPath)
            .headers(buildAuthorizationHeader()).params(Map.of("powerId", abilityCenterId)).build();
        // 发送请求
        List<Boolean> responses = httpClient.doRequest(requestEntity, new TypeReference<>() {
        });

        return checkResponse(responses, checkExistPath);
    }

    @Override
    public Long getAppId() {
        String baseUrl = abilityCenterProperties.getBaseUrl();
        String getAppIdPath = abilityCenterProperties.getAppPath();
        String clientName = abilityCenterProperties.getClientName();
        log.info("调用能力中心接口-获取应用信息，请求路径：{}，请求参数：{}", getAppIdPath, clientName);
        HttpRequestEntity requestEntity = HttpRequestEntity.get(baseUrl + getAppIdPath)
            .headers(buildAuthorizationHeader()).params(Map.of("appKey", clientName)).build();
        // 发送请求
        List<AbilityGetByAppKeyResponse> responses = httpClient.doRequest(requestEntity, new TypeReference<>() {
        });
        AbilityGetByAppKeyResponse data = checkResponse(responses, getAppIdPath);
        return data.getId();
    }

    private static <T> T checkResponse(List<T> data, String path) {
        if (data.isEmpty()) {
            throw new BizException(String.format("调用能力中心接口 %s 结果data为空！！", path));
        }
        // 返回结果只有一个元素
        log.info("调用能力中心接口 {} -返回结果：{}", path, data.get(0));
        return data.get(0);
    }

    @Override
    public void editAbility(DataServicePublishRequest request) {
        String baseUrl = abilityCenterProperties.getBaseUrl();
        String editPath = abilityCenterProperties.getEditPath();
        log.info("调用能力中心接口-修改能力参数，请求路径：{}，请求参数：{}", editPath, request);
        HttpRequestEntity requestEntity = HttpRequestEntity.post(baseUrl + editPath)
            .headers(buildAuthorizationHeader()).body(request).build();
        // 发送请求
        List<AbilityMsgResponse> responses = httpClient.doRequest(requestEntity, new TypeReference<>() {
        });
        checkResponse(responses, editPath);
    }
}
