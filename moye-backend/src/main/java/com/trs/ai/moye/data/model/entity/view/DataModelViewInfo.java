package com.trs.ai.moye.data.model.entity.view;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.trs.moye.base.common.typehandler.CustomJacksonTypeHandler;
import com.trs.moye.base.common.entity.AuditBaseEntity;
import com.trs.ai.moye.data.model.entity.view.TableInfo.FieldInfo;
import com.trs.ai.moye.data.model.enums.JoinType;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Queue;
import java.util.Set;
import java.util.function.BiConsumer;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.jetbrains.annotations.NotNull;

/**
 * 视图数据建模 的 视图信息
 *
 * <AUTHOR>
 * @since 2024/9/20 18:12
 **/
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "data_model_view_info", autoResultMap = true)
public class DataModelViewInfo extends AuditBaseEntity {

    /**
     * 数据建模id
     */
    private Integer dataModelId;
    /**
     * 数据库连接id
     */
    private Integer connectionId;
    /**
     * 表和字段信息
     */
    @TableField(typeHandler = CustomJacksonTypeHandler.class)
    private List<TableInfo> tablesFields;
    /**
     * 表联合查询的join关系 JSON 结构
     */
    @TableField(typeHandler = CustomJacksonTypeHandler.class)
    private List<JoinRelationInfo> joinRelations;
    /**
     * 联合查询 SQL
     */
    @TableField(value = "`sql`")
    private String sql;


    /**
     * 根据表字段信息 和 join信息 构建 sql
     */
    public void buildSql() {
        if (Objects.isNull(tablesFields)) {
            throw new IllegalArgumentException("tablesFields must be set before build sql!!!");
        }
        // 使用副本避免篡改
        List<TableInfo> tableFieldsCopy = new ArrayList<>(tablesFields);
        List<JoinRelationInfo> joinRelationsCopy = new ArrayList<>();
        if (Objects.nonNull(joinRelations)) {
            joinRelationsCopy = new ArrayList<>(joinRelations);
        }
        // 构建select子句元素列表
        String selectClause = getSelectClause(tableFieldsCopy);
        // 构建join子句元素列表
        String joinClause = getFromClause(tableFieldsCopy, joinRelationsCopy);

        this.sql = selectClause + "\n" + joinClause;
    }

    @NotNull
    private String getSelectClause(List<TableInfo> tablesFields) {
        List<String> selectClauseItems = new ArrayList<>();
        for (TableInfo table : tablesFields) {
            String tableName = table.getTableName();
            if (Objects.isNull(table.getFields())) {
                continue;
            }
            for (FieldInfo field : table.getFields()) {
                if (Boolean.TRUE.equals(field.getChecked())) {
                    String item = String.format("%s.%s AS %s", tableName, field.getFieldName(), field.getAlias());
                    selectClauseItems.add(item);
                }
            }
        }
        return "SELECT " + String.join(", ", selectClauseItems);
    }

    /**
     * 只处理 join关联关系形成树型结构或路径的情况，成环的图视为异常。
     *
     * @param tablesFields  表信息
     * @param joinRelations 连接关系
     * @return from ... join ... 语句
     */
    @NotNull
    private String getFromClause(List<TableInfo> tablesFields, List<JoinRelationInfo> joinRelations) {

        // 单表的情况，直接返回from子句
        if (joinRelations.isEmpty()) {
            return "FROM " + tablesFields.get(0).getTableName();
        }

        // 存储from子句或join子句
        List<String> joinClauseItems = new ArrayList<>();
        // 定义处理逻辑，根据主表和连接关系，处理出连接表的join语句
        BiConsumer<String, JoinRelationInfo> processJoinRelation = (primaryTable, joinRelation) -> {

            String onClause = String.format("ON %s.%s = %s.%s", joinRelation.getSourceTable(),
                joinRelation.getSourceField(), joinRelation.getTargetTable(), joinRelation.getTargetField());

            // 获取正确的连接表和连接类型
            JoinType joinType = joinRelation.getJoinType();
            String joinTable = joinRelation.getTargetTable();
            // 如果主表事实上是joinRelation中的targetTable, joinType需要反向
            if (joinRelation.getTargetTable().equals(primaryTable)) {
                joinType = joinType.getOpposite();
                joinTable = joinRelation.getSourceTable();
            }

            if (joinClauseItems.isEmpty()) {
                String fromClause = String.format("FROM %s", primaryTable);
                joinClauseItems.add(fromClause);
            }
            String joinClause = String.format("%s JOIN %s %s", joinType, joinTable, onClause);
            joinClauseItems.add(joinClause);
        };

        // 用邻接表存储图
        Map<String, List<JoinRelationInfo>> tableName2JoinRelationInfos = new HashMap<>();
        for (JoinRelationInfo joinRelation : joinRelations) {
            tableName2JoinRelationInfos
                .computeIfAbsent(joinRelation.getSourceTable(), k -> new ArrayList<>())
                .add(joinRelation);
            tableName2JoinRelationInfos
                .computeIfAbsent(joinRelation.getTargetTable(), k -> new ArrayList<>())
                .add(joinRelation);
        }

        // 以任一节点开始广搜
        String start = tablesFields.get(0).getTableName();
        Set<String> visited = new HashSet<>();
        Queue<String> queue = new LinkedList<>();
        queue.add(start);

        while (!queue.isEmpty()) {
            String vertex = queue.poll();
            visited.add(vertex);
            for (JoinRelationInfo edge : tableName2JoinRelationInfos.get(vertex)) {
                // 邻接点
                String neighbor = edge.getSourceTable().equals(vertex) ? edge.getTargetTable() : edge.getSourceTable();
                if (!visited.contains(neighbor)) {
                    // 处理join
                    processJoinRelation.accept(vertex, edge);
                    queue.add(neighbor);
                }
            }
        }

        return String.join("\n", joinClauseItems);
    }
}
