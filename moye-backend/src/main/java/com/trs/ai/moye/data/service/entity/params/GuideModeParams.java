package com.trs.ai.moye.data.service.entity.params;

import com.trs.ai.moye.common.validation.ValidCondition;
import com.trs.ai.moye.data.service.entity.query.Condition;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 向导模式参数
 *
 * <AUTHOR>
 * @since 2024/09/25 18:47:39
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class GuideModeParams extends DataServiceConfigParams {

    /**
     * 条件
     */
    @ValidCondition
    private List<Condition> condition;

    @Override
    public <T extends DataServiceConfigParams> DataServiceConfigParams merge(T dataServiceConfigParams) {
        super.merge(dataServiceConfigParams);
        if (dataServiceConfigParams instanceof GuideModeParams guideModeParams) {
            updateFieldsIfNotEmpty(guideModeParams.getCondition(), this::setCondition);
        }
        return this;
    }
}
