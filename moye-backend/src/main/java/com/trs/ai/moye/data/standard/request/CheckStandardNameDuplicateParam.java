package com.trs.ai.moye.data.standard.request;

import javax.validation.constraints.NotEmpty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2024/10/11
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CheckStandardNameDuplicateParam {

    private Integer id;

    /**
     * 英文名
     */
    @NotEmpty(message = "英文名称不能为空")
    private String enName;
    /**
     * 中文名
     */
    private String zhName;

}
