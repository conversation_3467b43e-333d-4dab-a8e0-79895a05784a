package com.trs.ai.moye.xxljob;

import com.trs.ai.moye.xxljob.entity.MoyeXxlJob;
import com.trs.ai.moye.xxljob.enums.XxlJobStatusEnum;
import com.trs.ai.moye.xxljob.service.XXLJobApiService;
import com.trs.moye.base.common.utils.AssertUtils;
import com.trs.moye.base.xxljob.XxlJobGroup;
import com.trs.moye.base.xxljob.XxlJobInfo;
import java.util.List;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-06-18 17:54
 */
@Slf4j
@Component
public class XXLJobManager {

    private static final String MSG_NO_XXL_JOB = "主键为【%s】的xxl-job任务不存在";

    @Resource
    private XXLJobApiService xxlJobApiService;

    /**
     * 启动任务
     *
     * @param xxlJobId xxl-job任务id
     */
    public void startJob(int xxlJobId) {
        XxlJobInfo xxlJobInfo = xxlJobApiService.getJobInfo(xxlJobId);
        AssertUtils.notEmpty(xxlJobInfo, MSG_NO_XXL_JOB, xxlJobId);
        xxlJobApiService.startJob(xxlJobId);
    }

    /**
     * 停止任务
     *
     * @param xxlJobId xxl-job任务id
     */
    public void stopJob(int xxlJobId) {
        XxlJobInfo xxlJobInfo = xxlJobApiService.getJobInfo(xxlJobId);
        if (xxlJobInfo == null) {
            log.error(String.format(MSG_NO_XXL_JOB, xxlJobId));
            return;
        }
        xxlJobApiService.stopJob(xxlJobInfo.getId());
    }

    /**
     * 创建任务
     *
     * @param job xxl-job任务
     * @return xxlJobInfo xxl-job任务
     */
    public XxlJobInfo createJob(MoyeXxlJob job) {
        XxlJobInfo jobInfo = createXxlJobInfo(job);
        xxlJobApiService.addJob(jobInfo);
        // 新增时任务状态是停止状态
        jobInfo.setTriggerStatus(XxlJobStatusEnum.STOP.getCode());
        return jobInfo;
    }

    /**
     * 修改任务
     *
     * @param xxlJobId xxl-job主键
     * @param job      moye xxl-job任务
     * @return xxlJobId
     */
    public Integer updateJob(Integer xxlJobId, MoyeXxlJob job) {
        XxlJobInfo oldJobInfo = xxlJobApiService.getJobInfo(xxlJobId);
        XxlJobInfo jobInfo = createXxlJobInfo(job);
        //任务不存在则创建
        if (oldJobInfo == null) {
            log.error(String.format(MSG_NO_XXL_JOB, xxlJobId));
            xxlJobApiService.addJob(jobInfo);
            return jobInfo.getId();
        }
        jobInfo.setId(xxlJobId);
        xxlJobApiService.updateJob(jobInfo);
        return xxlJobId;
    }

    private XxlJobInfo createXxlJobInfo(MoyeXxlJob job) {
        XxlJobInfo xxlJobInfo = job.toXxlJobInfo();
        XxlJobGroup group = xxlJobApiService.getOrCreateGroup(job.getApp().getName());
        xxlJobInfo.setJobGroup(group.getId());
        return xxlJobInfo;
    }

    /**
     * 删除任务
     *
     * @param jobId 任务id
     */
    public void deleteJob(int jobId) {
        xxlJobApiService.removeJob(jobId);
    }

    /**
     * 获取下次执行时间（后5次）
     *
     * @param xxlJobId 任务id
     * @return {@link List }<{@link String }>
     */
    public List<String> getNextExecuteTime(Integer xxlJobId) {
        XxlJobInfo xxlJobInfo = xxlJobApiService.getJobInfo(xxlJobId);
        AssertUtils.notEmpty(xxlJobInfo, MSG_NO_XXL_JOB, xxlJobId);
        return xxlJobApiService.getNextExecuteTime(xxlJobInfo.getScheduleType(), xxlJobInfo.getScheduleConf());
    }
}
