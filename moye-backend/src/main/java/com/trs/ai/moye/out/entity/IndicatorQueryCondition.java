package com.trs.ai.moye.out.entity;

import com.trs.moye.base.data.service.enums.EvaluateOperator;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 指示器查询条件
 *
 * <AUTHOR>
 * @since 2025/05/30 15:37:40
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class IndicatorQueryCondition {

    /**
     * 字段
     */
    private String field;

    /**
     * 操作符
     */
    private EvaluateOperator operator;

    /**
     * 值
     */
    private List<String> values;


}
