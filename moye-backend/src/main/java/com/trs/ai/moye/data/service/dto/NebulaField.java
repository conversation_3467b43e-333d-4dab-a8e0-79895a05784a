package com.trs.ai.moye.data.service.dto;

import com.trs.moye.base.data.model.entity.DataModelField;
import com.trs.moye.base.data.model.entity.DataModelFieldAttributes;
import com.trs.moye.base.data.standard.entity.TagEdgeField;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;

/**
 * <AUTHOR>
 * @since 2024/12/20
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class NebulaField extends DataModelFieldAttributes<TagEdgeField> {

    private String zhName;
    private String enName;
    private String description;

    /**
     * 构造函数
     *
     * @param dataModelField 数据建模字段
     */
    public NebulaField(DataModelField dataModelField) {
        this.zhName = dataModelField.getZhName();
        this.enName = dataModelField.getEnName();
        this.description = dataModelField.getDescription();

        DataModelFieldAttributes<?> tagOrEdge = dataModelField.getFields();
        if (tagOrEdge != null) {
            this.setPrimaryKey(tagOrEdge.getPrimaryKey());

            // 使用类型安全的方法获取TagEdgeField类型的字段
            List<TagEdgeField> tagEdgeFields = tagOrEdge.getTagEdgeFields();
            if (CollectionUtils.isNotEmpty(tagEdgeFields)) {
                this.setFields(tagEdgeFields);
            }
        }
    }
}
