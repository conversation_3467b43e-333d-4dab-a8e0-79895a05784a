package com.trs.ai.moye.bi.service;

import com.trs.ai.moye.bi.constant.enums.ChartTypeEnum;
import com.trs.ai.moye.bi.domain.request.ChartRequest;
import com.trs.ai.moye.bi.domain.request.ChartSearchRequest;
import com.trs.ai.moye.bi.domain.response.ChartResponse;
import com.trs.ai.moye.bi.domain.response.result.ResultResponse;

/**
 * 可视化分析-图表
 *
 * <AUTHOR>
 * @since 2024/12/11 15:18
 */
public interface ChartService {

    /**
     * 添加图表
     *
     * @param request 请求参数
     */
    void addChart(ChartRequest request);

    /**
     * 修改图表
     *
     * @param id      图表id
     * @param request 请求参数
     */
    void modifyChart(Integer id, ChartRequest request);

    /**
     * 回显图表配置
     *
     * @param id id
     * @return {@link ChartResponse}
     */
    ChartResponse getChartSchema(Integer id);

    /**
     * 删除图表
     *
     * @param id id
     */
    void deleteChart(Integer id);

    /**
     * 预览数据
     *
     * @param type         图表类型
     * @param chartRequest 图表配置
     * @return 查询结果
     */
    ResultResponse previewChart(ChartTypeEnum type, ChartSearchRequest chartRequest);

    /**
     * 复制图表
     *
     * @param id              图表id
     * @param targetSubjectId 主题id
     */
    void copyChart(Integer id, Integer targetSubjectId);

    /**
     * 移动图表
     *
     * @param id              图表id
     * @param targetSubjectId 主题id
     */
    void moveChart(Integer id, Integer targetSubjectId);
}
