package com.trs.ai.moye.data.service.response;

import com.trs.moye.base.common.response.IdNameResponse;
import com.trs.moye.base.data.connection.entity.DataConnection;
import com.trs.moye.base.data.model.entity.BusinessCategory;
import com.trs.moye.base.data.model.entity.DataModel;
import com.trs.moye.base.data.storage.DataStorage;
import com.trs.moye.base.common.enums.ModelLayer;
import com.trs.ai.moye.data.service.dto.DataServiceDto;
import com.trs.ai.moye.data.service.entity.DataService;
import com.trs.ai.moye.data.service.entity.DataServiceConfig;
import com.trs.ai.moye.permission.service.DynamicUserNameService;
import java.util.Objects;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.function.ToIntFunction;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;


/**
 * 数据服务请求
 *
 * <AUTHOR>
 * @since 2024/09/25 17:23:51
 */
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@Data
@AllArgsConstructor
public class DataServiceResponse extends DataService {

    /**
     * 创建人名称
     */
    private String createByName;

    /**
     * 更新人名称
     */
    private String updateByName;

    /**
     * 数据服务配置
     */
    private DataServiceConfig dataServiceConfig;

    /**
     * 数据型商分类
     */
    private IdNameResponse dataModelBusinessCategory;

    /**
     * 分层
     */
    private IdNameResponse layer;

    /**
     * 数据模型
     */
    private IdNameResponse dataModel;

    /**
     * 存储
     */
    private IdNameResponse storage;

    /**
     * 连接
     */
    private DataConnection connection;

    /**
     * 数据服务响应
     *
     * @param dataServiceDto         数据服务 DTO
     * @param dynamicUserNameService 动态用户名服务
     * <AUTHOR>
     * @since 2024/09/27 18:07:51
     */
    public DataServiceResponse(DataServiceDto dataServiceDto, DynamicUserNameService dynamicUserNameService) {
        super(dataServiceDto);
        this.setCreateByName(dynamicUserNameService.getUserName(dataServiceDto.getCreateBy()));
        this.setUpdateByName(dynamicUserNameService.getUserName(dataServiceDto.getUpdateBy()));
        this.setDataServiceConfig(dataServiceDto.getDataServiceConfig());
        setIdNameResponseIfPresent(dataServiceDto.getDataModelCategory(),
            BusinessCategory::getId,
            BusinessCategory::getZhName,
            this::setDataModelBusinessCategory);
        setIdNameResponseIfPresent(dataServiceDto.getDataModel(),
            DataModel::getId,
            DataModel::getZhName,
            this::setDataModel);
        ModelLayer modelLayer = null;
        if (Objects.nonNull(dataServiceDto.getDataModel())) {
            modelLayer = dataServiceDto.getDataModel().getLayer();
        }
        setIdNameResponseIfPresent(modelLayer,
            ModelLayer::getId,
            ModelLayer::getLabel,
            this::setLayer);
        setIdNameResponseIfPresent(dataServiceDto.getDataStorage(),
            DataStorage::getId,
            DataStorage::getEnName,
            this::setStorage);
        this.setConnection(dataServiceDto.getConnection());
    }

    /**
     * 设置id名字响应如果存在
     *
     * @param identifiable 识别
     * @param getId        获取id
     * @param getZhName    获取 zh 名称
     * @param setter       setter
     * @param <T>          目标类型
     * <AUTHOR>
     * @since 2024/09/27 18:08:00
     */
    private <T> void setIdNameResponseIfPresent(T identifiable,
        ToIntFunction<T> getId,
        Function<T, String> getZhName,
        Consumer<IdNameResponse> setter) {
        if (Objects.nonNull(identifiable)) {
            Integer id = getId.applyAsInt(identifiable);
            String zhName = getZhName.apply(identifiable);
            if (StringUtils.isNotEmpty(zhName)) {
                setter.accept(new IdNameResponse(id, zhName));
            }
        }
    }
}
