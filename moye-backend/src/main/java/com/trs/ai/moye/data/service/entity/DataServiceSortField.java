package com.trs.ai.moye.data.service.entity;

import com.trs.ai.moye.common.enums.SortOrder;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 数据服务排序字段
 *
 * <AUTHOR>
 * @since 2021/12/8 11:13
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
public class DataServiceSortField extends DataServiceField {

    /**
     * 排序
     */
    private SortOrder order;

    public DataServiceSortField(DataServiceField field, SortOrder order) {
        super(field.getId(), field.getZhName(), field.getEnName(), field.getTypeName(), field.getType(),
            field.getIsStandard(), field.getMultiValue(), field.getAdvanceConfig());
        this.order = order;
    }
}
