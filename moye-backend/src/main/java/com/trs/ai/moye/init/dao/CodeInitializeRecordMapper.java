package com.trs.ai.moye.init.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.trs.ai.moye.init.entity.CodeInitializeRecord;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * CodeInitializeRecord数据访问层
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025/03/18 15:19
 */
@Mapper
public interface CodeInitializeRecordMapper extends BaseMapper<CodeInitializeRecord> {

    /**
     * 单条查询：通过name查询
     *
     * @param name 名称
     * @return 初始化记录
     */
    CodeInitializeRecord getByName(@Param("name") String name);

    /**
     * 单条查询：通过name查询
     *
     * @return 全部初始化记录
     */
    List<CodeInitializeRecord> selectAll();
}