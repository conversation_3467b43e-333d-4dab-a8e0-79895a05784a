package com.trs.ai.moye.data.model.controller;

import com.trs.ai.moye.common.utils.BeanUtil;
import com.trs.ai.moye.data.model.request.BusinessCategoryRequest;
import com.trs.ai.moye.data.model.request.CategoryOrderSaveRequest;
import com.trs.ai.moye.data.model.request.CategoryTreeRequest;
import com.trs.ai.moye.data.model.response.BusinessCategoryResponse;
import com.trs.ai.moye.data.model.response.CategoryTreeResponse;
import com.trs.ai.moye.data.model.response.DataSourceConnectionResponse;
import com.trs.ai.moye.data.model.service.DataModelCategoryService;
import com.trs.ai.moye.data.standard.dao.DataStandardFieldMapper;
import com.trs.ai.moye.permission.service.DynamicUserNameService;
import com.trs.moye.base.common.annotaion.OperateLogSign;
import com.trs.moye.base.common.entity.ModuleEnum;
import com.trs.moye.base.common.exception.BizException;
import com.trs.moye.base.common.response.IdNameResponse;
import com.trs.moye.base.data.model.dao.BusinessCategoryMapper;
import com.trs.moye.base.data.model.dao.DataModelMapper;
import com.trs.moye.base.data.model.entity.DataModel;
import java.util.List;
import java.util.Objects;
import javax.annotation.Resource;
import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * 数据建模目录相关接口
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024/10/12 10:27
 **/
@Slf4j
@RestController
@RequestMapping("/data-model")
@Validated
public class DataModelCategoryController {

    @Resource
    private BusinessCategoryMapper businessCategoryMapper;

    @Resource
    private DataModelCategoryService dataModelCategoryService;

    @Resource
    private DataModelMapper dataModelMapper;

    @Resource
    private DataStandardFieldMapper dataStandardFieldMapper;


    /**
     * 新增业务分类 分类目录  <a href="http://192.168.210.40:3001/project/5419/interface/api/164265">...</a>
     *
     * @param request 前端请求
     * @return {@link Boolean}
     * <AUTHOR>
     * @since 2024/9/26 14:29
     */
    @OperateLogSign(module = ModuleEnum.DATA_MODELING, apiName = "新增业务分类")
    @PostMapping("/category")
    public Boolean addCategory(@Valid @RequestBody BusinessCategoryRequest request) throws BizException {
        return dataModelCategoryService.addCategory(request);
    }


    /**
     * 更新层级 <a href="http://192.168.210.40:3001/project/5419/interface/api/164040">...</a>
     *
     * @param request 前端请求
     * @param id      分类id
     * @return {@link Boolean}
     * <AUTHOR>
     * @since 2024/9/26 14:44
     */
    @OperateLogSign(module = ModuleEnum.DATA_MODELING, apiName = "业务分类更新层级")
    @PutMapping("/category/{id}")
    public Boolean updateCategory(@Valid @RequestBody BusinessCategoryRequest request, @PathVariable Integer id) {
        return dataModelCategoryService.updateCategory(request, id);
    }


    /**
     * 获取层级详细信息  <a href="http://192.168.210.40:3001/project/5419/interface/api/164045">...</a>
     *
     * @param id 分类层级ID
     * @return {@link BusinessCategoryResponse}
     * <AUTHOR>
     * @since 2024/9/26 14:46
     */
    @GetMapping("/category/{id}")
    public BusinessCategoryResponse getCategoryDetails(@PathVariable @NotNull Integer id) {
        DynamicUserNameService dynamicUserNameService = BeanUtil.getBean(DynamicUserNameService.class);
        return new BusinessCategoryResponse(businessCategoryMapper.selectById(id), dynamicUserNameService);
    }


    /**
     * 层级目录删除  <a href="http://192.168.210.40:3001/project/5419/interface/api/164050">...</a>
     *
     * @param id 分类层级ID
     * @return {@link Boolean}
     * <AUTHOR>
     * @since 2024/9/26 16:08
     */
    @OperateLogSign(module = ModuleEnum.DATA_MODELING, apiName = "删除业务分类")
    @DeleteMapping("/category/{id}")
    public Boolean deleteCategory(@PathVariable @NotNull Integer id) {
        List<DataModel> dataModels = dataModelMapper.selectByCategoryId(id);
        if (Objects.nonNull(dataModels) && !dataModels.isEmpty()) {
            throw new BizException("该业务分类在维度建模中使用,无法删除!");
        }
        //由于字段标准也是使用的该目录，删除的时候同时需要删除标准字段
        dataStandardFieldMapper.deleteByCategoryId(id);
        return businessCategoryMapper.deleteById(id) > 0;
    }


    /**
     * 校验中文名是否存在 <a href="http://192.168.210.40:3001/project/5419/interface/api/164570">...</a>
     *
     * @param zhName 中文名称
     * @return {@link Boolean} true 存在 false 不存在
     * <AUTHOR>
     * @since 2024/9/26 15:45
     */
    @GetMapping("/category/check-zh-name")
    public Boolean checkCategoryZhName(@RequestParam @NotBlank(message = "中文名称不能为空") String zhName) {
        return businessCategoryMapper.existByZhName(zhName);
    }


    /**
     * 检验英文名是否存在 <a href="http://192.168.210.40:3001/project/5419/interface/api/164575">...</a>
     *
     * @param enName 英文名称
     * @return {@link Boolean} true 存在 false 不存在
     * <AUTHOR>
     * @since 2024/9/26 15:46
     */
    @GetMapping("/category/check-en-name")
    public Boolean checkCategoryEnName(@RequestParam @NotBlank(message = "中文名称不能为空") String enName) {
        return businessCategoryMapper.existByEnName(enName);
    }


    /**
     * 查询业务层级列表  <a href="http://192.168.210.40:3001/project/5419/interface/api/164175">...</a>
     *
     * @return {@link IdNameResponse}
     * <AUTHOR>
     * @since 2024/9/29 14:42
     */
    @GetMapping("/category/list")
    public List<IdNameResponse> getCategoryList() {
        return businessCategoryMapper.selectList(null).stream()
            .map(item -> new IdNameResponse(item.getId(), item.getZhName())).toList();
    }


    /**
     * 获取层级目录树 <a href="http://192.168.210.40:3001/project/5419/interface/api/164005">...</a>
     *
     * @param request 请求参数
     * @return {@link CategoryTreeResponse}
     * <AUTHOR>
     * @since 2024/9/26 16:56
     */
    @PostMapping("/category/tree")
    public List<CategoryTreeResponse> getCategoryTree(@RequestBody CategoryTreeRequest request) {
        return dataModelCategoryService.getCategoryTree(request);
    }


    /**
     * 获取目录锅炉条件连接树 <a href="http://192.168.210.40:3001/project/5419/interface/api/165025">...</a>
     *
     * @return {@link DataSourceConnectionResponse}
     * <AUTHOR>
     * @since 2024/10/18 10:37
     */
    @GetMapping("/category/data-source-connection/tree")
    public List<DataSourceConnectionResponse> getDataSourceConnectionTree() {
        return dataModelCategoryService.getDataSourceConnectionTree();
    }

    /**
     * 保存分类次序配置
     *
     * @param request 请求
     * <AUTHOR>
     * @since 2025/06/19 16:39:16
     */
    @PostMapping("/category/order-config")
    public void getCategoryListByType(@RequestBody @Valid CategoryOrderSaveRequest request) {
        dataModelCategoryService.saveCategoryOrderConfig(request);
    }

}
