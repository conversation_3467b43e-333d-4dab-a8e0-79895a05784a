package com.trs.ai.moye.bi.domain.response;

import com.trs.ai.moye.bi.domain.entity.VisualAnalysisCategory;
import com.trs.ai.moye.common.constants.TimeFormat;
import com.trs.ai.moye.common.utils.BeanUtil;
import com.trs.ai.moye.permission.service.DynamicUserNameService;
import lombok.Data;

/**
 * 主题请求参数
 *
 * <AUTHOR>
 * @since 2024/12/11 15:45
 */
@Data
public class CategoryResponse {

    /**
     * 创建人
     */
    protected String createBy;
    /**
     * 最近更新人
     */
    protected String updateBy;
    /**
     * 创建时间
     */
    protected String createTime;
    /**
     * 最近更新时间
     */
    protected String updateTime;
    /**
     * 名称
     */
    private String name;
    /**
     * 详情
     */
    private String description;


    public CategoryResponse(VisualAnalysisCategory category) {
        this.name = category.getName();
        this.description = category.getDescription();
        DynamicUserNameService userNameService = BeanUtil.getBean(DynamicUserNameService.class);
        this.createBy = userNameService.getUserName(category.getCreateBy());
        this.updateBy = userNameService.getUserName(category.getUpdateBy());
        this.createTime = category.getCreateTime().format(TimeFormat.DEFAULT_FORMATTER);
        this.updateTime = category.getUpdateTime().format(TimeFormat.DEFAULT_FORMATTER);
    }
}
