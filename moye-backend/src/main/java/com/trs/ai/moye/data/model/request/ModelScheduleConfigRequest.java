package com.trs.ai.moye.data.model.request;

import com.trs.moye.base.data.execute.ExecuteModeEnum;
import com.trs.moye.base.common.enums.TimeUnit;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * 调度配置
 *
 * <AUTHOR>
 * @since 2024-09-27 16:56
 */
@Data
public class ModelScheduleConfigRequest {

    /**
     * 执行方式
     */
    private ExecuteModeEnum executeMode;

    /**
     * 执行一次的时间点：
     */
    private LocalDateTime executeOnceTimePoint;

    /**
     * 周期单位：年月日时
     */
    private TimeUnit periodUnit;

    /**
     * 日期：
     */
    private String datePoint;

    /**
     * 时间：时间
     */
    private String timePoint;

    /**
     * 固定时间间隔值
     */
    private Integer durationValue;

    /**
     * 固定时间间隔单位: 年月日是分秒
     */
    private TimeUnit durationUnit;
}
