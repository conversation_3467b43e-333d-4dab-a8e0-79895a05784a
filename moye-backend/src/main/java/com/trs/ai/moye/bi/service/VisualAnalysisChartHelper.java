package com.trs.ai.moye.bi.service;

import com.trs.ai.moye.bi.dao.VisualAnalysisChartMapper;
import com.trs.ai.moye.bi.dao.VisualAnalysisSubjectMapper;
import com.trs.ai.moye.bi.domain.entity.VisualAnalysisChart;
import com.trs.ai.moye.bi.domain.entity.VisualAnalysisSubject;
import com.trs.ai.moye.common.entity.UsageInfoResponse.UsingObjects;
import com.trs.ai.moye.data.model.response.KeyValueResponse;
import com.trs.moye.base.common.entity.AuditBaseEntity;
import com.trs.moye.base.common.entity.ModuleEnum;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Component;

/**
 * 该类的方法不写在service类的原因是避免service类相关引用产生循环依赖
 *
 * <AUTHOR>
 * @since 2025-06-27 17:52
 */
@Component
public class VisualAnalysisChartHelper {

    @Resource
    private VisualAnalysisChartMapper visualAnalysisChartMapper;
    @Resource
    private VisualAnalysisSubjectMapper visualAnalysisSubjectMapper;

    /**
     * 获取存储点使用的图表
     *
     * @param storagePointId 存储点ID
     * @return 图表信息
     */
    public UsingObjects getStoragePointUsingCharts(Integer storagePointId) {
        List<VisualAnalysisChart> charts = visualAnalysisChartMapper.selectByStoragePointId(
            storagePointId);
        UsingObjects usingObjects = new UsingObjects();
        usingObjects.setType(ModuleEnum.VISUAL_ANALYSIS);
        if (ObjectUtils.isEmpty(charts)) {
            usingObjects.setObjects(new ArrayList<>());
            return usingObjects;
        }
        Map<Integer, String> subjectNameMap = visualAnalysisSubjectMapper.selectBatchIds(
                charts.stream().map(VisualAnalysisChart::getSubjectId).collect(
                    Collectors.toSet())).stream()
            .collect(Collectors.toMap(AuditBaseEntity::getId, VisualAnalysisSubject::getName));
        usingObjects.setObjects(charts.stream().map(
            chart -> new KeyValueResponse(chart.getSubjectId(),
                "主题:" + subjectNameMap.getOrDefault(chart.getSubjectId(), "" + chart.getSubjectId()) + "," + "图表:"
                    + chart.getChartTitle())).toList());
        return usingObjects;
    }
}
