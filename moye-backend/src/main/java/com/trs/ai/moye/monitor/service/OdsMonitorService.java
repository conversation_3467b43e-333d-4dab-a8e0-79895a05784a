package com.trs.ai.moye.monitor.service;

import com.trs.moye.base.common.response.PageResponse;
import com.trs.ai.moye.monitor.request.DataModelMonitorRequest;
import com.trs.ai.moye.monitor.request.MonitorEventPeriodRequest;
import com.trs.ai.moye.monitor.response.HomePageResponse;
import com.trs.ai.moye.monitor.response.MonitorDetailTableResponse;
import com.trs.ai.moye.monitor.response.MonitorEventPeriodResponse;
import com.trs.ai.moye.monitor.response.MonitorTrendDetailResponse;
import com.trs.ai.moye.monitor.response.MonitorTrendResponse;
import com.trs.ai.moye.monitor.response.OdsMonitorConfigVersionResponse;
import com.trs.ai.moye.monitor.response.OdsMonitorStatisticsResponse;
import com.trs.moye.base.common.enums.ModelLayer;
import com.trs.moye.base.monitor.enums.MonitorConfigType;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/10/24
 **/
public interface OdsMonitorService {

    /**
     * ods监控首页，展示异常最多的六条数据
     *
     * @param monitorType 监控类型
     * @param request     请求参数
     * @param layer       层级
     * @return 首页数据
     */
    HomePageResponse odsMonitorHomePage(MonitorConfigType monitorType,
        DataModelMonitorRequest request, ModelLayer layer);

    /**
     * ods监控统计列表
     *
     * @param monitorType 监控类型
     * @param request     请求参数
     * @param layer       层级
     * @return 统计信息
     */
    PageResponse<OdsMonitorStatisticsResponse> odsMonitorStatisticsList(MonitorConfigType monitorType,
        DataModelMonitorRequest request, ModelLayer layer);

    /**
     * ods监控详情列表
     *
     * @param monitorType 监控类型
     * @param dataModelId 元数据id
     * @param request     请求对象
     * @return 分页数据
     */
    PageResponse<MonitorDetailTableResponse> odsMonitorDetailTable(MonitorConfigType monitorType,
        Integer dataModelId, DataModelMonitorRequest request);

    /**
     * 积压/断流 监控详情——周期列表
     *
     * @param monitorType 监控类型
     * @param dataModelId 元数据id
     * @param request     请求对象，分页参数
     * @return 分页数据
     */
    PageResponse<MonitorEventPeriodResponse> odsMonitorDetailPeriods(MonitorConfigType monitorType, Integer dataModelId,
        MonitorEventPeriodRequest request);

    /**
     * 获取元数据监控配置版本列表
     *
     * @param monitorType 监控配置类型
     * @param metaDataId  元数据id
     * @return OdsMonitorConfigVersionVO
     */
    List<OdsMonitorConfigVersionResponse> odsMonitorDetailVersions(MonitorConfigType monitorType,
        Integer metaDataId);

    /**
     * 积压数据波动趋势图
     *
     * @param type       监控类型
     * @param meteDataId 元数据id
     * @param configId   版本号
     * @param count      显示的条数
     * @return 波动值列表
     */
    List<MonitorTrendResponse> odsMonitorDetailTrend(MonitorConfigType type, Integer meteDataId, Integer configId,
        Integer count);

    /**
     * o
     *
     * @param dataModelId 元数据id
     * @param request     请求参数
     * @return 积压周期数据
     */
    MonitorTrendDetailResponse dataModelMonitorPeriod(Integer dataModelId, DataModelMonitorRequest request);
}
