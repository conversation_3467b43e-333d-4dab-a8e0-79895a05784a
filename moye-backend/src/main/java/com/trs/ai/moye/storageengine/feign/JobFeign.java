package com.trs.ai.moye.storageengine.feign;

import com.trs.ai.moye.common.config.OpenFeignConfig;
import com.trs.ai.moye.data.model.entity.StorageTask;
import com.trs.ai.moye.storageengine.request.SubmitFileJobRequest;
import com.trs.ai.moye.storageengine.request.SubmitJobRequest;
import com.trs.ai.moye.storageengine.response.RestfulResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * 存储引擎任务feign
 */
@FeignClient(name = "moye-storage-engine", path = "/storage/job", contextId = "job", configuration = OpenFeignConfig.class)
public interface JobFeign {

    /**
     * 提交任务
     *
     * @param taskStartParam 任务参数
     * @return 提交结果
     */
    @PostMapping("/submit")
    RestfulResponse submitJob(@Validated @RequestBody SubmitJobRequest taskStartParam);


    /**
     * 提交文件任务
     *
     * @param taskStartParam 任务参数
     * @return 提交结果
     */
    @PostMapping("/file/submit")
    RestfulResponse submitFileJob(@Validated @RequestBody SubmitFileJobRequest taskStartParam);

    /**
     * 停止任务
     *
     * @param dataModelId 任务ID
     */
    @GetMapping("/stop")
    void stopJob(@RequestParam("dataModelId") Integer dataModelId);

    /**
     * 通过建模id创建topic并返回topicName
     *
     * @param dataModelId 建模id
     * @return topicName
     */
    @GetMapping("/{dataModelId}/create-storage-topic")
    String createStorageOperatorTopic(@PathVariable("dataModelId") Integer dataModelId);

    /**
     * 更新seatunnel的job状态到clickhouse
     *
     * @param storageJobId 任务id
     * @return 任务状态
     */
    @GetMapping("/update-job-status")
    StorageTask updateJobStatus(@RequestParam("storageJobId") String storageJobId);

    /**
     * 停止单个任务
     *
     * @param storageJobId 任务Id
     */
    @GetMapping("/stop/single")
    void stopJobSingle(@RequestParam("storageJobId") String storageJobId);
}
