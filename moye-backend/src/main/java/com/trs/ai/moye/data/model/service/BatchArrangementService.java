package com.trs.ai.moye.data.model.service;

import com.trs.ai.moye.batchengine.entity.ExecuteResultMap;
import com.trs.ai.moye.batchengine.entity.ExecuteResultRequest;
import com.trs.ai.moye.common.entity.UsageInfoResponse.UsingObjects;
import com.trs.moye.base.data.model.entity.BatchProcessSparkConfig;
import java.util.List;

/**
 * 批处理算子编排service
 *
 * <AUTHOR>
 * @since 2024/10/21 15:15
 */
public interface BatchArrangementService {

    /**
     * 删除批处理编排
     *
     * @param dataModelId 建模id
     */
    void deleteBatchArrange(Integer dataModelId);

    /**
     * 执行dag模式任务
     *
     * @param dataModelId 数据建模ID
     * @param request     前端请求参数
     * @return {@link Boolean}
     */
    Boolean execute(Integer dataModelId, BatchProcessSparkConfig request);

    /**
     * 查询使用指定存储的批处理任务
     *
     * @param storageId 存储id
     * @return 使用信息
     */
    UsingObjects getStorageUsage(Integer storageId);

    /**
     * 获取执行结果
     *
     * @param executeResultRequest 请求参数
     * @return 执行展示结果
     */
    List<ExecuteResultMap> executeResult(ExecuteResultRequest executeResultRequest);

}
