package com.trs.ai.moye.data.operator.service.impl;

import com.fasterxml.jackson.core.type.TypeReference;
import com.trs.ai.moye.common.http.HttpClient;
import com.trs.ai.moye.common.http.HttpRequestEntity;
import com.trs.ai.moye.common.http.HttpResult;
import com.trs.ai.moye.data.ability.dao.AbilityMapper;
import com.trs.ai.moye.data.ability.response.AbilityCenterAbilityResponse;
import com.trs.ai.moye.data.ability.response.AbilityPageResponse;
import com.trs.ai.moye.data.ability.response.AbilityResponse;
import com.trs.ai.moye.data.operator.constants.enums.AbilityDataTypeEnum;
import com.trs.ai.moye.data.operator.constants.enums.OperatorUpdateStatus;
import com.trs.ai.moye.data.operator.constants.enums.RequestProtocolEnum;
import com.trs.ai.moye.data.operator.domain.request.AbilityListRequest;
import com.trs.ai.moye.data.operator.domain.request.AbilityVersionDiffRequest;
import com.trs.ai.moye.data.operator.domain.response.AbilityParamUpdateResponse;
import com.trs.ai.moye.data.operator.domain.response.AbilityVersionDiffItemResponse;
import com.trs.ai.moye.data.operator.domain.response.AbilityVersionDiffResponse;
import com.trs.ai.moye.data.operator.domain.response.AppInfoDTO;
import com.trs.ai.moye.data.operator.domain.response.CenterAppResponse;
import com.trs.ai.moye.data.operator.domain.response.VersionCheckResponse;
import com.trs.ai.moye.data.operator.domain.vo.AbilityDTO;
import com.trs.ai.moye.data.operator.domain.vo.AbilityVersionDTO;
import com.trs.ai.moye.data.operator.domain.vo.OperatorParamConfigDTO;
import com.trs.ai.moye.data.operator.domain.vo.OperatorParamDetail;
import com.trs.ai.moye.data.operator.service.OperatorAbilityCenterService;
import com.trs.ai.moye.data.service.config.AbilityCenterProperties;
import com.trs.moye.ability.entity.Ability;
import com.trs.moye.ability.entity.operator.AbilityCenterCallParams;
import com.trs.moye.ability.enums.AbilityType;
import com.trs.moye.base.common.enums.AbstractType;
import com.trs.moye.base.common.exception.BizException;
import com.trs.moye.base.common.exception.RemoteException;
import com.trs.moye.base.common.request.PageParams;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

/**
 * 能力中心service实现
 *
 * <AUTHOR>
 * @since 2024/9/29 14:40
 */
@Service
@Slf4j
public class OperatorAbilityCenterServiceImpl implements OperatorAbilityCenterService {

    @Resource
    private HttpClient httpClient;

    @Resource
    private AbilityCenterProperties properties;

    @Resource
    private AbilityMapper abilityMapper;

    private static final String DATA_TYPE = "dataType";


    /**
     * 根据能力英文名获取能力中心参数
     *
     * @param enName 英文名
     * @return 能力中心参数
     */
    @Override
    public AbilityCenterCallParams getAbilityParamsByEnName(String enName) {
        return getAllAbilityApiList().stream()
            .filter(abilityDTO -> StringUtils.equals(abilityDTO.getPowerEnName(), enName))
            .findFirst()
            .map(abilityDTO -> new AbilityCenterAbilityResponse(abilityDTO).getAbilityCenterParams())
            .orElse(null);
    }


    @Override
    public AbilityPageResponse getUnAccessAbilityCenterPage(AbilityListRequest pageRequest) {
        List<Integer> accessedAbilityIds = abilityMapper.getAccessedAbilityIds().stream()
            .filter(Objects::nonNull).collect(Collectors.toCollection(ArrayList::new));
        accessedAbilityIds.remove(pageRequest.getAbilityId());

        List<AbilityDTO> allAbilities = getAllAbilityApiList();
        List<AbilityCenterAbilityResponse> abilityList = allAbilities.stream()
            .filter(abilityDTO -> abilityListFilter(abilityDTO, pageRequest, accessedAbilityIds))
            .map(AbilityCenterAbilityResponse::new)
            .toList();
        PageParams pageParams = pageRequest.getPageParams();
        return new AbilityPageResponse(abilityList, pageParams, allAbilities.size(), accessedAbilityIds.size());
    }

    private boolean abilityListFilter(AbilityDTO dto, AbilityListRequest request, List<Integer> accessedAbilityIds) {
        boolean unAccessed = !accessedAbilityIds.contains(dto.getPowerId());
        boolean appFilter = request.getAppId() == null || request.getAppId().equals(dto.getAppId());
        boolean nameFilter = StringUtils.isBlank(request.getAbilityName())
            || (StringUtils.containsIgnoreCase(dto.getPowerName(), request.getAbilityName())
            || StringUtils.containsIgnoreCase(dto.getPowerEnName(), request.getAbilityName()));
        return unAccessed && appFilter && nameFilter;
    }

    @Override
    public List<AbilityDTO> getAllAbilityApiList() throws BizException {
        log.info("调用能力中心接口-获取能力列表，请求路径：{}", properties.getPowerListPath());
        HttpRequestEntity requestEntity = HttpRequestEntity
            .post(properties.getBaseUrl() + properties.getPowerListPath())
            .body(Map.of())
            .headers(properties.getHeaders()).build();
        try {
            TypeReference<HttpResult<List<AbilityDTO>>> tr = new TypeReference<>() {
            };
            //过滤掉协议类型不为http或https的
            return httpClient.doRequest(requestEntity, tr).stream()
                .filter(dto -> RequestProtocolEnum.HTTP.equals(dto.getProtocolStringType())
                    || RequestProtocolEnum.HTTPS.equals(dto.getProtocolStringType()))
                .toList();
        } catch (RemoteException e) {
            throw new BizException("获取能力列表失败", e);
        }
    }

    @Override
    public List<CenterAppResponse> getCenterAllApps() {
        log.info("调用能力中心接口-获取所有应用，请求路径：{}", properties.getAppListPath());
        HttpRequestEntity requestEntity = HttpRequestEntity
            .get(properties.getBaseUrl() + properties.getAppListPath())
            .headers(properties.getHeaders()).build();
        try {
            TypeReference<HttpResult<List<AppInfoDTO>>> tr = new TypeReference<>() {
            };
            List<AppInfoDTO> httpResult = httpClient.doRequest(requestEntity, tr);
            return httpResult.stream().map(app -> new CenterAppResponse(app.getDataId(), app.getDesc())).toList();
        } catch (RemoteException e) {
            throw new BizException("获取所有应用列表失败", e);
        }
    }

    @Override
    public VersionCheckResponse checkOperatorVersion(Integer operatorId) {
        Ability operator = abilityMapper.selectById(operatorId);

        if (operator != null && operator.getType().equals(AbilityType.ABILITY_CENTER)) {
            AbilityCenterCallParams callParams = operator.getAbilityCenterParams();
            Integer abilityId = callParams.getAbilityId();
            //获取最新版本信息
            List<AbilityVersionDTO> versionInfo = getOperatorVersionInfo(abilityId);
            Optional<AbilityVersionDTO> latestVersionInfoOptional = versionInfo.stream()
                .filter(AbilityVersionDTO::isLatestVersion).findFirst();

            if (latestVersionInfoOptional.isPresent()) {
                AbilityVersionDTO latestVersionInfo = latestVersionInfoOptional.get();
                VersionCheckResponse check = new VersionCheckResponse(callParams.getVersion(), latestVersionInfo);
                check.setAbilityInfo(getByAbilityId(abilityId));
                if (check.isNeedUpdate()) {
                    abilityMapper.updateUpdateStatus(operatorId, OperatorUpdateStatus.NEED_UPDATE);
                }
                return check;
            }
        }
        return null;
    }

    private AbilityResponse getByAbilityId(Integer abilityId) {
        Optional<AbilityResponse> ability = getAllAbilityApiList().stream()
            .filter(abilityDTO -> abilityDTO.getPowerId().equals(abilityId))
            .map(AbilityResponse::new).findFirst();
        if (ability.isPresent()) {
            return ability.get();
        } else {
            throw new BizException("未在能力中心查询到id为[%d]的能力，请检查能力是否存在或是否已发布！", abilityId);
        }
    }

    /**
     * 获取能力中心-能力版本差异
     *
     * @param abilityId  能力id
     * @param nowVersion 当前版本
     * @param newVersion 最新版本
     * @return 能力中心-能力版本差异
     */
    private AbilityVersionDiffResponse getAbilityVersionDiff(Integer abilityId, String nowVersion, String newVersion) {
        log.info("调用能力中心接口-指定能力对比某两个版本差异，请求路径：{}", properties.getParametricVariationPath());
        AbilityVersionDiffRequest request = new AbilityVersionDiffRequest(abilityId, nowVersion, newVersion);
        HttpRequestEntity requestEntity = HttpRequestEntity
            .post(properties.getBaseUrl() + properties.getParametricVariationPath())
            .body(request)
            .headers(properties.getHeaders()).build();
        TypeReference<HttpResult<List<AbilityVersionDiffResponse>>> tr = new TypeReference<>() {
        };
        List<AbilityVersionDiffResponse> responses = httpClient.doRequest(requestEntity, tr);
        if (responses.size() != 1) {
            throw new BizException("调用能力中心接口-指定能力对比某两个版本差异返回出错！");
        }
        return httpClient.doRequest(requestEntity, tr).get(0);
    }

    private List<AbilityVersionDTO> getOperatorVersionInfo(Integer abilityId) throws BizException {
        log.info("调用能力中心接口-获取算子版本，请求路径：{}", properties.getPowerVersionPath());
        HttpRequestEntity requestEntity = HttpRequestEntity
            .post(properties.getBaseUrl() + properties.getPowerVersionPath())
            .body(Map.of("powerId", abilityId))
            .headers(properties.getHeaders()).build();
        try {
            TypeReference<HttpResult<List<AbilityVersionDTO>>> tr = new TypeReference<>() {
            };
            return httpClient.doRequest(requestEntity, tr);
        } catch (RemoteException e) {
            throw new BizException("获取算子版本失败", e);
        }
    }

    private List<AbilityParamUpdateResponse> convertToParameterUpdate(List<OperatorParamConfigDTO> input,
        AbilityVersionDiffResponse abilityVersionDiff) {
        List<AbilityParamUpdateResponse> parameterUpdateList = new ArrayList<>();

        abilityVersionDiff.getAddDifVersion().forEach(difVersion -> {
            if (Objects.isNull(difVersion.getReqBody())) {
                parameterUpdateList.add(new AbilityParamUpdateResponse("新增", difVersion.getParameterName()));
            }
        });

        abilityVersionDiff.getDeleteDifVersion().forEach(difVersion -> {
            if (Objects.isNull(difVersion.getReqBody())) {
                parameterUpdateList.add(new AbilityParamUpdateResponse("删除", difVersion.getParameterName()));
            }
        });

        for (AbilityVersionDiffItemResponse difVersion : abilityVersionDiff.getUpdateDifVersion()) {

            if (Objects.nonNull(difVersion.getDifMap()) && difVersion.getDifMap().containsKey(DATA_TYPE)) {
                String dataType = difVersion.getDifMap().get(DATA_TYPE);
                AbstractType oldType = findDataTypeByName(input, difVersion.getParameterName());
                AbstractType newType = AbilityDataTypeEnum.valueOfName(dataType).getFieldType();
                if (Objects.nonNull(oldType) && !oldType.equals(newType)) {
                    String parameterName = difVersion.getParameterName();
                    AbilityParamUpdateResponse parameterUpdate = new AbilityParamUpdateResponse("修改",
                        parameterName);
                    String message = String.format("类型：%s -> %s", oldType, newType);
                    parameterUpdate.setMessage(message);
                    parameterUpdateList.add(parameterUpdate);
                }
            }
        }
        return parameterUpdateList;
    }

    private AbstractType findDataTypeByName(List<OperatorParamConfigDTO> input, String dataName) {
        return input.stream().flatMap(paramConfig -> paramConfig.getParamDetail().stream())
            .filter(detail -> detail.getEnName().equals(dataName))
            .map(OperatorParamDetail::getFieldType)
            .findFirst().orElse(null);
    }
}
