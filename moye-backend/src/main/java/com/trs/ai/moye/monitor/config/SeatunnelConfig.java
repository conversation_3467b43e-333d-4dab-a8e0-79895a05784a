package com.trs.ai.moye.monitor.config;

import lombok.Getter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @since 2025/2/27
 */
@Component
@Getter
public class SeatunnelConfig {

    /**
     * seatunnel server url
     */
    @Value("${seatunnel.server.url}")
    private String seatunnelUrl;

    /**
     * seatunnel server workers
     */
    @Value("${seatunnel.server.workers}")
    private Integer seatunnelWorkers;

}
