package com.trs.ai.moye.data.model.entity;

import com.trs.moye.base.common.enums.ModelLayer;
import com.trs.moye.base.data.connection.enums.ConnectionType;
import com.trs.moye.base.data.execute.ExecuteModeEnum;
import com.trs.moye.base.data.model.enums.CreateModeEnum;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 贴源库数据建模
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024/10/11 18:03
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
public class SourceDataMode {

    /**
     * 建模ID
     */
    private Integer id;

    /**
     * 建模英文名
     */
    private String enName;

    /**
     * 建模中文名
     */
    private String zhName;

    /**
     * 描述
     */
    private String description;

    /**
     * 建模层级ID
     */
    private Integer categoryId;

    /**
     * 建模层级
     */
    private ModelLayer modelLayer;

    /**
     * 是否已治理
     */
    private Boolean isArranged;

    /**
     * 建模数据源类型
     */
    private ConnectionType connectionType;


    /**
     * 调度模式
     */
    private ExecuteModeEnum executeMode;

    /**
     * 是否设置增量字段
     */
    private Boolean hasIncrementField;

    /**
     * 建模类型
     */
    private CreateModeEnum dataModelType;

    /**
     * 连接ID
     */
    private Integer connectionId;

}
