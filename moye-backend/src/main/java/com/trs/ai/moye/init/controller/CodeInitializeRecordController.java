package com.trs.ai.moye.init.controller;

import com.trs.ai.moye.init.code.CodeInitializeExecutor;
import com.trs.ai.moye.init.dto.CodeInitializeInfoDto;
import com.trs.ai.moye.init.entity.CodeInitializeRecord;
import java.util.List;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * CodeInitializeRecord控制层
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025/03/18 15:19
 */
@RestController
@RequestMapping(value = "/code/initialize")
@Slf4j
public class CodeInitializeRecordController {

    @Resource
    private CodeInitializeExecutor initializeExecutor;

    /**
     * 执行初始化代码
     *
     * @param name 初始化代码名称
     * @return 初始化记录
     */
    @PostMapping(value = "/invoke")
    public CodeInitializeRecord invoke(@RequestParam String name) {
        return initializeExecutor.invokeCodeInitialize(name);
    }

    /**
     * 执行初始化代码
     */
    @PostMapping(value = "/invoke/all")
    public void invoke() {
        initializeExecutor.invokeCodeInitialize();
    }

    /**
     * 获取代码初始化信息列表
     *
     * @return 代码初始化信息列表
     */
    @GetMapping(value = "/list")
    public List<CodeInitializeInfoDto> getCodeInitializeInfoList() {
        return initializeExecutor.getCodeInitializeInfoList();
    }
}