package com.trs.ai.moye.data.model.response;


import com.trs.ai.moye.data.model.entity.TracerData;
import com.trs.moye.base.common.utils.DateTimeUtils;
import java.util.Objects;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * 进程信息 vo
 *
 * <AUTHOR>
 * @date 2024/07/30 15:29:45
 */
@Data
@ToString
@AllArgsConstructor
@NoArgsConstructor
public class ProcessInfoVO {

    /**
     * 算子名称
     */
    private String operatorName;
    /**
     * 算子ID
     */
    private Integer operatorId;
    /**
     * 数据源名称
     */
    private String dataSourceName;
    /**
     * 开始时间
     */
    private Long startTime;
    /**
     * 处理时长
     */
    private Long duration;
    /**
     * 是否异常
     */
    private Boolean isException;
    /**
     * 异常信息
     */
    private String exception;

    /**
     * 租户 ID
     */
    private Integer tenantId;

    /**
     * 进程信息 vo
     *
     * @param tracerData 示踪剂数据
     */
    public ProcessInfoVO(TracerData tracerData) {
        this.operatorName = tracerData.getProcessingName();
        this.operatorId = tracerData.getOperatorId();
        this.dataSourceName = tracerData.getDataSourceName();
        this.startTime = DateTimeUtils.getTimestampOfDateTime(tracerData.getStartTime());
        this.duration = tracerData.getProcessingTime();
        this.isException = isException(tracerData.getIsError());
        this.exception = tracerData.getErrorMsg();
        this.tenantId = tracerData.getTenantId();

    }

    /**
     * 是例外
     *
     * @param isError 是错误
     * @return boolean
     */
    private boolean isException(Integer isError) {
        return Objects.nonNull(isError) && isError == 1;
    }
}
