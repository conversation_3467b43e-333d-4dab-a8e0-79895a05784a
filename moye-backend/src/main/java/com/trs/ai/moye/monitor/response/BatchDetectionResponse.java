package com.trs.ai.moye.monitor.response;

import com.trs.ai.moye.monitor.entity.BasicComponentDetection;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * BatchDetectionResponse
 *
 * <AUTHOR>
 * @since 2025/7/17 15:12
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class BatchDetectionResponse {

    private List<BatchDetectionItem> items;


    /**
     * item
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class BatchDetectionItem {
        private String name;
        private Boolean status;
        private String errorMessage;


        /**
         * fromBasicComponentDetection
         *
         * @param basicComponentDetection basicComponentDetection
         * @return BatchDetectionItem
         */
        public static BatchDetectionItem fromBasicComponentDetection(BasicComponentDetection basicComponentDetection) {
            BatchDetectionItem item = new BatchDetectionItem();
            item.setName(basicComponentDetection.getComponentName());
            item.setStatus(basicComponentDetection.getStatus() != 0);
            item.setErrorMessage(basicComponentDetection.getErrorMessage());
            return item;
        }
    }
}
