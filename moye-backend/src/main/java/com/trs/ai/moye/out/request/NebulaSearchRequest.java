package com.trs.ai.moye.out.request;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import lombok.Data;


/**
 * Nebula搜索请求
 *
 * <AUTHOR>
 * @since 2024/12/23 09:58:00
 */
@Data
public class NebulaSearchRequest {

    /**
     * sql语句
     */
    @NotBlank(message = "语句不能为空")
    private String statement;

    /**
     * 数据建模id
     */
    @NotNull(message = "数据建模id不能为空")
    private Integer dataModelId;

    /**
     * 需要解析条件
     */
    private Boolean needParseCondition = false;

}
