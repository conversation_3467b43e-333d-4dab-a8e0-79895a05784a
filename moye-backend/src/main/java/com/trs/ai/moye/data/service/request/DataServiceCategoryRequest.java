package com.trs.ai.moye.data.service.request;

import com.trs.moye.base.common.request.BaseNameRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * 数据服务分类请求
 *
 * <AUTHOR>
 * @since 2024/09/24 14:53:26
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class DataServiceCategoryRequest extends BaseNameRequest {

    /**
     * 父ID
     */
    private Integer pid = 0;

    /**
     * 描述
     */
    private String description;
}
