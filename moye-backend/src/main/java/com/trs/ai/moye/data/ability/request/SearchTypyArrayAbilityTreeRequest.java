package com.trs.ai.moye.data.ability.request;

import com.trs.moye.ability.enums.AbilityType;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 扩展的能力树请求类，继承自 AbilityTreeRequest，用于处理特殊的 AbilityType 筛选逻辑。
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class SearchTypyArrayAbilityTreeRequest extends AbilityTreeRequest {
    private AbilityType[] extendedAbility;
    /**
     * 获取扩展后的 AbilityType 数组。
     * 当父类的 type 为 LOCAL 时，返回包含 LOCAL、NLP、BATCH 的数组；
     * 当父类的 type 为 null 时，返回 null；
     * 当父类的 type 为其他值时，返回只包含该值的数组。
     *
     * @return AbilityType 数组
     */
    public AbilityType[] getExtendedTypes() {
        if (getType() == AbilityType.LOCAL) {
            return new AbilityType[]{AbilityType.LOCAL, AbilityType.NLP, AbilityType.BATCH};
        }
        if (getType() == null){
            return null;
        }
        return new AbilityType[]{getType()};
    }
    public SearchTypyArrayAbilityTreeRequest() {
        super();
        this.extendedAbility = getExtendedTypes();
    }

    public SearchTypyArrayAbilityTreeRequest(AbilityTreeRequest request) {
        super();
        super.setType(request.getType());
        super.setUpdateStatus(request.getUpdateStatus());
        super.setTestStatus(request.getTestStatus());
        super.setIsBatchSupported(request.getIsBatchSupported());
        this.extendedAbility = getExtendedTypes();
    }
}