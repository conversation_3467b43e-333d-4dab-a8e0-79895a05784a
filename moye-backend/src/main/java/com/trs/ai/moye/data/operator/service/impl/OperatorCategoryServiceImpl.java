package com.trs.ai.moye.data.operator.service.impl;

import com.trs.ai.moye.common.enums.NameTypeEnum;
import com.trs.ai.moye.common.response.TreeAddResponse;
import com.trs.ai.moye.common.response.TreeBaseResponse;
import com.trs.ai.moye.data.ability.dao.AbilityMapper;
import com.trs.ai.moye.data.operator.constants.enums.OperatorTreeNodeTypeEnum;
import com.trs.ai.moye.data.operator.domain.request.OperatorCategoryRequest;
import com.trs.ai.moye.data.operator.domain.response.OperatorCategoryResponse;
import com.trs.ai.moye.data.operator.domain.response.OperatorTreeResponse;
import com.trs.ai.moye.data.operator.service.OperatorCategoryService;
import com.trs.moye.ability.enums.AbilityCategory;
import com.trs.moye.base.common.exception.BizException;
import com.trs.moye.base.data.model.dao.OperatorBusinessCategoryMapper;
import com.trs.moye.base.data.model.entity.OperatorBusinessCategory;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

/**
 * 算子库业务分类service实现
 *
 * <AUTHOR>
 */
@Service
public class OperatorCategoryServiceImpl implements OperatorCategoryService {

    @Resource
    private OperatorBusinessCategoryMapper operatorCategoryMapper;

    @Resource
    private AbilityMapper abilityMapper;

    /**
     * 默认顶级分类的父ID
     */
    private static final Integer ROOT_PID = 0;


    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public TreeAddResponse addOperatorCategory(OperatorCategoryRequest request) {
        if (operatorCategoryCheckName(NameTypeEnum.EN, request.getEnAbbr())) {
            throw new BizException("英文缩写【%s】已存在！", request.getEnAbbr());
        }
        if (operatorCategoryCheckName(NameTypeEnum.ZH, request.getZhName())) {
            throw new BizException("中文名【%s】已存在！", request.getZhName());
        }

        // 如果指定了父分类，检查父分类是否存在
        Integer pid = request.getPid();
        if (pid != null && !pid.equals(ROOT_PID) && !operatorCategoryMapper.existById(pid)) {
            throw new BizException("父分类不存在，id: %d", pid);
        }

        OperatorBusinessCategory category = new OperatorBusinessCategory();
        category.setEnAbbr(request.getEnAbbr());
        category.setZhName(request.getZhName());
        category.setDescription(request.getDescription());
        category.setPid(pid != null ? pid : ROOT_PID);
        operatorCategoryMapper.insert(category);
        return new TreeAddResponse(OperatorTreeNodeTypeEnum.OPERATOR_BUSINESS_CATEGORY, category.getId());
    }

    @Override
    public boolean operatorCategoryCheckName(NameTypeEnum type, String name) {
        if (NameTypeEnum.EN.equals(type)) {
            return operatorCategoryMapper.existByEnAbbr(name);
        } else {
            return operatorCategoryMapper.existByZhName(name);
        }
    }

    @Override
    public OperatorCategoryResponse getOperatorCategoryDetail(Integer id) {
        checkCategoryExist(id);
        OperatorBusinessCategory category = operatorCategoryMapper.selectById(id);
        OperatorCategoryResponse response = new OperatorCategoryResponse(category);

        // 判断分类是否可编辑（系统内置分类不可编辑）
        response.setIsEnableEdit(!isCategorySystemOrSystemChild(id));
        return response;
    }

    /**
     * 判断分类是否为系统内置分类或系统内置分类的子分类
     *
     * @param categoryId 分类ID
     * @return true:是系统分类或其子分类(不可编辑), false:非系统分类(可编辑)
     */
    private boolean isCategorySystemOrSystemChild(Integer categoryId) {
        // 先检查自身是否为系统内置分类
        if (AbilityCategory.isSystemCategory(categoryId)) {
            return true;
        }

        // 一次性获取所有分类
        List<OperatorBusinessCategory> allCategories = operatorCategoryMapper.selectList(null);
        Map<Integer, OperatorBusinessCategory> categoryMap = allCategories.stream()
            .collect(Collectors.toMap(OperatorBusinessCategory::getId, category -> category));

        // 在内存中查找父级链路
        Integer currentId = categoryId;
        while (!ROOT_PID.equals(currentId)) {
            OperatorBusinessCategory current = categoryMap.get(currentId);
            if (current == null) {
                break;
            }

            Integer parentId = current.getPid();
            if (AbilityCategory.isSystemCategory(parentId)) {
                return true;
            }
            currentId = parentId;
        }

        return false;
    }

    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public void updateOperatorCategory(Integer id, OperatorCategoryRequest request) {
        checkCategoryExist(id);

        // 如果要修改父分类，检查新父分类是否存在
        Integer pid = request.getPid();
        if (pid != null && !pid.equals(ROOT_PID) && !operatorCategoryMapper.existById(pid)) {
            throw new BizException("父分类不存在，id: %d", pid);
        }

        // 不能将分类的父ID设置为自己或其子分类
        if (pid != null && !pid.equals(ROOT_PID)) {
            if (pid.equals(id)) {
                throw new BizException("不能将分类的父ID设置为自己");
            }

            List<Integer> childrenIds = operatorCategoryMapper.selectChildrenIds(id);
            if (!CollectionUtils.isEmpty(childrenIds) && childrenIds.contains(pid)) {
                throw new BizException("不能将分类的父ID设置为其子分类");
            }
        }

        OperatorBusinessCategory category = new OperatorBusinessCategory();
        category.setEnAbbr(request.getEnAbbr());
        category.setZhName(request.getZhName());
        category.setDescription(request.getDescription());
        category.setPid(pid); // 更新父ID
        category.setId(id);
        operatorCategoryMapper.updateById(category);
    }

    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public void deleteOperatorCategory(Integer id) {
        checkCategoryExist(id);

        // 检查是否有子分类
        List<OperatorBusinessCategory> children = operatorCategoryMapper.selectByPid(id);
        if (!CollectionUtils.isEmpty(children)) {
            throw new BizException("分类下有子分类，无法删除！");
        }

        // 检查是否有算子关联
        if (!abilityMapper.selectByOperatorCategoryId(id).isEmpty()) {
            throw new BizException("算子分类下有算子，无法删除！");
        }

        operatorCategoryMapper.deleteById(id);
    }

    private void checkCategoryExist(Integer id) {
        if (!operatorCategoryMapper.existById(id)) {
            throw new BizException("id为%d的算子业务分类不存在！", id);
        }
    }

    @Override
    public List<TreeBaseResponse> operatorCategoryList() {
        return buildCategoryTree();
    }

    /**
     * 【算子分类】算子分类列表，包括不存在于数据库的内置分类
     *
     * @return 算子分类列表
     */
    @Override
    public List<TreeBaseResponse> getAllOperatorCategoryList() {
        List<TreeBaseResponse> operatorBusinessCategoryTree = new ArrayList<>();
        operatorBusinessCategoryTree.add(OperatorTreeResponse.buildDefaultBatchOperatorTreeResponse());
        operatorBusinessCategoryTree.addAll(buildCategoryTree());
        return operatorBusinessCategoryTree;
    }

    /**
     * 构建分类树结构
     *
     * @return 树形结构的分类列表
     */
    private List<TreeBaseResponse> buildCategoryTree() {
        List<OperatorBusinessCategory> allCategories = operatorCategoryMapper.selectList(null);

        // 将所有分类按照父ID分组，处理null值
        Map<Integer, List<OperatorBusinessCategory>> categoryMap = allCategories.stream()
            .map(category -> {
                // 如果pid为null，设置为ROOT_PID
                if (category.getPid() == null) {
                    category.setPid(ROOT_PID);
                }
                return category;
            })
            .collect(Collectors.groupingBy(OperatorBusinessCategory::getPid));

        // 获取顶级分类
        List<OperatorBusinessCategory> rootCategories = categoryMap.getOrDefault(ROOT_PID, new ArrayList<>());

        // 递归构建树
        return rootCategories.stream()
            .map(category -> buildCategoryTreeNode(category, categoryMap))
            .toList();
    }

    /**
     * 递归构建分类树节点
     *
     * @param category    当前分类
     * @param categoryMap 按父ID分组的分类Map
     * @return 树节点
     */
    private TreeBaseResponse buildCategoryTreeNode(OperatorBusinessCategory category,
        Map<Integer, List<OperatorBusinessCategory>> categoryMap) {
        OperatorTreeResponse treeNode = new OperatorTreeResponse(category);

        // 获取子分类
        List<OperatorBusinessCategory> children = categoryMap.getOrDefault(category.getId(), new ArrayList<>());
        if (!children.isEmpty()) {
            List<TreeBaseResponse> childNodes = children.stream()
                .map(child -> buildCategoryTreeNode(child, categoryMap))
                .sorted(Comparator.comparing(TreeBaseResponse::getName))
                .collect(Collectors.toList());
            treeNode.setChildren(childNodes);
        }

        return treeNode;
    }
}
