package com.trs.ai.moye.data.ability.response;

import com.trs.ai.moye.common.constants.TimeFormat;
import com.trs.ai.moye.common.utils.BeanUtil;
import com.trs.ai.moye.data.ability.service.SchemaConvert;
import com.trs.ai.moye.data.operator.domain.vo.AbilityDTO;
import com.trs.ai.moye.permission.service.DynamicUserNameService;
import com.trs.moye.ability.entity.Ability;
import com.trs.moye.ability.entity.HttpRequestConfig;
import com.trs.moye.ability.entity.HttpRequestConfig.HttpContentType;
import com.trs.moye.ability.entity.Schema;
import com.trs.moye.ability.entity.operator.AbilityCenterCallParams;
import com.trs.moye.ability.enums.AbilityTestStatus;
import com.trs.moye.ability.enums.AbilityType;
import com.trs.moye.ability.enums.AbilityUpdateStatus;
import java.util.Optional;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 能力响应
 *
 * <AUTHOR>
 * @since 2025/02/28 17:02:13
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
public class AbilityResponse {

    private Integer id;

    /**
     * 算子业务分类
     */
    private String operatorCategory;

    /**
     * 英文名
     */
    private String enName;

    /**
     * 中文名
     */
    private String zhName;

    /**
     * 能力类型
     */
    private AbilityType type;

    /**
     * 能力路径
     */
    private String path;

    /**
     * 描述
     */
    private String description;

    /**
     * 图标
     */
    private String iconName;

    /**
     * 更新状态
     */
    private AbilityUpdateStatus updateStatus;

    /**
     * 测试状态
     */
    private AbilityTestStatus testStatus;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 最近更新人
     */
    private String updateBy;

    /**
     * 创建时间
     */
    private String createTime;

    /**
     * 最近更新时间
     */
    private String updateTime;

    /**
     * 能力请求配置·
     */
    HttpRequestConfig httpRequestConfig;

    /**
     * 输入参数
     */
    private Schema inputSchema;

    /**
     * 输出参数
     */
    private Schema outputSchema;

    /**
     * 能力中心配置
     */
    private AbilityCenterCallParams abilityCenterParams;


    public AbilityResponse(Ability ability) {
        this.id = ability.getId();
        this.enName = ability.getEnName();
        this.zhName = ability.getZhName();
        this.type = ability.getType().toDisplayType();
        this.path = ability.getPath();
        this.description = ability.getDescription();
        this.iconName = ability.getIconName();
        this.updateStatus = ability.getUpdateStatus();
        this.testStatus = ability.getTestStatus();
        DynamicUserNameService userNameService = BeanUtil.getBean(DynamicUserNameService.class);
        this.createBy = userNameService.getUserName(ability.getCreateBy());
        this.updateBy = userNameService.getUserName(ability.getUpdateBy());
        this.createTime = Optional.ofNullable(ability.getCreateTime())
            .map(time -> time.format(TimeFormat.DEFAULT_FORMATTER))
            .orElse(null);
        this.updateTime = Optional.ofNullable(ability.getUpdateTime())
            .map(time -> time.format(TimeFormat.DEFAULT_FORMATTER))
            .orElse(null);
        this.httpRequestConfig = ability.getHttpRequestConfig();
        this.inputSchema = ability.getInputSchema();
        this.outputSchema = ability.getOutputSchema();
        this.abilityCenterParams = ability.getAbilityCenterParams();
    }

    public AbilityResponse(AbilityDTO dto) {
        this.enName = dto.getPowerEnName();
        this.zhName = dto.getPowerName();
        this.description = dto.getPowerAbstract();
        SchemaConvert schemaConvert = new SchemaConvert(dto.getReqJsonSchema(), dto.getResJsonSchema());
        this.httpRequestConfig = buildHttpRequestConfig(dto, schemaConvert);
        this.inputSchema = schemaConvert.getBody();
        this.outputSchema = schemaConvert.getResponse();
        this.abilityCenterParams = new AbilityCenterCallParams(
            dto.getAppId(), dto.getAppName(), dto.getPowerId(), dto.getReleaseVersion());
    }

    protected HttpRequestConfig buildHttpRequestConfig(AbilityDTO dto, SchemaConvert schemaConvert) {
        HttpRequestConfig config = new HttpRequestConfig();
        config.setUrl("/athena/forward/" + dto.getEncryptedUrl());
        config.setHeaders(schemaConvert.getHeaders());
        config.setQueryParams(schemaConvert.getQueryParams());
        config.setMethod(dto.getRequestStringMethod());
        config.setContentType(schemaConvert.isFormBody() ? HttpContentType.FORM : HttpContentType.JSON);
        return config;
    }

}
