package com.trs.ai.moye.common.web.response;

import com.trs.moye.base.common.response.ResponseMessage;
import java.util.Arrays;
import org.springframework.core.MethodParameter;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.http.server.ServerHttpRequest;
import org.springframework.http.server.ServerHttpResponse;
import org.springframework.http.server.ServletServerHttpResponse;
import org.springframework.lang.NonNull;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.servlet.mvc.method.annotation.ResponseBodyAdvice;

/**
 * 统一包装返回值
 */
@ControllerAdvice
public class CustomerResponseBodyConvert implements ResponseBodyAdvice<Object> {

    /**
     * Whether this component supports the given controller method return type and the selected
     * {@code HttpMessageConverter} type.
     *
     * @param returnType    the return type
     * @param converterType the selected converter type
     * @return {@code true} if {@link #beforeBodyWrite} should be invoked; {@code false} otherwise
     */
    @Override
    public boolean supports(MethodParameter returnType,
        @NonNull Class<? extends HttpMessageConverter<?>> converterType) {
        // 已经封装过了就不需要再封装一次
        return Arrays.stream(returnType.getMethodAnnotations())
            .noneMatch(s -> s.annotationType().isAssignableFrom(SkipResponseBodyAdvice.class))
            && !returnType.getParameterType().equals(ResponseMessage.class);
    }

    /**
     * Invoked after an {@code HttpMessageConverter} is selected and just before its write method is invoked.
     *
     * @param body                  the body to be written
     * @param returnType            the return type of the controller method
     * @param selectedContentType   the content type selected through content negotiation
     * @param selectedConverterType the converter type selected to write to the response
     * @param request               the current request
     * @param response              the current response
     * @return the body that was passed in or a modified (possibly new) instance
     */
    @Override
    public Object beforeBodyWrite(Object body, MethodParameter returnType, MediaType selectedContentType,
        Class<? extends HttpMessageConverter<?>> selectedConverterType, ServerHttpRequest request,
        ServerHttpResponse response) {
        int status = ((ServletServerHttpResponse) response).getServletResponse().getStatus();
        HttpStatus httpStatus = HttpStatus.valueOf(status);
        // 优先处理非json的情况，避免内容和content-type对不上
        if ("video".equals(selectedContentType.getType()) || "image".equals(selectedContentType.getType())) {
            return body;
        } else if (HttpStatus.UNAUTHORIZED.equals(httpStatus)) {
            return ResponseMessage.unauthorized(body);
        }else if (HttpStatus.NOT_FOUND.equals(httpStatus)){
            return ResponseMessage.notFound(body);
        }
        else if (body instanceof ResponseMessage) {
            return body;
        } else {
            return ResponseMessage.okWithStatus(status, body);
        }
    }
}
