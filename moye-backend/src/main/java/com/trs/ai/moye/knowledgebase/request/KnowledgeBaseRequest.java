package com.trs.ai.moye.knowledgebase.request;

import com.trs.moye.base.knowledgebase.entity.KnowledgeBase;
import com.trs.moye.base.knowledgebase.entity.KnowledgeBaseField;
import com.trs.moye.base.knowledgebase.enums.KnowledgeBaseType;
import com.trs.moye.base.common.annotaion.validation.NotRepeat;
import com.trs.moye.base.knowledgebase.request.KnowledgeBaseFieldRequest;
import java.util.ArrayList;
import java.util.List;
import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 2024-11-20 21:40
 */
@Data
@NoArgsConstructor
public class KnowledgeBaseRequest {

    /**
     * 实体中文名称
     */
    @NotBlank(message = "知识库中文名称不能为空")
    private String zhName;

    /**
     * 实体英文名称
     */
    @NotBlank(message = "知识库英文名称不能为空")
    private String enName;


    @Length(max = 100, message = "描述信息长度不能超过1000个字符")
    private String desc;

    /**
     * 实体标签 用于分组
     */
    private List<String> tags;



    /**
     * 知识库类型
     */
    @NotNull(message = "知识库类型不能为空")
    private KnowledgeBaseType type;

    @Valid
    @NotRepeat(fields = {"zhName:中文名", "enName:英文名"}, message = "知识库字段列表存在重复元素")
    private List<KnowledgeBaseFieldRequest> fields;

    /**
     * 转换为知识库实体
     *
     * @return 知识库实体
     */
    public KnowledgeBase toKnowledgeBase() {
        KnowledgeBase knowledgeBase = new KnowledgeBase();
        knowledgeBase.setZhName(this.getZhName());
        knowledgeBase.setEnName(this.getEnName());
        knowledgeBase.setType(this.getType());
        knowledgeBase.setDesc(this.getDesc());
        knowledgeBase.setTags(this.getTags());
        return knowledgeBase;
    }

    /**
     * 转换知识库字段
     *
     * @param baseId 知识库id
     * @return 知识库字段列表
     */
    public List<KnowledgeBaseField> toKnowledgeBaseFields(Integer baseId) {
        if (fields == null) {
            return new ArrayList<>();
        }
        return fields.stream().map(fieldRequest -> new KnowledgeBaseField(baseId, fieldRequest)).toList();
    }
}
