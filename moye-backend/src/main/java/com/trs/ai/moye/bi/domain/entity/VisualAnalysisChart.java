package com.trs.ai.moye.bi.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.trs.ai.moye.bi.domain.request.ChartRequest;
import com.trs.ai.moye.bi.domain.request.StatisticGroup;
import com.trs.moye.base.common.entity.AuditBaseEntity;
import com.trs.moye.base.common.typehandler.CustomJacksonTypeHandler;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 可视化分析-图表(VisualAnalysisChart)数据访问类
 *
 * <AUTHOR>
 * @since 2024-12-11 15:41:49
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@TableName(value = "visual_analysis_chart", autoResultMap = true)
public class VisualAnalysisChart extends AuditBaseEntity {

    /**
     * 主题id
     */
    private Integer subjectId;

    /**
     * 标题
     */
    private String chartTitle;

    /**
     * 建模id
     */
    private Integer dataModelId;

    /**
     * 存储id
     */
    private Integer storageId;

    /**
     * 查询schema，存储引擎用
     */
    @TableField(typeHandler = CustomJacksonTypeHandler.class)
    private StatisticGroup[] statisticGroups;


    public VisualAnalysisChart(ChartRequest request) {
        this.chartTitle = request.getChartTitle();
        this.subjectId = request.getSubjectId();
        this.dataModelId = request.getDataModelId();
        this.storageId = request.getStorageId();
        this.statisticGroups = request.getStatisticGroups().toArray(new StatisticGroup[0]);
    }
}