package com.trs.ai.moye.monitor.dao;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.trs.ai.moye.monitor.request.DataModelMonitorRequest;
import com.trs.ai.moye.monitor.request.MonitorEventPeriodRequest;
import com.trs.ai.moye.monitor.response.DataModelMonitorHomeResponse;
import com.trs.ai.moye.monitor.response.MonitorEventPeriodResponse;
import com.trs.ai.moye.monitor.response.OdsMonitorStatisticsResponse;
import com.trs.moye.base.common.enums.ModelLayer;
import com.trs.moye.base.common.request.SortParams;
import com.trs.moye.base.common.request.TimeRangeParams;
import com.trs.moye.base.monitor.entity.MonitorOdsLag;
import java.time.LocalDateTime;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * MonitorOdsLag数据访问层
 *
 * <AUTHOR>
 * @since 2024/08/28 16:49
 */
@Mapper
@DS("clickhouse")
public interface MonitorOdsLagMapper {

    /**
     * 根据时间排序，查询积压量最大的六条数据
     *
     * @param sourceType   来源分类（大类）
     * @param layer        层级
     * @param dataModelIds 元数据id列表
     * @return 最新积压列表
     */
    List<DataModelMonitorHomeResponse> selectTopSix(@Param("sourceType") String sourceType,
        @Param("layer") ModelLayer layer, @Param("dataModelIds") List<Integer> dataModelIds);

    /**
     * 首页查询
     *
     * @param sourceCategory 来源分类（大类）
     * @param layer          层级
     * @param dataModelIds   元数据id列表
     * @return 最新积压列表
     */
    Long odsMonitorHomePageStatisticsCount(@Param("sourceType") String sourceCategory,
        @Param("layer") ModelLayer layer, @Param("dataModelIds") List<Integer> dataModelIds);

    /**
     * 统计页面列表
     *
     * @param request  请求参数
     * @param sortable 排序参数
     * @param layer    层级
     * @param page     分页信息
     * @return 统计信息
     */
    Page<OdsMonitorStatisticsResponse> odsMonitorStatisticsList(@Param("request") DataModelMonitorRequest request,
        @Param("sortable") SortParams sortable,
        @Param("layer") ModelLayer layer,
        Page<OdsMonitorStatisticsResponse> page);

    /**
     * 根据元数据id和配置版本查询最新的积压数据
     *
     * @param dataModelId 元数据id
     * @param configId    配置版本
     * @param count       查询条数
     * @return 积压数据
     */
    List<MonitorOdsLag> selectLatestByDataModelIdAndConfigVersion(@Param("dataModelId") Integer dataModelId,
        @Param("configId") Integer configId, @Param("count") Integer count);

    /**
     * 详情列表
     *
     * @param dataModelId 元数据id
     * @param request     请求对象
     * @param timeParams  时间参数
     * @param page        分页信息
     * @return 波动数据
     */
    Page<MonitorOdsLag> odsMonitorDetailTable(@Param("dataModelId") Integer dataModelId,
        @Param("request") DataModelMonitorRequest request, @Param("timeParam") TimeRangeParams timeParams,
        Page<MonitorOdsLag> page);

    /**
     * 详情列表
     *
     * @param dataModelId 元数据id
     * @param request     请求对象
     * @return 波动数据
     */
    List<MonitorOdsLag> selectMonitorOdsLagRecords(@Param("dataModelId") Integer dataModelId,
        @Param("request") DataModelMonitorRequest request);

    /**
     * 周期列表
     *
     * @param dataModelId 数据建模id
     * @param request     带时间范围的参数
     * @param page        分页参数
     * @return 分页数据
     */
    Page<MonitorEventPeriodResponse> odsMonitorDetailPeriods(@Param("dataModelId") Integer dataModelId,
        @Param("request") MonitorEventPeriodRequest request, Page<Object> page);

    /**
     * 查询最新的积压数据
     *
     * @param dataModelId 数据建模id
     * @param startTime   开始时间
     * @param endTime     结束时间
     * @return 积压数据
     */
    List<MonitorOdsLag> selectLatestByDataModelIdAndTimeRange(@Param("dataModelId") Integer dataModelId,
        @Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime);

    /**
     * 根据层级查询积压数据
     *
     * @param layer 层级
     * @return 积压数据
     */
    List<MonitorOdsLag> selectByLayer(@Param("layer") ModelLayer layer);
}








