package com.trs.ai.moye.data.operator.domain.response;

import com.trs.ai.moye.data.ability.dto.AbilityConfigDto;
import com.trs.moye.ability.entity.Ability;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 算子编排--能力目录树
 *
 * <AUTHOR>
 * @since 2025/03/11 14:13:25
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class AbilityArrangementTreeResponse extends OperatorTreeResponse {

    /**
     * 能力配置
     */
    private AbilityConfigDto abilityConfig;

    /**
     * 是启用编辑
     */
    private Boolean isEnableEdit = true;

    public AbilityArrangementTreeResponse(Ability entity) {
        super(entity);
        AbilityConfigDto dto = new AbilityConfigDto();
        dto.setType(entity.getType().toDisplayType());
        dto.setPath(entity.getPath());
        dto.setHttpRequestConfig(entity.getHttpRequestConfig());
        dto.setInputSchema(entity.getInputSchema());
        dto.setOutputSchema(entity.getOutputSchema());
        dto.setIsBatchConditionSupported(entity.getIsBatchConditionSupported());
        this.abilityConfig = dto;
    }
}
