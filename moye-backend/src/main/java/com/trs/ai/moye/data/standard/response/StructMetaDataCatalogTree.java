package com.trs.ai.moye.data.standard.response;

import com.trs.ai.moye.data.standard.entity.StructMetaData;
import com.trs.ai.moye.data.standard.entity.StructMetaDataCatalog;
import com.trs.ai.moye.data.standard.enums.MetaDataStandardCategoryTypeEnum;
import java.util.List;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 结构化元数据目录树响应对象
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025/5/12 14:45
 **/
@Data
@NoArgsConstructor
public class StructMetaDataCatalogTree {

    /**
     * 节点ID
     */
    private Integer id;

    /**
     * 节点名称
     */
    private String name;

    /**
     * 父级ID
     */
    private Integer pid;

    /**
     * 节点类型：1-目录，2-元数据
     */
    private MetaDataStandardCategoryTypeEnum type;

    /**
     * 英文名称（元数据节点特有）
     */
    private String enName;

    /**
     * 描述（元数据节点特有）
     */
    private String description;

    /**
     * 子节点
     */
    private List<StructMetaDataCatalogTree> children;

    /**
     * 从目录创建树节点
     *
     * @param catalog 目录对象
     * @return StructMetaDataCatalogTree
     */
    public static StructMetaDataCatalogTree fromCatalog(StructMetaDataCatalog catalog) {
        StructMetaDataCatalogTree tree = new StructMetaDataCatalogTree();
        tree.setId(catalog.getId());
        tree.setName(catalog.getName());
        tree.setPid(catalog.getPid());
        tree.setType(MetaDataStandardCategoryTypeEnum.METADATA_CATEGORY); // 目录类型
        tree.setDescription(catalog.getDescription());
        return tree;
    }

    /**
     * 从元数据创建树节点
     *
     * @param metaData 元数据对象
     * @return StructMetaDataCatalogTree
     */
    public static StructMetaDataCatalogTree fromMetaData(StructMetaData metaData) {
        StructMetaDataCatalogTree tree = new StructMetaDataCatalogTree();
        tree.setId(metaData.getId());
        tree.setName(metaData.getZhName());
        tree.setPid(metaData.getCatalogId());
        tree.setType(MetaDataStandardCategoryTypeEnum.STANDARD); // 元数据类型
        tree.setEnName(metaData.getEnName());
        tree.setDescription(metaData.getDescription());
        return tree;
    }
} 