package com.trs.ai.moye.out.service;

import com.trs.ai.moye.out.request.OutIndicatorCurrentPeriodRequest;
import com.trs.ai.moye.out.request.OutIndicatorFieldValueRequest;
import com.trs.ai.moye.out.request.OutIndicatorQueryRequest;
import com.trs.ai.moye.out.request.OutIndicatorTimeRangeRequest;
import com.trs.ai.moye.out.response.OutIndicatorBasicResponse;
import com.trs.ai.moye.out.response.OutIndicatorCurrentPeriod;
import com.trs.ai.moye.out.response.StatisticPeriodResponse;
import java.util.List;
import java.util.Map;

/**
 * OUT 指标服务
 *
 * <AUTHOR>
 * @since 2025/06/03 11:28:03
 */
public interface OutIndicatorService {

    /**
     * 查询指标数据
     *
     * @param request     请求
     * @param dataModelId 数据型id
     * @return {@link List }<{@link Map }<{@link String }, {@link Object }>>
     * <AUTHOR>
     * @since 2025/06/03 11:30:52
     */
    List<Map<String, Object>> queryIndicatorData(OutIndicatorQueryRequest request, Integer dataModelId);

    /**
     * 查询指标数据并对结果分组
     *
     * @param request     请求
     * @param dataModelId 数据型id
     * @return {@link List }<{@link Map }<{@link String }, {@link Object }>>
     * <AUTHOR>
     * @since 2025/06/25 16:30:26
     */
    List<Map<String, Object>> queryIndicatorDataWithGrouping(OutIndicatorQueryRequest request, Integer dataModelId);

    /**
     * 查询数据服务数据
     *
     * @param request     请求
     * @param dataModelId 数据型id
     * @return {@link List }<{@link Map }<{@link String }, {@link Object }>>
     * <AUTHOR>
     * @since 2025/06/16 18:37:23
     */
    List<Map<String, Object>> queryServiceData(OutIndicatorQueryRequest request, Integer dataModelId);

    /**
     * 获取统计时间范围
     *
     * @param timeRange 时间范围
     * @return {@link StatisticPeriodResponse }
     * <AUTHOR>
     * @since 2025/06/06 10:46:40
     */
    StatisticPeriodResponse getStatisticPeriod(OutIndicatorTimeRangeRequest timeRange);

    /**
     * 获取当前周期指标
     *
     * @param request 请求
     * @return {@link OutIndicatorCurrentPeriod }
     * <AUTHOR>
     * @since 2025/06/04 11:07:00
     */
    OutIndicatorCurrentPeriod getCurrentPeriodIndicator(OutIndicatorCurrentPeriodRequest request);

    /**
     * 获取指标库和数据服务
     *
     * @param categoryId 分类id
     * @return {@link List }<{@link OutIndicatorBasicResponse }>
     * <AUTHOR>
     * @since 2025/06/09 15:25:03
     */
    List<OutIndicatorBasicResponse> getIndicatorAndService(Integer categoryId);

    /**
     * 指标库按字段分组
     *
     * @param request     请求
     * @param dataModelId 数据型id
     * @return {@link List }<{@link String }>
     * <AUTHOR>
     * @since 2025/06/12 14:58:46
     */
    List<String> indicatorGroupByField(OutIndicatorFieldValueRequest request, Integer dataModelId);
}
