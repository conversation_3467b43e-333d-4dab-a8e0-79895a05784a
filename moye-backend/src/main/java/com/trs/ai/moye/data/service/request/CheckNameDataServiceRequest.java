package com.trs.ai.moye.data.service.request;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;


/**
 * 数据服务重名检查请求
 *
 * <AUTHOR>
 * @since 2024/09/25 17:23:51
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class CheckNameDataServiceRequest {

    /**
     * id
     */
    private Integer id;

    /**
     * 名称
     */
    @NotBlank(message = "名称不能为空")
    private String name;

    /**
     * 分类id:关联分类表
     */
    @NotNull(message = "分类id不能为空")
    private Integer categoryId;

    public CheckNameDataServiceRequest(DataServiceRequest dataServiceRequest) {
        this.id = dataServiceRequest.getId();
        this.name = dataServiceRequest.getName();
        this.categoryId = dataServiceRequest.getCategoryId();
    }
}
