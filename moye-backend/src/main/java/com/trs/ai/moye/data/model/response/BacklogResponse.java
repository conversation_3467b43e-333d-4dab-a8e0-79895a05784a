package com.trs.ai.moye.data.model.response;

import java.time.LocalDateTime;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.validation.annotation.Validated;

/**
 * 积压响应实体
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025/2/24 11:25
 **/
@Data
@Validated
@AllArgsConstructor
@NoArgsConstructor
public class BacklogResponse {


    /**
     * 积压数量
     */
    private List<KafkaBacklogResponse> backlogs;


    /**
     * 统计时间
     */
    private LocalDateTime countDateTime;

}
