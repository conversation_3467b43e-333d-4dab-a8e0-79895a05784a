package com.trs.ai.moye.monitor.utils;

import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.Objects;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 2021/5/20 15:29
 **/
@Slf4j
public class TimeStringUtil {

    /**
     * 获取时间段内的总时长
     *
     * @param localTime 当地时间
     * @return 总时长
     * <AUTHOR>
     * @since 2024/10/25 22:20:42
     */
    public static LocalDateTime getLocalDateTime(LocalTime localTime) {
        return LocalDateTime.of(LocalDateTime.now().toLocalDate(), localTime);
    }


    /**
     * 获取格式时间 str
     *
     * @param localDateTime 本地日期时间
     * @return {@link String }
     * <AUTHOR>
     * @since 2024/10/25 22:20:44
     */
    public static String getFormatTimeStr(LocalDateTime localDateTime) {
        if (Objects.isNull(localDateTime)) {
            return "";
        }
        return localDateTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
    }

    /**
     * 获取格式化时间字符串
     *
     * @param timestamp 时间戳
     * @return 格式化时间字符串
     */
    public static LocalDateTime getLocalByTimestamp(Long timestamp) {
        return LocalDateTime.ofEpochSecond(timestamp, 0, ZoneOffset.ofHours(8));
    }

    private TimeStringUtil() {

    }


}
