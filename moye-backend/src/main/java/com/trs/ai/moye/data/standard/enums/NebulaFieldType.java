package com.trs.ai.moye.data.standard.enums;

import java.util.ArrayList;
import java.util.List;
import lombok.Getter;

/**
 * <AUTHOR>
 * @since 2024/10/11
 **/
@Getter
public enum NebulaFieldType {

    /**
     * 字段类型
     */
    STRING("string"),
    INT8("int8"),
    INT16("int16"),
    INT32("int32"),
    INT64("int64"),
    DOUBLE("double"),
    BOOL("bool"),
    DATE("date"),
    TIME("time"),
    DATETIME("datetime"),
    TIMESTAMP("timestamp"),
    GEOGRAPHY("geography"),
    GEOGRAPHY_POINT("geography(point)"),
    GEOGRAPHY_LINESTRING("geography(linestring)"),
    GEOGRAPHY_POLYGON("geography(polygon)"),
    DURATION("duration");


    private final String name;

    NebulaFieldType(String name) {
        this.name = name;
    }

    /**
     * 名称列表
     *
     * @return 名称列表
     */
    public static List<String> getNames() {
        NebulaFieldType[] values = values();
        ArrayList<String> names = new ArrayList<>();
        for (NebulaFieldType value : values) {
            names.add(value.name);
        }
        return names;
    }
}
