package com.trs.ai.moye.common.config;

import com.trs.ai.moye.common.enums.HttpStatusHandler;
import feign.Response;
import feign.codec.ErrorDecoder;
import java.util.Optional;
import org.springframework.http.HttpStatus;

/**
 * 自定义错误解码器
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-01-07 11:58
 */
public class MoyeErrorDecoder implements ErrorDecoder {

    public static final String UNKNOWN_SERVICE = "UnknownService";

    @Override
    public Exception decode(String methodKey, Response response) {
        HttpStatus status = Optional.ofNullable(HttpStatus.resolve(response.status()))
            .orElse(HttpStatus.OK);
        HttpStatusHandler handler = HttpStatusHandler.fromStatus(status);
        return handler.createException(methodKey, response);
    }
}