package com.trs.ai.moye.data.model.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.trs.moye.base.data.model.entity.DataModelField;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * DataModelField数据访问层
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/09/26 17:15
 */
@Mapper
public interface DataModelFieldWithKnowledgeFieldsMapper extends BaseMapper<DataModelField> {

    /**
     * 获取数据模型的字段列表，同时加载复合类型、实体类型和标签类型字段的相关属性
     *
     * @param dataModelId 数据模型ID
     * @return 包含知识库字段的数据模型字段列表
     */
    List<DataModelField> listFieldsWithKnowledgeBaseFields(Integer dataModelId);

    /**
     * 根据字段ID列表获取字段信息，同时加载复合类型、实体类型和标签类型字段的相关属性
     *
     * @param fieldIds 字段ID列表
     * @return 包含知识库字段的数据模型字段列表
     */
    List<DataModelField> listFieldsWithKnowledgeBaseFieldsByIds(@Param("fieldIds") List<Integer> fieldIds);
}
