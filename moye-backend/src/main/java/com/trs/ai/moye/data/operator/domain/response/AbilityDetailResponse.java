package com.trs.ai.moye.data.operator.domain.response;

import com.trs.ai.moye.data.operator.constants.AbilityCenterConstants;
import com.trs.ai.moye.data.operator.constants.enums.RequestProtocolEnum;
import com.trs.ai.moye.data.operator.domain.vo.AbilityDTO;
import com.trs.ai.moye.data.operator.domain.vo.OperatorParamConfigDTO;
import com.trs.ai.moye.data.operator.service.impl.AbilityCenterParamConvertor;
import java.io.Serial;
import java.io.Serializable;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.http.HttpMethod;

/**
 * 能力中心算子
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023/6/14
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class AbilityDetailResponse implements Serializable {

    @Serial
    private static final long serialVersionUID = 7268144857340040277L;

    /**
     * 能力id
     */
    private Integer abilityId;

    /**
     * 能力名称
     */
    private String abilityName;

    /**
     * 能力英文名称
     */
    private String abilityEnName;

    /**
     * 版本号
     */
    private String version;

    /**
     * 所属应用名称
     */
    private String appName;

    /**
     * 所属应用id
     */
    private Integer appId;

    /**
     * 能力摘要
     */
    private String description;

    /**
     * 请求路径(加密)
     */
    private String requestUrl;

    /**
     * 请求方式
     */
    private String requestType;

    /**
     * 协议类型
     */
    private RequestProtocolEnum requestProtocol;

    /**
     * 请求参数
     */
    private  List<OperatorParamConfigDTO> input;

    /**
     * 返回参数
     */
    private List<OperatorParamConfigDTO> output;


    public AbilityDetailResponse(AbilityDTO dto) {
        this.appId = dto.getAppId();
        this.appName = dto.getAppName();
        this.abilityId = dto.getPowerId();
        this.abilityEnName = dto.getPowerEnName();
        this.abilityName = dto.getPowerName();
        this.description = dto.getPowerAbstract();
        this.requestUrl = AbilityCenterConstants.URL_PREFIX + dto.getEncryptedUrl();
        this.requestProtocol = dto.getProtocolStringType();
        this.requestType = dto.getRequestStringMethod();
        this.version = dto.getReleaseVersion();
        this.input = AbilityCenterParamConvertor.convertRequestParameter(dto.getReqJsonSchema(),
            HttpMethod.valueOf(dto.getRequestStringMethod()));
        this.output = AbilityCenterParamConvertor.convertResponseParameter(dto.getResJsonSchema());
    }

}