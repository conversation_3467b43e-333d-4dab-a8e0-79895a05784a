package com.trs.ai.moye.monitor.entity;

import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2025-01-14 10:09
 */
@Data
@NoArgsConstructor
public class NodeProcessCountInfo {

    private Integer dataModelId;

    /**
     * 节点地址，例如：192.168.0.1:8080
     */
    private String node;

    /**
     * 处理量
     */
    private Integer processCount;

    /**
     * 处理时间，单位：毫秒
     */
    private Long processingTimeMillis;

    /**
     * 处理出错量
     */
    private Integer processErrorCount;

    public NodeProcessCountInfo(String node) {
        this.node = node;
        this.processCount = 0;
        this.processErrorCount = 0;
        this.processingTimeMillis = 0L;
    }

    /**
     * 获取平均处理时间，单位：毫秒
     *
     * @return 平均处理时间，单位：毫秒
     */
    public Integer getAvgProcessTimeMillis() {
        return (processCount == null || processCount == 0) ? 0 : (int) (processingTimeMillis / processCount);
    }

    /**
     * 获取唯一标识
     *
     * @return 唯一标识
     */
    public String getUniqueIdentify(){
        return buildUniqueIdentify(dataModelId, node);
    }

    /**
     * 构建唯一标识
     *
     * @param dataModelId 数据模型ID
     * @param node 节点地址
     * @return 唯一标识
     */
    public static String buildUniqueIdentify(Integer dataModelId, String node){
        return dataModelId + "-" + node;
    }
}
