package com.trs.ai.moye.common.utils;

import javax.servlet.http.HttpServletRequest;

import org.apache.commons.lang3.StringUtils;

/**
 * IP工具类
 *
 * <AUTHOR>
 * @since 2024/9/14 17:59
 **/
public class IPUtils {

    private static final String UNKNOWN = "unknown";
    private static final String[] IP_HEADERS = {
        "X-Forwarded-For",
        "X-Real-IP",
        "Proxy-Client-IP",
        "WL-Proxy-Client-IP",
        "HTTP_CLIENT_IP",
        "HTTP_X_FORWARDED_FOR"
    };

    private IPUtils() {
    }

    /**
     * 获取真实id
     *
     * @param request {@link HttpServletRequest}
     * @return java.lang.String
     * <AUTHOR>
     * @since 2020/10/28 10:31
     */
    public static String getIpAddr(HttpServletRequest request) {
        String ip;
        for (String header : IP_HEADERS) {
            ip = getIpFromHeader(request, header);
            if (ip != null) {
                return ip;
            }
        }
        return request.getRemoteAddr();
    }

    private static String getIpFromHeader(HttpServletRequest request, String headerName) {
        String ip = request.getHeader(headerName);
        if (StringUtils.isNotEmpty(ip) && !UNKNOWN.equalsIgnoreCase(ip)) {
            return extractFirstIp(ip);
        }
        return null;
    }

    private static String extractFirstIp(String ip) {
        int index = ip.indexOf(",");
        return index != -1 ? ip.substring(0, index) : ip;
    }
}
