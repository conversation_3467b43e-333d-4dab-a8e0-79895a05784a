package com.trs.ai.moye.etcd.service.impl;

import static com.fasterxml.jackson.databind.DeserializationFeature.ACCEPT_EMPTY_ARRAY_AS_NULL_OBJECT;
import static com.fasterxml.jackson.databind.DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES;
import static com.fasterxml.jackson.databind.DeserializationFeature.READ_UNKNOWN_ENUM_VALUES_AS_NULL;
import static com.fasterxml.jackson.databind.MapperFeature.REQUIRE_SETTERS_FOR_GETTERS;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.databind.json.JsonMapper;
import com.trs.ai.moye.etcd.service.EtcdService;
import com.trs.ai.moye.etcd.util.EtcdUtil;
import com.trs.moye.base.common.exception.BizException;
import com.trs.moye.base.common.exception.JsonParseException;
import com.trs.moye.base.common.utils.DataProcessUtils;
import com.trs.moye.base.data.model.task.entity.StreamProcessTask;
import java.util.TimeZone;
import javax.annotation.Resource;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 2024-10-17 22:11
 */
@Slf4j
@Service
public class EtcdServiceImpl implements EtcdService {

    /**
     * 到期时间，单位秒
     **/
    private static final int TTL_SECONDS = 600;

    /**
     * 续期时间，单位秒
     **/
    private static final int FREQ_SECONDS = 300;

    /**
     * 超时时间，单位秒
     **/
    private static final int TIMEOUT_SECONDS = 10;

    /**
     * 全链路监控配置
     */
    private static final String MONITOR_PREFIX = "/service/LINK_MONITOR/";

    private static final String KEEP_ALIVE_TASK_FAIL_MSG_FORMAT = "流处理任务注册到etcd失败. 任务id: %s，任务名称：%s，etcd key：%s";

    @Resource
    private EtcdUtil etcdUtil;


    @Override
    public void keepAliveStreamProcessTask(StreamProcessTask task) {
        String serviceKey = DataProcessUtils.DATA_PROCESS_SOURCE_ETCD_PREFIX + task.getId();
        try {
            etcdUtil.keepAliveWithTimeout(serviceKey
                , toFormatJsonString(task)
                , FREQ_SECONDS, TTL_SECONDS, TIMEOUT_SECONDS);
        } catch (InterruptedException e) {
            log.error(String.format(KEEP_ALIVE_TASK_FAIL_MSG_FORMAT, task.getId(), task.getName(), serviceKey), e);
            Thread.currentThread().interrupt();
        } catch (Exception e) {
            log.error(String.format(KEEP_ALIVE_TASK_FAIL_MSG_FORMAT, task.getId(), task.getName(), serviceKey), e);
            throw new BizException("注册到etcd失败！失败原因：" + e.getLocalizedMessage());
        }
    }

    @Override
    public void keepAliveMonitorStatus(Integer dataModelId, Boolean status) {
        String serviceKey = MONITOR_PREFIX + dataModelId;
        MonitorTask monitorStatus = new MonitorTask(dataModelId, status);
        try {
            etcdUtil.keepAliveWithTimeout(serviceKey
                , toFormatJsonString(monitorStatus)
                , FREQ_SECONDS, TTL_SECONDS, TIMEOUT_SECONDS);
        } catch (InterruptedException e) {
            log.error("任务监控注册到etcd失败！原因：发生中断异常", e);
            Thread.currentThread().interrupt();
        } catch (Exception e) {
            throw new BizException(e, "任务监控注册到etcd失败！失败原因：" + e.getLocalizedMessage());
        }
    }

    @Data
    private static class MonitorTask {

        private Integer id;

        private Boolean monitorStatus;


        public MonitorTask(Integer id, Boolean enable) {
            this.id = id;
            this.monitorStatus = enable;
        }
    }

    /**
     * 转为格式化的json字符串
     *
     * @param obj obj
     * @return json字符串
     */
    private String toFormatJsonString(Object obj) {
        try {
            ObjectMapper objectMapper = createObjectMapper();
            return objectMapper.writeValueAsString(obj);
        } catch (JsonProcessingException e) {
            throw new JsonParseException("", e);
        }
    }

    private ObjectMapper createObjectMapper() {
        // 格式化输出json的ObjectMapper对象
        return JsonMapper.builder().configure(FAIL_ON_UNKNOWN_PROPERTIES, false)
            .configure(ACCEPT_EMPTY_ARRAY_AS_NULL_OBJECT, true).configure(READ_UNKNOWN_ENUM_VALUES_AS_NULL, true)
            .configure(REQUIRE_SETTERS_FOR_GETTERS, true).defaultTimeZone(TimeZone.getDefault()).enable(
                SerializationFeature.INDENT_OUTPUT).build();
    }
}
