package com.trs.ai.moye.monitor.dao;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.trs.moye.base.common.request.TimeRangeParams;
import com.trs.moye.base.monitor.entity.DataProcessMonitorRecord;
import com.trs.moye.base.monitor.entity.DataStorageMonitorRecord;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * DataStorageMonitorRecord数据访问层
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025/03/13 10:16
 */
@DS("clickhouse")
@Mapper
public interface DataStorageMonitorRecordMapper extends BaseMapper<DataStorageMonitorRecord> {

    /**
     * 多条查询：通过dataModelId查询
     *
     * @param dataModelId             要素库ID
     * @param dataStorageConnectionId 数据存储连接id
     * @return 监控记录列表
     */
    DataStorageMonitorRecord selectLatestByDataModelId(@Param("dataModelId") Integer dataModelId,
        @Param("dataStorageConnectionId") Integer dataStorageConnectionId);

    /**
     * 查询列表
     *
     * @param dataModelId             数据建模id
     * @param dataStorageConnectionId 数据存储连接id
     * @param timeRangeParams         时间范围参数
     * @param page                    分页参数
     * @return 监控记录列表
     */
    Page<DataStorageMonitorRecord> pageList(@Param("dataModelId") Integer dataModelId,
        @Param("dataStorageConnectionId") Integer dataStorageConnectionId,
        @Param("timeRangeParams") TimeRangeParams timeRangeParams,
        Page<DataProcessMonitorRecord> page);

    /**
     * 查询列表
     *
     * @param dataModelId             数据建模id
     * @param dataStorageConnectionId 数据存储连接id
     * @param timeRangeParams         时间范围参数
     * @return 监控记录列表
     */
    List<DataStorageMonitorRecord> trendChart(@Param("dataModelId") Integer dataModelId,
        @Param("dataStorageConnectionId") Integer dataStorageConnectionId,
        @Param("timeRangeParams") TimeRangeParams timeRangeParams);
}