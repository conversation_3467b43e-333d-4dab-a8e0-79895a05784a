package com.trs.ai.moye.etcd.config;

import com.ibm.etcd.client.EtcdClient;
import com.ibm.etcd.client.kv.KvClient;
import com.ibm.etcd.client.lease.LeaseClient;
import com.ibm.etcd.client.lock.LockClient;
import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @since 2024-10-17 11:13
 */
@Data
@Configuration
public class EtcdConfig {

    @Value("${etcd.node.url}")
    private String etcdClusterNodes;

    @Value("${etcd.user.name}")
    private String etcdUser;

    @Value("${etcd.user.password}")
    private String etcdPwd;

    /**
     * etcd client
     *
     * @return etcd 客户端
     */
    @Bean
    public EtcdClient etcdClient() {
        return EtcdClient
            .forEndpoints(etcdClusterNodes)
            .withCredentials(etcdUser, etcdPwd)
            .withPlainText()
            .build();
    }

    /**
     * 键值对客户端，用于对key-value的操作
     *
     * @param etcdClient etcd 客户端
     * @return kv 客户端
     */
    @Bean
    public KvClient etcdKvClient(EtcdClient etcdClient) {
        return etcdClient.getKvClient();
    }

    /**
     * 租约客户端，用于设置超时时间
     *
     * @param etcdClient etcd 客户端
     * @return lease 客户端
     */
    @Bean
    public LeaseClient etcdLeaseClient(EtcdClient etcdClient) {
        return etcdClient.getLeaseClient();
    }

    /**
     * 分布式锁客户端
     *
     * @param etcdClient etcd 客户端
     * @return lock 客户端
     */
    @Bean
    public LockClient etcdLockClient(EtcdClient etcdClient) {
        return etcdClient.getLockClient();
    }
}
