package com.trs.ai.moye.data.model.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.trs.ai.moye.data.model.entity.ArrangedOperatorParam;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 编排算子参数详情(ArrangedOperatorParam)持久层
 *
 * <AUTHOR>
 * @date 2024-10-11 14:28:32
 */
@Mapper
public interface ArrangedOperatorParamMapper extends BaseMapper<ArrangedOperatorParam> {

    /**
     * 根据建模id删除
     *
     * @param dataModelId 建模id
     */
    @Delete("delete from stream_arranged_operator_param where data_model_id = #{dataModelId}")
    void deleteByDataModelId(@Param("dataModelId") Integer dataModelId);
}