package com.trs.ai.moye.data.model.service;

import com.trs.ai.moye.data.model.dto.arrangement.DataProcessTrace;
import com.trs.ai.moye.data.model.dto.arrangement.TestPipelineRequest;
import com.trs.ai.moye.data.model.dto.arrangement.stream.OperatorPipelineDTO;
import java.util.List;


/**
 * 数据治理算子编排Service
 *
 * <AUTHOR>
 * @since 2025/03/11 15:48:41
 */
public interface OperatorPipelineService {

    /**
     * 保存编排
     *
     * @param dataModelId         建模id
     * @param operatorPipelineDTO 算子编排
     */
    void saveOperatorPipeline(Integer dataModelId, OperatorPipelineDTO operatorPipelineDTO);

    /**
     * 获取编排
     *
     * @param dataModelId 数据建模id
     * @return {@link OperatorPipelineDTO }
     */
    OperatorPipelineDTO getOperatorPipeline(Integer dataModelId);

    /**
     * 测试算子编排
     *
     * @param dataModelId 数据模型id
     * @param request     测试请求
     * @return 测试结果
     */
    List<DataProcessTrace> testOperatorPipeline(Integer dataModelId, TestPipelineRequest request);

    /**
     * 删除编排
     *
     * @param dataModelId 数据型id
     * @return boolean
     */
    boolean deleteOperatorPipeline(Integer dataModelId);

    /**
     * 保存编排草稿
     *
     * @param dataModelId         建模id
     * @param operatorPipelineDTO 算子编排
     */
    void saveOperatorPipelineDraft(Integer dataModelId, OperatorPipelineDTO operatorPipelineDTO);

    /**
     * 获取编排草稿
     *
     * @param dataModelId 数据建模id
     * @return {@link OperatorPipelineDTO }
     */
    OperatorPipelineDTO getOperatorPipelineDraft(Integer dataModelId);
}