package com.trs.ai.moye.data.ability.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.trs.ai.moye.data.ability.request.SearchTypyArrayAbilityTreeRequest;
import com.trs.ai.moye.data.operator.constants.enums.OperatorTestStatus;
import com.trs.ai.moye.data.operator.constants.enums.OperatorUpdateStatus;
import com.trs.moye.ability.entity.Ability;
import java.util.List;
import java.util.Set;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * Mapper接口，用于操作Ability实体。 该接口扩展了BaseMapper接口，提供了对Ability表的基本CRUD操作。
 */
@Mapper
public interface AbilityMapper extends BaseMapper<Ability> {

    /**
     * 根据ID查询能力
     *
     * @param id 能力ID
     * @return 能力实体
     */
    Ability selectById(@Param("id") Integer id);

    /**
     * 根据一组ID查询能力列表
     *
     * @param ids 能力ID列表
     * @return 能力实体列表
     */
    List<Ability> selectBatchIds(@Param("ids") Set<Integer> ids);

    /**
     * 根据请求参数查询能力列表
     *
     * @param request 请求
     * @return {@link List }<{@link Ability }>
     */
    List<Ability> selectAllByRequest(@Param("request") SearchTypyArrayAbilityTreeRequest request);

    /**
     * 判断英文名是否存在
     *
     * @param enName 英文名
     * @return 是否存在
     */
    boolean existByEnName(@Param("enName") String enName);

    /**
     * 判断中文名是否存在
     *
     * @param zhName 中文名
     * @return 是否存在
     */
    boolean existByZhName(@Param("zhName") String zhName);

    /**
     * 修改算子业务分类
     *
     * @param ids        算子id
     * @param categoryId 业务分类id
     */
    void updateCategoryByOperatorIds(@Param("ids") List<Integer> ids, @Param("categoryId") Integer categoryId);

    /**
     * 更新测试状态
     *
     * @param id         算子id
     * @param testStatus 测试状态
     */
    void updateTestStatus(@Param("id") Integer id, @Param("testStatus") OperatorTestStatus testStatus);

    /**
     * 更新算子更新状态
     *
     * @param id     算子id
     * @param status 更新状态
     */
    void updateUpdateStatus(@Param("id") Integer id, @Param("status") OperatorUpdateStatus status);

    /**
     * 判断id是否存在
     *
     * @param id id
     * @return 是否存在
     */
    boolean existById(@Param("id") Integer id);

    /**
     * 根据英文名查询能力
     *
     * @param enName 英文名
     * @return {@link Ability }
     */
    Ability selectByEnName(@Param("enName") String enName);

    /**
     * 根据分类id查询
     *
     * @param categoryId 分类id
     * @return 算子
     */
    List<Ability> selectByOperatorCategoryId(@Param("categoryId") Integer categoryId);

    /**
     * 查询已接入的能力中心能力id
     *
     * @return id列表
     */
    List<Integer> getAccessedAbilityIds();
}
