package com.trs.ai.moye.monitor.response;

import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * nacos server list response
 *
 * <AUTHOR>
 * @since 2025/2/26
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
public class NacosServerListResponse {

    /**
     * 状态码
     */
    private Integer code;

    /**
     * 消息
     */
    private String message;

    /**
     * 数据
     */
    private NacosServerListData data;

    /**
     * nacos server list data
     */
    @AllArgsConstructor
    @NoArgsConstructor
    @Data
    public class NacosServerListData {
        /**
         * 数量
         */
        private Integer count;

        /**
         * 服务列表
         */
        private List<String> services;
    }
}
