package com.trs.ai.moye.data.service.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.trs.ai.moye.data.service.entity.DataServiceCategory;
import com.trs.ai.moye.data.service.entity.DataServiceCategoryTree;
import com.trs.ai.moye.data.service.response.DataServiceOutResponse;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;


/**
 * 数据服务分类mapper
 *
 * <AUTHOR>
 * @since 2024/09/24 15:04:27
 */
@Mapper
public interface DataServiceCategoryMapper extends BaseMapper<DataServiceCategory> {

    /**
     * 查询分类树
     *
     * @return {@link List }<{@link DataServiceCategoryTree }>
     * <AUTHOR>
     * @since 2024/09/24 15:09:14
     */
    List<DataServiceCategoryTree> selectCategoryTree();

    /**
     * 检查分类名称重复
     *
     * @param dataServiceCategory 数据服务类别
     * @return {@link Boolean }
     * <AUTHOR>
     * @since 2024/09/24 15:09:46
     */
    Boolean checkCategoryName(@Param("category") DataServiceCategory dataServiceCategory);

    /**
     * 根据ID查询分类树
     *
     * @param pid PID
     * @return {@link List }<{@link DataServiceCategoryTree }>
     * <AUTHOR>
     * @since 2024/10/08 14:19:28
     */
    List<DataServiceCategoryTree> selectCategoryTreeByPid(@Param("pid") Integer pid);

    /**
     * 根据ID查询分类树
     *
     * @param isRoot   是否为根节点
     * @param parentId 父节点id
     * @return {@link List }<{@link DataServiceOutResponse }>
     */
    List<DataServiceOutResponse> selectCatalogTree(@Param("isRoot") boolean isRoot, @Param("parentId") Integer parentId);
}
