package com.trs.ai.moye.data.model.response;

import com.trs.moye.base.common.enums.ModelLayer;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 新建表返回表id和type
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024/9/27 18:12
 **/

@Data
@NoArgsConstructor
@AllArgsConstructor
public class DwdAddResponse {

    /**
     * 建模ID
     */
    private Integer id;

    /**
     * 模型分层
     */
    private ModelLayer modelLayer;

}
