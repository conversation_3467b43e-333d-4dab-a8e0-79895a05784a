package com.trs.ai.moye.data.model.ddl.parse;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.trs.moye.base.data.connection.enums.ConnectionType;
import com.trs.ai.moye.data.model.entity.imports.DdlColumn;
import com.trs.ai.moye.data.model.entity.imports.ESTable;
import com.trs.moye.base.common.enums.FieldType;
import com.trs.moye.base.common.exception.BizException;
import com.trs.moye.base.common.exception.JsonParseException;
import com.trs.moye.base.common.utils.AssertUtils;
import com.trs.moye.base.common.utils.JsonUtils;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @since 2024-10-31 13:57
 */
@Slf4j
@Component
public class ElasticSearchDdlParseStrategy implements DdlParseStrategy {

    @Override
    public ConnectionType getSupportConnectionType() {
        return ConnectionType.ELASTIC_SEARCH;
    }

    @Override
    public DDLColumnParseResult parseColumns(String ddlContent) {
        DDLColumnParseResult result = new DDLColumnParseResult();
        try {
            ESTable table = parse(ddlContent);
            result.addColumns(table.getColumnList());
        } catch (Exception e) {
            String errorMessage = String.format("解析【%s】put语句发生异常，异常信息：%s", ddlContent, e.getMessage());
            log.error(errorMessage, e);
            result.addErrorMessage(errorMessage);
        }
        return result;
    }

    private ESTable parse(String ddlContent) {
        try {
            ObjectNode objectNode = (ObjectNode) JsonUtils.parseJsonNode(ddlContent);
            return parseObjectNode(objectNode);
        } catch (JsonParseException e) {
            return parsePutStatement(ddlContent);
        }
    }

    private ESTable parseObjectNode(ObjectNode objectNode) {
        String fieldName = objectNode.fieldNames().next();
        return parseTable(fieldName, (ObjectNode) objectNode.get(fieldName));
    }

    private ESTable parsePutStatement(String putStatement) {
        putStatement = putStatement.trim();
        String tableDefinitionPrefix = putStatement.substring(0, 3);
        if (!"put".equalsIgnoreCase(tableDefinitionPrefix)) {
            throw new BizException("ES索引定义语句格式不正确，前缀不是【put】");
        }
        int firstLineFeedIndex = putStatement.indexOf('\n');
        if (firstLineFeedIndex < 1) {
            throw new BizException("ES索引定义语句格式不正确，未找到换行符，无法确定索引名称");
        }
        String firstLine = putStatement.substring(0, firstLineFeedIndex);
        int backslashIndex = firstLine.lastIndexOf('/');
        if (backslashIndex < 0) {
            throw new BizException("ES索引定义语句格式不正确，首行未找到'/'符，无法确定索引名称");
        }
        String tableName = firstLine.substring(backslashIndex + 1).trim();
        AssertUtils.notEmpty(tableName, "ES索引定义语句格式不正确，无法确定索引名称");

        String fieldDefinitionText = putStatement.substring(firstLineFeedIndex);
        ObjectNode objectNode = (ObjectNode) JsonUtils.parseJsonNode(fieldDefinitionText);
        return parseTable(tableName, objectNode);
    }

    private ESTable parseTable(String tableName, ObjectNode columnDefinitions) {
        AssertUtils.notEmpty(tableName, "表名为空");
        ObjectNode sourceAsMap = (ObjectNode) columnDefinitions.get("sourceAsMap");
        if (ObjectUtils.isEmpty(sourceAsMap)) {
            sourceAsMap = (ObjectNode) columnDefinitions.get("mappings");
        }
        AssertUtils.notEmpty(sourceAsMap, "表字段定义为空");
        ObjectNode properties = (ObjectNode) sourceAsMap.get("properties");
        AssertUtils.notEmpty(properties, "未发现可用的表属性");
        Iterator<String> fieldNameIterator = properties.fieldNames();
        List<DdlColumn> columnList = new ArrayList<>();
        while (fieldNameIterator.hasNext()) {
            String fieldName = fieldNameIterator.next();
            ObjectNode fieldDefinition = (ObjectNode) properties.get(fieldName);
            JsonNode typeNode = fieldDefinition.get("type");
            String type = typeNode == null ? FieldType.STRING.name() : typeNode.asText();
            columnList.add(new DdlColumn(fieldName, type));
        }
        return new ESTable(tableName, columnList);
    }

}
