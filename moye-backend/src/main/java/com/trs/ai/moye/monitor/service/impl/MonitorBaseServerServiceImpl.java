package com.trs.ai.moye.monitor.service.impl;

import static com.trs.ai.moye.common.constants.DefaultUser.SYSTEM_DETECTION_USER_NAME;

import com.alibaba.druid.pool.DruidDataSource;
import com.alibaba.nacos.api.NacosFactory;
import com.alibaba.nacos.api.exception.NacosException;
import com.alibaba.nacos.api.naming.NamingService;
import com.alibaba.nacos.api.naming.pojo.Instance;
import com.fasterxml.jackson.core.type.TypeReference;
import com.trs.ai.moye.common.http.HttpClient;
import com.trs.ai.moye.common.http.HttpRequestEntity;
import com.trs.ai.moye.common.http.response.HttpResponseEntity;
import com.trs.ai.moye.etcd.config.EtcdConfig;
import com.trs.ai.moye.monitor.config.JdbcConfig;
import com.trs.ai.moye.monitor.config.NacosConfig;
import com.trs.ai.moye.monitor.config.SeatunnelConfig;
import com.trs.ai.moye.monitor.config.kafka.KafkaProducerProperties;
import com.trs.ai.moye.monitor.dao.BasicComponentDetectionMapper;
import com.trs.ai.moye.monitor.entity.BasicComponentDetection;
import com.trs.ai.moye.monitor.entity.NoticeVO;
import com.trs.ai.moye.monitor.enums.DetectionType;
import com.trs.ai.moye.monitor.enums.MoyeBaseComponentEnum;
import com.trs.ai.moye.monitor.feign.MonitorCenterService;
import com.trs.ai.moye.monitor.response.EtcdHealthResponse;
import com.trs.ai.moye.monitor.response.SeatunnelOverviewResponse;
import com.trs.ai.moye.monitor.service.MonitorBaseServerService;
import com.trs.moye.base.common.exception.BizException;
import com.trs.moye.base.common.utils.JsonUtils;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.ResultSet;
import java.sql.Statement;
import java.time.Duration;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Properties;
import java.util.Set;
import java.util.concurrent.ExecutionException;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.kafka.clients.admin.AdminClient;
import org.apache.kafka.clients.admin.AdminClientConfig;
import org.apache.kafka.clients.admin.CreateTopicsResult;
import org.apache.kafka.clients.admin.DescribeClusterResult;
import org.apache.kafka.clients.admin.ListTopicsResult;
import org.apache.kafka.clients.admin.NewTopic;
import org.apache.kafka.clients.consumer.ConsumerConfig;
import org.apache.kafka.clients.consumer.ConsumerRecords;
import org.apache.kafka.clients.consumer.KafkaConsumer;
import org.apache.kafka.clients.producer.KafkaProducer;
import org.apache.kafka.clients.producer.ProducerConfig;
import org.apache.kafka.clients.producer.ProducerRecord;
import org.apache.kafka.clients.producer.RecordMetadata;
import org.apache.kafka.common.Node;
import org.apache.kafka.common.serialization.StringDeserializer;
import org.apache.kafka.common.serialization.StringSerializer;
import org.jetbrains.annotations.NotNull;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;

/**
 * 用于连接Nacos的服务实现类
 *
 * <AUTHOR>
 * @since 2025/2/24
 */
@Service
@Slf4j
public class MonitorBaseServerServiceImpl implements MonitorBaseServerService {


    @Resource
    private NacosConfig nacosConfig;

    @Resource
    private HttpClient httpClient;

    @Resource
    private MonitorCenterService monitorCenterService;

    @Resource
    private KafkaProducerProperties kafkaProducerProperties;

    @Resource
    private JdbcConfig jdbcConfig;

    @Resource
    private SeatunnelConfig seatunnelConfig;

    @Resource
    private EtcdConfig etcdConfig;

    @Resource
    private BasicComponentDetectionMapper basicComponentDetectionMapper;


    /**
     * MOYE单个服务的健康检查
     *
     * @param componentName 组件名称
     */
    @Override
    public void moyeSingleBaseHealthCheck(String componentName) {
        int instanceCount = 0;
        try {
            List<Instance> instanceList = getNamingService().getAllInstances(componentName);
            log.debug("当前检测服务={}, 服务实例列表={}", componentName, instanceList);
            if (Objects.isNull(instanceList) || instanceList.isEmpty()) {
                throw new BizException(String.format("获取实例个数为0个, 请在技术中台上检查 %s 服务的状态", componentName));
            }
            instanceCount = instanceList.size();

            String unhealthyIps = instanceList.stream()
                .filter(instance -> !instance.isHealthy())
                .map(Instance::getIp)
                .collect(Collectors.joining(", "));
            if (StringUtils.isNotBlank(unhealthyIps)) {
                throw new BizException(unhealthyIps + "获取到的健康状态healthy = false");
            }
        } catch (Exception e) {
            String errMsg = componentName + "服务异常" + e.getMessage();
            sendServerException(componentName, "trs-" + componentName + "-svc", nacosConfig.getNameSpace(), instanceCount, errMsg);
            throw new BizException(errMsg, e);
        }
    }

    private @NotNull NamingService getNamingService() throws NacosException {
        NamingService namingService = NacosFactory.createNamingService(nacosConfig.getProperties());
        String serverStatus = namingService.getServerStatus();
        log.debug("nacos服务状态={}", serverStatus);
        return namingService;
    }


    /**
     * Mysql的健康检查
     */
    @Override
    public void mysqlHealthCheck() {
        try (DruidDataSource dataSource = getDruidDataSource();
            Connection connection = dataSource.getConnection();
            Statement statement = connection.createStatement();
            ResultSet resultSet = statement.executeQuery(jdbcConfig.getMysqlValidateQuery())) {
            if (resultSet.next()) {
                log.debug("MySQL is healthy. Response: {}", resultSet.getInt(1));
            }
        } catch (Exception e) {
            String errMsg = "MySQL 连接异常: " + e.getMessage();
            sendServerException("mysql", jdbcConfig.getMysqlUrl(), "mysql", 1, errMsg);
            throw new BizException(errMsg, e);
        }
    }

    @NotNull
    private DruidDataSource getDruidDataSource() {
        DruidDataSource dataSource = new DruidDataSource();
        dataSource.setUrl(jdbcConfig.getMysqlUrl());
        dataSource.setUsername(jdbcConfig.getMysqlUserName());
        dataSource.setPassword(jdbcConfig.getMysqlPassword());
        dataSource.setValidationQuery(jdbcConfig.getMysqlValidateQuery());
        return dataSource;
    }


    /**
     * ClickHouse的健康检查
     */
    @Override
    public void clickhouseHealthCheck() {
        try (Connection connection = DriverManager.getConnection(jdbcConfig.getClickhouseUrl(),
            jdbcConfig.getClickhouseUserName(), jdbcConfig.getClickhousePassword());
            Statement statement = connection.createStatement();
            ResultSet resultSet = statement.executeQuery(jdbcConfig.getClickhouseValidateQuery())) {
            if (resultSet.next()) {
                log.debug("ClickHouse is healthy. Response: {}", resultSet.getInt(1));
            }
        } catch (Exception e) {
            String errMsg = "ClickHouse 异常:" + e.getMessage();
            sendServerException("Clickhouse", jdbcConfig.getClickhouseUrl(), "clickhouse", 1, errMsg);
            throw new BizException(errMsg, e);
        }
    }


    /**
     * etcd健康检测
     */
    public void etcdHealthCheck() {
        HttpRequestEntity requestEntity = HttpRequestEntity.get(
            "http://" + etcdConfig.getEtcdClusterNodes() + "/health").build();
        HttpResponseEntity etcdResponseEntity = httpClient.doRequestForEntity(requestEntity);
        String etcdResponseBody = etcdResponseEntity.getBodyAsString();
        EtcdHealthResponse etcdHealthResponse = JsonUtils.parseObject(etcdResponseBody,
            new TypeReference<EtcdHealthResponse>() {
            });
        if (Objects.nonNull(etcdHealthResponse) && Boolean.TRUE.equals(etcdHealthResponse.getHealth())) {
            log.debug("etcd健康检测正常");
        } else {
            String errMsg = "etcd健康检测异常, healthy = false,请检查配置";
            sendServerException("etcd", etcdConfig.getEtcdClusterNodes(), "etcd", 1, errMsg);
            throw new BizException(errMsg);
        }
    }


    /**
     * kafka健康检测
     */
    @Override
    public void kafkaHealthCheck() {
        int instanceCount = 0;
        String testTopic = "trs_moye_check_health_test_topic";

        Properties props = new Properties();
        props.put(AdminClientConfig.BOOTSTRAP_SERVERS_CONFIG, kafkaProducerProperties.getBootStrapServers());
        props.put(ProducerConfig.KEY_SERIALIZER_CLASS_CONFIG, StringSerializer.class.getName());
        props.put(ProducerConfig.VALUE_SERIALIZER_CLASS_CONFIG, StringSerializer.class.getName());
        props.put(ConsumerConfig.GROUP_ID_CONFIG, "test-group");
        props.put(ConsumerConfig.KEY_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class.getName());
        props.put(ConsumerConfig.VALUE_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class.getName());

        try (AdminClient adminClient = AdminClient.create(props)) {
            // 获取集群信息和broker实例数
            instanceCount = getInstanceCount(adminClient);

            // 检查topic并创建测试用topic
            checkAndCreateTopic(adminClient, testTopic);

            //检查kafka producer是否正常发送消息
            checkProducer(props, testTopic);

            //检查kafka consumer是否正常拉取消息
            checkConsumer(props, testTopic);
        } catch (Exception e) {
            String errMsg = "检测kafka集群状态异常: " + e.getMessage();
            sendServerException("kafka", kafkaProducerProperties.getHost(), "kafka", instanceCount, errMsg);
            if (e instanceof InterruptedException) {
                Thread.currentThread().interrupt();
            }
            throw new BizException(errMsg, e);
        }
    }

    private void checkAndCreateTopic(AdminClient adminClient, String testTopic)
        throws InterruptedException, ExecutionException {
        ListTopicsResult topicsResult = adminClient.listTopics();
        Set<String> topics = topicsResult.names().get();
        if (!topics.contains(testTopic)) {
            NewTopic newTopic = new NewTopic(testTopic, 1, (short) 1);
            CreateTopicsResult result = adminClient.createTopics(Collections.singleton(newTopic));
            result.all().get();
        }
    }

    private int getInstanceCount(AdminClient adminClient)
        throws InterruptedException, ExecutionException {
        DescribeClusterResult clusterResult = adminClient.describeCluster();
        Node controller = clusterResult.controller().get(); // 获取控制器节点
        log.debug("Kafka Broker is healthy. Controller: {}", controller);
        return clusterResult.nodes().get().size();
    }

    private void checkConsumer(Properties props, String topicName) {
        try (KafkaConsumer<String, String> consumer = new KafkaConsumer<>(props)) {
            // 订阅 Topic, 拉取消息
            consumer.subscribe(Collections.singletonList(topicName));
            ConsumerRecords<String, String> records = consumer.poll(Duration.ofMillis(1000));
            log.debug("kafka 健康检测, 成功拉取到 {} 的消息 {} 条", topicName, records.count());
        } catch (Exception e) {
            throw new BizException(String.format("kafka consumer 拉取消息异常: %s", e.getMessage()), e);
        }
    }

    private void checkProducer(Properties props, String topicName) throws InterruptedException {
        try (KafkaProducer<String, String> producer = new KafkaProducer<>(props)) {
            // 发送测试消息
            ProducerRecord<String, String> testRecord = new ProducerRecord<>(topicName, "key", "value");
            RecordMetadata metadata = producer.send(testRecord).get();
            log.debug("kafka 健康检测, 成功推送消息到 partition {}, offset {}", metadata.partition(), metadata.offset());
        } catch (ExecutionException e) {
            throw new BizException(String.format("kafka producer 发送消息异常: %s", e.getMessage()), e);
        }
    }


    /**
     * seatunnel健康检测
     */
    @Override
    public void seatunnelHealthCheck() {
        try {
            SeatunnelOverviewResponse seatunnelResponse = getSeatunnelOverviewResponse();
            if (Objects.nonNull(seatunnelResponse) && !"0".equals(seatunnelResponse.getWorkers())) {
                checkWorkers(seatunnelResponse);
            } else {
                throw new BizException("获取seatunnel工作节点为空/0，请检查配置");
            }
        } catch (Exception e) {
            String errMsg = "seatunnel请求健康检测接口存在异常, 请在技术中台上检查 seatunnel-master seatunnel-worker 服务状态, 异常信息: " + e.getMessage();
            sendServerException("seatunnel", seatunnelConfig.getSeatunnelUrl(), "seatunnel",
                seatunnelConfig.getSeatunnelWorkers(),
                errMsg);
            throw new BizException(errMsg, e);
        }
    }

    private SeatunnelOverviewResponse getSeatunnelOverviewResponse() {
        HttpRequestEntity requestEntity = HttpRequestEntity.get(
                "http://" + seatunnelConfig.getSeatunnelUrl() + "/hazelcast/rest/maps/overview")
            .build();
        HttpResponseEntity seatunnelResponseEntity = httpClient.doRequestForEntity(requestEntity);
        String seatunnelResponseBody = seatunnelResponseEntity.getBodyAsString();
        return JsonUtils.parseObject(seatunnelResponseBody,
            new TypeReference<SeatunnelOverviewResponse>() {
            });
    }

    private void checkWorkers(SeatunnelOverviewResponse seatunnelResponse) {
        //判断seatunnel工作节点是否达到上限
        if (Integer.parseInt(seatunnelResponse.getWorkers()) < seatunnelConfig.getSeatunnelWorkers()) {
            throw new BizException(
                "seatunnel配置工作节点个数: " + seatunnelConfig.getSeatunnelWorkers() + ", 当前工作节点个数:"
                    + seatunnelResponse.getWorkers() + ", 工作节点不足");
        } else {
            log.debug("seatunnel工作节点正常");
        }
    }


    /**
     * nlp组件健康检查
     *
     * @param componentName 组件名称
     * @param url           请求地址
     * @param body          请求体
     */
    @Override
    public void nlpHealthCheck(String componentName, String url, String body) {
        try {
            HttpRequestEntity requestEntity = HttpRequestEntity.post(url)
                .headers(Map.of(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE))
                .body(body)
                .build();

            HttpResponseEntity response = httpClient.doRequestForEntity(requestEntity);
            if (!HttpStatus.OK.equals(response.getStatus())) {
                String errMsg = String.format("响应异常，请求url: %s, 请求体: %s, 状态码: %s, 响应体: %s",
                    requestEntity.getUrl(), requestEntity.getBodyString(), response.getStatus().value(),
                    response.getBodyAsString());
                throw new BizException(errMsg);
            }
        } catch (Exception e) {
            String errMsg = componentName + " 异常, 请在技术中台上检查对应服务状态, 异常信息: " + e.getMessage();
            sendServerException(componentName, url, "nlp", 1, errMsg);
            throw new BizException(errMsg, e);
        }
    }


    /**
     * 发送服务异常告警 若发送异常则记录monitor-center异常
     *
     * @param serverName    服务名称
     * @param serverIp      服务IP
     * @param clusterName   集群名称
     * @param instanceCount 实例数
     * @param reason        异常原因
     */
    private void sendServerException(String serverName, String serverIp, String clusterName, int instanceCount,
        String reason) {
        NoticeVO vo = new NoticeVO();
        vo.setPublishTime(LocalDateTime.now());
        vo.setServerName(serverName);
        vo.setServerIp(serverIp);
        vo.setClusterName(clusterName);
        vo.setInstanceCount(instanceCount);
        vo.setReason(reason);
        vo.setEnvironment(nacosConfig.getEnvironment());
        try {
            monitorCenterService.sendServerMessage(vo);
        } catch (Exception e) {
            // 如果消息发不出去了, 说明monitor-center有异常, 监控把monitor自动改成异常状态
            BasicComponentDetection basicComponentDetection = BasicComponentDetection.error(
                MoyeBaseComponentEnum.MOYE_MONITOR_CENTER.getName(), DetectionType.SCHEDULED,
                SYSTEM_DETECTION_USER_NAME,
                "monitor-center服务异常, 站内消息无法正常发送, 请检查nacos配置以及技术中台状态");
            basicComponentDetectionMapper.insert(basicComponentDetection);
            log.error("发送服务异常通知失败, serverName={}, serverIp={}, clusterName={}, instanceCount={}, reason={}",
                serverName, serverIp, clusterName, instanceCount, reason, e);
        }
    }

}
