package com.trs.ai.moye.data.service.processer.process.condition;

import com.trs.ai.moye.data.service.dao.DataServiceConfigMapper;
import com.trs.ai.moye.data.service.entity.DataServiceConfig;
import com.trs.ai.moye.data.service.entity.query.Condition;
import com.trs.ai.moye.data.service.enums.DataServiceConditionType;
import com.trs.moye.base.common.exception.BizException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @since 2024/9/30
 **/
@Slf4j
@Component("referenceConditionProcessor")
public class ReferenceConditionProcessor extends ConditionProcessor {

    @Resource
    private DataServiceConfigMapper dataServiceConfigMapper;

    @Override
    public boolean support(DataServiceConditionType type) {
        return DataServiceConditionType.REFERENCE.equals(type);
    }

    @Override
    public void process(List<Condition> processedConditions, Condition pendingCondition,
        Map<String, List<String>> replaceFields) {
        throw new UnsupportedOperationException("reference condition can not be processed");
    }

    private void checkCycleReference(List<Integer> refIds, DataServiceConfig dataServiceConfig) {
        // 检查是否存在循环引用
        if (refIds.contains(dataServiceConfig.getId())) {
            log.error("存在循环引用，id:{}", dataServiceConfig.getId());
            throw new BizException("存在循环引用, 循环引用数据服务id0:%s", dataServiceConfig.getDataServiceId());
        }
    }

    private Integer getRefId(Condition condition) {
        return Integer.parseInt(condition.getValues().get(0).getKey());
    }


    /**
     * 根据引用id获取引用条件
     *
     * @param refIds    引用id
     * @param condition 条件
     * @return {@link List }<{@link Condition }>
     */
    public List<Condition> getALLReferenceConditionsByRefId(List<Integer> refIds, Condition condition) {
        Integer refId = getRefId(condition);
        DataServiceConfig dataServiceConfig = dataServiceConfigMapper.selectOneByDataServiceId(getRefId(condition));
        checkCycleReference(refIds, dataServiceConfig);
        refIds.add(refId);
        List<Condition> conditions = dataServiceConfig.getConditions();
        if (Objects.isNull(conditions) || conditions.isEmpty()) {
            return new ArrayList<>();
        }
        for (Condition c : conditions) {
            if (Boolean.TRUE.equals(DataServiceConditionType.REPLACEABLE.equals(condition.getType()))) {
                merge(conditions, getALLReferenceConditionsByRefId(refIds, c));
            }
        }
        return conditions;
    }
}
