package com.trs.ai.moye.data.model.feign;

import com.trs.ai.moye.common.config.OpenFeignConfig;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * <AUTHOR>
 * @since 2025/4/24 16:38
 */
@FeignClient(name = "moye-mcp-service", path = "", configuration = OpenFeignConfig.class)
public interface McpServiceFeign {

    /**
     * 接收到数据建模更新时，更新resources
     *
     * @param dataModelId 建模id
     * @param isAdd       增加/减少
     */
    @PutMapping("/mcp/resources/update")
    void updateResources(@RequestParam("dataModelId") Integer dataModelId, @RequestParam("isAdd") Boolean isAdd);

}
