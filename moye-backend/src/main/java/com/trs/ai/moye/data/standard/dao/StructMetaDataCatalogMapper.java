package com.trs.ai.moye.data.standard.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.trs.ai.moye.data.standard.entity.StructMetaDataCatalog;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 结构化元数据目录Mapper接口
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025/5/12 14:35
 **/
@Mapper
public interface StructMetaDataCatalogMapper extends BaseMapper<StructMetaDataCatalog> {

    /**
     * 统计指定名称的目录数量（排除指定ID）
     *
     * @param name 目录名称
     * @param id   排除的目录ID
     * @param pid  父级ID
     * @return 数量
     */
    Integer countByNameAndExcludeId(@Param("name") String name, @Param("id") Integer id, @Param("pid") Integer pid);

    /**
     * 统计指定父级ID的目录数量
     *
     * @param pid 父级ID
     * @return 数量
     */
    Integer countByPid(@Param("pid") Integer pid);
}

