package com.trs.ai.moye.monitor.response;

import com.trs.ai.moye.monitor.entity.LagPeriod;
import com.trs.moye.base.data.connection.enums.DataSourceCategory;
import com.trs.moye.base.monitor.entity.MonitorBatchTask;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-08-30 11:29
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class DataModelMonitorHomeResponse {

    private Integer dataModelId;

    private String dataModelName;

    private Long count;

    private DataSourceCategory sourceType;

    /**
     * 从MonitorBatchTask转换为DataModelMonitorHomeResponse
     *
     * @param batchTask {@link MonitorBatchTask}
     * @return {@link DataModelMonitorHomeResponse}
     */
    public static DataModelMonitorHomeResponse from(MonitorBatchTask batchTask) {
        DataModelMonitorHomeResponse response = new DataModelMonitorHomeResponse();
        response.setDataModelName(batchTask.getDataModelName());
        response.setDataModelId(batchTask.getDataModelId());
        response.setCount(batchTask.getMonitorValue());
        return response;
    }

    /**
     * 从LagPeriod转换为DataModelMonitorHomeResponse
     *
     * @param lagPeriod     {@link LagPeriod}
     * @param dataModelName 数据模型名称
     * @return {@link DataModelMonitorHomeResponse}
     */
    public static DataModelMonitorHomeResponse from(LagPeriod lagPeriod, String dataModelName) {
        DataModelMonitorHomeResponse response = new DataModelMonitorHomeResponse();
        response.setDataModelId(lagPeriod.getDataModelId());
        response.setCount(lagPeriod.getCheckCount().longValue());
        response.setDataModelName(dataModelName);
        return response;
    }
}
