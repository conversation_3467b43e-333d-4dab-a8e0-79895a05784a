package com.trs.ai.moye.data.standard.response;

import com.trs.moye.base.data.standard.entity.MetaDataStandardField;
import com.trs.moye.base.data.standard.entity.TagEdgeField;
import com.trs.moye.base.common.enums.FieldType;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2024/10/9
 **/

@Data
@NoArgsConstructor
@AllArgsConstructor
public class MetaDataStandardFieldResponse {

    /**
     * 主键id
     */
    private Integer id;

    /**
     * 中文名称
     */
    private String zhName;

    /**
     * 英文名称
     */
    private String enName;

    /**
     * 描述
     */
    private String description;

    /**
     * 元数据标准属性的类型
     */
    private FieldType type;

    /**
     *
     */
    private List<TagEdgeField> fields;

    /**
     *
     */
    private String primaryKey;

    /**
     * 转换为响应对象
     *
     * @param metaDataStandardField 元数据标准属性
     * @return 响应对象
     */
    public static MetaDataStandardFieldResponse from(MetaDataStandardField metaDataStandardField) {
        MetaDataStandardFieldResponse metaDataStandardFieldResponse = new MetaDataStandardFieldResponse();
        metaDataStandardFieldResponse.setId(metaDataStandardField.getId());
        metaDataStandardFieldResponse.setZhName(metaDataStandardField.getZhName());
        metaDataStandardFieldResponse.setEnName(metaDataStandardField.getEnName());
        metaDataStandardFieldResponse.setDescription(metaDataStandardField.getDescription());
        metaDataStandardFieldResponse.setType(metaDataStandardField.getType());
        metaDataStandardFieldResponse.setFields(metaDataStandardField.getFields());
        metaDataStandardFieldResponse.setPrimaryKey(metaDataStandardField.getPrimaryKey());
        return metaDataStandardFieldResponse;
    }
}
