package com.trs.ai.moye.monitor.service;

import com.trs.moye.base.common.response.PageResponse;
import com.trs.ai.moye.data.model.response.DwdRealtimeMonitorResponse;
import com.trs.ai.moye.monitor.response.MonitorTrendResponse;
import com.trs.moye.base.common.request.BaseRequestParams;
import com.trs.moye.base.monitor.entity.ConsumeMonitorMetrics;
import com.trs.moye.base.monitor.entity.DataProcessMonitorRecord;
import com.trs.moye.base.monitor.entity.TodayMonitorMetric;
import java.util.List;

/**
 * 监控实时 DWD 服务 DataProcessMonitorRecordService
 *
 * <AUTHOR>
 * @since 2025/02/17 14:34:41
 */
public interface DataProcessMonitorRecordService {

    /**
     * 刷新监控记录（实时消费信息）
     *
     * @param dataModelId 数据型id
     * @return {@link ConsumeMonitorMetrics }
     * <AUTHOR>
     * @since 2025/02/19 16:35:27
     */
    ConsumeMonitorMetrics refreshMonitor(Integer dataModelId);

    /**
     * 今日监控数据
     *
     * @param dataModelId 数据型id
     * @return {@link TodayMonitorMetric }
     */
    TodayMonitorMetric todayMonitor(Integer dataModelId);

    /**
     * 获取记录分页列表
     *
     * @param dataModelId 数据型id
     * @param request     请求
     * @return {@link PageResponse }<{@link DwdRealtimeMonitorResponse }>
     * <AUTHOR>
     * @since 2025/02/18 17:23:09
     */
    PageResponse<DataProcessMonitorRecord> pageList(Integer dataModelId, BaseRequestParams request);

    /**
     * 趋势图
     *
     * @param dataModelId 数据型id
     * @param request     请求
     * @return {@link List }<{@link MonitorTrendResponse }>
     * <AUTHOR>
     * @since 2025/02/18 17:23:09
     */
    List<DataProcessMonitorRecord> trendChart(Integer dataModelId, BaseRequestParams request);
}
