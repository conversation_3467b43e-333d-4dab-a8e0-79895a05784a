package com.trs.ai.moye.monitor.dto;

import java.time.LocalDateTime;
import lombok.Data;

/**
 * DataConnectionHealthStatisticsDto
 *
 * <AUTHOR>
 * @since 2025/7/10 18:06
 */
@Data
public class DataConnectionHealthStatisticsDto {
    private Integer connectionId;
    private Long detectionCount;
    private Long errorCount;
    private Boolean lastIsError;
    private LocalDateTime lastDetectionTime;
    private LocalDateTime lastErrorTime;
    private String lastErrorMessage;
}
