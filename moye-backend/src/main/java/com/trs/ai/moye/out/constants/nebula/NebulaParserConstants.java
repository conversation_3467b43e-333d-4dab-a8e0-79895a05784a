package com.trs.ai.moye.out.constants.nebula;

/**
 * Nebula 解析器常量
 *
 * <AUTHOR>
 * @since 2024/12/23 10:06:47
 */
public class NebulaParserConstants {
    public static final String AND = "AND";
    public static final String WHERE = "WHERE";
    public static final String RETURN = "RETURN";
    public static final String CONTAINS = "CONTAINS";
    public static final String ENDS_WITH = "ENDS WITH";
    public static final String STARTS_WITH = "STARTS WITH";
    public static final String LIKE = "LIKE";
    public static final String IN = "IN";
    public static final String BETWEEN = "BETWEEN";
    public static final String REPLACE_AND = "__AND__";
    public static final String IS_NULL = "IS NULL";
    public static final String IS_NOT_NULL = "IS NOT NULL";
    public static final String BLANK_SPACE = " ";
    public static final String OR = "OR";
    public static final String MATCH = "MATCH";


    private NebulaParserConstants() {}
}
