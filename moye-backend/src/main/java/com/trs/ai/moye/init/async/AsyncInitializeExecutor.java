package com.trs.ai.moye.init.async;

import com.trs.ai.moye.init.InitializeExecutor;
import java.util.List;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 莫邪应用初始化Runner，异步运行初始化方法，提高应用启动速度
 *
 * <AUTHOR>
 * @since 2024-10-18 18:01
 */
@Slf4j
@Component
public class AsyncInitializeExecutor implements InitializeExecutor {

    @Resource
    private List<AsyncInitialize> asyncInitializeList;

    @Override
    public void execute() {
        // 创建线程池
        AtomicInteger threadCounter = new AtomicInteger(1);
        ThreadPoolExecutor executor = new ThreadPoolExecutor(4, 4, 0L, TimeUnit.SECONDS,
            new LinkedBlockingQueue<>(1000), r -> {
            Thread thread = new Thread(r);
            thread.setName("莫邪应用初始化线程-" + threadCounter.getAndIncrement());
            return thread;
        });
        // 异步执行初始化方法
        for (AsyncInitialize asyncInitialize : asyncInitializeList) {
            executor.execute(() -> this.executeInitialize(asyncInitialize));
        }
        // 所有任务执行完成后关闭线程池
        executor.shutdown();
    }

    private void executeInitialize(AsyncInitialize asyncInitialize) {
        String simpleName = asyncInitialize.getClass().getSimpleName();
        try {
            log.info("{}：开始初始化", simpleName);
            asyncInitialize.initialize();
            log.info("{}：初始化结束", simpleName);
        } catch (Throwable e) {
            log.error(String.format("%s：初始化失败", simpleName), e);
        }
    }
}
