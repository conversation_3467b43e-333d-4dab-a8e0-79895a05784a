package com.trs.ai.moye.data.model.dao.operator;

import com.trs.ai.moye.data.model.dto.arrangement.stream.OperatorPipelineDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 流处理编排草稿mapper
 */
@Mapper
public interface OperatorPipelineDraftMapper {

    /**
     * 根据数据模型ID查询操作管道。
     *
     * @param dataModelId 数据模型ID
     * @return 操作管道实体
     */
    OperatorPipelineDTO selectByDataModelId(@Param("dataModelId") long dataModelId);

    /**
     * 插入
     *
     * @param operatorPipeline 编排
     */
   void insert(OperatorPipelineDTO operatorPipeline);

    /**
     * 删除编排草稿
     *
     * @param dataModelId 数据模型ID
     */
   void deleteByDataModelId(@Param("dataModelId") Integer dataModelId);
}