package com.trs.ai.moye.data.operator.domain.request;

import java.util.List;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 算子批量移动
 *
 * <AUTHOR>
 * @since 2024/10/9 10:55
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class OperatorBatchMoveRequest {

    /**
     * 算子id列表
     */
    @NotEmpty(message = "算子id不能为空")
    private List<Integer> operatorIds;

    /**
     * 目标分类id
     */
    @NotNull(message = "目标分类id不能为空")
    private Integer targetCategoryId;
}
