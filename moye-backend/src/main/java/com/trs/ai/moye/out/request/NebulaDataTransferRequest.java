package com.trs.ai.moye.out.request;

import javax.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Nebula 数据同步请求
 *
 * <AUTHOR>
 * @since 2024/12/24 15:33:18
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class NebulaDataTransferRequest {

    /**
     * 来源-数据建模id
     */
    @NotNull(message = "来源数据建模id不能为空")
    private Integer sourceModelId;

    /**
     * 目标-数据建模id
     */
    @NotNull(message = "目标数据建模id不能为空")
    private Integer targetModelId;

}
