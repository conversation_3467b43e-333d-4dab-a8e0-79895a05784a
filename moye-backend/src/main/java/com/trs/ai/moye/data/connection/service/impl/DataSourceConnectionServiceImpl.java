package com.trs.ai.moye.data.connection.service.impl;

import static com.trs.ai.moye.common.constants.Number.ZERO;

import com.trs.ai.moye.common.utils.BeanUtil;
import com.trs.ai.moye.data.connection.request.ConnectionListRequest;
import com.trs.ai.moye.data.connection.response.DataConnectionCardResponse;
import com.trs.ai.moye.data.connection.response.DataConnectionResponse;
import com.trs.ai.moye.data.connection.response.DataConnectionStatisticsResponse;
import com.trs.ai.moye.data.connection.service.DataSourceConnectionService;
import com.trs.ai.moye.data.model.dao.DataConnectionDisplayMapper;
import com.trs.ai.moye.permission.service.DynamicUserNameService;
import com.trs.moye.base.data.connection.entity.DataConnection;
import com.trs.moye.base.data.connection.enums.DataSourceCategory;
import com.trs.moye.base.data.model.entity.DataSourceConfig;
import com.trs.moye.base.data.source.dao.DataSourceConfigMapper;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * 数据源服务层
 *
 * <AUTHOR>
 */
@RequiredArgsConstructor
@Service
public class DataSourceConnectionServiceImpl implements DataSourceConnectionService {

    @Resource
    private DataConnectionDisplayMapper dataConnectionDisplayMapper;

    @Resource
    private DataSourceConfigMapper dataSourceConfigMapper;


    /**
     * 获取统计信息
     *
     * @return 统计信息
     */
    @Override
    public DataConnectionStatisticsResponse getStatistics() {
        return dataConnectionDisplayMapper.countStatistics(true);
    }


    @Override
    public List<DataConnectionCardResponse> getCardList(ConnectionListRequest request) {
        List<DataConnection> dataConnections =
            dataConnectionDisplayMapper.selectConnections(request, true);
        DynamicUserNameService dynamicUserNameService = BeanUtil.getBean(DynamicUserNameService.class);
        Map<DataSourceCategory, List<DataConnectionResponse>> map = dataConnections.stream()
            .map(dataConnection -> new DataConnectionResponse(dataConnection, dynamicUserNameService))
            .collect(Collectors.groupingBy(DataConnectionResponse::getCategory));
        return map.entrySet().stream()
            .map(entry -> new DataConnectionCardResponse(entry.getKey().getLabel(), entry.getValue()))
            .toList();
    }


    @Override
    public Integer getConnectionUsedCount(Integer id) {
        List<DataSourceConfig> dataSourceList = dataSourceConfigMapper.selectByConnectionId(id);
        return Objects.nonNull(dataSourceList) ? dataSourceList.size() : ZERO;

    }
}
