package com.trs.ai.moye.monitor.entity;

import java.time.LocalDateTime;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 基础组件监控统计
 *
 * <AUTHOR>
 * @since 2025/6/10
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class BasicComponentDetectionStatistics {

    /**
     * id
     */
    private Long id;

    /**
     * 组件名称
     */
    private String componentName;

    /**
     * 组件类型
     */
    private String componentType;

    /**
     * 异常次数
     */
    private long errorCount;

    /**
     * 检测次数
     */
    private long detectionCount;

    /**
     * 最近一次检测状态
     */
    private int lastStatus;

    /**
     * 最近一次异常时间
     */
    private LocalDateTime lastErrorTime;

    /**
     * 最近一次检测时间
     */
    private LocalDateTime lastDetectionTime;

    /**
     * 最近一次异常信息
     */
    private String lastErrorMessage;


}
