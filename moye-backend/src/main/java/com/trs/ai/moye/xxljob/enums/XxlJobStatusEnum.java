package com.trs.ai.moye.xxljob.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-09-04 14:26
 */
@AllArgsConstructor
@Getter
public enum XxlJobStatusEnum {

    START(1, "启动"), STOP(0, "停止")
    ;

    private final int code;

    private final String label;

    /**
     * 根据code获取枚举
     *
     * @param code code
     * @return XxlJobStatusEnum
     */
    public static XxlJobStatusEnum fromCode(int code){
        for (XxlJobStatusEnum value : values()) {
            if (value.code == code){
                return value;
            }
        }
        return null;
    }
}
