package com.trs.ai.moye.data.standard.request;

import com.trs.moye.base.data.standard.enums.MetaDataStandardTypeEnum;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2024/10/9
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
public class MetaDataStandardRequest {

    private Integer id;

    /**
     * 中文名称
     */
    @NotEmpty
    @Size(max = 50, message = "中文名称长度不能超过50")
    private String zhName;

    /**
     * 英文名称
     */
    @NotEmpty
    @Size(max = 50, message = "英文名称长度不能超过50")
    private String enName;

    /**
     * 元数据标准的类型，枚举值
     */
    private MetaDataStandardTypeEnum type;

    /**
     * 描述
     */
    @Size(max = 1000, message = "描述长度不能超过1000")
    private String description;

    /**
     * 元数据分类目录
     */
    private Integer catalogId;

    /**
     * vid类型
     */
    @NotEmpty(message = "vid类型不能为空")
    private String vidType;

}
