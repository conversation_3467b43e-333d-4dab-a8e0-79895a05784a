package com.trs.ai.moye.monitor.response.statistics;

import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 2021/5/19 14:44
 **/
@Getter
@Setter
@ToString
@AllArgsConstructor
@NoArgsConstructor
public class ProcessSegmentationStatistics {

    /**
     * 时间
     */
    private Long dateTime;

    /**
     * 处理的量
     */
    private Long processed;

    /**
     * 根据时间获取处理量
     *
     * @param dataTime 时间
     * @param old      旧数据
     * @return 处理量
     */
    public static ProcessSegmentationStatistics getProcessSegmentationStatisticsByDataTime(Long dataTime,
        List<ProcessSegmentationStatistics> old) {
        ProcessSegmentationStatistics timeSharingStatistics = new ProcessSegmentationStatistics();
        timeSharingStatistics.setDateTime(dataTime);
        for (ProcessSegmentationStatistics statistics : old) {
            if (statistics.getDateTime().equals(dataTime / 1000)) {
                timeSharingStatistics.setProcessed(statistics.getProcessed());
                break;
            } else {
                timeSharingStatistics.setProcessed(0L);
            }
        }
        return timeSharingStatistics;
    }
}
