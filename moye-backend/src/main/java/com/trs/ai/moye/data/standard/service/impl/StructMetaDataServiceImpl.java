package com.trs.ai.moye.data.standard.service.impl;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.trs.moye.base.common.response.PageResponse;
import com.trs.ai.moye.data.standard.dao.StructMetaDataCatalogMapper;
import com.trs.ai.moye.data.standard.dao.StructMetaDataFieldMapper;
import com.trs.ai.moye.data.standard.dao.StructMetaDataMapper;
import com.trs.ai.moye.data.standard.entity.StructMetaData;
import com.trs.ai.moye.data.standard.entity.StructMetaDataCatalog;
import com.trs.ai.moye.data.standard.entity.StructMetaDataField;
import com.trs.ai.moye.data.standard.enums.MetaDataStandardCategoryTypeEnum;
import com.trs.ai.moye.data.standard.request.CheckStructCatalogNameDuplicateParam;
import com.trs.ai.moye.data.standard.request.CheckStructMetaDataFieldNameDuplicateParam;
import com.trs.ai.moye.data.standard.request.CheckStructMetaDataNameDuplicateParam;
import com.trs.ai.moye.data.standard.request.StructMetaDataCategoryRequest;
import com.trs.ai.moye.data.standard.request.StructMetaDataFieldRequest;
import com.trs.ai.moye.data.standard.request.StructMetaDataListRequest;
import com.trs.ai.moye.data.standard.request.StructMetaDataRequest;
import com.trs.ai.moye.data.standard.response.StructMetaDataCatalogTree;
import com.trs.ai.moye.data.standard.response.StructMetaDataCategoryDetailResponse;
import com.trs.ai.moye.data.standard.response.StructMetaDataDetailResponse;
import com.trs.ai.moye.data.standard.response.StructMetaDataFieldResponse;
import com.trs.ai.moye.data.standard.service.StructMetaDataService;
import com.trs.ai.moye.permission.service.DynamicUserNameService;
import com.trs.moye.base.common.exception.BizException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Objects;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * Implementation class for structured metadata service operations.
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025/5/12 14:27
 */
@Service
@Slf4j
public class StructMetaDataServiceImpl implements StructMetaDataService {

    @Resource
    private StructMetaDataMapper structMetaDataMapper;

    @Resource
    private StructMetaDataFieldMapper structMetaDataFieldMapper;

    @Resource
    private StructMetaDataCatalogMapper structMetaDataCatalogMapper;

    @Resource
    private DynamicUserNameService dynamicUserNameService;

    /**
     * Creates a new metadata catalog/directory. Validates that the catalog name is not duplicate and the parent catalog
     * exists.
     *
     * @param request The request object containing catalog creation parameters
     * @return The ID of the newly created catalog
     * <AUTHOR>
     * @since 2025/5/12 14:27
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer addCatalog(StructMetaDataCategoryRequest request) {
        // 校验名称是否重复
        if (structMetaDataCatalogMapper.countByNameAndExcludeId(request.getName(), null, request.getPid()) > 0) {
            throw new BizException("目录名称已存在");
        }
        // 校验父级目录是否存在
        if (!Objects.equals(request.getPid(), 0) && Objects.isNull(
            structMetaDataCatalogMapper.selectById(request.getPid()))) {
            throw new BizException("父级目录不存在");
        }
        StructMetaDataCatalog catalog = new StructMetaDataCatalog();
        catalog.setName(request.getName());
        catalog.setPid(request.getPid());
        catalog.setDescription(request.getDescription());
        structMetaDataCatalogMapper.insert(catalog);
        return catalog.getId();
    }

    /**
     * Updates an existing metadata catalog/directory. Validates that the catalog exists and the new name is not
     * duplicate.
     *
     * @param request The request object containing catalog update parameters
     * <AUTHOR>
     * @since 2025/5/12 14:27
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateCatalog(StructMetaDataCategoryRequest request) {
        // 校验目录是否存在
        StructMetaDataCatalog catalog = structMetaDataCatalogMapper.selectById(request.getId());
        if (Objects.isNull(catalog)) {
            throw new BizException("目录不存在");
        }
        // 校验名称是否重复
        if (structMetaDataCatalogMapper.countByNameAndExcludeId(request.getName(), request.getId(), request.getPid())
            > 0) {
            throw new BizException("目录名称已存在");
        }
        // 校验父级目录是否存在
        if (!Objects.equals(request.getPid(), 0) && Objects.isNull(
            structMetaDataCatalogMapper.selectById(request.getPid()))) {
            throw new BizException("父级目录不存在");
        }
        // 校验是否为自己的子目录
        if (isChildCatalog(request.getId(), request.getPid())) {
            throw new BizException("不能选择自己或自己的子目录作为父级目录");
        }
        catalog.setName(request.getName());
        catalog.setPid(request.getPid());
        catalog.setDescription(request.getDescription());
        structMetaDataCatalogMapper.updateById(catalog);
    }

    /**
     * Deletes a metadata catalog/directory by its ID. Validates that the catalog exists and has no child catalogs or
     * metadata entries.
     *
     * @param id The ID of the catalog to delete
     * <AUTHOR>
     * @since 2025/5/12 14:27
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteCatalog(Integer id) {
        // 校验目录是否存在
        StructMetaDataCatalog catalog = structMetaDataCatalogMapper.selectById(id);
        if (Objects.isNull(catalog)) {
            throw new BizException("目录不存在");
        }
        // 校验是否有子目录
        if (structMetaDataCatalogMapper.countByPid(id) > 0) {
            throw new BizException("存在子目录，不能删除");
        }
        // 校验是否有元数据
        if (structMetaDataMapper.countByCatalogId(id) > 0) {
            throw new BizException("目录下存在元数据，不能删除");
        }
        structMetaDataCatalogMapper.deleteById(id);
    }

    /**
     * Retrieves detailed information about a metadata catalog/directory.
     *
     * @param id The ID of the catalog to retrieve
     * @return Detailed information about the catalog
     * <AUTHOR>
     * @since 2025/5/12 14:27
     */
    @Override
    public StructMetaDataCategoryDetailResponse getCatalogDetail(Integer id) {
        StructMetaDataCatalog catalog = structMetaDataCatalogMapper.selectById(id);
        if (Objects.isNull(catalog)) {
            throw new BizException("目录不存在");
        }
        return StructMetaDataCategoryDetailResponse.from(catalog, dynamicUserNameService);
    }

    /**
     * Retrieves the complete catalog tree structure. Builds a hierarchical tree of catalogs and their associated
     * metadata entries.
     *
     * @return List of catalog tree nodes representing the hierarchy
     * <AUTHOR>
     * @since 2025/5/12 14:27
     */
    @Override
    public List<StructMetaDataCatalogTree> getCatalogTree() {
        // 查询所有目录
        List<StructMetaDataCatalog> catalogs = structMetaDataCatalogMapper.selectList(null);
        // 查询所有元数据
        List<StructMetaData> metaDataList = structMetaDataMapper.selectList(null);
        return buildTree(catalogs, metaDataList);
    }

    /**
     * Checks if a catalog name already exists to prevent duplicates.
     *
     * @param param The parameters for checking catalog name duplication
     * @return true if the name is duplicate, false otherwise
     * <AUTHOR>
     * @since 2025/5/12 14:27
     */
    @Override
    public Boolean checkCatalogNameDuplicate(CheckStructCatalogNameDuplicateParam param) {
        return structMetaDataCatalogMapper.countByNameAndExcludeId(param.getName(), param.getId(), param.getPid()) > 0;
    }

    /**
     * 判断是否为子目录
     *
     * @param id  目录ID
     * @param pid 父级ID
     * @return true:是 false:否
     */
    private boolean isChildCatalog(Integer id, Integer pid) {
        if (Objects.equals(id, pid)) {
            return true;
        }
        List<StructMetaDataCatalog> catalogs = structMetaDataCatalogMapper.selectList(null);
        return isChildCatalogRecursive(id, pid, catalogs);
    }

    private boolean isChildCatalogRecursive(Integer id, Integer targetPid, List<StructMetaDataCatalog> catalogs) {
        for (StructMetaDataCatalog catalog : catalogs) {
            if (Objects.equals(catalog.getId(), targetPid)) {
                if (Objects.equals(catalog.getPid(), id)) {
                    return true;
                }
                if (!Objects.equals(catalog.getPid(), 0)) {
                    return isChildCatalogRecursive(id, catalog.getPid(), catalogs);
                }
            }
        }
        return false;
    }

    /**
     * 构建目录树
     *
     * @param catalogs     目录列表
     * @param metaDataList 元数据列表
     * @return 目录树
     */
    private List<StructMetaDataCatalogTree> buildTree(List<StructMetaDataCatalog> catalogs,
        List<StructMetaData> metaDataList) {
        List<StructMetaDataCatalogTree> trees = new ArrayList<>();
        HashMap<Integer, List<StructMetaDataCatalogTree>> childrenMap = new HashMap<>();

        // 转换目录为树节点并按父ID分组
        for (StructMetaDataCatalog catalog : catalogs) {
            StructMetaDataCatalogTree tree = StructMetaDataCatalogTree.fromCatalog(catalog);
            if (Objects.equals(catalog.getPid(), 0)) {
                trees.add(tree);
            } else {
                childrenMap.computeIfAbsent(catalog.getPid(), k -> new ArrayList<>()).add(tree);
            }
        }

        // 转换元数据为树节点并按目录ID分组
        for (StructMetaData metaData : metaDataList) {
            StructMetaDataCatalogTree tree = StructMetaDataCatalogTree.fromMetaData(metaData);
            childrenMap.computeIfAbsent(metaData.getCatalogId(), k -> new ArrayList<>()).add(tree);
        }

        // 递归设置子节点
        for (StructMetaDataCatalogTree tree : trees) {
            setChildren(tree, childrenMap);
        }

        return trees;
    }

    /**
     * Helper method to set children nodes in the catalog tree structure. Recursively sets children for each catalog
     * node, sorting them by type and name.
     *
     * @param tree        The parent tree node to set children for
     * @param childrenMap Map of parent IDs to their child nodes
     * <AUTHOR>
     * @since 2025/5/12 14:27
     */
    private void setChildren(StructMetaDataCatalogTree tree,
        HashMap<Integer, List<StructMetaDataCatalogTree>> childrenMap) {
        List<StructMetaDataCatalogTree> children = childrenMap.get(tree.getId());
        if (Objects.nonNull(children)) {
            // 对子节点进行排序：目录在前，元数据在后，同类型按名称排序
            children.sort((a, b) -> {
                if (Objects.equals(a.getType(), b.getType())) {
                    return a.getName().compareTo(b.getName());
                }
                return a.getType().compareTo(b.getType());
            });
            tree.setChildren(children);
            // 递归处理所有目录类型的子节点
            children.stream()
                .filter(child -> MetaDataStandardCategoryTypeEnum.METADATA_CATEGORY.equals(child.getType()))
                .forEach(child -> setChildren(child, childrenMap));
        }
    }

    /**
     * Creates a new structured metadata entry. Validates that the catalog exists and the metadata name is not
     * duplicate.
     *
     * @param request The request object containing metadata creation parameters
     * @return The ID of the newly created metadata entry
     * <AUTHOR>
     * @since 2025/5/12 14:27
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer addStructMetaData(StructMetaDataRequest request) {
        // 校验目录是否存在
        StructMetaDataCatalog catalog = structMetaDataCatalogMapper.selectById(request.getCatalogId());
        if (Objects.isNull(catalog)) {
            throw new BizException("目录不存在");
        }
        // 校验英文名称是否重复
        if (structMetaDataMapper.countByEnNameAndCatalogId(request.getEnName(), request.getCatalogId()) > 0) {
            throw new BizException("英文名称已存在");
        }
        StructMetaData structMetaData = new StructMetaData();
        structMetaData.setZhName(request.getZhName());
        structMetaData.setEnName(request.getEnName());
        structMetaData.setDescription(request.getDescription());
        structMetaData.setCatalogId(request.getCatalogId());
        structMetaDataMapper.insert(structMetaData);
        return structMetaData.getId();
    }

    /**
     * Updates an existing structured metadata entry. Validates that the metadata exists, the catalog exists, and the
     * new name is not duplicate.
     *
     * @param request The request object containing metadata update parameters
     * <AUTHOR>
     * @since 2025/5/12 14:27
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateStructMetaData(StructMetaDataRequest request) {
        // 校验元数据是否存在
        StructMetaData structMetaData = structMetaDataMapper.selectById(request.getId());
        if (Objects.isNull(structMetaData)) {
            throw new BizException("元数据不存在");
        }
        // 校验目录是否存在
        StructMetaDataCatalog catalog = structMetaDataCatalogMapper.selectById(request.getCatalogId());
        if (Objects.isNull(catalog)) {
            throw new BizException("目录不存在");
        }
        // 校验英文名称是否重复
        if (structMetaDataMapper.countByEnNameAndExcludeId(request.getEnName(), request.getId()) > 0) {
            throw new BizException("英文名称已存在");
        }
        structMetaData.setZhName(request.getZhName());
        structMetaData.setEnName(request.getEnName());
        structMetaData.setDescription(request.getDescription());
        structMetaData.setCatalogId(request.getCatalogId());
        structMetaDataMapper.updateById(structMetaData);
    }

    /**
     * Deletes a structured metadata entry by its ID. Validates that the metadata exists and has no associated fields.
     *
     * @param id The ID of the metadata entry to delete
     * <AUTHOR>
     * @since 2025/5/12 14:27
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteStructMetaData(Integer id) {
        // 校验元数据是否存在
        StructMetaData structMetaData = structMetaDataMapper.selectById(id);
        if (Objects.isNull(structMetaData)) {
            throw new BizException("元数据不存在");
        }
        // 校验是否有字段
        if (structMetaDataFieldMapper.countByStructMetaDataId(id) > 0) {
            throw new BizException("元数据下存在字段，不能删除");
        }
        structMetaDataMapper.deleteById(id);
    }

    /**
     * Checks if a metadata name already exists to prevent duplicates.
     *
     * @param param The parameters for checking metadata name duplication
     * @return true if the name is duplicate, false otherwise
     * <AUTHOR>
     * @since 2025/5/12 14:27
     */
    @Override
    public Boolean checkStructMetaDataNameDuplicate(CheckStructMetaDataNameDuplicateParam param) {
        return
            structMetaDataMapper.countByEnNameOrZhNameAndExcludeId(param.getEnName(), param.getZhName(), param.getId(),
                param.getCatalogId()) > 0;

    }

    /**
     * Creates a new metadata field. Validates that the metadata exists and the field name is not duplicate.
     *
     * @param request The request object containing field creation parameters
     * @return The ID of the newly created field
     * <AUTHOR>
     * @since 2025/5/12 14:27
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer addField(StructMetaDataFieldRequest request) {
        // 校验元数据是否存在
        StructMetaData structMetaData = structMetaDataMapper.selectById(request.getStructMetaDataId());
        if (Objects.isNull(structMetaData)) {
            throw new BizException("元数据不存在");
        }
        // 校验英文名称是否重复
        if (countByEnNameAndExcludeId(request.getEnName(), request.getEnName(), request.getId(),
            request.getStructMetaDataId())) {
            throw new BizException("英文/中文名称已存在");
        }
        StructMetaDataField field = new StructMetaDataField();
        field.setZhName(request.getZhName());
        field.setEnName(request.getEnName());
        field.setType(request.getType());
        field.setTypeName(request.getTypeName());
        field.setMultiValue(request.getIsMultiValue());
        field.setNullable(request.getIsNullable());
        field.setDescription(request.getDescription());
        field.setStructMetaDataId(request.getStructMetaDataId());
        field.setStatistic(request.getIsStatistic());
        field.setBuiltIn(request.getIsBuiltIn());
        field.setAdvanceConfig(request.getAdvanceConfig());
        field.setDefaultValue(request.getDefaultValue());
        structMetaDataFieldMapper.insert(field);
        return field.getId();
    }

    /**
     * Updates an existing metadata field. Validates that the field exists and the new name is not duplicate.
     *
     * @param request The request object containing field update parameters
     * <AUTHOR>
     * @since 2025/5/12 14:27
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateField(StructMetaDataFieldRequest request) {
        // 校验字段是否存在
        StructMetaDataField field = structMetaDataFieldMapper.selectById(request.getId());
        if (Objects.isNull(field)) {
            throw new BizException("字段不存在");
        }
        // 校验元数据是否存在
        StructMetaData structMetaData = structMetaDataMapper.selectById(request.getStructMetaDataId());
        if (Objects.isNull(structMetaData)) {
            throw new BizException("元数据不存在");
        }
        // 校验英文名称是否重复
        if (countByEnNameAndExcludeId(request.getEnName(), request.getEnName(), request.getId(),
            request.getStructMetaDataId())) {
            throw new BizException("英文/中文名称已存在");
        }
        field.setZhName(request.getZhName());
        field.setEnName(request.getEnName());
        field.setType(request.getType());
        field.setTypeName(request.getTypeName());
        field.setMultiValue(request.getIsMultiValue());
        field.setNullable(request.getIsNullable());
        field.setDescription(request.getDescription());
        field.setStructMetaDataId(request.getStructMetaDataId());
        field.setStatistic(request.getIsStatistic());
        field.setBuiltIn(request.getIsBuiltIn());
        field.setAdvanceConfig(request.getAdvanceConfig());
        field.setDefaultValue(request.getDefaultValue());
        structMetaDataFieldMapper.updateById(field);
    }

    /**
     * Deletes a metadata field by its ID. Validates that the field exists.
     *
     * @param id The ID of the field to delete
     * <AUTHOR>
     * @since 2025/5/12 14:27
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteField(Integer id) {
        // 校验字段是否存在
        StructMetaDataField field = structMetaDataFieldMapper.selectById(id);
        if (Objects.isNull(field)) {
            throw new BizException("字段不存在");
        }
        structMetaDataFieldMapper.deleteById(id);
    }

    /**
     * Retrieves all fields associated with a specific metadata entry.
     *
     * @param request The ID of the metadata entry whose fields to retrieve
     * @return List of fields associated with the metadata entry
     * <AUTHOR>
     * @since 2025/5/12 14:27
     */
    @Override
    public PageResponse<StructMetaDataFieldResponse> getFieldList(StructMetaDataListRequest request) {
        Page<StructMetaDataField> page = structMetaDataFieldMapper.selectPageList(request,
            request.getPageParams().toPage());
        return PageResponse.of(page).toNewPageResult(standardField -> {
            return StructMetaDataFieldResponse.from(standardField, dynamicUserNameService);
        });
    }

    /**
     * Checks if a field name already exists within a metadata entry to prevent duplicates.
     *
     * @param param The parameters for checking field name duplication
     * @return true if the name is duplicate, false otherwise
     * <AUTHOR>
     * @since 2025/5/12 14:27
     */
    @Override
    public Boolean checkFieldNameDuplicate(CheckStructMetaDataFieldNameDuplicateParam param) {
        return countByEnNameAndExcludeId(param.getEnName(), param.getZhName(), param.getId(),
            param.getStructMetaDataId());

    }

    private Boolean countByEnNameAndExcludeId(String enName, String zhNam, Integer excludeId,
        Integer structMetaDataId) {
        return structMetaDataFieldMapper.countByEnNameOrZhNameAndExcludeId(enName, zhNam, excludeId, structMetaDataId)
            > 0;
    }

    @Override
    public StructMetaDataDetailResponse getStructMetaData(Integer id) {
        return StructMetaDataDetailResponse.from(structMetaDataMapper.selectById(id), dynamicUserNameService);
    }
}

