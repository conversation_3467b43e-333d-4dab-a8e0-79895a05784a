package com.trs.ai.moye.common.dao;

import com.trs.ai.moye.common.response.DictResponse;
import com.trs.moye.base.knowledgebase.enums.KnowledgeBaseType;
import java.util.Collection;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 字典数据访问层
 */
@Mapper
public interface DictMapper {

    /**
     * 获取知识库字典列表
     *
     * @param baseTypes 知识库类型列表
     * @return 字典列表
     */
    List<DictResponse> getKnowledgeBaseDictList(@Param("baseTypes") Collection<KnowledgeBaseType> baseTypes);
}
