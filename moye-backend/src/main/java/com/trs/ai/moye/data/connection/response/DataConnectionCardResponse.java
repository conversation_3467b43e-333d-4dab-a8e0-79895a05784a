package com.trs.ai.moye.data.connection.response;

import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;

/**
 * 数据源卡片
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-04-12 15:08
 */
@Data
@AllArgsConstructor
public class DataConnectionCardResponse {

    /**
     * 数据源类别名称
     */
    private String dataSourceCategory;

    /**
     * 数据源列表
     */
    private List<DataConnectionResponse> connectionList;
}
