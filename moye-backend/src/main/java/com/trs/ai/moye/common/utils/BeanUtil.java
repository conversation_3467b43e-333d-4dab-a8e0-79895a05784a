package com.trs.ai.moye.common.utils;

import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.function.Predicate;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.BeanWrapper;
import org.springframework.beans.BeanWrapperImpl;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

/**
 * Bean 工具类
 *
 * <AUTHOR>
 * @since 2021/3/5
 */
@Slf4j
@Component
public class BeanUtil implements ApplicationContextAware {

    private static ApplicationContext applicationContext;

    /**
     * 获取applicationContext
     *
     * @return 上下文对象
     */
    public static synchronized ApplicationContext getApplicationContext() {
        return applicationContext;
    }

    @Override
    public synchronized void setApplicationContext(@NonNull ApplicationContext applicationContext) {
        log.info("BeanUtil 初始化：{}", applicationContext.getId());
        BeanUtil.applicationContext = applicationContext;
    }

    /**
     * 上下文刷新事件回调函数，将新的上下文内容覆盖掉原来的应用程序上下文
     *
     * @param event 上下文刷新事件
     */
    @EventListener
    public synchronized void handleContextRefresh(ContextRefreshedEvent event) {
        BeanUtil.applicationContext = event.getApplicationContext();
    }

    /**
     * 通过name获取 Bean
     *
     * @param name bean的名称
     * @return bean对象
     */
    public static synchronized Object getBean(String name) {
        return getApplicationContext().getBean(name);
    }

    /**
     * 通过name获取 Bean
     *
     * @param <T>   返回值泛型
     * @param clazz 参数类型
     * @return 泛型对象
     */
    public static synchronized <T> T getBean(Class<T> clazz) {
        return getApplicationContext().getBean(clazz);
    }

    /**
     * 获取Bean对象
     *
     * @param <T>    返回值泛型
     * @param clazz  参数类型
     * @param filter 参数类型
     * @return 泛型对象
     */
    public static synchronized <T> Optional<T> getBean(Class<T> clazz, Predicate<T> filter) {
        return getBeans(clazz).stream().filter(filter).findAny();
    }

    /**
     * 获取Bean对象
     *
     * @param <T>       返回值泛型
     * @param clazz     参数类型
     * @param filter    参数类型
     * @param mustExist 参数类型
     * @return 泛型对象
     */
    public static synchronized <T> Optional<T> getBean(Class<T> clazz, Predicate<T> filter, boolean mustExist) {
        Optional<T> opt = getBean(clazz, filter);
        if (mustExist && opt.isEmpty()) {
            throw new RuntimeException("class[" + clazz.getName() + "]不存在对应实现类");
        }
        return opt;
    }

    /**
     * 获取Bean对象列表
     *
     * @param <T>   返回值泛型
     * @param clazz 参数类型
     * @return 泛型对象
     */
    public static synchronized <T> Collection<T> getBeans(Class<T> clazz) {
        return getApplicationContext().getBeansOfType(clazz).values();
    }

    private static String[] getNullPropertyNames(Object source) {
        final BeanWrapper src = new BeanWrapperImpl(source);
        java.beans.PropertyDescriptor[] pds = src.getPropertyDescriptors();

        Set<String> emptyNames = new HashSet<>();
        for (java.beans.PropertyDescriptor pd : pds) {
            Object srcValue = src.getPropertyValue(pd.getName());
            if (srcValue == null) {
                emptyNames.add(pd.getName());
            }
        }
        String[] result = new String[emptyNames.size()];
        return emptyNames.toArray(result);
    }

    /**
     * 根据属性名设置属性值
     *
     * @param obj       对象
     * @param fieldName 属性名
     * @param value     属性值
     */
    public static void setProperty(Object obj, String fieldName, Object value) {
        try {
            Class<?> clazz = obj.getClass();
            Field field = clazz.getDeclaredField(fieldName);
            Method method = clazz.getMethod("set" + fieldName.substring(0, 1).toUpperCase() + fieldName.substring(1),
                field.getType());
            method.invoke(obj, value);
        } catch (Exception e) {
            // 异常处理
            log.error(String.format("类:%s未找到对应的set方法:%s", obj.getClass().getName(), fieldName));
        }
    }

    /**
     * 判断对象所有属性是否都为null
     *
     * @param obj 对象
     * @return 是否都为null
     */
    public static boolean isAllPropertyNull(Object obj) {
        try {
            Class<?> clazz = obj.getClass();
            Field[] fields = clazz.getDeclaredFields();
            for (Field field : fields) {
                field.setAccessible(true);
                if (field.get(obj) != null) {
                    return false;
                }
            }
            return true;
        } catch (Exception e) {
            // 异常处理
            log.error(String.format("failed to check all property nullable of class [%s]", obj.getClass().getName()),
                e);
            return false;
        }
    }

    /**
     * 获取所有字段，包括父类的字段
     *
     * @param clazz 类
     * @return {@link List }<{@link Field }>
     * <AUTHOR>
     * @since 2024/09/30 18:04:20
     */
    public static List<Field> getAllFields(Class<?> clazz) {
        List<Field> fields = new ArrayList<>();
        while (clazz != null && !clazz.equals(Object.class)) {
            // 获取当前类的所有声明字段
            Field[] declaredFields = clazz.getDeclaredFields();
            Collections.addAll(fields, declaredFields);
            // 继续获取父类的字段
            clazz = clazz.getSuperclass();
        }
        return fields;
    }

    /**
     * 拷贝继承的属性
     *
     * @param source 源对象
     * @param target 目标对象
     * @param <S> 源对象泛型
     * @param <T> 目标对象泛型
     */
    public static <S, T extends S> void copyInheritProperties(S source, T target) {
        BeanUtils.copyProperties(source, target);
    }
}
