package com.trs.ai.moye.data.standard.request;

import com.trs.moye.base.common.enums.FieldType;
import com.trs.moye.base.common.request.BasePageRequest;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 字段分页列表请求参数
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-09-19 16:53
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
public class FieldPageListRequest extends BasePageRequest {

    /**
     * 目录id
     */
    private Integer categoryId;

    /**
     * 字段类型
     */
    private FieldType type;

    /**
     * 关键词
     */
    private String keyword;

    /**
     * 数据建模id
     */
    private Integer dataModelId;
}
