package com.trs.ai.moye.monitor.service.impl;

import com.trs.ai.moye.monitor.dao.MonitorOdsCutoffMapper;
import com.trs.ai.moye.monitor.dao.MonitorOdsLagMapper;
import com.trs.ai.moye.monitor.entity.CutoffPeriod;
import com.trs.ai.moye.monitor.entity.LagPeriod;
import com.trs.ai.moye.monitor.request.DataModelMonitorRequest;
import com.trs.ai.moye.monitor.service.PeriodDataQueryService;
import com.trs.moye.base.common.enums.ModelLayer;
import com.trs.moye.base.monitor.entity.MonitorOdsCutoff;
import com.trs.moye.base.monitor.entity.MonitorOdsLag;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @since 2025/3/13
 **/

@Slf4j
@Service
public class PeriodDataQueryServiceImpl implements PeriodDataQueryService {

    @Resource
    private MonitorOdsLagMapper odsLagMapper;

    @Resource
    private MonitorOdsCutoffMapper monitorOdsCutoffMapper;

    @Override
    public List<LagPeriod> detectLagPeriods(Integer dataModelId, DataModelMonitorRequest request) {
        List<MonitorOdsLag> monitorOdsLags = odsLagMapper.selectMonitorOdsLagRecords(dataModelId,
            request);
        LagPeriodDetector lagPeriodDetector = new LagPeriodDetector();
        return lagPeriodDetector.processRecords(monitorOdsLags);
    }

    @Override
    public List<LagPeriod> getTopLagSix(ModelLayer layer) {
        List<MonitorOdsLag> monitorOdsLags = odsLagMapper.selectByLayer(layer);
        // 根据dataModelId分组
        Map<Integer, List<MonitorOdsLag>> groupLagRecords = monitorOdsLags.stream()
            .collect(Collectors.groupingBy(MonitorOdsLag::getDataModelId));

        LagPeriodDetector lagPeriodDetector = new LagPeriodDetector();

        HashMap<Integer, LagPeriod> groupLagPeriod = new HashMap<>();
        for (Map.Entry<Integer, List<MonitorOdsLag>> entry : groupLagRecords.entrySet()) {
            List<LagPeriod> lagPeriods = lagPeriodDetector.processRecords(entry.getValue());
            if (lagPeriods.isEmpty()) {
                continue;
            }
            groupLagPeriod.put(entry.getKey(), lagPeriods.get(lagPeriods.size()-1));
        }
        // 根据LagPeriod的checkCount排序groupLagPeriod，DESC
        List<LagPeriod> sortedLagPeriods = groupLagPeriod.values().stream()
            .sorted(Comparator.comparing(LagPeriod::getCheckCount).reversed())
            .toList();
        // 取前6个
        return sortedLagPeriods.stream().limit(6).toList();



    }

    @Override
    public List<CutoffPeriod> detectCutoffPeriods(Integer dataModelId, DataModelMonitorRequest request) {
        List<MonitorOdsCutoff> monitorOdsCutoffs = monitorOdsCutoffMapper.selectMonitorDetailList(dataModelId, request,
            request.getTimeRangeParams());
        CutoffPeriodDetector cutoffPeriodDetector = new CutoffPeriodDetector();
        return cutoffPeriodDetector.processRecords(monitorOdsCutoffs);
    }
}
