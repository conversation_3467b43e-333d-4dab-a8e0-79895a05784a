package com.trs.ai.moye.out.controller;

import com.trs.ai.moye.out.request.PasswordRequest;
import com.trs.moye.base.common.response.PageResponse;
import com.trs.ai.moye.data.service.dto.TableStructDto;
import com.trs.ai.moye.data.service.entity.DataServiceField;
import com.trs.ai.moye.data.service.enums.DbTypeMap;
import com.trs.ai.moye.data.service.request.DataServiceOutRequest;
import com.trs.ai.moye.data.service.request.SqlRequest;
import com.trs.ai.moye.data.service.request.ability.DataServiceInvokeRequest;
import com.trs.ai.moye.data.service.response.AccessibleDbTypeResponse;
import com.trs.ai.moye.data.service.response.DataServiceOutResponse;
import com.trs.ai.moye.data.service.response.NativeStatementExecuteResult;
import com.trs.ai.moye.data.service.response.StorageInfoListResponse;
import com.trs.ai.moye.data.service.service.DataServiceOutService;
import com.trs.ai.moye.out.request.NebulaDataTransferRequest;
import com.trs.ai.moye.out.request.NebulaSearchRequest;
import com.trs.ai.moye.out.request.NebulaStorageRequest;
import com.trs.ai.moye.out.service.OutApiService;
import com.trs.moye.base.common.annotaion.ApiLogSign;
import com.trs.moye.base.common.annotaion.DataServiceLogSign;
import com.trs.moye.base.common.log.api.ApiLogTracerUtils;
import com.trs.moye.base.common.utils.AesUtils;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Set;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.validation.constraints.NotBlank;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 给一体化平台提供的接口
 *
 * <AUTHOR>
 * @since 2024/10/11
 **/
@RestController
@RequestMapping("out")
@Slf4j
public class OutApiController {

    @Resource
    private OutApiService outService;
    @Resource
    private DataServiceOutService dataServiceOutService;

    /**
     * 获取支持连表JOIN查询的库类型
     * <a href="http://192.168.210.40:3001/project/4220/interface/api/161970">【外部-获取数据库类型】</a>
     *
     * @return {@link List}<{@link AccessibleDbTypeResponse}>
     */
    @GetMapping("/service/dbType")
    public List<AccessibleDbTypeResponse> getAccessibleDbType() {
        // 过滤出 DataSourceCategory.DATA_BASE 类型的库，然后返回库的名称
        DbTypeMap[] values = DbTypeMap.values();
        return Arrays.stream(values)
            .map(dbTypeMap -> new AccessibleDbTypeResponse(dbTypeMap.getId(), dbTypeMap.getDb().getLabel()))
            .toList();
    }

    /**
     * 根据库类型获取对应的库信息
     * <a href="http://192.168.210.40:3001/project/4220/interface/api/161975">【外部-根据库类型获取库信息】</a>
     *
     * @param dbTypes 库类型
     * @return {@link List}<{@link StorageInfoListResponse}>
     */
    @PostMapping("/service/dbInfo")
    public List<StorageInfoListResponse> getDbInfoByType(@RequestBody Set<Integer> dbTypes) {
        return outService.getDbInfoByType(new ArrayList<>(dbTypes));
    }
    /**
     * 解密接口
     * <a href="http://192.168.210.40:3001/project/4220/interface/api/168827">【外部-根据库类型获取库信息】</a>
     *
     * @param encryptedPassword 加密后的密码
     * @return 解密后的明文密码
     */
    @PostMapping("/service/decrypt")
    public String decrypt(@RequestBody @Validated PasswordRequest encryptedPassword) {
        return AesUtils.decrypt(encryptedPassword.getEncryptedPassword(), AesUtils.DEFAULT_KEY);
    }
    /**
     * 获取一个库里所有表对用的表结构信息
     * <a href="http://192.168.210.40:3001/project/4220/interface/api/161980">【外部-获取表结构】</a>
     *
     * @param dbId dbInfo的Id
     * @return {@link List}<{@link TableStructDto}>
     */
    @GetMapping("/service/getTableStruct/{dbId}")
    public List<TableStructDto> getTableStructs(@PathVariable("dbId") Integer dbId) {
        return outService.getTableStructInfos(dbId);
    }

    /**
     * 直接执行传入的SQL，然后返回数据 yapi：<a href="http://192.168.210.40:3001/project/4220/interface/api/161995">【外部-执行传入的SQL】</a>
     *
     * @param request {@link SqlRequest}
     * @return 分页后的数据 {@link NativeStatementExecuteResult}
     */
    @ApiLogSign(apiName = "执行任意SQL")
    @PostMapping("service/execute/sql")
    public NativeStatementExecuteResult<Map<String, Object>> doExecuteSQL(@RequestBody @Validated SqlRequest request) {
        ApiLogTracerUtils.start("开始进入http接口", "SQL语句", request.getSql());
        ApiLogTracerUtils.appendInputDetail("数据连接id", request.getDbId());
        return outService.executeSQL(request);
    }

    /**
     * 调用服务
     *
     * @param code               code
     * @param request            请求
     * @param httpServletRequest HTTP Servlet 请求
     * @return {@link PageResponse }<{@link Map }<{@link String }, {@link Object }>>
     * <AUTHOR>
     * @since 2024/10/10 10:37:41
     */
    @DataServiceLogSign
    @PostMapping("/service/{code}/search-data")
    public PageResponse<Map<String, Object>> invokeService(
        @PathVariable("code") @NotBlank(message = "应用场景code不能为空") String code,
        @RequestBody DataServiceInvokeRequest request,
        HttpServletRequest httpServletRequest) {
        ApiLogTracerUtils.start("请求参数解析", "原始请求参数", request);
        log.info("请求参数request：{}, 请求参数code: {}", request, code);
        String requestUri = httpServletRequest.getRequestURI();
        return outService.getDataListByCode(code, request, requestUri);
    }

    /**
     * 获取所有的业务
     * <a href="http://192.168.210.40:3001/project/4220/interface/api/157740">yapi 【数据服务-外部】获取所有的目录，以及目录下的业务分类</a>
     *
     * @return {@link List}<{@link DataServiceOutResponse}>
     */
    @GetMapping("/service/business/tree")
    public List<DataServiceOutResponse> getBusinessServiceTypeAll() {
        return dataServiceOutService.businessTree();
    }

    /**
     * 根据业务id(businessId)获取数据服务列表
     * <a href="http://192.168.210.40:3001/project/4220/interface/api/157745">yapi 【数据服务-外部】服务查询列表</a>
     *
     * @param request 请求参数
     * @param id      业务id
     * @return {@link PageResponse}<{@link DataServiceOutResponse}>
     */
    @PostMapping("/type/{id}/list")
    public PageResponse<DataServiceOutResponse> dataApplyList(@RequestBody DataServiceOutRequest request,
        @PathVariable("id") Integer id) {
        // 根据前端请求参数进行校验，如果为true，分页查询该目录下所有服务类型的所有数据，否则分页查询某一条服务类型的所有数据
        return dataServiceOutService.dataServiceListAllByCatalogOut(request, id);
    }


    /**
     * 根据数据服务的code获取数据服务的返回字段
     * <a href="http://192.168.210.40:3001/project/4220/interface/api/164400">yapi 【数据服务-外部】获取指定数据服务的返回字段</a>
     *
     * @param code 数据服务的code
     * @return 返回字段 {@link List}<{@link DataServiceField}>
     */
    @GetMapping("/service/fields/{code}")
    public List<DataServiceField> getRuleApplyFields(@PathVariable("code") String code) {
        return dataServiceOutService.getDataServiceReturnFieldByCode(code);
    }

    /**
     * nebula图数据库存储
     *
     * @param request 请求参数
     */
    @PostMapping("/nebula/storage")
    public void nebulaStorage(@RequestBody @Validated NebulaStorageRequest request) {
        outService.nebulaStorage(request);
    }


    /**
     * nebula图数据库查询
     *
     * @param request 请求
     * @return {@link List }<{@link Map }<{@link String }, {@link Object }>>
     * <AUTHOR>
     * @since 2024/12/23 10:47:40
     */
    @PostMapping("/nebula/execute")
    public List<Map<String, Object>> nebulaExecute(@RequestBody @Validated NebulaSearchRequest request) {
        return outService.nebulaExecute(request);
    }

    /**
     * Nebula 数据同步
     *
     * @param request 请求
     * @return boolean
     * <AUTHOR>
     * @since 2024/12/24 15:42:22
     */
    @PostMapping("/nebula/data-transfer")
    public boolean nebulaDataTransfer(@RequestBody @Validated NebulaDataTransferRequest request) {
        return outService.nebulaDataTransfer(request);
    }

}
