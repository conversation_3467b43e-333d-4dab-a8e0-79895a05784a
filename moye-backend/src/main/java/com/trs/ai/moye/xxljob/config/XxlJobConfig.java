package com.trs.ai.moye.xxljob.config;

import com.trs.moye.base.common.utils.JsonUtils;
import com.trs.moye.base.common.utils.ReflectUtils;
import com.xxl.job.core.executor.impl.XxlJobSpringExecutor;
import javax.annotation.PostConstruct;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * xxl-job config
 *
 * <AUTHOR> 2017-04-28
 */
@Slf4j
@Data
@Configuration
public class XxlJobConfig {

    public static final String JOB_ADD_URL = "/jobinfo/add";

    public static final String JOB_UPDATE_URL = "/jobinfo/update";

    public static final String JOB_STOP_URL = "/jobinfo/stop";

    public static final String JOB_START_URL = "/jobinfo/start";

    public static final String JOB_INFO_URL = "/jobinfo/pageList";

    public static final String JOB_REMOVE_URL = "/jobinfo/remove";

    public static final String JOB_EXECUTE_URL = "/jobinfo/trigger";

    public static final String JOB_NEXT_TRIGGER_TIME = "/jobinfo/nextTriggerTime";

    public static final String GROUP_INFO_URL = "/jobgroup/pageList";

    public static final String GROUP_ADD_URL = "/jobgroup/save";

    public static final String APP_JOB_INFO_LIST_URL = "/moye/job/app-job-info-list";

    public static final String MOYE_JOB_INFO_URL = "/moye/job/info";

    public static final String JOB_LOGIN_URL = "/login";

    public static final String TOKEN_KEY = "XXL_JOB_LOGIN_IDENTITY";

    public static final String ACCESS_TOKEN = "7493f8cd-2310-45b6-a16a-3b46e07856a6";

    public static final String APP_NAME = "moye-backend";

    /**
     * 应用标题，不是xxl-job原生配置，自动注册执行器到xxl-job调度中心功能使用
     */
    public static final String APP_TITLE = "莫邪后端";

    @Value("${xxl.job.admin.addresses}")
    private String adminAddresses;

    @Value("${xxl.job.executor.login.user-name:admin}")
    private String userName;

    @Value("${xxl.job.executor.login.password:123456}")
    private String password;

    @Value("${xxl.job.executor.enable-auto-register:true}")
    private boolean isEnableAutoRegister;

    @Value("${xxl.job.executor.log-path:./logs/xxl-job}")
    private String logPath;

    @Value("${xxl.job.executor.log-retention-days:1}")
    private int logRetentionDays;

    @PostConstruct
    private void printConfigInfo() {
        if (isEnableAutoRegister) {
            log.info(">>>>>>>>>>> xxl-job 配置信息：{}", JsonUtils.toJsonString(ReflectUtils.toMap(this)));
        } else {
            log.warn(">>>>>>>>>>> xxl-job 未开启执行器自动注册功能，当前应用【{}】不注册到调度中心，使用当前应用JobHandler的任务将执行失败", APP_TITLE);
        }
    }

    /**
     * 向容器注册xxl-job执行器
     *
     * @return xxl-job executor
     */
    @ConditionalOnProperty(value = "xxl.job.executor.enable-auto-register", havingValue = "true")
    @Bean
    public XxlJobSpringExecutor xxlJobExecutor() {
        log.info("准备注册xxl-job执行器到配置中心：{}", adminAddresses);
        XxlJobSpringExecutor xxlJobSpringExecutor = new XxlJobSpringExecutor();
        xxlJobSpringExecutor.setAdminAddresses(adminAddresses);
        xxlJobSpringExecutor.setAppname(APP_NAME);
        xxlJobSpringExecutor.setAddress(null);
        xxlJobSpringExecutor.setIp(null);
        xxlJobSpringExecutor.setPort(0);
        xxlJobSpringExecutor.setAccessToken(ACCESS_TOKEN);
        xxlJobSpringExecutor.setLogPath(logPath);
        xxlJobSpringExecutor.setLogRetentionDays(logRetentionDays);
        return xxlJobSpringExecutor;
    }
}