package com.trs.ai.moye.data.standard.entity;

import com.trs.ai.moye.data.standard.enums.MetaDataStandardCategoryTypeEnum;
import com.trs.moye.base.data.standard.entity.MetaDataStandard;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2024/10/9
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
public class MetaDataStandardCatalogTree {

    /**
     * 节点ID
     */
    private Integer id;

    /**
     * 节点名称
     */
    private String name;

    private String enName;

    /**
     * 父节点ID
     */
    private Integer pid;
    /**
     * 描述
     */
    private String description;
    /**
     * 类型
     */
    private MetaDataStandardCategoryTypeEnum type = MetaDataStandardCategoryTypeEnum.METADATA_CATEGORY;


    private List<MetaDataStandardCatalogTree> children;

    /**
     * 是否禁用,前端使用
     */
    private Boolean disable;

    /**
     * 检查并禁用如果只有目录
     */
    public void checkAndDisableIfOnlyDirectory() {
        // 如果当前节点不是目录类型，直接将disable设为true
        if (this.type != MetaDataStandardCategoryTypeEnum.METADATA_CATEGORY) {
            this.disable = true;
            return;
        }
        // 如果当前节点是目录类型，继续检查子节点
        this.disable = false;  // 先假定此节点可以启用
        if (children != null && !children.isEmpty()) {
            for (MetaDataStandardCatalogTree child : children) {
                child.checkAndDisableIfOnlyDirectory();  // 递归检查子节点
                if (Boolean.TRUE.equals(child.disable)) {
                    this.disable = true;  // 如果任何一个子节点被禁用，则禁用当前节点
                }
            }
        }
    }

    /**
     * 转换为响应对象
     *
     * @param graphicsMetaDataStandard 元数据标准
     * @return 响应对象
     */
    public static MetaDataStandardCatalogTree from(GraphicsMetaDataStandard graphicsMetaDataStandard) {
        MetaDataStandardCatalogTree metaDataStandardCatalogTree = new MetaDataStandardCatalogTree();
        metaDataStandardCatalogTree.setId(graphicsMetaDataStandard.getId());
        metaDataStandardCatalogTree.setName(graphicsMetaDataStandard.getZhName());
        metaDataStandardCatalogTree.setPid(graphicsMetaDataStandard.getMetaDataStandardCatalogId());
        metaDataStandardCatalogTree.setDescription(graphicsMetaDataStandard.getDescription());
        metaDataStandardCatalogTree.setType(MetaDataStandardCategoryTypeEnum.STANDARD);
        return metaDataStandardCatalogTree;
    }

    /**
     * 转换为响应对象
     *
     * @param metaDataStandard 元数据标准
     * @return 响应对象
     */
    public static MetaDataStandardCatalogTree from(MetaDataStandard metaDataStandard) {
        MetaDataStandardCatalogTree metaDataStandardCatalogTree = new MetaDataStandardCatalogTree();
        metaDataStandardCatalogTree.setId(metaDataStandard.getId());
        metaDataStandardCatalogTree.setName(metaDataStandard.getZhName());
        metaDataStandardCatalogTree.setEnName(metaDataStandard.getEnName());
        metaDataStandardCatalogTree.setPid(metaDataStandard.getMetaDataStandardCatalogId());
        metaDataStandardCatalogTree.setDescription(metaDataStandard.getDescription());
        metaDataStandardCatalogTree.setType(MetaDataStandardCategoryTypeEnum.STANDARD);
        return metaDataStandardCatalogTree;
    }

}
