package com.trs.ai.moye.data.model.response;

import com.trs.ai.moye.data.model.annotation.ProcessMonitorDataValueSerializer;
import com.trs.ai.moye.data.model.entity.BatchTaskTracer;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * BatchTaskTracerResponse
 *
 * <AUTHOR>
 * @since 2025/2/12 11:10
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BatchTaskTracerResponse {

    /**
     * 主键，自增
     */
    private String id;

    /**
     * 节点
     */
    private String node;

    /**
     * 执行id，主键，定时任务触发时产生一条记录
     */
    private String executeId;

    /**
     * 开始时间
     */
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    private LocalDateTime endTime;

    /**
     * 处理时长，单位毫秒
     */
    private Long processTime;

    /**
     * 是否异常：
     */
    private Integer isError;

    /**
     * 异常信息
     */
    private String errorMsg;

    /**
     * 详情
     */
    private List<ProcessMonitorDataValue> detail;

    /**
     * 算子输出数据 字段名
     */
    private List<String> fields;

    /**
     * 数据
     */
    private List<Map<String, Object>> sampleData;

    /**
     * 数据量
     */
    private Long dataCount;

    /**
     * 数据量
     */
    private Long preProcessDataCount;

    /**
     * 构建response
     *
     * @param batchTaskTracer {@link BatchTaskTracer}
     * @return {@link BatchTaskTracerResponse}
     */
    public static BatchTaskTracerResponse fromBatchTaskTracer(BatchTaskTracer batchTaskTracer) {
        BatchTaskTracerDetailResponse detail = BatchTaskTracerDetailResponse.fromBatchTaskTracer(batchTaskTracer);
        List<ProcessMonitorDataValue> serializedDetail = ProcessMonitorDataValueSerializer.serialize(detail);
        return BatchTaskTracerResponse.builder()
            .id(String.valueOf(batchTaskTracer.getId()))
            .node(batchTaskTracer.getArrangedName())
            .executeId(batchTaskTracer.getExecuteId())
            .startTime(batchTaskTracer.getStartTime())
            .endTime(batchTaskTracer.getEndTime())
            .processTime(batchTaskTracer.getProcessTime())
            .isError(batchTaskTracer.getIsError())
            .errorMsg(batchTaskTracer.getErrorMsg())
            .detail(serializedDetail)
            .fields(batchTaskTracer.getFields())
            .sampleData(batchTaskTracer.getSampleData())
            .dataCount(batchTaskTracer.getDataCount())
            .preProcessDataCount(batchTaskTracer.getPreProcessDataCount())
            .build();
    }
}
