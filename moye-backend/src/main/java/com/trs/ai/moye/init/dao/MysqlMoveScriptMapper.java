package com.trs.ai.moye.init.dao;

import com.trs.moye.base.common.entity.TwoTuple;
import java.util.List;
import java.util.Set;
import org.apache.ibatis.annotations.Mapper;

/**
 * <AUTHOR>
 * @since 2025-03-18 17:08
 */
@Mapper
public interface MysqlMoveScriptMapper {

    /**
     * 获取数据迁移监控配置信息
     *
     * @return 数据迁移监控配置信息
     */
    List<TwoTuple<Integer, String>> getDataProcessOldMonitorConfigList();

    /**
     * 查询所有配置了数据监控的数据建模id
     *
     * @return 数据建模id集合
     */
    Set<Integer> getExistDataProcessMonitorDataModelIds();
}
