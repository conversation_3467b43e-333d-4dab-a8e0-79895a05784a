package com.trs.ai.moye.data.standard.response;

import com.trs.moye.base.data.standard.entity.MetaDataStandard;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2024/10/12
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
public class MetaDataStandardResponse {

    private Integer id;
    /**
     * 中文名称
     */
    private String zhName;
    /**
     * 英文名称
     */
    private String enName;
    /**
     * /** 描述
     */
    private String description;

    /**
     * 将MetaDataStandard转换为MetaDataStandardResponse
     *
     * @param metaDataStandard 元数据标准
     * @return MetaDataStandardResponse
     */
    public static MetaDataStandardResponse from(MetaDataStandard metaDataStandard) {
        MetaDataStandardResponse metaDataStandardResponse = new MetaDataStandardResponse();
        metaDataStandardResponse.setId(metaDataStandard.getId());
        metaDataStandardResponse.setZhName(metaDataStandard.getZhName());
        metaDataStandardResponse.setEnName(metaDataStandard.getEnName());
        metaDataStandardResponse.setDescription(metaDataStandard.getDescription());
        return metaDataStandardResponse;
    }
}
