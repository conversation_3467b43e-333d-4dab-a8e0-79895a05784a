package com.trs.ai.moye.data.model.dto.arrangement;

import com.trs.ai.moye.data.operator.domain.request.ActionParamRequest;
import com.trs.moye.ability.entity.ArrangeOperatorConfig;
import com.trs.moye.ability.entity.OperatorStyleInfo;
import com.trs.moye.ability.enums.ArrangeNodeType;
import com.trs.moye.base.common.enums.ModelLayer;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;
import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 算子和算子编排新增请求
 *
 * <AUTHOR>
 * @since 2024/5/21 14:39
 */
@Data
public class ArrangeOperatorDTO {

    /**
     * 算子id
     */
    @NotNull(message = "算子id不能为空")
    private Integer id;
    /**
     * 算子名称
     */
    @NotBlank(message = "算子名称不能为空!")
    private String zhName;
    /**
     * 算子英文名
     */
    @NotBlank(message = "算子英文名不能为空!")
    private String enName;
    /**
     * 编排中算子名称
     */
    private String arrangedName;
    /**
     * 算子描述
     */
    private String abilityDesc;
    /**
     * 前端回显使用
     */
    private String iconName;
    /**
     * 节点描述
     */
    private String desc;
    /**
     * 是否启用
     */
    private Boolean enabled;
    /**
     * 算子编排位置信息
     */
    private OperatorStyleInfo style;
    /**
     * 算子连接节点
     */
    private List<Long> targetIds;
    /**
     * 用于前端绘制算子编排的id
     */
    private Long displayId;
    /**
     * 顺序
     */
    @NotNull(message = "order不能为空")
    private Integer order;
    /**
     * 算子执行配置
     */
    private ArrangeOperatorConfig config;
    /**
     * 前端回显使用 table/operator
     */
    @NotNull(message = "type不能为空")
    private ArrangeNodeType type;
    /**
     * 表类型
     */
    private ModelLayer tableType;
    /**
     * 条件表达式
     */
    private OperatorConditionDTO compConditions;
    /**
     * 参数
     */
    @Valid
    @NotNull(message = "params不能为空")
    private Params params;
    /**
     * 要素表存储id，用于生成存储算子
     */
    private List<Integer> storageIds;

    /**
     * 数据拆分开关(用于es的向量字段)
     */
    private Boolean flatMapSwitch = false;

    /**
     * 数据拆分字段(用于es的向量字段)
     */
    private List<String> flatMapFields;


    /**
     * 参数信息
     */
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class Params {

        /**
         * 输入参数
         */
        @Valid
        private List<ActionParamRequest> input;
        /**
         * 输出参数
         */
        @Valid
        private List<ActionParamRequest> output;


        /**
         * 获取params中用到的字段英文名
         *
         * @return 字段名列表
         */
        public List<String> getFieldNames() {
            List<String> inputFieldNames = getFieldNames(getInput());
            List<String> outputFieldNames = getFieldNames(getOutput());
            inputFieldNames.addAll(outputFieldNames);
            return inputFieldNames;
        }

        private List<String> getFieldNames(List<ActionParamRequest> params) {
            if (params == null || params.isEmpty()) {
                return new ArrayList<>();
            }
            return params.stream()
                .flatMap(request -> request.getParamDetail().stream()
                    .flatMap(detail -> detail.getPropertyValue().stream().map(ArrangeFieldDTO::getEnName)))
                .collect(Collectors.toCollection(ArrayList::new));
        }
    }

}
