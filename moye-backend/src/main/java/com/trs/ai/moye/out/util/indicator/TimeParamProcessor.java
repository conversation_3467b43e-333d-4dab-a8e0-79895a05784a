package com.trs.ai.moye.out.util.indicator;

import com.trs.ai.moye.out.entity.OutIndicatorTimeRangeParams;
import com.trs.moye.base.data.indicator.entity.DailyStatRange;
import com.trs.moye.base.data.indicator.entity.IndicatorPeriodConfig;
import com.trs.moye.base.data.indicator.entity.IntervalStatRange;
import com.trs.moye.base.data.indicator.entity.MonthlyStatRange;
import com.trs.moye.base.data.indicator.entity.StatRange;
import com.trs.moye.base.data.indicator.entity.StatisticPeriod;
import com.trs.moye.base.data.indicator.entity.WeeklyStatRange;
import com.trs.moye.base.data.indicator.utils.IndicatorPeriodConverter;
import java.time.LocalDateTime;
import java.util.Objects;

/**
 * Time Param 处理器
 *
 * <AUTHOR>
 * @since 2025/05/30 17:12:34
 */
public interface TimeParamProcessor {

    /**
     * 计算触发时间
     *
     * @return {@link LocalDateTime }
     * <AUTHOR>
     * @since 2025/05/30 18:27:40
     */
    LocalDateTime calculateTriggerTime();

    /**
     * 计算周期
     *
     * @return {@link StatisticPeriod }
     * <AUTHOR>
     * @since 2025/05/30 18:27:42
     */
    default StatisticPeriod calculatePeriod() {
        return IndicatorPeriodConverter.calculateStatPeriod(
            calculateTriggerTime(), getConfig()
        );
    }

    /**
     * 获取配置
     *
     * @return {@link IndicatorPeriodConfig }
     * <AUTHOR>
     * @since 2025/05/30 18:27:49
     */
    IndicatorPeriodConfig<?> getConfig();

    /**
     * 创建处理器
     *
     * @param param  参数
     * @param config 配置
     * @return {@link TimeParamProcessor }
     * <AUTHOR>
     * @since 2025/05/30 18:27:55
     */
    static TimeParamProcessor createProcessor(
        OutIndicatorTimeRangeParams param,
        IndicatorPeriodConfig<?> config
    ) {
        Objects.requireNonNull(param, "时间参数不能为空");
        Objects.requireNonNull(config, "配置不能为空");

        StatRange statRange = config.getStatRange();

        if (statRange instanceof DailyStatRange) {
            return new DailyParamProcessor(param, config);
        } else if (statRange instanceof WeeklyStatRange) {
            return new WeeklyParamProcessor(param, config);
        } else if (statRange instanceof MonthlyStatRange) {
            return new MonthlyParamProcessor(param, config);
        } else if (statRange instanceof IntervalStatRange interval) {
            int duration = interval.getDurationMonths();

            if (duration == 3) {
                return new QuarterlyParamProcessor(param, config);
            } else if (duration == 6) {
                return new SemiannualParamProcessor(param, config);
            } else if (duration == 12) {
                return new YearlyParamProcessor(param, config);
            }
        }
        throw new IllegalArgumentException("不支持的统计周期类型: " +
            statRange.getClass().getSimpleName());
    }
}
