package com.trs.ai.moye.common.mybatis;

import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;
import com.trs.ai.moye.backstage.entity.User;
import java.time.LocalDateTime;
import java.util.Objects;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.reflection.MetaObject;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;

/**
 * mybatis 审计 填充数据的创建以及更新时间用户信息
 *
 * <AUTHOR>
 */
@Slf4j
public class MybatisMetaObjectHandler implements MetaObjectHandler {

    /**
     * 创建时间
     */
    private static final String FIELD_CREATE_TIME = "createTime";

    /**
     * 创建用户主键
     */
    private static final String FIELD_CREATE_USER_ID = "createBy";

    /**
     * 更新时间
     */
    private static final String FIELD_UPDATE_TIME = "updateTime";

    /**
     * 更新用户主键
     */
    private static final String FIELD_UPDATE_USER_ID = "updateBy";

    @Override
    public void insertFill(MetaObject metaObject) {
        this.strictInsertFill(metaObject, FIELD_UPDATE_TIME, LocalDateTime.class, LocalDateTime.now());
        this.strictInsertFill(metaObject, FIELD_CREATE_TIME, LocalDateTime.class, LocalDateTime.now());
        User currentUser = getCurrentUser();
        if (Objects.isNull(currentUser)) {
            return;
        }
        this.strictInsertFill(metaObject, FIELD_CREATE_USER_ID, Integer.class, currentUser.getId());
        this.strictInsertFill(metaObject, FIELD_UPDATE_USER_ID, Integer.class, currentUser.getId());
    }

    @Override
    public void updateFill(MetaObject metaObject) {
        this.setFieldValByName(FIELD_UPDATE_TIME, LocalDateTime.now(), metaObject);
        User currentUser = getCurrentUser();
        if (Objects.isNull(currentUser)) {
            return;
        }
        this.setFieldValByName(FIELD_UPDATE_USER_ID, currentUser.getId(), metaObject);
    }

    /**
     * 获取当前登录用户；如果未登录则返回null
     *
     * @return User
     */
    private User getCurrentUser() {
        try {
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            return (User) authentication.getPrincipal();
        } catch (Exception e) {
            return null;
        }
    }
}
