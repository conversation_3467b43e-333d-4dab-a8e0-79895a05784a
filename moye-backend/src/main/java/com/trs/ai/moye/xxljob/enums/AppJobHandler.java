package com.trs.ai.moye.xxljob.enums;

import com.trs.moye.base.common.enums.ModelLayer;
import com.trs.moye.base.monitor.enums.DataProcessMonitorType;
import com.trs.moye.base.monitor.enums.MonitorConfigType;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 各应用的jobHandler
 *
 * <AUTHOR>
 * @since 2024-10-14 09:49
 */
@Getter
@AllArgsConstructor
public enum AppJobHandler {

    /**
     * 存储引擎-建模任务
     */
    STORAGE_ENGINE_MODEL_TASK(XxlJobAppEnum.STORAGE_ENGINE
        , "storage-engine-start-task"
        , "存储引擎-建模任务"
        , true),
    BATCH_ENGINE_MODEL_TASK(XxlJobAppEnum.BATCH_ENGINE
        , "moye-batch-engine-job"
        , "批处理引擎-建模任务"
        , false),
    MONITOR_CENTER_LAG(XxlJobAppEnum.MONITOR_CENTER
        , "collect-lag-monitor"
        , "监控中心-积压监控"
        , true),
    MONITOR_CENTER_FLUCTUATION(XxlJobAppEnum.MONITOR_CENTER
        , "collect-fluctuation-monitor"
        , "监控中心-波动监控"
        , true),
    MONITOR_CENTER_CUTOFF(XxlJobAppEnum.MONITOR_CENTER
        , "collect-cutoff-monitor"
        , "监控中心-断流监控"
        , true),
    MONITOR_CENTER_TASK_EXECUTION_TIME(XxlJobAppEnum.MONITOR_CENTER
        , "collect-task-execution-time-monitor"
        , "监控中心-批任务执行时间监控"
        , false),
    DWD_STORAGE_ENGINE_MODEL_TASK(XxlJobAppEnum.STORAGE_ENGINE
        , "dwd-storage-engine-start-task"
        , "存储引擎-要素库流处理任务"
        , false),
    REALTIME_DATA_PROCESS_MONITOR(XxlJobAppEnum.MONITOR_CENTER
        , "collect-realtime-consume-monitor"
        , "实时要素库-实时数据处理监控"
        , false),
    REALTIME_DATA_STORAGE_MONITOR(XxlJobAppEnum.MONITOR_CENTER
        , "collect-realtime-data-storage-monitor-record"
        , "实时要素库-实时数据存储监控"
        , false),
    STREAM_PROCESS_MONITOR_COLLECT(XxlJobAppEnum.MONITOR_CENTER
        , "collect-stream_process_monitor-record"
        , "采集流处理监控记录"
        , false),
    DATA_CONNECTION_MONITOR(XxlJobAppEnum.MOYE_BACKEND
        , "moye-backend-data-connection-monitor"
        , "moye后端-数据连接监控"
        , false),
    BASIC_COMPONENT_MONITOR(XxlJobAppEnum.MOYE_BACKEND
        , "moye-backend-basic-component-monitor"
        , "moye后端-基础组件监控"
        , false);

    /**
     * 应用
     */
    private final XxlJobAppEnum app;

    /**
     * xxl-job的JobHandler名称
     */
    private final String jobHandler;

    /**
     * 应用标签：备注用
     */
    private final String label;

    /**
     * 是否支持自动删除不存在的job
     */
    private final boolean isSupportAutoDeleteNotExistJob;


    /**
     * 贴源表返回STORAGE_ENGINE_MODEL_TASK；要素表/主题表/专题表返回DWD_STORAGE_ENGINE_MODEL_TASK
     *
     * @param layer {@link ModelLayer}
     * @return {@link AppJobHandler}
     */
    public static AppJobHandler getRealTimeJobHandler(ModelLayer layer) {
        if (layer.equals(ModelLayer.ODS)) {
            return STORAGE_ENGINE_MODEL_TASK;
        } else {
            return DWD_STORAGE_ENGINE_MODEL_TASK;
        }
    }

    /**
     * 获取监控中心的jobHandler
     *
     * @param monitorConfigType 监控配置类型
     * @return {@link AppJobHandler}
     */
    public static AppJobHandler getHandler(MonitorConfigType monitorConfigType) {
        return switch (monitorConfigType) {
            case LAG -> MONITOR_CENTER_LAG;
            case FLUCTUATION -> MONITOR_CENTER_FLUCTUATION;
            case CUTOFF -> MONITOR_CENTER_CUTOFF;
            case TASK_EXECUTION_TIME -> MONITOR_CENTER_TASK_EXECUTION_TIME;
        };
    }

    /**
     * 获取监控中心的jobHandler
     *
     * @param monitorConfigType 监控配置类型
     * @return {@link AppJobHandler}
     */
    public static AppJobHandler getHandler(DataProcessMonitorType monitorConfigType) {
        return switch (monitorConfigType) {
            case DATA_PROCESS -> REALTIME_DATA_PROCESS_MONITOR;
            case DATA_STORAGE -> REALTIME_DATA_STORAGE_MONITOR;
            case STREAM_PROCESS_V1 -> STREAM_PROCESS_MONITOR_COLLECT;
            case ODS_COMPLETE_LOG -> null;
        };
    }
}
