package com.trs.ai.moye.data.operator.domain.response;

import com.trs.ai.moye.data.operator.domain.vo.AbilityVersionDTO;
import java.util.List;
import lombok.Data;
import com.trs.ai.moye.data.ability.response.AbilityResponse;

/**
 * 算子版本检查结果
 *
 * <AUTHOR>
 * @since 2024/10/15 16:36
 */
@Data
public class VersionCheckResponse {

    /**
     * 算子当前版本号
     */
    private String currentVersion;
    /**
     * 能力中心最新版本号
     */
    private String newestVersion;
    /**
     * 能力中心最新更新时间
     */
    private String updateTime;
    /**
     * 是否过期
     */
    private Boolean isExpired;
    /**
     * 是否需要更新
     */
    private boolean needUpdate;
    /**
     * 提示消息
     */
    private String message;
    /**
     * 最新能力信息
     */
    private AbilityResponse abilityInfo;
    /**
     * 参数更新信息
     */
    private List<AbilityParamUpdateResponse> parameterUpdate;


    public VersionCheckResponse(String currentVersion, AbilityVersionDTO versionInfo) {
        this.currentVersion = currentVersion;
        this.newestVersion = versionInfo.getVersion();
        this.needUpdate = !currentVersion.equals(newestVersion);
        this.updateTime = versionInfo.getUpdateTime();
        this.isExpired = false;
        this.message = needUpdate ? "算子当前版本不为最新版本，请及时更新" : "算子版本已为最新版本";
    }
}
