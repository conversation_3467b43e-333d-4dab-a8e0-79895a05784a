package com.trs.ai.moye.data.model.controller;

import com.trs.ai.moye.data.model.dao.DataModelViewInfoMapper;
import com.trs.ai.moye.data.model.entity.view.DataModelViewInfo;
import com.trs.ai.moye.data.model.request.view.CreateViewRequest;
import com.trs.ai.moye.data.model.response.CreateViewResponse;
import com.trs.ai.moye.data.model.response.ViewDataSourceResponse;
import com.trs.ai.moye.data.model.service.impl.ViewService;
import com.trs.moye.base.common.annotaion.OperateLogSign;
import com.trs.moye.base.common.entity.ModuleEnum;
import com.trs.moye.base.common.exception.BizException;
import com.trs.moye.base.data.connection.dao.DataConnectionMapper;
import com.trs.moye.base.data.connection.entity.DataConnection;
import com.trs.moye.base.data.model.dao.DataModelMapper;
import com.trs.moye.base.data.model.entity.DataModel;
import com.trs.moye.base.data.model.enums.CreateModeEnum;
import java.util.Objects;
import javax.annotation.Resource;
import javax.validation.Valid;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 数据建模-视图功能
 *
 * <AUTHOR>
 * @since 2024/11/7 15:57
 */
@Validated
@RestController
@RequestMapping("/data-model/view")
public class ViewController {

    @Resource
    private ViewService viewService;

    @Resource
    private DataModelMapper dataModelMapper;
    @Resource
    private DataModelViewInfoMapper dataModelViewInfoMapper;
    @Resource
    private DataConnectionMapper dataConnectionMapper;

    /**
     * 创建视图 <br>
     * <a href="https://yapi-192.trscd.com.cn/project/5419/interface/api/165430">YApi</a>
     *
     * @param request 请求体
     * @return 返回元数据id和分层信息
     */
    @OperateLogSign(module = ModuleEnum.DATA_MODELING, apiName = "创建视图")
    @PostMapping()
    public CreateViewResponse createView(@RequestBody @Valid CreateViewRequest request) throws BizException {
        request.checkFields();
        CreateViewResponse response = new CreateViewResponse();
        response.setModelLayer(request.getModelLayer());
        response.setId(viewService.createView(request));
        return response;
    }

    /**
     * 查询视图 数据源信息，即 数据存储点信息和联表信息<br>
     * <a href="https://yapi-192.trscd.com.cn/project/5419/interface/api/165440">YApi</a>
     *
     * @param dataModelId 数据建模id
     * @return 返回视图数据源信息
     */
    @GetMapping("/{dataModelId}/data-source")
    public ViewDataSourceResponse queryViewDataSource(@PathVariable Integer dataModelId) {
        DataModel dataModel = dataModelMapper.selectById(dataModelId);
        if (Objects.isNull(dataModel) || !CreateModeEnum.VIEW.equals(dataModel.getCreateMode())) {
            throw new BizException("id:" + dataModelId + " 不是视图");
        }
        DataModelViewInfo viewInfo = dataModelViewInfoMapper.selectByDataModelId(dataModelId);
        DataConnection dataConnection = dataConnectionMapper.selectById(viewInfo.getConnectionId());
        return new ViewDataSourceResponse(dataConnection, viewInfo.getTablesFields());
    }

}
