package com.trs.ai.moye.data.model.service;

import com.trs.ai.moye.data.model.request.imports.FieldDdlImportRequest;
import com.trs.ai.moye.data.model.request.imports.FieldJsonImportRequest;
import com.trs.ai.moye.data.model.response.FieldParseResponse;
import org.springframework.web.multipart.MultipartFile;

/**
 * <AUTHOR>
 * @since 2024-10-29 16:35
 */
public interface ImportService {

    /**
     * 解析json文件字段
     *
     * @param id      数据建模ID
     * @param request 请求参数
     * @return {@link FieldParseResponse }
     */
    FieldParseResponse parseJsonFields(Integer id, FieldJsonImportRequest request);

    /**
     * 解析excel文件字段
     *
     * @param id   数据建模ID
     * @param file excel文件
     * @return {@link FieldParseResponse }
     */
    FieldParseResponse parseExcelFields(Integer id, MultipartFile file);

    /**
     * 解析ddl文件字段
     *
     * @param id      数据建模ID
     * @param request 请求参数
     * @return {@link FieldParseResponse }
     */
    FieldParseResponse parseDdlFields(Integer id, FieldDdlImportRequest request);

}
