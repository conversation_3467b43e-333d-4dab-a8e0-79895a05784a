package com.trs.ai.moye.data.model.task.start;

import com.trs.ai.moye.data.model.dao.operator.OperatorPipelineMapper;
import com.trs.ai.moye.data.model.dto.ExecuteParamDTO;
import com.trs.ai.moye.etcd.service.EtcdService;
import com.trs.ai.moye.init.async.AsyncInitialize;
import com.trs.ai.moye.storageengine.service.StorageEngineService;
import com.trs.ai.moye.xxljob.XXLJobManager;
import com.trs.moye.base.common.enums.ModelLayer;
import com.trs.moye.base.common.utils.AssertUtils;
import com.trs.moye.base.data.execute.DatabaseExecuteParams;
import com.trs.moye.base.data.execute.ExecuteModeEnum;
import com.trs.moye.base.data.execute.ExecuteParams;
import com.trs.moye.base.data.execute.FTPExecuteParams;
import com.trs.moye.base.data.execute.KafkaExecuteParams;
import com.trs.moye.base.data.execute.RocketMQExecuteParams;
import com.trs.moye.base.data.model.dao.DataModelExecuteConfigMapper;
import com.trs.moye.base.data.model.dao.DataModelMapper;
import com.trs.moye.base.data.model.dao.DataModelScheduleConfigMapper;
import com.trs.moye.base.data.model.entity.BatchProcessSparkConfig;
import com.trs.moye.base.data.model.entity.DataModel;
import com.trs.moye.base.data.model.entity.DataModelExecuteConfig;
import com.trs.moye.base.data.model.entity.DataModelScheduleConfig;
import com.trs.moye.base.data.model.enums.ModelExecuteStatus;
import com.trs.moye.base.data.model.task.entity.DatasourceStartParams;
import com.trs.moye.base.data.schedule.ScheduleInfo;
import com.trs.moye.base.data.source.dao.DataSourceConfigMapper;
import com.trs.moye.base.data.source.enums.KafkaOffsetResetType;
import com.trs.moye.base.data.source.enums.RocketMQOffsetResetType;
import com.trs.moye.base.data.storage.IncrementInfo;
import java.util.Objects;
import javax.annotation.Resource;
import org.springframework.stereotype.Component;

/**
 * 任务启动器：启动数据建模任务
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-10-15 20:03
 */
@Component
public class TaskStart implements AsyncInitialize {

    @Resource
    private DataModelMapper dataModelMapper;

    @Resource
    private DataModelExecuteConfigMapper dataModelExecuteConfigMapper;

    @Resource
    private DataModelScheduleConfigMapper dataModelScheduleConfigMapper;

    @Resource
    private XXLJobManager xxlJobManager;

    @Resource
    private DataSourceConfigMapper dataSourceConfigMapper;

    @Resource
    private EtcdService etcdService;

    @Resource
    private StorageEngineService storageEngineService;

    @Resource
    private OperatorPipelineMapper operatorPipelineMapper;

    @Resource
    private StreamProcessHelper streamProcessHelper;

    /**
     * 启动任务
     *
     * @param id              模型id
     * @param executeParamDTO 执行参数
     */
    public synchronized void startTask(Integer id, ExecuteParamDTO executeParamDTO) {
        DataModel dataModel = dataModelMapper.getById(id);
        AssertUtils.notEmpty(dataModel, "主键为【%s】的数据建模不存在", id);
        ModelExecuteStatus oldExecuteStatus = dataModel.getExecuteStatus();
        AssertUtils.notEquals(oldExecuteStatus, ModelExecuteStatus.START, "【%s】已经是启动状态，无需再次启动",
            dataModel.getZhName());
        DataModelExecuteConfig executeConfig = dataModelExecuteConfigMapper.selectByDataModelId(id);
        AssertUtils.notEmpty(executeConfig, "【%s】数据建模未配置执行参数，无法启动", dataModel.getZhName());
        // 如果由暂停状态启动，则使用之前的执行参数
        ExecuteParams executeParams;
        BatchProcessSparkConfig sparkConfig;
        if (oldExecuteStatus == ModelExecuteStatus.PAUSE) {
            executeParams = executeConfig.getExecuteParams();
            // kafka和rocketmq类型，重启时默认从消费offset开始消费
            if (executeParams instanceof KafkaExecuteParams kafkaExecuteParams) {
                kafkaExecuteParams.setOffsetResetType(KafkaOffsetResetType.GROUP_OFFSETS);
            }
            if (executeParams instanceof RocketMQExecuteParams rocketMQExecuteParams) {
                rocketMQExecuteParams.setOffsetResetType(RocketMQOffsetResetType.CONSUME_FROM_GROUP_OFFSETS);
            }
            sparkConfig = executeConfig.getSparkConfig();
        } else {
            executeParams = executeParamDTO.getExecuteParams();
            sparkConfig = executeParamDTO.getSparkConfig();
        }
        // 修改执行配置
        executeConfig.setExecuteParams(executeParams);
        executeConfig.setSparkConfig(sparkConfig);
        if (Objects.nonNull(executeParams) && executeParams.isFileType()) {
            FTPExecuteParams ftpExecuteParams = (FTPExecuteParams) executeParams;
            String startTime = ftpExecuteParams.getStartTime();
            if (Objects.nonNull(executeConfig.getIncrementInfo())) {
                executeConfig.getIncrementInfo().setIncrementValue(startTime);
            }
        } else if (Objects.nonNull(executeParams) && executeParams.isDbType()) {
            DatabaseExecuteParams databaseExecuteParams = (DatabaseExecuteParams) executeParams;
            IncrementInfo incrementInfo = databaseExecuteParams.getIncrementInfo();
            if (Objects.nonNull(incrementInfo)) {
                executeConfig.setIncrementInfo(incrementInfo);
            }
        }
        // 修改启动状态
        dataModel.setExecuteStatus(ModelExecuteStatus.START);
        DataModelScheduleConfig scheduleConfig = dataModelScheduleConfigMapper.selectByDataModelId(id);
        AssertUtils.notEmpty(scheduleConfig, "%s【%s】未配置调度任务，无法启动", ModelLayer.ODS.getLabel(),
            dataModel.getZhName());
        // 启动调度任务
        ScheduleInfo scheduleInfo = scheduleConfig.getScheduleInfo();
        boolean isStreamProcess =
            scheduleInfo.getExecuteMode() == ExecuteModeEnum.REALTIME && dataModel.getLayer() == ModelLayer.DWD;
        if (isStreamProcess) {
            //前端用户自定义的MQ启动参数
            DatasourceStartParams mqStartParams = DatasourceStartParams.createMqStartParams(executeParams);
            startStreamProcessTask(dataModel, scheduleConfig, mqStartParams);
        } else {
            xxlJobManager.startJob(scheduleConfig.getXxlJobId());
        }
        dataModelExecuteConfigMapper.updateById(executeConfig);
        dataModelMapper.updateById(dataModel);
    }

    /**
     * 启动流处理任务
     *
     * @param dataModel      模型
     * @param scheduleConfig 调度配置
     * @param executeParams  执行参数
     */
    protected void startStreamProcessTask(DataModel dataModel, DataModelScheduleConfig scheduleConfig,
        DatasourceStartParams executeParams) {
        //TODO 不再判断是否有存储算子
        //将任务信息更新到etcd中
        streamProcessHelper.updateStreamTaskToEtcd(dataModel, executeParams);
        //2.若有则调用存储引擎接口创建topic, 每次调用存储引擎的接口都会判断是否已经存在若存在则不创建
        storageEngineService.createStorageTopic(dataModel.getId());
        //3.提交xxl-job, 调用的时候单独开一个接口去调用
        xxlJobManager.startJob(scheduleConfig.getXxlJobId());
    }


    /**
     * 停止任务
     *
     * @param id 模型id
     */
    public synchronized void stopTask(Integer id) {
        stopTask(id, ModelExecuteStatus.STOP);
    }

    /**
     * 停止任务
     *
     * @param id            模型id
     * @param executeStatus 执行状态（只能是停止或暂停）
     */
    private void stopTask(Integer id, ModelExecuteStatus executeStatus) {
        DataModel dataModel = dataModelMapper.getById(id);
        AssertUtils.notEquals(executeStatus, ModelExecuteStatus.START,
            "停止【%s】建模任务，任务状态不能是启动状态", dataModel.getZhName());
//        AssertUtils.equals(oldExecuteStatus, ModelExecuteStatus.START,
//            "【%s】任务不是启动状态，不允许停止或暂停", dataModel.getZhName());
        DataModelExecuteConfig executeConfig = dataModelExecuteConfigMapper.selectByDataModelId(id);
        AssertUtils.notEmpty(executeConfig, "【%s】数据建模未配置执行参数，无法停止或暂停任务", dataModel.getZhName());
        // 修改执行状态
        dataModel.setExecuteStatus(executeStatus);
        DataModelScheduleConfig scheduleConfig = dataModelScheduleConfigMapper.selectByDataModelId(id);
        AssertUtils.notEmpty(scheduleConfig, "%s【%s】未配置调度任务，无法启动", ModelLayer.ODS.getLabel(),
            dataModel.getZhName());
        ScheduleInfo scheduleInfo = scheduleConfig.getScheduleInfo();
        boolean isStreamProcess =
            scheduleInfo.getExecuteMode() == ExecuteModeEnum.REALTIME && dataModel.getLayer() == ModelLayer.DWD;
        if (isStreamProcess) {
            streamProcessHelper.updateStreamTaskToEtcd(dataModel, null);
        }
        // 停止调度任务
        xxlJobManager.stopJob(scheduleConfig.getXxlJobId());
        // 通知存储引擎停止任务
        storageEngineService.stopJob(id);
        // 更新数据库
        dataModelExecuteConfigMapper.updateById(executeConfig);
        dataModelMapper.updateById(dataModel);
    }

    /**
     * 暂停任务
     *
     * @param id 模型id
     */
    public synchronized void pauseOdsTask(Integer id) {
        stopTask(id, ModelExecuteStatus.PAUSE);
    }

    @Override
    public void initialize() {
        dataModelMapper.selectStartStreamProcessTask().forEach(
            dataModel -> streamProcessHelper.updateStreamTaskToEtcd(dataModel, null));
    }
}
