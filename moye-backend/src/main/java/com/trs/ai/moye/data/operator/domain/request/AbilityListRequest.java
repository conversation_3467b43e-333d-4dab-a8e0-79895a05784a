package com.trs.ai.moye.data.operator.domain.request;

import com.trs.moye.base.common.request.PageParams;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import lombok.Data;

/**
 * 能力中心请求参数
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023/6/18
 */

@Data
public class AbilityListRequest {

    /**
     * 能力中心-能力id
     */
    private Integer abilityId;

    /**
     * 应用id
     */
    private Integer appId;

    /**
     * 分页信息
     */
    @NotNull
    @Valid
    private PageParams pageParams;

    /**
     * 检索信息
     */
    private String abilityName;

}
