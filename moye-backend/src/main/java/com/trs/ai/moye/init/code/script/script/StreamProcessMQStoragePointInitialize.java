package com.trs.ai.moye.init.code.script.script;

import com.trs.ai.moye.common.dao.PrimaryLibraryCommonMapper;
import com.trs.ai.moye.common.utils.BizUtils;
import com.trs.ai.moye.common.utils.WatchUtils;
import com.trs.ai.moye.data.model.request.CreateTableRequests;
import com.trs.ai.moye.data.model.request.CreateTableRequests.CreateTableRequest;
import com.trs.ai.moye.data.model.response.CreateTableResponse;
import com.trs.ai.moye.data.model.service.DataModelService;
import com.trs.ai.moye.data.model.service.DwdModelService;
import com.trs.ai.moye.data.model.service.OdsModelService;
import com.trs.ai.moye.init.code.CodeInitialize;
import com.trs.ai.moye.init.utils.CodeInitializeRecordUtils;
import com.trs.moye.base.common.entity.AuditBaseEntity;
import com.trs.moye.base.common.exception.BizException;
import com.trs.moye.base.common.utils.AssertUtils;
import com.trs.moye.base.common.utils.ExceptionUtils;
import com.trs.moye.base.data.connection.dao.DataConnectionMapper;
import com.trs.moye.base.data.connection.entity.DataConnection;
import com.trs.moye.base.data.connection.enums.ConnectionType;
import com.trs.moye.base.data.connection.enums.DataSourceCategory;
import com.trs.moye.base.data.execute.ExecuteParams;
import com.trs.moye.base.data.execute.KafkaExecuteParams;
import com.trs.moye.base.data.execute.RocketMQExecuteParams;
import com.trs.moye.base.data.model.dao.DataModelExecuteConfigMapper;
import com.trs.moye.base.data.model.dao.DataModelMapper;
import com.trs.moye.base.data.model.entity.DataModel;
import com.trs.moye.base.data.model.entity.DataModelExecuteConfig;
import com.trs.moye.base.data.model.entity.DataSourceConfig;
import com.trs.moye.base.data.model.enums.CreateTableStatus;
import com.trs.moye.base.data.model.enums.ModelExecuteStatus;
import com.trs.moye.base.data.source.enums.KafkaOffsetResetType;
import com.trs.moye.base.data.source.enums.RocketMQOffsetResetType;
import com.trs.moye.base.data.storage.DataStorage;
import com.trs.moye.base.data.storage.dao.DataStorageMapper;
import com.trs.moye.base.data.storage.setting.DataStorageSettings;
import com.trs.moye.base.data.storage.setting.KafkaDataStorageSettings;
import com.trs.moye.base.data.storage.setting.RocketmqDataStorageSettings;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import javax.annotation.Resource;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;


/**
 * 数据建模分层配置初始化
 *
 * <AUTHOR>
 * @since 2025/04/15 10:06:57
 */
@CodeInitialize
@Slf4j
@Component
public class StreamProcessMQStoragePointInitialize {

    private static final String[] INFLUENCE_TABLES = new String[]{"data_storage", "data_model_execute_config"};
    private static final String BACKUP_TABLE_SUFFIX = "auto_bak250530demand";
    private static final String INTERNAL_MQ_STORAGE_POINT_NAME = "流处理支持MQ存储来源内置存储";

    @Resource
    private PrimaryLibraryCommonMapper primaryLibraryCommonMapper;
    @Resource
    private DataConnectionMapper dataConnectionMapper;
    @Resource
    private DataModelMapper dataModelMapper;
    @Resource
    private MQStoragePointInitHelper mqStoragePointInitHelper;
    @Resource
    private DwdModelService dwdModelService;
    @Resource
    private OdsModelService odsModelService;
    @Resource
    private DataModelService dataModelService;

    /**
     * 初始化方法
     */
    @CodeInitialize(name = "流处理支持MQ类型存储改造初始化脚本")
    public void invokeInit() {
        WatchUtils.start("查询流处理建模");
        List<DataModel> dataModelList = dataModelMapper.selectStreamProcessTask();
        if (ObjectUtils.isEmpty(dataModelList)) {
            WatchUtils.start("未发现流处理数据建模，不需要执行当前流处理初始化方法");
            return;
        }
        CodeInitializeRecordUtils.appendDetail("流处理建模数量", dataModelList.size());
        // 备份受到影响的库表
        WatchUtils.start("查询【" + INTERNAL_MQ_STORAGE_POINT_NAME + "】");
        DataConnection dataConnection = dataConnectionMapper.selectByName(false, INTERNAL_MQ_STORAGE_POINT_NAME);
        AssertUtils.notNull(dataConnection, "名称为【%s】的MQ类型存储点不存在，您需要创建该数据存储后才能执行这个初始化方法",
            INTERNAL_MQ_STORAGE_POINT_NAME);
        AssertUtils.equals(DataSourceCategory.MQ, dataConnection.getConnectionType().getCategory(),
            "名称为【%s】的存储点不是MQ类型存储，请把该数据存储删除后创建一个同名的MQ类型数据存储");
        List<ModelDTO> modelDtos = buildModelDTOList(dataModelList, dataConnection);
        if (modelDtos.isEmpty()) {
            WatchUtils.start("未发现需要初始化的数据建模，原因可查看不需要初始化的日志信息");
            return;
        }
        WatchUtils.start("准备备份受到影响的库表");
        backupTables();
        initStreamProcessModels(modelDtos);
    }

    /**
     * 更新流处理任务的源模型存储点
     *
     * @param modelDTOS 流处理任务列表
     */
    public void initStreamProcessModels(List<ModelDTO> modelDTOS) {
        List<String> initSuccessModels = new ArrayList<>();
        Map<String, List<String>> initFailModelMap = new LinkedHashMap<>();
        for (ModelDTO modelDTO : modelDTOS) {
            String modelIdentity = BizUtils.getDataModelIdentity(modelDTO.getModel());
            WatchUtils.start("===>准备初始化建模" + modelIdentity);
            try {
                mqStoragePointInitHelper.initStreamProcessModel(modelDTO);
                initSuccessModels.add(modelIdentity);
                WatchUtils.start("初始化成功");
            } catch (Exception e) {
                String errorLogKey = "建模[" + modelIdentity + "]异常信息";
                log.error(errorLogKey, e);
                initFailModelMap.put(modelIdentity, ExceptionUtils.readTraceMessage(e));
                WatchUtils.start(
                    String.format("初始化失败，可通过[%s]日志查看执行失败信息", errorLogKey));
            }
            WatchUtils.stop();
        }
        CodeInitializeRecordUtils.appendDetail("初始化成功的建模", initSuccessModels);
        CodeInitializeRecordUtils.appendDetail("初始化成功的建模数量", initSuccessModels.size());
        CodeInitializeRecordUtils.appendDetail("初始化失败的建模", initFailModelMap);
        CodeInitializeRecordUtils.appendDetail("初始化失败的建模数量", initFailModelMap.size());
    }

    private List<ModelDTO> buildModelDTOList(List<DataModel> dataModelList, DataConnection dataConnection) {
        List<ModelDTO> modelDTOList = new ArrayList<>();
        List<String> notInitMessages = new ArrayList<>(dataModelList.size());
        List<String> needInitMessages = new ArrayList<>(dataModelList.size());
        for (DataModel streamProcessModel : dataModelList) {
            DataSourceConfig sourceConfig = streamProcessModel.getDataSource().get(0);
            DataModel sourceModel = dataModelMapper.selectById(sourceConfig.getSourceModelId());
            String modelIdentity = BizUtils.getDataModelIdentity(streamProcessModel);
            String sourceModelIdentity = BizUtils.getDataModelIdentity(sourceModel);
            if (streamProcessModel.getExecuteStatus() == ModelExecuteStatus.START) {
                notInitMessages.add(String.format("启动的数据建模【%s】不允许执行初始化脚本，应该在moye后端版本升级前关闭建模", modelIdentity));
                continue;
            }
            if (sourceModel.getExecuteStatus() == ModelExecuteStatus.START) {
                notInitMessages.add(
                    String.format("数据建模【%s】的来源建模【%s】是启动状态，不允许执行初始化脚本，应该在moye后端版本升级前关闭来源建模"
                        , modelIdentity, sourceModelIdentity));
                continue;
            }
            List<DataStorage> sourceStorages = sourceModel.getDataStorages();
            if (ObjectUtils.isEmpty(sourceStorages)) {
                notInitMessages.add(String.format("数据模型【%s】的来源建模【%s】没有配置存储点，请先为其创建存储点，本次初始化跳过该流处理数据建模"
                    , modelIdentity, sourceModelIdentity));
                continue;
            }
            if (isAlreadyInit(sourceStorages, dataConnection)) {
                notInitMessages.add(String.format("数据模型【%s】已经执行过初始化脚本，跳过该建模", modelIdentity));
                continue;
            }
            if (isExistMQStorage(sourceStorages)) {
                notInitMessages.add(String.format("数据模型【%s】的来源建模【%s】存在MQ类型存储点，是升级版本后的建模，无需初始化，跳过该建模"
                    , modelIdentity, sourceModelIdentity));
                continue;
            }
            needInitMessages.add(String.format("数据模型【%s】需要执行初始化脚本"
                , BizUtils.getDataModelIdentity(streamProcessModel)));
            sourceStorages.sort(Comparator.comparing(AuditBaseEntity::getId));
            DataStorage sourceStorage = sourceModel.getDataStorages().get(0);
            ModelDTO modelDTO = new ModelDTO();
            modelDTO.setModel(streamProcessModel);
            modelDTO.setSourceConfig(sourceConfig);
            modelDTO.setSourceModel(sourceModel);
            modelDTO.setSourceStorage(sourceStorage);
            modelDTO.setDataConnection(dataConnection);
            modelDTOList.add(modelDTO);
        }
        CodeInitializeRecordUtils.appendDetail("不初始化的流处理建模", notInitMessages);
        CodeInitializeRecordUtils.appendDetail("不初始化的流处理建模数量", notInitMessages.size());
        CodeInitializeRecordUtils.appendDetail("需要初始化的流处理建模", needInitMessages);
        CodeInitializeRecordUtils.appendDetail("需要初始化的流处理建模数量", needInitMessages.size());
        return modelDTOList;
    }

    private boolean isAlreadyInit(List<DataStorage> sourceStorages, DataConnection dataConnection) {
        for (DataStorage sourceStorage : sourceStorages) {
            if (sourceStorage.getConnectionId().equals(dataConnection.getId())) {
                return true;
            }
        }
        return false;
    }

    private boolean isExistMQStorage(List<DataStorage> sourceStorages) {
        for (DataStorage sourceStorage : sourceStorages) {
            if (sourceStorage.getConnection().getConnectionType().getCategory() == DataSourceCategory.MQ) {
                return true;
            }
        }
        return false;
    }

    private void backupTables() {
        for (String tableName : INFLUENCE_TABLES) {
            String backupTableName = tableName + "_" + BACKUP_TABLE_SUFFIX;
            if (primaryLibraryCommonMapper.isTableExists(backupTableName)) {
                WatchUtils.start(String.format("准备备份【%s】表，但是目标表【%s】已存在，不进行备份", tableName, backupTableName));
            } else {
                WatchUtils.start(String.format("备份【%s】表结构，目标表【%s】", tableName, backupTableName));
                primaryLibraryCommonMapper.copyTableStructure(tableName, backupTableName);
                WatchUtils.start(String.format("备份【%s】表数据，目标表【%s】", tableName, backupTableName));
                primaryLibraryCommonMapper.copyTableData(tableName, backupTableName);
            }
        }
    }

    /**
     * MQ存储点初始化帮助类，避免事务被修改表结构影响事务执行结果
     */
    @Component
    public static class MQStoragePointInitHelper {

        @Resource
        private DataConnectionMapper dataConnectionMapper;
        @Resource
        private DataModelMapper dataModelMapper;
        @Resource
        private DataStorageMapper dataStorageMapper;
        @Resource
        private DataModelExecuteConfigMapper dataModelExecuteConfigMapper;
        @Resource
        private DwdModelService dwdModelService;
        @Resource
        private OdsModelService odsModelService;
        @Resource
        private DataModelService dataModelService;

        /**
         * 更新流处理任务的源模型存储点
         *
         * @param modelDto 流处理任务列表
         */
        @Transactional(rollbackFor = Exception.class)
        public void initStreamProcessModel(ModelDTO modelDto) {
            WatchUtils.start("替换来源建模存储点为内置存储，存储点id:" + modelDto.getSourceStorage().getId());
            replaceSourceModelStoragePoint(modelDto.getSourceStorage(), modelDto.getDataConnection());
            WatchUtils.start("来源建模继承旧版本流处消费者分组" + modelDto.getSourceStorage().getId());
            sourceModelInheritOldStreamProcessConsumerGroup(modelDto.getModel(), modelDto.getSourceConfig(), modelDto.getSourceModel());
            WatchUtils.start("内置存储点建表" + modelDto.getSourceStorage().getId());
            createTable(modelDto);
        }

        private void pauseStreamProcessTask(ModelDTO modelDTO) {
            DataModel model = modelDTO.getModel();
            if (model.getExecuteStatus() == ModelExecuteStatus.START) {
                dwdModelService.pauseTask(model.getId());
            }
        }

        private void pauseSourceModelTask(ModelDTO modelDTO) {
            DataModel sourceModel = modelDTO.getSourceModel();
            if (sourceModel.getExecuteStatus() == ModelExecuteStatus.START) {
                odsModelService.pauseTask(sourceModel.getId());
            }
        }

        private void createTable(ModelDTO modelDTO) {
            DataModel sourceModel = modelDTO.getSourceModel();
            List<CreateTableResponse> responses = dataModelService.createTable(sourceModel.getId(),
                buildCreateTableRequests(modelDTO.getSourceStorage()));
            CreateTableResponse response = responses.get(0);
            if (!response.isSuccess()) {
                throw new BizException("【%s】建模的来源建模【%s】未内置MQ存储点建表失败，失败信息：%s"
                    , BizUtils.getDataModelIdentity(modelDTO.getModel())
                    , BizUtils.getDataModelIdentity(sourceModel)
                    , response.getMessage());
            }
        }

        private CreateTableRequests buildCreateTableRequests(DataStorage sourceStorage) {
            CreateTableRequest sourceStorageRequest = new CreateTableRequest();
            sourceStorageRequest.setDataStorageId(sourceStorage.getId());
            sourceStorageRequest.setConnectionId(sourceStorage.getConnectionId());
            sourceStorageRequest.setSettings(sourceStorage.getSettings());
            CreateTableRequests requests = new CreateTableRequests();
            requests.setTables(Arrays.asList(sourceStorageRequest));
            return requests;
        }

        private void replaceSourceModelStoragePoint(DataStorage sourceStorage, DataConnection dataConnection) {
            sourceStorage.setConnectionId(dataConnection.getId());
            sourceStorage.setSettings(createDefaultDataStorageSettings(dataConnection.getConnectionType()));
            sourceStorage.setCreateTableStatus(CreateTableStatus.NOT);
            dataStorageMapper.updateById(sourceStorage);
        }

        private DataStorageSettings createDefaultDataStorageSettings(ConnectionType connectionType) {
            DataStorageSettings settings;
            switch (connectionType) {
                case KAFKA -> settings = new KafkaDataStorageSettings();
                case ROCKETMQ -> settings = new RocketmqDataStorageSettings();
                default -> throw new IllegalArgumentException("不支持的连接类型：" + connectionType);
            }
            settings.setConnectionType(connectionType);
            return settings;
        }

        private void sourceModelInheritOldStreamProcessConsumerGroup(DataModel streamProcessModel,
            DataSourceConfig sourceConfig, DataModel sourceModel) {
            String consumerGroup = String.format("moyeGroup_%s_%d", sourceConfig.getEnName(),
                streamProcessModel.getId());
            ConnectionType connectionType = sourceConfig.getConnection().getConnectionType();
            DataModelExecuteConfig executeConfig = sourceModel.getExecuteConfig();
            ExecuteParams executeParams = executeConfig.getExecuteParams();
            switch (connectionType) {
                case KAFKA -> {
                    if (executeParams == null) {
                        executeParams = new KafkaExecuteParams();
                        executeParams.setConnectionType(connectionType);
                        executeConfig.setExecuteParams(executeParams);
                    }
                    KafkaExecuteParams params = (KafkaExecuteParams) executeParams;
                    params.setConsumerGroup(consumerGroup);
                    params.setOffsetResetType(KafkaOffsetResetType.GROUP_OFFSETS);
                }
                case ROCKETMQ -> {
                    if (executeParams == null) {
                        executeParams = new RocketMQExecuteParams();
                        executeParams.setConnectionType(connectionType);
                        executeConfig.setExecuteParams(executeParams);
                    }
                    RocketMQExecuteParams params = (RocketMQExecuteParams) executeParams;
                    params.setConsumerGroup(consumerGroup);
                    params.setOffsetResetType(RocketMQOffsetResetType.CONSUME_FROM_GROUP_OFFSETS);
                }
                default -> throw new IllegalArgumentException("不支持的连接类型：" + executeParams.getConnectionType());
            }
            dataModelExecuteConfigMapper.updateById(executeConfig);
        }
    }

    /**
     * 流处理任务建模信息
     */
    @Data
    public static class ModelDTO {

        private DataModel model;

        private DataSourceConfig sourceConfig;

        private DataModel sourceModel;

        private DataConnection dataConnection;

        private DataStorage sourceStorage;
    }
}