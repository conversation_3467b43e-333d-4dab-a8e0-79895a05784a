package com.trs.ai.moye.out.request;

import com.trs.moye.base.data.standard.entity.MetaDataStandardField;
import com.trs.moye.base.common.enums.FieldType;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import lombok.Data;

/**
 * AddTagEdgeRequest
 *
 * <AUTHOR>
 * @since 2025/1/17 11:48
 */
@Data
public class DeleteTagEdgeRequest {

    /**
     * 数据标准id
     */
    @NotNull(message = "数据标准id不能为空")
    private Integer metaDataStandardId;

    /**
     * 数据建模id
     */
    @NotEmpty(message = "数据建模id不能为空")
    private List<Integer> dataModelIds;

    /**
     * 要添加的tag
     */
    private List<String> tag;
    /**
     * 要添加的edge
     */
    private List<String> edge;

    /**
     * 获取标准字段属性列表
     *
     * @return 标准字段属性列表
     */
    public List<MetaDataStandardField> getStandardFields() {
        List<MetaDataStandardField> standardFields = new ArrayList<>();
        if (Objects.nonNull(this.tag)) {
            this.tag.forEach(tagField -> {
                MetaDataStandardField metaDataStandardField = new MetaDataStandardField();
                metaDataStandardField.setEnName(tagField);
                metaDataStandardField.setType(FieldType.GRAPHICS_TAG);
                standardFields.add(metaDataStandardField);
            });
        }
        if (Objects.nonNull(this.edge)) {
            this.edge.forEach(edgeField -> {
                MetaDataStandardField metaDataStandardField = new MetaDataStandardField();
                metaDataStandardField.setEnName(edgeField);
                metaDataStandardField.setType(FieldType.GRAPHICS_EDGE);
                standardFields.add(metaDataStandardField);
            });
        }
        return standardFields;
    }
}
