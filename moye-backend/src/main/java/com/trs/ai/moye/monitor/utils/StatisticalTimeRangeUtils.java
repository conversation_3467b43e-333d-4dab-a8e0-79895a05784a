package com.trs.ai.moye.monitor.utils;


import com.trs.ai.moye.monitor.entity.ProcessTimeRange;
import com.trs.ai.moye.monitor.enums.GranularityEnum;
import com.trs.moye.base.common.enums.TimeRange;
import com.trs.moye.base.common.exception.BizException;
import com.trs.moye.base.common.request.TimeRangeParams;
import com.trs.moye.base.common.utils.JsonUtils;
import java.time.DayOfWeek;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.temporal.ChronoUnit;
import java.time.temporal.TemporalAdjusters;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 2021/6/7 15:57
 */
public class StatisticalTimeRangeUtils {

    private static final String TO_START_OF_DAY = "toStartOfDay(storageTime)";

    /**
     * 获取时间范围
     *
     * @param timeParams 时间参数
     * @return {@link ProcessTimeRange }
     * @throws BizException 业务异常类
     * <AUTHOR>
     * @since 2024/10/25 22:20:31
     */
    public static ProcessTimeRange getTimeRange(TimeRangeParams timeParams) throws BizException {

        if (Objects.isNull(timeParams) || Objects.isNull(timeParams.getType())) {
            throw new BizException("invalid time params: " + JsonUtils.toJsonString(timeParams));
        }

        TimeRange granularityType = timeParams.getType();
        String startTime = timeParams.getMinTimeStr();
        String endTime = timeParams.getMaxTimeStr();
        if (Objects.nonNull(startTime) && Objects.nonNull(endTime)) {
            return new ProcessTimeRange(startTime, endTime);
        }
        switch (granularityType) {
            case ALL:
                startTime = null;
                endTime = null;
                break;
            case LAST_HOUR:
                startTime = TimeStringUtil.getFormatTimeStr(LocalDateTime.now().minusHours(1));
                endTime = TimeStringUtil.getFormatTimeStr(LocalDateTime.now());
                break;
            case LAST_TODAY:
                startTime = TimeStringUtil.getFormatTimeStr(TimeStringUtil.getLocalDateTime(LocalTime.MIN));
                endTime = TimeStringUtil.getFormatTimeStr(TimeStringUtil.getLocalDateTime(LocalTime.MAX));
                break;
            case LAST_YESTERDAY:
                startTime = TimeStringUtil.getFormatTimeStr(
                    LocalDateTime.of(LocalDateTime.now().minusDays(1L).toLocalDate(), LocalTime.MIN));
                endTime = TimeStringUtil.getFormatTimeStr(
                    LocalDateTime.of(LocalDateTime.now().minusDays(1L).toLocalDate(), LocalTime.MAX));
                break;
            case LAST_THREE_DAY:
                startTime = TimeStringUtil.getFormatTimeStr(
                    LocalDateTime.now().minusDays(3).truncatedTo(ChronoUnit.SECONDS));
                endTime = TimeStringUtil.getFormatTimeStr((LocalDateTime.now().truncatedTo(ChronoUnit.SECONDS)));
                break;
            case LAST_WEEK:
                startTime =
                    TimeStringUtil.getFormatTimeStr(
                        TimeStringUtil.getLocalDateTime(LocalTime.MIN).with(DayOfWeek.MONDAY));
                endTime = TimeStringUtil.getFormatTimeStr(TimeStringUtil.getLocalDateTime(LocalTime.MAX));
                break;
            case LAST_MONTH:
                startTime =
                    TimeStringUtil.getFormatTimeStr(
                        TimeStringUtil.getLocalDateTime(LocalTime.MIN)
                            .with(TemporalAdjusters.firstDayOfMonth()));
                endTime = TimeStringUtil.getFormatTimeStr(TimeStringUtil.getLocalDateTime(LocalTime.MAX));
                break;
            case CUSTOM:
                startTime =
                    TimeStringUtil.getFormatTimeStr(timeParams.getMinTime());
                endTime =
                    TimeStringUtil.getFormatTimeStr(timeParams.getMaxTime());
                break;
            default:
                break;
        }
        return new ProcessTimeRange(startTime, endTime);
    }

    /**
     * 获取时间范围
     *
     * @param granularity 粒度
     * @param timeParams  时间参数
     * @return 时间范围
     */
    public static ProcessTimeRange getTimeRange(String granularity, TimeRangeParams timeParams)
        throws BizException {
        GranularityEnum granularityEnum = GranularityEnum.getGranularityEnum(granularity);
        ProcessTimeRange timeRange = new ProcessTimeRange();
        switch (granularityEnum) {
            case DAY:
                ProcessTimeRange processTimeRange = StatisticalTimeRangeUtils.getTimeRange(timeParams);
                timeRange.setTimeFunction(TO_START_OF_DAY);
                timeRange.setStartTime(processTimeRange.getStartTime());
                timeRange.setEndTime(processTimeRange.getEndTime());
                timeRange.setType(timeParams.getType().getType());
                break;
            case HOUR:
                timeRange.setTimeFunction("toStartOfHour(storageTime)");
                timeRange.setStartTime(
                    TimeStringUtil.getFormatTimeStr(TimeStringUtil.getLocalDateTime(LocalTime.MIN)));
                timeRange.setEndTime(
                    TimeStringUtil.getFormatTimeStr(TimeStringUtil.getLocalDateTime(LocalTime.MAX)));
                break;
            default:
                break;
        }
        TimeRange granularityType = TimeRange.getTimeRange(timeRange.getType());
        if (TimeRange.CUSTOM == granularityType) {
            timeRange.setStartTime(
                TimeStringUtil.getFormatTimeStr(timeParams.getMinTime()));
            timeRange.setEndTime(
                TimeStringUtil.getFormatTimeStr(timeParams.getMaxTime()));
        }
        return timeRange;
    }

    private StatisticalTimeRangeUtils() {
    }
}
