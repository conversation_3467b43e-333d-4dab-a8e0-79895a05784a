package com.trs.ai.moye.common.dao;

import java.util.List;
import java.util.Map;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 分组统计 通用类
 *
 * @param <T> 分组值类型
 * <AUTHOR>
 * @since 2024/10/25 20:23
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class GroupCount<T> {

    /**
     * 分组值
     */
    private T value;

    /**
     * 数量
     */
    private Long count;

    /**
     * 转换为map, {@link #value} 作为key, {@link #count} 作为value
     *
     * @param groupCounts 分组统计结果
     * @param <T>         分组值类型
     * @return map
     */
    public static <T> Map<T, Long> toMap(List<GroupCount<T>> groupCounts) {
        return groupCounts.stream()
            .collect(java.util.stream.Collectors.toMap(GroupCount::getValue, GroupCount::getCount));
    }
}
