package com.trs.ai.moye.data.model.response;

import com.trs.ai.moye.common.utils.BeanUtil;
import com.trs.ai.moye.data.model.entity.BatchArrangement;
import com.trs.ai.moye.data.model.entity.CodeSubTasks;
import com.trs.ai.moye.data.model.enums.CodeType;
import com.trs.ai.moye.data.model.request.BatchCodeRequest;
import com.trs.ai.moye.data.model.request.BatchCodeSubtask;
import com.trs.ai.moye.permission.service.DynamicUserNameService;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 代码模式返回实体类
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024/10/22 17:17
 **/
@EqualsAndHashCode(callSuper = true)
@Data
public class BatchCodeResponse extends BatchCodeRequest {

    private String updateBy;

    private LocalDateTime updateTime;

    /**
     * 有参构造
     *
     * @param batchCodeSubtasks 子任务信息
     * @param codeType          代码类型
     * @param isUpdatedTasks    是否更新
     * <AUTHOR>
     * @since 2024/10/22 17:32
     */
    public BatchCodeResponse(String updateBy, LocalDateTime updateTime, List<BatchCodeSubtask> batchCodeSubtasks,
        CodeType codeType, Boolean isUpdatedTasks) {
        this.updateBy = updateBy;
        this.updateTime = updateTime;
        this.setSubtasks(batchCodeSubtasks);
        this.setCodeType(codeType);
        this.setIsUpdatedTasks(isUpdatedTasks);
    }

    /**
     * 构造为BatchCodeResponse实体
     *
     * @param batchArrangement 批任务治理
     * @return {@link BatchCodeResponse}
     * <AUTHOR>
     * @since 2024/10/22 17:32
     */

    public static BatchCodeResponse fromBatchCodeResponse(BatchArrangement batchArrangement) {
        CodeSubTasks[] codeSubTasks = batchArrangement.getCodeSubTasks();
        // 没有子任务默认为python任务
        if (codeSubTasks == null || codeSubTasks.length == 0) {
            return empty();
        }
        DynamicUserNameService service = BeanUtil.getBean(DynamicUserNameService.class);
        return fromBatchCodeResponse(service.getUserName(batchArrangement.getUpdateBy()), batchArrangement.getUpdateTime(),
            List.of(codeSubTasks), batchArrangement.getIsUpdatedTasks());
    }

    /**
     * 构造为BatchCodeResponse实体
     *
     * @param updateBy       更新人
     * @param updateTime     更新时间
     * @param codeSubTasks   子任务信息
     * @param isUpdatedTasks 是否更新
     * @return {@link BatchCodeResponse}
     */

    public static BatchCodeResponse fromBatchCodeResponse(String updateBy, LocalDateTime updateTime,
        List<CodeSubTasks> codeSubTasks, boolean isUpdatedTasks) {
        if (codeSubTasks == null || codeSubTasks.size() == 0) {
            return empty();
        }
        List<BatchCodeSubtask> batchCodeSubtasks = new ArrayList<>();
        CodeType codeType = null;
        for (CodeSubTasks subtask : codeSubTasks) {
            BatchCodeSubtask batchCodeSubtask = new BatchCodeSubtask();
            batchCodeSubtask.setName(subtask.getName());
            batchCodeSubtask.setCode(subtask.getCode());
            batchCodeSubtasks.add(batchCodeSubtask);
            codeType = subtask.getCodeType();
        }
        return new BatchCodeResponse(updateBy, updateTime, batchCodeSubtasks, codeType, isUpdatedTasks);
    }

    /**
     * 创建一个空的BatchCodeResponse对象
     *
     * @return {@link BatchCodeResponse}
     */
    public static BatchCodeResponse empty() {
        return new BatchCodeResponse(null, null, new ArrayList<>(), CodeType.PYTHON, true);
    }
}
