package com.trs.ai.moye.data.model.response;

import com.trs.moye.base.common.enums.ModelLayer;
import javax.validation.constraints.NotNull;
import lombok.Data;

/**
 * 功能模块: <b>数据建模</b>
 * <br>
 * 创建视图 响应体
 * <br>
 *
 * <AUTHOR>
 * @since 2024/09/23 14:05
 **/
@Data
public class CreateViewResponse {

    /**
     * 数据建模id
     */
    @NotNull
    private Integer id;

    /**
     * 贴源表/要素表/...
     */
    @NotNull
    private ModelLayer modelLayer;

}
