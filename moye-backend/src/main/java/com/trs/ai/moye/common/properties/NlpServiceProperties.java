package com.trs.ai.moye.common.properties;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * NLP服务属性配置
 */
@Configuration
@ConfigurationProperties(prefix = "nlp-service")
@Data
public class NlpServiceProperties {

    /**
     * 词云URL
     */
    private String wordCloudUrl = "http://trs-nlp-event-svc:8234/tianyuan/nlp/word_cloud";

    /**
     * 关键词提取URL
     */
    private String keywordExtractUrl = "http://trs-nlp-event-svc:8234/tianyuan/nlp/keyword_extract";

    /**
     * 接受事件状态信息URL
     */
    private String eventStatusUrl = "http://trs-nlp-event-svc:8234/tianyuan/nlp/event_status";

    /**
     * 相似度计算URL
     */
    private String similarEventUrl = "http://trs-nlp-event-svc:8234/tianyuan/nlp/similar_event";

    /**
     * 接收事件消息URL
     */
    private String receiveEventMessageUrl = "http://trs-nlp-event-svc:8234/tianyuan/nlp/receive_event_message";

    /**
     * 实体识别与关系抽取自动标记URL
     */
    private String autoMarkUrl = "http://trs-nlp-event-svc:9527/tianyuan/nlp/entity_relation_predict";

    /**
     * 向量化URL
     */
    private String wordEmbeddingUrl = "http://trs-nlp-embedding-svc:7000/nlp/embedding";

    /**
     * 批量向量化URL
     */
    private String wordEmbeddingBatchUrl = "http://trs-nlp-embedding-svc:8001/nlp/embedding/batch";

    /**
     * cws URL
     */
    private String cws = "http://trs-nlp-cws-svc:8800/nlp/cws/event";

    /**
     * 情感分析URL
     */
    private String emotion = "http://trs-nlp-emotion-classify-svc:8083/nlp/emotion";

    /**
     * 垃圾文 URL
     */
    private String spam = "http://nlp-spam-classify-svc:8903/nlp/spam";
}