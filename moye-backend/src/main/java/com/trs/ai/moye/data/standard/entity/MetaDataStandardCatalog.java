package com.trs.ai.moye.data.standard.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.trs.moye.base.common.entity.AuditBaseEntity;
import com.trs.ai.moye.data.standard.request.MetaDataStandardCategoryRequest;
import java.io.Serial;
import java.io.Serializable;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 元数据标准--目录 表名： meta_data_standard_catalog
 */
@Data
@TableName(value = "meta_data_standard_catalog")
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class MetaDataStandardCatalog extends AuditBaseEntity implements Serializable {

    /**
     * 名称
     */
    @NotEmpty(message = "名称不能为空")
    @Size(max = 50, message = "名称长度不能超过50个字符")
    private String name;

    /**
     * 描述
     */
    private String description;

    /**
     * 目录父编号；第一级目录编号为0
     */
    private Integer pid;

    @Serial
    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    /**
     * 转换为实体目录对象
     *
     * @param request 请求对象
     * @return 目录对象
     */
    public static MetaDataStandardCatalog from(MetaDataStandardCategoryRequest request) {
        MetaDataStandardCatalog catalog = new MetaDataStandardCatalog();
        catalog.setId(request.getId());
        catalog.setName(request.getName());
        catalog.setDescription(request.getDescription());
        catalog.setPid(request.getPid());
        return catalog;
    }


}