package com.trs.ai.moye.data.model.response;

import com.trs.moye.base.data.connection.enums.ConnectionType;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * DWD 实时状态响应
 *
 * <AUTHOR>
 * @since 2025/04/03 16:31:29
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class DwdRealtimeStatusResponse extends DataModelStatusResponse {

    /**
     * 连接类型
     */
    private ConnectionType connectionType;

}
