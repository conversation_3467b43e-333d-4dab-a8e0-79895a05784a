package com.trs.ai.moye.data.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.trs.moye.base.data.model.constant.DataModelingConstants;
import com.trs.ai.moye.data.model.dto.arrangement.ArrangeOperatorDTO;
import com.trs.ai.moye.data.model.dto.arrangement.OperatorConditionDTO;
import com.trs.ai.moye.data.model.service.impl.OperatorExpressionCompiler;
import com.trs.ai.moye.data.operator.constants.enums.OperatorType;
import com.trs.ai.moye.data.operator.domain.request.ActionParamRequest;
import com.trs.ai.moye.data.operator.domain.tyengine.ActionParamInfo;
import com.trs.ai.moye.data.operator.domain.vo.OperatorDetails;
import com.trs.moye.ability.entity.ArrangeOperatorConfig;
import com.trs.moye.base.common.typehandler.CustomJacksonTypeHandler;
import java.util.List;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * 规则编排，提供给ty-engine用于执行
 *
 * <AUTHOR>
 * @since 2020/8/11
 */
@Setter
@Getter
@NoArgsConstructor
@TableName(value = "stream_execute_operator")
public class ExecuteOperator {

    @TableId(type = IdType.AUTO)
    private Integer id;
    /**
     * 算子中文名
     */
    private String zhName;
    /**
     * 算子英文名
     */
    private String enName;
    /**
     * 算子启用状态
     */
    private Boolean status;
    /**
     * 建模id
     */
    private Integer dataModelId;
    /**
     * 算子id
     */
    private Integer operatorId;
    /**
     * 编译结果
     */
    private String ruleExpress;
    /**
     * 执行顺序
     */
    private Integer executeOrder;
    /**
     * 算子编排开启关闭，1启用，0关闭
     */
    private Boolean arrangementStatus;
    /**
     * 租户
     */
    private Integer tenantId;
    /**
     * 算子编排执行配置
     */
    @TableField(typeHandler = CustomJacksonTypeHandler.class)
    private ArrangeOperatorConfig executeConfig;

    /**
     * 输出字段
     */
    @TableField(typeHandler = CustomJacksonTypeHandler.class)
    private ActionParamInfo[] storage;
    /**
     * 未编码的过滤参数
     */
    private String conditions;
    /**
     * 未编码的执行参数
     */
    private String actions;

    /**
     * 算子类型
     */
    private OperatorType type;


    public ExecuteOperator(Integer dataModelId, boolean arrangementStatus, ArrangeOperatorDTO operatorInfo) {
        this.arrangementStatus = arrangementStatus;
        this.tenantId = DataModelingConstants.DEFAULT_TENANT;
        this.dataModelId = dataModelId;
        this.executeOrder = operatorInfo.getOrder();
        this.status = operatorInfo.getEnabled();
        this.operatorId = operatorInfo.getId();
        this.zhName = operatorInfo.getZhName();
        this.enName = operatorInfo.getEnName();
        this.ruleExpress = OperatorExpressionCompiler.getRuleExpress(operatorInfo.getEnName(),
            operatorInfo.getCompConditions(), operatorInfo.getParams().getInput(),
            operatorInfo.getParams().getOutput());
        this.executeConfig = operatorInfo.getConfig();
        this.storage = OperatorExpressionCompiler.getActionParamInfoList(operatorInfo.getParams().getOutput())
            .toArray(new ActionParamInfo[0]);
        this.conditions = OperatorExpressionCompiler.getCondition(operatorInfo.getCompConditions(), false);
        this.actions = OperatorExpressionCompiler.getActions(operatorInfo.getEnName(),
            operatorInfo.getParams().getInput(), operatorInfo.getParams().getOutput(), false);
        this.type = OperatorType.STREAM;
    }


    public ExecuteOperator(Integer dataModelId, OperatorDetails operator, OperatorConditionDTO compConditions,
        List<ActionParamRequest> input, List<ActionParamRequest> output) {
        this.arrangementStatus = true;
        this.tenantId = DataModelingConstants.DEFAULT_TENANT;
        this.dataModelId = dataModelId;
        this.executeOrder = -1;
        this.status = true;
        this.operatorId = operator.getId();
        this.zhName = operator.getZhName();
        this.enName = operator.getEnName();
        this.ruleExpress = OperatorExpressionCompiler.getRuleExpress(operator.getEnName(),
            compConditions, input, output);
        this.storage = OperatorExpressionCompiler.getActionParamInfoList(output).toArray(new ActionParamInfo[0]);
        this.conditions = OperatorExpressionCompiler.getCondition(compConditions, false);
        this.actions = OperatorExpressionCompiler.getActions(operator.getEnName(), input, output, false);
        this.type = OperatorType.BATCH;
    }

}
