package com.trs.ai.moye.monitor.service.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.trs.moye.base.common.response.PageResponse;
import com.trs.ai.moye.monitor.dao.MonitorOdsLagMapper;
import com.trs.ai.moye.monitor.request.DataModelMonitorRequest;
import com.trs.ai.moye.monitor.request.MonitorEventPeriodRequest;
import com.trs.ai.moye.monitor.response.DataModelMonitorHomeResponse;
import com.trs.ai.moye.monitor.response.HomePageResponse;
import com.trs.ai.moye.monitor.response.MonitorEventPeriodResponse;
import com.trs.ai.moye.monitor.response.OdsMonitorStatisticsResponse;
import com.trs.ai.moye.monitor.service.MonitorOdsLagService;
import com.trs.ai.moye.monitor.service.PeriodDataQueryService;
import com.trs.moye.base.common.enums.ModelLayer;
import com.trs.moye.base.data.connection.dao.DataConnectionMapper;
import com.trs.moye.base.data.connection.entity.DataConnection;
import com.trs.moye.base.data.model.dao.DataModelMapper;
import com.trs.moye.base.data.model.entity.DataModel;
import com.trs.moye.base.data.model.entity.DataSourceConfig;
import com.trs.moye.base.data.source.dao.DataSourceConfigMapper;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import org.springframework.stereotype.Service;

/**
 * 积压数据查询接口实现
 *
 * <AUTHOR>
 * @since 2024-10-24
 */
@Service
public class MonitorOdsLagServiceImpl implements MonitorOdsLagService {

    @Resource
    private MonitorOdsLagMapper monitorOdsLagMapper;
    @Resource
    DataModelMapper dataModelMapper;
    @Resource
    private DataSourceConfigMapper dataSourceMapper;
    @Resource
    private DataConnectionMapper dataConnectionMapper;
    @Resource
    private PeriodDataQueryService periodDataQueryService;

    @Override
    public PageResponse<OdsMonitorStatisticsResponse> dataModelMonitorPageList(DataModelMonitorRequest request,
        ModelLayer layer) {
        Page<OdsMonitorStatisticsResponse> page = monitorOdsLagMapper.odsMonitorStatisticsList(request,
            request.getSortParams(),
            layer,
            request.getPageParams()
                .toPage());
        if (page.getRecords().isEmpty()) {
            return PageResponse.of(page);
        }

        paddingMetaDataAndDataSourceProperty(page);
        return PageResponse.of(page);
    }

    /**
     * 数据模型监控详情——周期列表
     *
     * @param dataModelId 数据建模id
     * @param request     分页参数
     * @return 分页数据
     */
    @Override
    public PageResponse<MonitorEventPeriodResponse> odsMonitorDetailPeriods(Integer dataModelId,
        MonitorEventPeriodRequest request) {
        return PageResponse.of(
            monitorOdsLagMapper.odsMonitorDetailPeriods(dataModelId, request, request.getPageParams().toPage()));
    }

    @Override
    public HomePageResponse dataModelHomePage(String sourceType, ModelLayer layer, List<Integer> dataModelIds) {
        List<DataModelMonitorHomeResponse> dataModelMonitorHomeResponses = monitorOdsLagMapper.selectTopSix(sourceType,
            layer, dataModelIds);
        Optional.ofNullable(dataModelMonitorHomeResponses).ifPresent(
            list -> list.forEach(dataModelMonitorHomeResponse -> {
                DataModel dataModel = dataModelMapper.selectById(dataModelMonitorHomeResponse.getDataModelId());
                if (dataModel != null) {
                    dataModelMonitorHomeResponse.setDataModelName(dataModel.getZhName());
                }
            })
        );

        Long total = monitorOdsLagMapper.odsMonitorHomePageStatisticsCount(sourceType, layer, dataModelIds);
        return new HomePageResponse(dataModelMonitorHomeResponses, total);
    }

    private void paddingMetaDataAndDataSourceProperty(Page<OdsMonitorStatisticsResponse> page) {
        List<Integer> dataModelIds = page.getRecords().stream().map(OdsMonitorStatisticsResponse::getId)
            .toList();

        Map<Integer, DataModel> dataModelMap = dataModelMapper.selectByIds(new HashSet<>(dataModelIds)).stream()
            .collect(Collectors.toMap(DataModel::getId, Function.identity()));

        Map<Integer, DataSourceConfig> dataSourceConfigMap = dataSourceMapper.selectByDataModelIds(dataModelIds)
            .stream().collect(Collectors.toMap(DataSourceConfig::getDataModelId, Function.identity(), (k1, k2) -> k1));

        page.getRecords().forEach(vo -> {
            DataModel dataModel = dataModelMap.get(vo.getId());
            if (dataModel != null) {
                vo.setName(dataModel.getZhName());
            }
            DataSourceConfig dataSource = dataSourceConfigMap.get(vo.getId());
            if (dataSource != null && dataSource.getConnectionId() != null) {
                DataConnection dataConnection = dataConnectionMapper.selectById(dataSource.getConnectionId());
                vo.setSourceType(dataConnection.getConnectionType().getCategory());
                vo.setSourceSubType(dataConnection.getConnectionType().name());
            }
        });
    }
}
