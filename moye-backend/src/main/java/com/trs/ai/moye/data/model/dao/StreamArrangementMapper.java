package com.trs.ai.moye.data.model.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.trs.ai.moye.data.model.entity.StreamArrangement;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.ResultMap;
import org.apache.ibatis.annotations.Select;

/**
 * 流处理算子编排，用于返回给前端(StreamArrangement)持久层
 *
 * <AUTHOR>
 * @date 2024-10-11 14:27:53
 */
@Mapper
public interface StreamArrangementMapper extends BaseMapper<StreamArrangement> {

    /**
     * 根据建模id删除
     *
     * @param dataModelId 建模id
     */
    @Delete("delete from stream_arrangement where data_model_id = #{dataModelId}")
    void deleteByDataModelId(@Param("dataModelId") Integer dataModelId);

    /**
     * 根据建模id查询
     *
     * @param dataModelId 建模id
     * @return {@link StreamArrangement}
     */
    @ResultMap("mybatis-plus_StreamArrangement")
    @Select("select * from stream_arrangement where data_model_id = #{dataModelId}")
    StreamArrangement selectByDataModelId(@Param("dataModelId") Integer dataModelId);
}