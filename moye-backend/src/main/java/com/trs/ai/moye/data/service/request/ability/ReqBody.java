package com.trs.ai.moye.data.service.request.ability;

import com.trs.ai.moye.data.service.enums.ability.AbilityBodyDataType;
import java.util.Map;
import lombok.Data;

/**
 * req body
 *
 * <AUTHOR>
 * @since 2024/09/29 15:28:23
 */
@Data
public class ReqBody {

    private static final String ROOT = "root";

    private Integer type = AbilityBodyDataType.OBJECT.getCode();

    private Integer dataType = AbilityBodyDataType.OBJECT.getCode();

    private Map<String, BodyProperty> properties;
}

