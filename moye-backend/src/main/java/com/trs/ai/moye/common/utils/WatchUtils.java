package com.trs.ai.moye.common.utils;

import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.StopWatch;

/**
 * 计时工具类：用于打印消息发送执行链路日志 只是打印监控链路日志的工具方法，应该保证所有方法都不抛出异常而影响到正常的业务流程
 *   使用ThreadLocal来存储StopWatch对象，注意结束后调用remove方法，否则会造成内存泄露。
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-08-13 08:59
 */
@Slf4j
public class WatchUtils {

    private static final ThreadLocal<StopWatch> THREAD_LOCAL_WATCH = new ThreadLocal<>();

    private WatchUtils() {
    }

    /**
     * 设置监控器
     *
     * @param watch 监控器
     */
    public static void set(StopWatch watch) {
        THREAD_LOCAL_WATCH.set(watch);
    }

    /**
     * 移除监控器
     */
    public static void remove() {
        THREAD_LOCAL_WATCH.remove();
    }

    /**
     * 获取监控器
     *
     * @return 监控器
     */
    public static StopWatch get() {
        return THREAD_LOCAL_WATCH.get();
    }

    /**
     * 创建监控任务
     *
     * @param watchName 计时器名称
     */
    public static void create(String watchName) {
        StopWatch watch = new StopWatch(watchName);
        set(watch);
    }

    /**
     * 创建并开始监控任务
     *
     * @param watchName 计时器名称
     * @param taskName 任务名称
     */
    public static void createStart(String watchName, String taskName) {
        create(watchName);
        start(taskName);
    }

    /**
     * 开始监控任务：如果计时器不存在；在开始一个新的任务之前，需要先停止上一个任务的监控。如果上一个任务还在运行中，则需要先调用stop()方法结束它。
     *
     * @param taskName 任务名称
     */
    public static void start(String taskName) {
        StopWatch watch = THREAD_LOCAL_WATCH.get();
        if (watch == null) {
            return;
        }
        if (watch.isRunning()) {
            watch.stop();
        }
        watch.start(taskName);
    }

    /**
     * 结束监控任务
     */
    public static void stop() {
        StopWatch watch = THREAD_LOCAL_WATCH.get();
        if (watch == null) {
            return;
        }
        if (watch.isRunning()){
            watch.stop();
        }
    }

    /**
     * 获取监控日志信息
     *
     * @return 监控日志信息
     */
    public static String getWatchLogInfo() {
        stop();
        StopWatch watch = THREAD_LOCAL_WATCH.get();
        return watch == null || watch.getTaskCount() == 0 ? "" : watch.prettyPrint();
    }

    /**
     * 停止并打印监控Debug日志信息
     */
    public static void stopAndPrintDebugLog(){
        WatchUtils.stop();
        StopWatch watch = WatchUtils.get();
        WatchUtils.remove();
        if (watch == null){
            return;
        }
        Logger logger = LoggerFactory.getLogger(watch.getId());
        if (logger.isDebugEnabled()){
            logger.debug(watch.prettyPrint());
        }
    }
}
