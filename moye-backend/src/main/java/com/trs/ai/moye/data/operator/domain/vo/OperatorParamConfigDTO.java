package com.trs.ai.moye.data.operator.domain.vo;

import com.trs.ai.moye.data.ability.entity.RequestSchema;
import com.trs.ai.moye.data.operator.constants.enums.OperatorParamTypeEnum;
import com.trs.ai.moye.data.operator.domain.entity.OperatorParamConfig;
import com.trs.ai.moye.data.operator.service.impl.AbilityCenterParamConvertor;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 算子参数请求
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
public class OperatorParamConfigDTO implements java.io.Serializable {

    /**
     * 参数类型 单字段/键值对
     */
    @NotNull
    private OperatorParamTypeEnum paramType;

    /**
     * 字段详情
     */
    @Valid
    @NotEmpty
    private List<OperatorParamDetail> paramDetail;

    /**
     * 级联字段信息
     */
    private List<String> cascadeField;

    /**
     * 是否多组
     */
    private Boolean isGroup;

    /**
     * 参数位置
     */
    private String location;


    public OperatorParamConfigDTO(OperatorParamConfig config) {
        this.paramType = config.getParamType();
        this.paramDetail = Arrays.asList(config.getParamDetail());
        this.cascadeField =
            config.getCascadeField() == null ? new ArrayList<>() : Arrays.asList(config.getCascadeField());
        this.isGroup = config.getIsGroup();
    }

    public OperatorParamConfigDTO(RequestSchema fieldNode, String enName) {
        this.location = fieldNode.getParameterLocation();
        this.isGroup = false;
        this.cascadeField = new ArrayList<>();
        this.paramType = OperatorParamTypeEnum.SINGLE;
        this.paramDetail = List.of(AbilityCenterParamConvertor.toOperatorParam(fieldNode, enName));
    }
}
