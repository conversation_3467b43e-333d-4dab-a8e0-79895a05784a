package com.trs.ai.moye.data.model.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.trs.ai.moye.data.model.entity.BatchCodeHistory;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.ResultMap;
import org.apache.ibatis.annotations.Select;

/**
 * <AUTHOR>
 * @since 2025/7/22 14:04
 */
@Mapper
public interface BatchCodeHistoryMapper extends BaseMapper<BatchCodeHistory> {


    /**
     * 根据数据模型ID查询批处理编排草稿
     *
     * @param dataModelId 数据模型ID
     * @return 批处理编排草稿
     */
    @ResultMap("mybatis-plus_BatchCodeHistory")
    @Select("SELECT * FROM batch_arrangement_code_history WHERE data_model_id = #{dataModelId}")
    BatchCodeHistory selectByDataModelId(Integer dataModelId);
}
