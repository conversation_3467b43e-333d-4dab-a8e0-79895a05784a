package com.trs.ai.moye.data.model.entity.view;

import com.trs.ai.moye.data.model.enums.JoinType;
import java.io.Serial;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;


/**
 * 数据建模视图信息 中的 联表查询信息
 *
 * <AUTHOR>
 * @since 2024/9/23 11:43
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class JoinRelationInfo implements Serializable {

    @Serial
    private static final long serialVersionUID = 5144830667755163807L;
    /**
     * 主表连接字段英文名
     */
    private String sourceField;
    /**
     * 主表英文名
     */
    private String sourceTable;
    /**
     * 联表查询的关系
     */
    private JoinType joinType;
    /**
     * 连接表连接字段英文名
     */
    private String targetField;
    /**
     * 连接表英文名
     */
    private String targetTable;

}
