package com.trs.ai.moye.data.service.dto;

import com.trs.moye.base.common.enums.FieldType;
import com.trs.moye.base.data.model.entity.DataModelField;
import com.trs.moye.base.data.standard.enums.MetaDataStandardTypeEnum;
import java.util.List;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2024/10/12
 **/
@Data
@NoArgsConstructor
public class TableStructDto {

    /**
     * 表英文名
     */
    private String enName;
    /**
     * 表中文名
     */
    private String zhName;

    /**
     * 数据建模id
     */
    private Integer dataModelId;

    /**
     * 元数据标准类型
     */
    private MetaDataStandardTypeEnum type;

    /**
     * 表字段
     */
    List<TableField> fields;

    List<NebulaField> tags;

    List<NebulaField> edges;


    /**
     * 构造函数
     *
     * @param dataModelId     数据建模id
     * @param enName          表英文名
     * @param zhName          表中文名
     * @param dataModelFields 表字段
     */
    public TableStructDto(Integer dataModelId, String enName, String zhName, List<DataModelField> dataModelFields,
        MetaDataStandardTypeEnum type) {
        this.enName = enName;
        this.zhName = zhName;
        this.dataModelId = dataModelId;
        if (MetaDataStandardTypeEnum.STRUCTURAL.equals(type)) {
            this.fields = dataModelFields.stream().map(TableField::from).toList();
        } else {
            this.edges = processTagAndEdge(dataModelFields, FieldType.GRAPHICS_EDGE);
            this.tags = processTagAndEdge(dataModelFields, FieldType.GRAPHICS_TAG);
        }
        this.type = type;
    }

    private List<NebulaField> processTagAndEdge(List<DataModelField> dataModelFields, FieldType fieldType) {
        return dataModelFields.stream()
            .filter(dataModelField -> fieldType.equals(dataModelField.getType())).map(NebulaField::new).toList();
    }


}
