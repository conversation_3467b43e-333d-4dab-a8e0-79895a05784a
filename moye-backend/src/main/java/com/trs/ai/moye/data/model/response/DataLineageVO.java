package com.trs.ai.moye.data.model.response;

import com.trs.ai.moye.data.model.dto.arrangement.Canvas;
import com.trs.moye.base.common.enums.ModelLayer;
import com.trs.moye.base.data.model.entity.DataModel;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import lombok.Data;

/**
 * 数据血缘vo
 *
 * <AUTHOR>
 */
@Data
public class DataLineageVO {

    /**
     * 画布信息
     */
    private Canvas canvas;
    /**
     * 表节点信息
     */
    private List<DataLineageDetailVO> tables;

    /**
     * 数据血缘详情
     */
    @Data
    public static class DataLineageDetailVO {

        /**
         * 表id
         */
        private Integer id;
        /**
         * 表分类id
         */
        private Integer categoryId;
        /**
         * 英文名
         */
        private String enName;
        /**
         * 中文名
         */
        private String cnName;
        /**
         * 表层级类型
         */
        private ModelLayer type;
        /**
         * 连接id
         */
        private Set<Integer> targetIds;
        /**
         * 处理总量
         */
        private Long processCount;
        /**
         * 存储总量
         */
        private Long storageCount;
        /**
         * 接入总量
         */
        private Long accessCount;

        public DataLineageDetailVO(DataModel dataModel) {
            this.id = dataModel.getId();
            this.categoryId = dataModel.getBusinessCategoryId();
            this.enName = dataModel.getEnName();
            this.cnName = dataModel.getZhName();
            this.type = dataModel.getLayer();
            this.targetIds = new HashSet<>();
        }
    }
}
