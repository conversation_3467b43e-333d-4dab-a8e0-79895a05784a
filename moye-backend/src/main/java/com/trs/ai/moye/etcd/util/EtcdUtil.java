package com.trs.ai.moye.etcd.util;

import static java.util.concurrent.TimeUnit.SECONDS;

import com.google.protobuf.ByteString;
import com.ibm.etcd.api.KeyValue;
import com.ibm.etcd.api.LeaseGrantResponse;
import com.ibm.etcd.api.LockResponse;
import com.ibm.etcd.api.RangeResponse;
import com.ibm.etcd.api.UnlockResponse;
import com.ibm.etcd.client.KeyUtils;
import com.ibm.etcd.client.kv.KvClient;
import com.ibm.etcd.client.kv.WatchUpdate;
import com.ibm.etcd.client.lease.LeaseClient;
import com.ibm.etcd.client.lease.PersistentLease;
import com.ibm.etcd.client.lock.LockClient;
import io.grpc.stub.StreamObserver;
import java.util.List;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeoutException;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

/**
 * etcd 工具类
 *
 * <AUTHOR>
 * @since 2020/9/19 15:18
 */
@Slf4j
@Component
public class EtcdUtil {

    @Resource
    private KvClient kvClient;

    @Resource
    private LeaseClient leaseClient;

    @Resource
    private LockClient lockClient;


    /**
     * 存入key，value
     *
     * @param key   key
     * @param value value
     */
    public void put(String key, String value) {
        kvClient.put(ByteString.copyFromUtf8(key), ByteString.copyFromUtf8(value)).sync();
    }

    /**
     * 存入key、value，和租约id
     *
     * @param key     key
     * @param value   value
     * @param leaseId 租约id
     */
    public void put(String key, String value, long leaseId) {
        kvClient.put(ByteString.copyFromUtf8(key), ByteString.copyFromUtf8(value), leaseId).sync();
    }

    /**
     * 存入key、value，和过期时间，单位是秒
     *
     * @param key   key
     * @param value value
     * @param ttl   超时时间
     */
    public void putAndGrant(String key, String value, long ttl) {
        LeaseGrantResponse lease = leaseClient.grant(ttl).sync();
        put(key, value, lease.getID());
    }

    /**
     * 根据key，获取value
     *
     * @param key key
     * @return 值
     */
    public String get(String key) {
        lockClient.lock(ByteString.copyFromUtf8(key));
        RangeResponse rangeResponse = kvClient.get(ByteString.copyFromUtf8(key)).sync();
        List<KeyValue> keyValues = rangeResponse.getKvsList();
        if (CollectionUtils.isEmpty(keyValues)) {
            return null;
        }
        lockClient.unlock(ByteString.copyFromUtf8(key));
        return keyValues.get(0).getValue().toStringUtf8();
    }

    /**
     * 获取指定前缀的所有key-value
     *
     * @param key key前缀
     * @return key-value列表
     */
    public List<KeyValue> getPrefix(String key) {
        RangeResponse rangeResponse = kvClient.get(ByteString.copyFromUtf8(key)).asPrefix().sync();
        return rangeResponse.getKvsList();
    }

    /**
     * 监听key
     *
     * @param key key
     * @return WatchIterator
     */
    public KvClient.WatchIterator watch(String key) {
        return kvClient.watch(ByteString.copyFromUtf8(key)).start();
    }

    /**
     * 监听前缀为key的所有key
     *
     * @param key key前缀
     * @return WatchIterator
     */
    public KvClient.WatchIterator watchPrefix(String key) {
        return kvClient.watch(ByteString.copyFromUtf8(key)).asPrefix().start();
    }

    /**
     * 监听前缀为key的所有key
     *
     * @param key      key前缀
     * @param observer 观察者
     * @return Watch
     */
    public KvClient.Watch watchPrefix(String key, StreamObserver<WatchUpdate> observer) {
        return kvClient.watch(ByteString.copyFromUtf8(key)).asPrefix().start(observer);
    }

    /**
     * 自动续约
     *
     * @param key           key
     * @param value         value
     * @param frequencySecs 续约频率，最小是4秒，默认是5秒
     * @param minTtl        最小存活时间，最小是2秒，默认是10秒
     * @return leaseId 租约id
     */
    public long keepAlive(String key, String value, int frequencySecs, int minTtl)
        throws InterruptedException, ExecutionException, TimeoutException {
        //minTtl秒租期，每frequencySecs秒续约一下
        PersistentLease lease = leaseClient.maintain().leaseId(System.currentTimeMillis()).keepAliveFreq(frequencySecs)
            .minTtl(minTtl).start();
        long newId = lease.get(3L, SECONDS);
        put(key, value, newId);
        return newId;
    }

    /**
     * 支持设置调用timeout的续约
     *
     * @param key           key
     * @param value         value
     * @param frequencySecs 续约频率，最小是4秒，默认是5秒
     * @param minTtl        最小存活时间，最小是2秒，默认是10秒
     * @param timeout       超时时间
     * @return leaseId 租约id
     **/
    public long keepAliveWithTimeout(String key, String value, int frequencySecs, int minTtl, long timeout)
        throws InterruptedException, ExecutionException, TimeoutException {
        //minTtl秒租期，每frequencySecs秒续约一下
        PersistentLease lease = leaseClient.maintain().leaseId(System.currentTimeMillis()).keepAliveFreq(frequencySecs)
            .minTtl(minTtl).start();
        long newId = lease.get(timeout, SECONDS);
        put(key, value, newId);
        return newId;
    }

    /**
     * 判断剩余的过期时间
     *
     * @param leaseId 租约id
     * @return 剩余时间
     */
    public long timeToLive(long leaseId) {
        try {
            return leaseClient.ttl(leaseId).get().getTTL();
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.error("判断剩余时间出现中断异常", e);
        }catch (ExecutionException e) {
            log.error("获取剩余ttl时间发生异常", e);
        }
        return 0L;
    }

    /**
     * 删除key
     *
     * @param key etcd值
     */
    public void delete(String key) {
        kvClient.delete(ByteString.copyFromUtf8(key)).sync();
    }

    /**
     * 更新value 不影响续约跟监听
     *
     * @param key   etcd值
     * @param value 值
     */
    public void setValue(String key, String value) {
        kvClient.setValue(ByteString.copyFromUtf8(key), ByteString.copyFromUtf8(value)).sync();
    }

    /**
     * 分布式加锁
     *
     * @param name 锁的名称
     * @return 锁响应对象
     **/
    public LockResponse lock(String name) {
        return lockClient.lock(ByteString.copyFromUtf8(name)).sync();
    }

    /**
     * 支持超时设置的分布式锁
     *
     * @param name        锁的名称
     * @param timeoutSecs 超时时间，单位秒
     * @return 锁响应对象
     **/
    public LockResponse lockWithTimeout(String name, long timeoutSecs) {
        LeaseGrantResponse lgr = leaseClient.grant(timeoutSecs).sync();
        return lockClient.lock(KeyUtils.bs(name)).withLease(lgr.getID()).sync();
    }

    /**
     * 释放锁
     *
     * @param lockResponse 锁的响应
     * @return 释放锁的响应
     **/
    public UnlockResponse unlock(LockResponse lockResponse) {
        return lockClient.unlock(lockResponse.getKey()).sync();
    }

}
