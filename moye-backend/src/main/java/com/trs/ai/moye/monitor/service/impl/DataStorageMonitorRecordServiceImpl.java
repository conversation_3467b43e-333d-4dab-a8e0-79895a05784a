package com.trs.ai.moye.monitor.service.impl;

import com.trs.ai.moye.data.model.dao.DataProcessMonitorConfigMapper;
import com.trs.ai.moye.monitor.dao.DataStorageMonitorRecordMapper;
import com.trs.ai.moye.monitor.dto.DataStorageMonitorRecordDTO;
import com.trs.ai.moye.monitor.request.DataStorageMonitorRequest;
import com.trs.ai.moye.monitor.response.DataStorageTodayMonitorResponse;
import com.trs.ai.moye.monitor.service.DataStorageMonitorRecordService;
import com.trs.ai.moye.storageengine.feign.MonitorFeign;
import com.trs.moye.base.common.entity.TwoTuple;
import com.trs.moye.base.common.entity.mq.MqConsumeInfoResponse;
import com.trs.moye.base.common.enums.status.StartStop;
import com.trs.moye.base.common.response.PageResponse;
import com.trs.moye.base.common.utils.AssertUtils;
import com.trs.moye.base.common.utils.DataProcessUtils;
import com.trs.moye.base.data.connection.enums.DataSourceCategory;
import com.trs.moye.base.data.model.dao.DataModelMapper;
import com.trs.moye.base.data.model.entity.DataModel;
import com.trs.moye.base.data.storage.DataStorage;
import com.trs.moye.base.data.storage.dao.DataStorageMapper;
import com.trs.moye.base.monitor.entity.DataProcessMonitorConfig;
import com.trs.moye.base.monitor.entity.DataStorageMonitorRecord;
import com.trs.moye.base.monitor.entity.TodayMonitorMetric;
import com.trs.moye.base.monitor.enums.DataProcessMonitorType;
import com.trs.moye.base.monitor.utils.MonitorUtils;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @since 2025-03-14 16:15
 */
@Slf4j
@Service
public class DataStorageMonitorRecordServiceImpl implements DataStorageMonitorRecordService {

    @Resource
    private DataStorageMonitorRecordMapper mapper;
    @Resource
    private MonitorFeign monitorFeign;
    @Resource
    private DataModelMapper dataModelMapper;
    @Resource
    private DataStorageMapper dataStorageMapper;
    @Resource
    private DataProcessMonitorConfigMapper dataProcessMonitorConfigMapper;

    @Override
    public List<DataStorageMonitorRecordDTO> refreshMonitor(Integer dataModelId) {
        LocalDateTime scheduleTime = LocalDateTime.now();
        DataModel dataModel = dataModelMapper.selectById(dataModelId);
        AssertUtils.notEmpty(dataModel, "主键为【%s】的元数据不存在", dataModelId);
        List<DataStorage> dataStorages = dataModel.getDataStorages();
        List<DataStorageMonitorRecord> monitorRecordList = new ArrayList<>(dataStorages.size());
        List<DataStorageMonitorRecord> databaseMonitorRecordList = collectDatabaseMonitorInfo(dataModel,
            scheduleTime, dataStorages.stream().filter(storage ->
                    storage.getConnection().getConnectionType().getCategory() == DataSourceCategory.DATA_BASE)
                .toList());
        monitorRecordList.addAll(databaseMonitorRecordList);
//        // todo：MQ类型存储一般有外部程序消费数据，无法确定其消费者，暂时不做监控
//        List<DataStorageMonitorRecord> mqMonitorRecordList = collectMqMonitorInfo(dataModel, scheduleTime,
//            dataStorages.stream().filter(storage ->
//                    storage.getConnection().getConnectionType().getCategory() == DataSourceCategory.MQ)
//                .toList());
//        monitorRecordList.addAll(mqMonitorRecordList);
        Map<Integer, DataStorage> dataStorageMap = dataStorages.stream()
            .collect(Collectors.toMap(DataStorage::getConnectionId, Function.identity()));
        return monitorRecordList.stream()
            .map(monitorRecord -> DataStorageMonitorRecordDTO.form(monitorRecord, dataStorageMap)).toList();
    }

    @Override
    public List<DataStorageTodayMonitorResponse> todayMonitor(Integer dataModelId) {
        DataModel dataModel = dataModelMapper.selectById(dataModelId);
        AssertUtils.notEmpty(dataModel, "主键为【%s】的元数据不存在", dataModelId);
        List<DataStorage> dataStorages = dataModel.getDataStorages().stream().filter(storage ->
                storage.getConnection().getConnectionType().getCategory() == DataSourceCategory.DATA_BASE)
            .toList();
        if (dataStorages.isEmpty()) {
            return new ArrayList<>();
        }
        Map<String, String> topicGroupMap = new HashMap<>();
        for (DataStorage storage : dataStorages) {
            TwoTuple<String, String> topicGroup = DataProcessUtils.buildDataStorageTopicGroup(dataModel.getId(),
                storage.getId());
            topicGroupMap.put(topicGroup.getFirst(), topicGroup.getSecond());
        }
        Map<String, TodayMonitorMetric> monitorMetricMap = monitorFeign.dataStorageTodayMonitor(
            topicGroupMap);
        boolean isEnableMonitor = isEnableMonitor(dataModelId);
        List<DataStorageTodayMonitorResponse> dtos = new ArrayList<>();
        for (DataStorage storage : dataStorages) {
            String topic = DataProcessUtils.buildDataStorageTopic(dataModelId, storage.getId());
            TodayMonitorMetric todayMonitorMetric = monitorMetricMap.get(topic);
            dtos.add(new DataStorageTodayMonitorResponse(todayMonitorMetric, storage.getConnection().getName(),
                isEnableMonitor));
        }
        return dtos;
    }

    private boolean isEnableMonitor(Integer dataModelId) {
        DataProcessMonitorConfig monitorConfig = dataProcessMonitorConfigMapper.getTypeMonitorConfig(dataModelId,
            DataProcessMonitorType.DATA_STORAGE);
        if (monitorConfig == null) {
            return false;
        }
        StartStop scheduleStatus = monitorConfig.getScheduleStatus();
        if (scheduleStatus == null) {
            return false;
        }
        return scheduleStatus.isStart();
    }

    private List<DataStorageMonitorRecord> collectDatabaseMonitorInfo(DataModel dataModel, LocalDateTime scheduleTime,
        List<DataStorage> dataStorages) {
        // 初始化监控记录和topic分组map
        TwoTuple<List<DataStorageMonitorRecord>, Map<String, String>> twoTuple = MonitorUtils.buildInitMonitorRecordAndTopicGroupMap(
            dataModel, scheduleTime, dataStorages);
        // 查询消费信息
        Map<String, MqConsumeInfoResponse> consumeInfoMap = monitorFeign.getDataProcessKafkaConsumeInfoMap(
            twoTuple.getSecond());
        // 设置消费指标
        for (DataStorageMonitorRecord monitorRecord : twoTuple.getFirst()) {
            setConsumeMonitorMetrics(consumeInfoMap, monitorRecord);
        }
        // 设置存储时间
        LocalDateTime storageTime = LocalDateTime.now();
        for (DataStorageMonitorRecord monitorRecord : twoTuple.getFirst()) {
            monitorRecord.setStorageTime(storageTime);
        }
        return twoTuple.getFirst();
    }

    private List<DataStorageMonitorRecord> collectMqMonitorInfo(DataModel dataModel, LocalDateTime scheduleTime,
        List<DataStorage> dataStorages) {
        List<DataStorageMonitorRecord> monitorRecordList = new ArrayList<>(dataStorages.size());
        for (DataStorage storage : dataStorages) {
            DataStorageMonitorRecord monitorRecord = MonitorUtils.createMqInitMonitorRecord(dataModel, storage,
                scheduleTime);
            MqConsumeInfoResponse consumeInfo = monitorFeign.getDataProcessKafkaConsumeInfo(
                monitorRecord.getDataStorageConnectionId(),
                monitorRecord.getTopic(), monitorRecord.getGroup());
            setConsumeMonitorMetrics(consumeInfo, monitorRecord);
            monitorRecordList.add(monitorRecord);
        }
        return monitorRecordList;
    }

    private void setConsumeMonitorMetrics(Map<String, MqConsumeInfoResponse> consumeInfoMap,
        DataStorageMonitorRecord monitorRecord) {
        MqConsumeInfoResponse consumeInfo = consumeInfoMap.get(monitorRecord.getTopic());
        setConsumeMonitorMetrics(consumeInfo, monitorRecord);
    }

    private void setConsumeMonitorMetrics(MqConsumeInfoResponse consumeInfo,
        DataStorageMonitorRecord monitorRecord) {
        try {
            DataStorageMonitorRecord lastRecord = mapper.selectLatestByDataModelId(
                monitorRecord.getDataModelId(), monitorRecord.getDataStorageConnectionId());
            // 设置监控指标信息
            MonitorUtils.setConsumeMonitorMetrics(monitorRecord, consumeInfo, lastRecord);
        } catch (Exception e) {
            log.error("实时要素库监控任务执行失败，元数据【主键：{}，名称：{}】", monitorRecord.getDataModelId(),
                monitorRecord.getDataModelName(), e);
        }
    }

    @Override
    public PageResponse<DataStorageMonitorRecord> pageList(Integer dataModelId,
        DataStorageMonitorRequest request) {
        return PageResponse.of(
            mapper.pageList(dataModelId, request.getDataStorageConnectionId(), request.getTimeRangeParams(),
                request.getPageParams().toPage()));
    }

    @Override
    public List<DataStorageMonitorRecord> trendChart(Integer dataModelId,
        DataStorageMonitorRequest request) {
        return mapper.trendChart(dataModelId, request.getDataStorageConnectionId(), request.getTimeRangeParams());
    }
}
