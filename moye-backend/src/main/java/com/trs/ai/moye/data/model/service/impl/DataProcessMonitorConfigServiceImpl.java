package com.trs.ai.moye.data.model.service.impl;

import com.trs.ai.moye.data.model.dao.DataProcessMonitorConfigMapper;
import com.trs.ai.moye.data.model.service.DataProcessMonitorConfigService;
import com.trs.ai.moye.xxljob.XXLJobManager;
import com.trs.ai.moye.xxljob.entity.MoyeXxlJob;
import com.trs.ai.moye.xxljob.entity.MoyeXxlJob.MoyeXxlJobBuilder;
import com.trs.ai.moye.xxljob.enums.AppJobHandler;
import com.trs.moye.base.common.enums.status.StartStop;
import com.trs.moye.base.common.utils.AssertUtils;
import com.trs.moye.base.data.model.dao.DataModelMapper;
import com.trs.moye.base.data.model.entity.DataModel;
import com.trs.moye.base.data.model.enums.ModelExecuteStatus;
import com.trs.moye.base.data.schedule.ScheduleTypeEnum;
import com.trs.moye.base.monitor.entity.DataProcessMonitorConfig;
import com.trs.moye.base.monitor.enums.DataProcessMonitorType;
import com.trs.moye.base.xxljob.XxlJobInfo;
import java.util.Objects;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @since 2025-03-14 15:18
 */
@Slf4j
@Service
public class DataProcessMonitorConfigServiceImpl implements DataProcessMonitorConfigService {

    @Resource
    private DataProcessMonitorConfigMapper mapper;
    @Resource
    private XXLJobManager xxlJobManager;
    @Resource
    private DataModelMapper dataModelMapper;

    @Override
    public void updateMonitorConfigInfo(DataProcessMonitorConfig monitorConfig) {
        DataModel dataModel = dataModelMapper.getById(monitorConfig.getDataModelId());
        AssertUtils.notNull(dataModel, "主键为【%s】的数据建模不存在", monitorConfig.getDataModelId());
        StartStop scheduleStatus =
            monitorConfig.isEnable() && dataModel.getExecuteStatus().isStart() ? StartStop.START : StartStop.STOP;
        monitorConfig.setScheduleStatus(scheduleStatus);
        // 查询数据库找那个已存在的记录
        DataProcessMonitorConfig oldData = mapper.getTypeMonitorConfig(monitorConfig.getDataModelId(),
            monitorConfig.getType());

        if (oldData == null) {
            if (monitorConfig.getType() == DataProcessMonitorType.ODS_COMPLETE_LOG) {
                mapper.insert(monitorConfig);
            } else {
                initMonitorConfig(monitorConfig);
            }
            return;
        }
        monitorConfig.setId(oldData.getId());
        monitorConfig.setXxlJobId(oldData.getXxlJobId());
        mapper.updateById(monitorConfig);

        // 非ODS类型需要额外处理
        if (monitorConfig.getType() != DataProcessMonitorType.ODS_COMPLETE_LOG) {
            xxlJobManager.updateJob(monitorConfig.getXxlJobId(), buildMonitorConfigXxlJob(dataModel, monitorConfig));
            if (scheduleStatus.isStart()) {
                xxlJobManager.startJob(monitorConfig.getXxlJobId());
            } else {
                xxlJobManager.stopJob(monitorConfig.getXxlJobId());
            }
        }
    }

    private void initMonitorConfig(DataProcessMonitorConfig monitorConfig) {
        monitorConfig.setId(null);
        int xxlJobId = createMonitorConfigXxlJob(monitorConfig);
        monitorConfig.setXxlJobId(xxlJobId);
        mapper.insert(monitorConfig);
        xxlJobManager.updateJob(monitorConfig.getXxlJobId(), buildMonitorConfigXxlJob(monitorConfig));
        if (monitorConfig.getScheduleStatus().isStart()) {
            xxlJobManager.startJob(monitorConfig.getXxlJobId());
        } else {
            xxlJobManager.stopJob(monitorConfig.getXxlJobId());
        }
    }

    @Override
    public MoyeXxlJob buildMonitorConfigXxlJob(DataProcessMonitorConfig monitorConfig) {
        DataModel dataModel = dataModelMapper.getById(monitorConfig.getDataModelId());
        return buildMonitorConfigXxlJob(dataModel, monitorConfig);
    }

    private MoyeXxlJob buildMonitorConfigXxlJob(DataModel dataModel, DataProcessMonitorConfig monitorConfig) {
        DataProcessMonitorType monitorConfigType = monitorConfig.getType();
        AppJobHandler jobHandler = AppJobHandler.getHandler(monitorConfigType);
        if (Objects.isNull(jobHandler)) {
            throw new IllegalArgumentException("不支持的监控配置类型: " + monitorConfigType);
        }
        MoyeXxlJobBuilder xxlJobBuilder = MoyeXxlJob.builder()
            .jobName(buildMonitorConfigTaskName(monitorConfigType, dataModel))
            .jobParam(dataModel.buildXxlTaskParam())
            .app(jobHandler.getApp())
            .jobHandler(jobHandler.getJobHandler());
        return xxlJobBuilder.scheduleType(ScheduleTypeEnum.FIX_RATE)
            .scheduleConf(String.valueOf(monitorConfig.getPeriodUnit().getSeconds(monitorConfig.getPeriodValue())))
            .build();
    }

    @Override
    public int createMonitorConfigXxlJob(DataProcessMonitorConfig monitorConfig) {
        XxlJobInfo job = xxlJobManager.createJob(buildMonitorConfigXxlJob(monitorConfig));
        return job.getId();
    }

    @Override
    public void updateXxlJobStatus(Integer dataModelId, ModelExecuteStatus modelExecuteStatus) {
        updateXxlJobStatus(modelExecuteStatus, mapper.getTypeMonitorConfig(dataModelId,
            DataProcessMonitorType.STREAM_PROCESS_V1));
    }

    private void updateXxlJobStatus(ModelExecuteStatus modelExecuteStatus, DataProcessMonitorConfig monitorConfig) {
        if (monitorConfig == null) {
            return;
        }
        StartStop scheduleStatus =
            monitorConfig.isEnable() && modelExecuteStatus.isStart() ? StartStop.START : StartStop.STOP;
        monitorConfig.setScheduleStatus(scheduleStatus);
        mapper.updateById(monitorConfig);
        if (monitorConfig.getScheduleStatus().isStart()) {
            xxlJobManager.startJob(monitorConfig.getXxlJobId());
        } else {
            xxlJobManager.stopJob(monitorConfig.getXxlJobId());
        }
    }

    /**
     * 构建建模任务名称
     *
     * @param monitorConfigType 监控配置类型
     * @param dataModel         数据建模
     * @return 建模任务名称
     */
    private String buildMonitorConfigTaskName(DataProcessMonitorType monitorConfigType, DataModel dataModel) {
        return monitorConfigType.getLabel() + "监控-" + dataModel.getZhName() + "-" + dataModel.getId();
    }
}
