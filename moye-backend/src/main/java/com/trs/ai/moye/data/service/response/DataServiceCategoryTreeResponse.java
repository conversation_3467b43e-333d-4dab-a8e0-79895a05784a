package com.trs.ai.moye.data.service.response;

import com.trs.ai.moye.common.response.TreeBaseResponse;
import com.trs.ai.moye.data.service.entity.DataServiceCategoryTree;
import com.trs.ai.moye.data.service.enums.ServiceCategoryTreeNode;
import com.trs.ai.moye.data.service.enums.ServiceCreateMode;
import com.trs.ai.moye.data.service.enums.ServiceHealthStatus;
import com.trs.ai.moye.data.service.enums.ServicePublishStatus;
import com.trs.ai.moye.permission.service.DynamicUserNameService;
import java.time.LocalDateTime;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.ObjectUtils;


/**
 * 数据服务分类树响应
 *
 * <AUTHOR>
 * @since 2024/09/24 16:11:58
 */
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
public class DataServiceCategoryTreeResponse extends TreeBaseResponse {

    /**
     * 描述
     */
    private String description;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 最近更新人
     */
    private String updateBy;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 最近更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 服务发布状态
     */
    private ServicePublishStatus publishStatus;

    /**
     * 服务健康状态
     */
    private ServiceHealthStatus healthStatus;

    /**
     * 节点类型
     */
    private ServiceCategoryTreeNode type;

    /**
     * 创建模式
     */
    private ServiceCreateMode createMode;

    /**
     * 数据服务分类树响应
     *
     * @param tree 树
     * <AUTHOR>
     * @since 2024/09/24 16:11:51
     */
    public DataServiceCategoryTreeResponse(DataServiceCategoryTree tree,
        DynamicUserNameService dynamicUserNameService) {
        super.setId(tree.getId());
        super.setPid(ObjectUtils.isEmpty(tree.getPid()) ? 0 : tree.getPid());
        super.setName(tree.getName());
        if (ObjectUtils.isNotEmpty(tree.getChildren())) {
            List<DataServiceCategoryTreeResponse> children = tree.getChildren().stream()
                .map(e -> new DataServiceCategoryTreeResponse(e, dynamicUserNameService)).toList();
            this.setChildren(children);
        }
        this.setDescription(tree.getDescription());
        this.setCreateTime(tree.getCreateTime());
        this.setUpdateTime(tree.getUpdateTime());
        this.setCreateBy(dynamicUserNameService.getUserName(tree.getCreateBy()));
        this.setUpdateBy(dynamicUserNameService.getUserName(tree.getUpdateBy()));
        this.setPublishStatus(tree.getPublishStatus());
        this.setHealthStatus(tree.getHealthStatus());
        this.setType(tree.getType());
        this.setCreateMode(tree.getCreateMode());
    }
}
