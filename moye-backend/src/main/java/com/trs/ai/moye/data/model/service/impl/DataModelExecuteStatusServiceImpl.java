package com.trs.ai.moye.data.model.service.impl;

import com.trs.ai.moye.common.dao.GroupCount;
import com.trs.ai.moye.data.model.dao.BatchTaskRecordMapper;
import com.trs.ai.moye.data.model.dao.DataModelDisplayMapper;
import com.trs.ai.moye.data.model.dao.ProcessMapper;
import com.trs.ai.moye.data.model.dao.StorageTaskMapper;
import com.trs.ai.moye.data.model.entity.MetadataRelation;
import com.trs.ai.moye.data.model.service.DataModelExecuteStatusService;
import com.trs.moye.base.data.execute.ExecuteModeEnum;
import com.trs.moye.base.common.enums.ModelLayer;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

/**
 * DataModelExecuteStatusServiceImpl
 *
 * <AUTHOR>
 * @since 2025/1/23 13:54
 */
@Slf4j
@Service
public class DataModelExecuteStatusServiceImpl implements DataModelExecuteStatusService {

    @Resource
    private StorageTaskMapper storageTaskMapper;
    @Resource
    private ProcessMapper processMapper;
    @Resource
    private BatchTaskRecordMapper batchTaskRecordMapper;
    @Resource
    private DataModelDisplayMapper dataModelDisplayMapper;

    /**
     * key: dataModelId 数据建模id <br/> value: unreadErrorCount 未读运行失败数
     */
    private final Map<Integer, Integer> cache = new ConcurrentHashMap<>();


    /**
     * 获取数据建模 <b>近10条监控数据内</b> 的未读运行失败数
     *
     * @param dataModelId 数据建模id
     * @param modelLayer  数据建模分层
     * @param executeMode 调度类型
     * @return 未读运行失败数
     */
    @Override
    public Integer getUnreadErrorCountFromCache(Integer dataModelId, ModelLayer modelLayer,
        ExecuteModeEnum executeMode) {
        return cache.computeIfAbsent(dataModelId, id -> queryUnreadErrorCount(dataModelId, modelLayer, executeMode));
    }

    /**
     * 获取数据建模近期未读运行失败数, 同步给缓存
     *
     * @param dataModelId 数据建模id
     * @param modelLayer  数据建模分层
     * @param executeMode 调度类型
     * @return 未读运行失败数
     */
    @Override
    public Integer getUnreadErrorCountFromDb(Integer dataModelId, ModelLayer modelLayer, ExecuteModeEnum executeMode) {
        return cache.compute(dataModelId,
            (id, oldValue) -> queryUnreadErrorCount(dataModelId, modelLayer, executeMode));
    }

    private Integer queryUnreadErrorCount(Integer dataModelId, ModelLayer modelLayer, ExecuteModeEnum executeMode) {
        if (ModelLayer.ODS.equals(modelLayer)) {
            // 贴源库
            return storageTaskMapper.countErrorByDataModelId(dataModelId);
        } else {
            if (Objects.isNull(executeMode)) {
                return 0;
            } else if (ExecuteModeEnum.REALTIME.equals(executeMode)) {
                return processMapper.countErrorByDataModelId(dataModelId);
            } else {
                return batchTaskRecordMapper.countErrorByDataModelId(dataModelId);
            }
        }
    }

    /**
     * 定时任务，每10分钟刷新缓存
     */
    @Scheduled(fixedRate = 10, timeUnit = TimeUnit.MINUTES)
    public void scheduledRefresh() {
        log.info("触发定时任务[每10分钟刷新数据建模未读异常信息数量缓存]");
        List<MetadataRelation> dataModels = dataModelDisplayMapper.selectAllDataModelWithExecuteMode();

        // 删除不存在的dataModel缓存
        Set<Integer> dataModelIds = dataModels.stream().map(MetadataRelation::getDataModelId)
            .collect(Collectors.toSet());
        List<Integer> nonExistentDataModelIds = cache.keySet().stream().filter(key -> !dataModelIds.contains(key))
            .toList();
        nonExistentDataModelIds.forEach(cache::remove);

        // 按QueryType分组
        Map<QueryType, List<Integer>> groupedDataModelIds = dataModels.stream().collect(
            Collectors.groupingBy(mr -> QueryType.from(mr.getModelLayer(), mr.getExecuteMode()),
                Collectors.mapping(MetadataRelation::getDataModelId, Collectors.toList())));

        // 批量查询并更新缓存
        groupedDataModelIds.forEach((queryType, ids) -> {
            Map<Integer, Integer> dataModelId2ErrorCounts = batchQueryUnreadErrorCount(ids, queryType);
            cache.putAll(dataModelId2ErrorCounts);
        });
    }

    private Map<Integer, Integer> batchQueryUnreadErrorCount(List<Integer> dataModelIds, QueryType queryType) {
        // 如果dataModelIds为空，返回空Map
        if (dataModelIds == null || dataModelIds.isEmpty()) {
            return new HashMap<>();
        }

        // 查询数据
        List<GroupCount<Integer>> result = switch (queryType) {
            case ODS -> storageTaskMapper.countAllUnreadErrors();
            case REALTIME -> processMapper.countAllUnreadErrors();
            case BATCH -> batchTaskRecordMapper.countAllUnreadErrors();
            default -> List.of();
        };

        // 将结果转换为Map
        Map<Integer, Long> dataModelId2Count = GroupCount.toMap(result);

        // 过滤结果，只保留需要的dataModelId
        return dataModelIds.stream().collect(Collectors.toMap(id -> id, id -> dataModelId2Count.getOrDefault(id, 0L).intValue()));
    }

    private enum QueryType {
        ODS, REALTIME, BATCH, OTHER;

        public static QueryType from(ModelLayer modelLayer, ExecuteModeEnum executeMode) {
            if (ModelLayer.ODS.equals(modelLayer)) {
                return ODS;
            }
            if (Objects.isNull(executeMode)) {
                return OTHER;
            } else if (ExecuteModeEnum.REALTIME.equals(executeMode)) {
                return REALTIME;
            }
            return BATCH;
        }
    }
}
