package com.trs.ai.moye.out.response;

import com.trs.ai.moye.data.service.dto.DataServiceDto;
import com.trs.ai.moye.out.constants.OutIndicatorType;
import com.trs.moye.base.data.model.entity.DataModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * OUT 指标和服务响应
 *
 * <AUTHOR>
 * @since 2025/06/05 14:25:30
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class OutIndicatorAndServiceResponse extends OutIndicatorBasicResponse {

    private OutIndicatorType type;

    public OutIndicatorAndServiceResponse(DataModel dataModel) {
        super(dataModel);
        this.type = OutIndicatorType.INDICATOR;
    }

    public OutIndicatorAndServiceResponse(DataServiceDto dataServiceDto) {
        this.type = OutIndicatorType.SERVICE;
        this.setId(dataServiceDto.getId());
        this.setZhName(dataServiceDto.getName());
        this.setEnName(dataServiceDto.getCode());
        this.setDescription(dataServiceDto.getDescription());
    }
}
