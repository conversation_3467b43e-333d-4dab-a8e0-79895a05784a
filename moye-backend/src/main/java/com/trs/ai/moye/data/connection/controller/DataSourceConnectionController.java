package com.trs.ai.moye.data.connection.controller;

import com.trs.ai.moye.data.connection.request.ConnectionListRequest;
import com.trs.ai.moye.data.connection.request.ConnectionRequest;
import com.trs.ai.moye.data.connection.response.DataConnectionCardResponse;
import com.trs.ai.moye.data.connection.response.DataConnectionStatisticsResponse;
import com.trs.ai.moye.data.connection.service.DataConnectionService;
import com.trs.ai.moye.data.connection.service.DataSourceConnectionService;
import com.trs.moye.base.data.connection.dao.DataConnectionMapper;
import java.util.List;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * 数据源管理相关接口
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/data-source")
public class DataSourceConnectionController {

    @Resource
    private DataSourceConnectionService dataSourceService;

    @Resource
    private DataConnectionService dataConnectionService;

    @Resource
    private DataConnectionMapper dataConnectionMapper;


    /**
     * 获取数据源统计信息
     * <a href="http://192.168.210.40:3001/project/5419/interface/api/168830">YApi</a>
     *
     * @return 统计信息
     */
    @GetMapping("/statistics")
    DataConnectionStatisticsResponse getConnectionStatistics() {
        return dataSourceService.getStatistics();
    }


    /**
     * 获取数据源连接卡片列表 <a href="http://192.168.210.40:3001/project/5419/interface/api/163575">...</a>
     *
     * @param request 请求参数
     * @return 数据源连接卡片列表
     */
    @PostMapping("/card-list")
    List<DataConnectionCardResponse> getConnectionCardList(@RequestBody @Validated ConnectionListRequest request) {
        List<DataConnectionCardResponse> cardList = dataSourceService.getCardList(request);
        if (request.getCategory() != null) {
            cardList = cardList.stream()
                .filter(item -> item.getDataSourceCategory().equals(request.getCategory().getLabel())).toList();
        }
        return cardList;
    }

    /**
     * 数据源名称校验 <a href="http://192.168.210.40:3001/project/5419/interface/api/163630">...</a>
     *
     * @param name 名称
     * @return java.lang.Boolean
     * <AUTHOR>
     * @since 2024/9/12 16:16
     */
    @GetMapping("/check-name")
    Boolean checkName(@RequestParam String name) {
        return dataConnectionMapper.existsByName(name, true);
    }

    /**
     * 获取数据连接被使用次数 <a href="http://192.168.210.40:3001/project/4220/interface/api/162020">...</a>
     *
     * @param id 连接ID
     * @return java.lang.Integer
     * <AUTHOR>
     * @since 2024/7/23 15:11
     */
    @GetMapping("/{id}/used-count")
    public Integer getUsedCount(@PathVariable Integer id) {
        return dataSourceService.getConnectionUsedCount(id);
    }


    /**
     * 添加数据源 <a href="http://192.168.210.40:3001/project/5419/interface/api/163640">...</a>
     *
     * @param request 请求参数
     * @return 连接id
     */
    @PostMapping
    public Integer addDataSource(@Validated @RequestBody ConnectionRequest request) {
        return dataConnectionService.addDataConnection(request, true);
    }

    /**
     * 修改数据源 <a href="http://192.168.210.40:3001/project/5419/interface/api/163650">...</a>
     *
     * @param id      数据源主键
     * @param request 请求参数
     */
    @PutMapping("/{id}")
    public void updateDataSource(@PathVariable Integer id, @Validated @RequestBody ConnectionRequest request) {
        dataConnectionService.updateConnection(id, request, true);
    }

    /**
     * 删除数据源 <a href="http://192.168.210.40:3001/project/5419/interface/api/163575">...</a>
     *
     * @param id 数据源主键
     */
    @DeleteMapping("/{id}")
    public void deleteDataSource(@PathVariable Integer id) {
        dataConnectionService.deleteConnection(id, true);
    }
}
