package com.trs.ai.moye.data.ability.utils;

import org.apache.commons.lang.StringEscapeUtils;

/**
 * Markdown文本转义处理工具类
 * 用于去除文本中的转义字符（如\\n、\\"等），恢复原始Markdown格式
 */
public class MarkdownUnescapeUtils {

    /**
     * 去除Markdown文本中的转义字符
     *
     * @param escapedMarkdown 包含转义字符的Markdown文本
     * @return 恢复原始格式的Markdown文本
     */
    public static String unescapeMarkdown(String escapedMarkdown) {
        if (escapedMarkdown == null || escapedMarkdown.isEmpty()) {
            return "";
        }
        return StringEscapeUtils.unescapeJava(escapedMarkdown);
    }

    /**
     * 处理包含JSON片段的Markdown文本
     * （先去除整体转义，再处理JSON内部的转义）
     *
     * @param escapedMarkdown 包含JSON片段的Markdown文本
     * @return 恢复原始格式的Markdown文本
     */
    public static String unescapeMarkdownWithJson(String escapedMarkdown) {
        String unescaped = unescapeMarkdown(escapedMarkdown);
        // 进一步处理Markdown中代码块内的JSON转义（如```json块）
        return unescaped.replace("\\\"", "\"");
    }

}