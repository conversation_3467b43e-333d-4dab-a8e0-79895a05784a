package com.trs.ai.moye.monitor.request;

import com.trs.moye.base.common.request.BasePageRequest;
import com.trs.moye.base.common.request.TimeRangeParams;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2025-03-14 17:29
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
public class DataStorageMonitorRequest extends BasePageRequest {

    /**
     * 数据存储连接ID
     */
    private Integer dataStorageConnectionId;

    /**
     * 时间范围参数
     */
    protected TimeRangeParams timeRangeParams;
}
