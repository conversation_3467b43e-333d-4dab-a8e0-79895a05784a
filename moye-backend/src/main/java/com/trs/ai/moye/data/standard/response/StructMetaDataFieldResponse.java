package com.trs.ai.moye.data.standard.response;

import com.trs.ai.moye.data.standard.entity.StructMetaDataField;
import com.trs.ai.moye.permission.service.DynamicUserNameService;
import com.trs.moye.base.common.entity.field.FieldAdvanceConfig;
import com.trs.moye.base.common.enums.FieldType;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * 结构化元数据字段响应对象
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025/5/14 17:45
 **/
@Data
public class StructMetaDataFieldResponse {

    private Integer id;

    /**
     * 中文名：系统属性中文名
     */
    private String zhName;

    /**
     * 系统属性英文名：系统属性英文名
     */
    private String enName;

    /**
     * 系统属性类型：系统属性类型
     */
    private FieldType type;

    /**
     * 类型中文名称
     */
    private String typeName;

    /**
     * 允许多值。1：是否允许多值。1：是；0：否；默认否
     */
    private Boolean isMultiValue;

    /**
     * 允许空
     */
    private Boolean isNullable = true;

    /**
     * 系统属性描述：系统属性描述
     */
    private String description;

    /**
     * 元数据ID：元数据ID
     */
    private Integer structMetaDataId;


    /**
     * 是否为统计字段。1：是否为统计字段。1：是；0：否；默认否
     */
    private Boolean isStatistic;

    /**
     * 内置类型：内置类型：1普通类型，2系统内置
     */
    private Boolean isBuiltIn;
    /**
     * 高级属性：高级属性
     */
    private FieldAdvanceConfig advanceConfig;

    /**
     * 默认值
     */
    private String defaultValue;


    /**
     * 创建人I
     */

    private String createBy;

    /**
     * 最近更新人
     */

    private String updateBy;

    /**
     * 创建时间
     */

    private LocalDateTime createTime;

    /**
     * 最近更新时间
     */

    private LocalDateTime updateTime;


    /**
     * 构建前端响应体
     *
     * @param structMetaDataField    结构化元数据
     * @param dynamicUserNameService 动态用户获取
     * @return {@link StructMetaDataFieldResponse}
     * <AUTHOR>
     * @since 2025/5/14 17:52
     */
    public static StructMetaDataFieldResponse from(StructMetaDataField structMetaDataField,
        DynamicUserNameService dynamicUserNameService) {
        StructMetaDataFieldResponse structMetaDataFieldResponse = new StructMetaDataFieldResponse();
        structMetaDataFieldResponse.setId(structMetaDataField.getId());
        structMetaDataFieldResponse.setZhName(structMetaDataField.getZhName());
        structMetaDataFieldResponse.setEnName(structMetaDataField.getEnName());
        structMetaDataFieldResponse.setType(structMetaDataField.getType());
        structMetaDataFieldResponse.setTypeName(structMetaDataField.getTypeName());
        structMetaDataFieldResponse.setIsMultiValue(structMetaDataField.isMultiValue());
        structMetaDataFieldResponse.setIsNullable(structMetaDataField.isNullable());
        structMetaDataFieldResponse.setDescription(structMetaDataField.getDescription());
        structMetaDataFieldResponse.setStructMetaDataId(structMetaDataField.getStructMetaDataId());
        structMetaDataFieldResponse.setIsStatistic(structMetaDataField.isStatistic());
        structMetaDataFieldResponse.setIsBuiltIn(structMetaDataField.isBuiltIn());
        structMetaDataFieldResponse.setAdvanceConfig(structMetaDataField.getAdvanceConfig());
        structMetaDataFieldResponse.setDefaultValue(structMetaDataField.getDefaultValue());
        structMetaDataFieldResponse.setCreateTime(structMetaDataField.getCreateTime());
        structMetaDataFieldResponse.setUpdateTime(structMetaDataField.getUpdateTime());
        structMetaDataFieldResponse.setCreateBy(dynamicUserNameService.getUserName(structMetaDataField.getCreateBy()));
        structMetaDataFieldResponse.setUpdateBy(dynamicUserNameService.getUserName(structMetaDataField.getUpdateBy()));
        return structMetaDataFieldResponse;

    }
}
