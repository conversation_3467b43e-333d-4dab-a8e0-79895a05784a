package com.trs.ai.moye.data.model.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 流处理监控类型，作为过滤条件
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024/12/25 16:56
 **/
@Getter
@AllArgsConstructor
public enum StreamProcessMonitorType {
    EXECUTED_OPERATOR_COUNT("算子执行数"),
    OPERATOR_COUNT("算子编排"),
    PROCESSING_TIME("处理时长");


    /**
     * 标签：备注用
     */
    private final String label;
}
