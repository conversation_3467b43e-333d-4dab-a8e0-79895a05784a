package com.trs.ai.moye.data.model.dao.batch;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.trs.ai.moye.data.model.response.KeyValueResponse;
import com.trs.moye.ability.entity.operator.BatchOperator;
import com.trs.moye.base.common.enums.ModelLayer;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * BatchOperatorMapper
 *
 * <AUTHOR>
 * @since 2025/3/25 14:53
 */
@Mapper
public interface BatchOperatorMapper extends BaseMapper<BatchOperator> {

    /**
     * 删除编排
     *
     * @param arrangementId 编排id
     */
    void deleteByArrangementId(@Param("arrangementId") Integer arrangementId);

    /**
     * 根据数据建模id删除算子
     *
     * @param dataModelId 数据建模id
     */
    void deleteByDataModelId(@Param("dataModelId") Integer dataModelId);

    /**
     * 根据编排id查询算子
     *
     * @param arrangementId 编排id
     * @return 算子列表
     */
    List<BatchOperator> selectByArrangementId(@Param("arrangementId") Integer arrangementId);


    /**
     * 根据建模id拿到存储连接id
     *
     * @param dataModelId 建模id
     * @param layer       模型层
     * @param modelId     模型id
     * @return 存储连接id
     */
    Integer selectConnectionStorageByDataModelId(@Param("dataModelId") Integer dataModelId,
        @Param("layer") ModelLayer layer, @Param("modelId") Integer modelId);

    /**
     * 查询使用了该能力的元数据
     *
     * @param abilityId 能力id
     * @return 元数据信息
     */
    List<KeyValueResponse> selectUsingDataModel(@Param("abilityId") Integer abilityId);

    /**
     * 查询使用了指定存储点的批处理建模
     *
     * @param storagePointId 存储点id
     * @return 元数据信息
     */
    List<KeyValueResponse> selectStoragePointUsingBatchModel(@Param("storagePointId") Integer storagePointId);

}
