package com.trs.ai.moye.common.config.minio;

import com.trs.ai.moye.minio.starter.properties.MinioProperties;
import io.minio.BucketExistsArgs;
import io.minio.MakeBucketArgs;
import io.minio.MinioClient;
import io.minio.errors.MinioException;
import java.io.IOException;
import java.security.GeneralSecurityException;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * minio初始化类
 *
 * <AUTHOR>
 * @since 2024/10/29 14:14
 */
@Slf4j
@Component
public class MinioInitializer {

    @Resource
    MinioClient minioClient;
    @Resource
    MinioProperties minioProperties;

    /**
     * 检查bucket是否存在并创建
     *
     * @throws IOException              io异常
     * @throws MinioException           minio异常
     * @throws GeneralSecurityException 安全异常
     */
//    @PostConstruct
    public void checkBucketExist() throws IOException, MinioException, GeneralSecurityException {
        checkAndCreateBucket(minioClient, minioProperties.getBucket().getLogs());
    }

    private void checkAndCreateBucket(MinioClient minioClient, String bucketName)
        throws IOException, MinioException, GeneralSecurityException {
        BucketExistsArgs bucketArgs = BucketExistsArgs.builder().bucket(bucketName).build();
        // bucket不存在则创建
        if (!minioClient.bucketExists(bucketArgs)) {
            log.info("minio bucket[{}] 不存在!", bucketName);
            MakeBucketArgs makeBucketArgs = MakeBucketArgs.builder().bucket(bucketName).build();
            minioClient.makeBucket(makeBucketArgs);
            log.info("创建 bucket [{}] 成功!", bucketName);
        }
    }
}
