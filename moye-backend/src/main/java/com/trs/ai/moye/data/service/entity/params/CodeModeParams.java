package com.trs.ai.moye.data.service.entity.params;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 代码模式参数
 *
 * <AUTHOR>
 * @since 2024/09/25 18:47:39
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class CodeModeParams extends DataServiceConfigParams {

    /**
     * 代码块
     */
    private String codeBlock;

    @Override
    public <T extends DataServiceConfigParams> DataServiceConfigParams merge(T dataServiceConfigParams) {
        super.merge(dataServiceConfigParams);
        if (dataServiceConfigParams instanceof CodeModeParams params) {
            updateFieldsIfNotEmpty(params.getCodeBlock(), this::setCodeBlock);
        }
        return this;
    }
}
