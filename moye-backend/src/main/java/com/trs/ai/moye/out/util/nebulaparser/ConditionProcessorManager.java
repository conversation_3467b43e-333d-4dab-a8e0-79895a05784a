package com.trs.ai.moye.out.util.nebulaparser;

import java.util.List;
import javax.annotation.Resource;
import org.springframework.stereotype.Component;

/**
 * 条件处理器管理器
 *
 * <AUTHOR>
 * @since 2024/12/23 10:07:40
 */
@Component
public class ConditionProcessorManager {

    @Resource
    private List<ConditionProcessor> processors;


    /**
     * 处理条件
     *
     * @param condition  条件
     * @param propPrefix prop 前缀
     * @return {@link String }
     * <AUTHOR>
     * @since 2024/12/23 11:18:27
     */
    public String processCondition(String condition, String propPrefix) {
        for (ConditionProcessor processor : processors) {
            if (processor.isSupport(condition)) {
                return processor.process(condition, propPrefix);
            }
        }
        throw new IllegalArgumentException("未找到对应条件类型的处理器: " + condition);
    }
}
