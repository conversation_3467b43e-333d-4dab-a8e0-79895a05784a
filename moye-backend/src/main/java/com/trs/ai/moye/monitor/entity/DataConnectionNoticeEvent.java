package com.trs.ai.moye.monitor.entity;

import com.trs.ai.moye.storageengine.response.ConnectionTestDetailResponse;
import com.trs.moye.base.data.connection.entity.DataConnection;
import com.trs.moye.base.data.connection.entity.params.ConnectionParams;
import java.time.LocalDateTime;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 数据连接测试异常事件
 *
 * <AUTHOR>
 * @since 2025/7/22
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class DataConnectionNoticeEvent {

    /**
     * 关联元数据id
     */
    private Integer metadataId;

    /**
     * 推送时间
     */
    private LocalDateTime publishTime;

    /**
     * 连接名称
     */
    private String connectionName;

    /**
     * 连接类型 数据源/数据存储
     */
    private String connectionType;

    /**
     * 连接详情
     */
    private ConnectionParams connectionParams;

    /**
     * 异常信息
     */
    private String errMsg;


    /**
     * 从连接测试详情响应转换为数据连接测试异常事件
     *
     * @param dataConnection 连接测试请求
     * @param result         连接测试详情响应
     * @return 数据连接测试异常事件
     */
    public static DataConnectionNoticeEvent from(DataConnection dataConnection, ConnectionTestDetailResponse result) {
        return new DataConnectionNoticeEvent(
            dataConnection.getId(),
            LocalDateTime.now(),
            dataConnection.getName(),
            dataConnection.isSource() ? "数据源" : "数据存储",
            dataConnection.getConnectionParams(),
            result.getErrorMessage()
        );
    }
}
