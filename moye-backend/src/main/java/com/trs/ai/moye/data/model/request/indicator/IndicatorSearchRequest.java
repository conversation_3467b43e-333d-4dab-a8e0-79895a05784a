package com.trs.ai.moye.data.model.request.indicator;

import com.trs.ai.moye.storageengine.request.ConditionSearchParams;
import com.trs.moye.base.common.request.SearchParams;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @since 2025/5/28 11:29
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class IndicatorSearchRequest extends ConditionSearchParams {

    private IndicatorTimeRangeParams timeRangeParams;

    private SearchParams searchParams;
}
