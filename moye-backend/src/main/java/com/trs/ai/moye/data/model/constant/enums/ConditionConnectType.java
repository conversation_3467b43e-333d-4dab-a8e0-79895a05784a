package com.trs.ai.moye.data.model.constant.enums;

import lombok.Getter;

/**
 * 算子过滤条件-条件组连接类型
 *
 * <AUTHOR>
 * @since 2024/10/16 14:01
 */
public enum ConditionConnectType {
    /**
     * 满足全部条件
     */
    ALL(" && "),
    /**
     * 满足任意条件
     */
    ANY(" || ");

    @Getter
    private final String connector;

    ConditionConnectType(String connector) {
        this.connector = connector;
    }
}
