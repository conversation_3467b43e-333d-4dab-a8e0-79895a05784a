package com.trs.ai.moye.data.operator.domain.request;

import com.trs.ai.moye.data.operator.domain.vo.CallParams;
import com.trs.moye.base.common.group.Insert;
import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import lombok.Data;

/**
 * 算子基本信息参数
 *
 * <AUTHOR>
 * @since 2024/9/27 15:51
 */
@Data
public class OperatorBasicInfoRequest {

    /**
     * 算子业务分类id
     */
    @NotNull(message = "算子业务分类id不可为空", groups = Insert.class)
    private Integer operatorCategoryId;

    /**
     * 英文名
     */
    @NotBlank(message = "英文名不可为空", groups = Insert.class)
    private String enName;

    /**
     * 中文名
     */
    @NotBlank(message = "中文名不可为空", groups = Insert.class)
    private String zhName;

    /**
     * 描述
     */
    @NotBlank(message = "描述不可为空", groups = Insert.class)
    private String description;

    /**
     * 图标
     */
    @NotBlank(message = "图标不可为空", groups = Insert.class)
    private String iconName;

    /**
     * 调用参数
     */
    @Valid
    @NotNull(message = "调用参数不可为空", groups = Insert.class)
    private CallParams callParams;
}
