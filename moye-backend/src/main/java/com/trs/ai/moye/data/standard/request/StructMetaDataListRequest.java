package com.trs.ai.moye.data.standard.request;

import com.trs.moye.base.common.enums.FieldType;
import com.trs.moye.base.common.request.BasePageRequest;
import javax.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 结构化数据元数据列表请求
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025/5/15 09:59
 **/
@EqualsAndHashCode(callSuper = true)
@Data
public class StructMetaDataListRequest extends BasePageRequest {

    /**
     * 元数据ID
     */
    @NotNull(message = "元数据ID不能为空")
    private Integer metaDataId;

    /**
     * 字段类型
     */
    private FieldType type;

    /**
     * 关键词
     */
    private String keyword;


}
