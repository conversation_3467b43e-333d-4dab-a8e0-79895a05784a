package com.trs.ai.moye.data.connection.service.impl;

import com.trs.moye.base.data.connection.dao.AuthCertificateMapper;
import com.trs.ai.moye.data.connection.response.KerberosCertificateResponse;
import com.trs.ai.moye.data.connection.service.AuthCertificateService;
import com.trs.ai.moye.permission.service.CurrentUserService;
import java.util.List;
import javax.annotation.Resource;
import org.springframework.stereotype.Service;

/**
 * 用户证书服务实现
 *
 * <AUTHOR>
 * @since 2024-09-24 14:00
 */
@Service
public class AuthCertificateServiceImpl implements AuthCertificateService {

    @Resource
    private AuthCertificateMapper authCertificateMapper;

    @Resource
    private CurrentUserService currentUserService;

    @Override
    public List<KerberosCertificateResponse> getCurrentUserAuthCertificates() {
        return authCertificateMapper.selectByUserId(currentUserService.getUserId()).stream()
            .map(KerberosCertificateResponse::new).toList();
    }
}
