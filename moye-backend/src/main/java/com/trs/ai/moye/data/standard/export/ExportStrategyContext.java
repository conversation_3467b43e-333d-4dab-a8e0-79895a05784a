package com.trs.ai.moye.data.standard.export;

import com.trs.moye.base.data.connection.enums.FileExtension;
import java.util.List;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 导出策略上下文
 *
 * <AUTHOR>
 * @since 2024-09-20 15:16
 */
@Slf4j
@Component
public class ExportStrategyContext {

    @Resource
    private List<ExportStrategy> exportStrategies;

    /**
     * 获取文件类型的导出策略
     *
     * @param fileType 文件类型
     * @return 导出策略
     */
    public ExportStrategy getExportStrategy(FileExtension fileType){
        for (ExportStrategy strategy : exportStrategies){
            if (strategy.getFileExtension() == fileType){
                return strategy;
            }
        }
        throw new IllegalArgumentException(String.format("不支持【%s】文件类型导出", fileType));
    }
}
