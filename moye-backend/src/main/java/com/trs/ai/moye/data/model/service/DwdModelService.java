package com.trs.ai.moye.data.model.service;

import com.trs.moye.base.common.response.PageResponse;
import com.trs.ai.moye.data.model.dto.ConnectionStoragePoints;
import com.trs.ai.moye.data.model.entity.BatchTaskRecord;
import com.trs.ai.moye.data.model.entity.BatchTaskTracer;
import com.trs.ai.moye.data.model.request.BatchTaskLogRequest;
import com.trs.ai.moye.data.model.request.DwdAddRequest;
import com.trs.ai.moye.data.model.request.DwdStartRequest;
import com.trs.ai.moye.data.model.request.ProcessDataListRequest;
import com.trs.ai.moye.data.model.request.ProcessRetryRequest;
import com.trs.ai.moye.data.model.request.StreamProcessDataListRequest;
import com.trs.ai.moye.data.model.request.fields.CheckExistedTableFieldsRequest;
import com.trs.ai.moye.data.model.response.BatchTaskLogResponse;
import com.trs.ai.moye.data.model.response.BatchTaskRecordResponse;
import com.trs.ai.moye.data.model.response.CheckExistedTableFieldsResponse;
import com.trs.ai.moye.data.model.response.DwdAddResponse;
import com.trs.ai.moye.data.model.response.DwdExecuteScheduleResponse;
import com.trs.ai.moye.data.model.response.ProcessFlowResponse;
import com.trs.ai.moye.data.model.response.ProcessInfoVO;
import com.trs.ai.moye.data.model.response.ProcessMonitorDetailsResponse;
import com.trs.ai.moye.data.model.response.ProcessRetryResponse;
import com.trs.ai.moye.data.model.response.StreamProcessDataResponse;
import com.trs.moye.base.common.exception.BizException;
import com.trs.moye.base.data.connection.enums.SourceStructureType;
import com.trs.moye.base.data.execute.ExecuteModeEnum;
import com.trs.moye.base.data.model.entity.DataModel;
import java.util.List;

/**
 * 要素库实现类接口
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024/10/12 10:39
 **/
public interface DwdModelService {

    /**
     * 要素库新增
     *
     * @param request 前端请求
     * @return {@link DwdAddResponse}
     * <AUTHOR>
     * @since 2024/9/27 18:13
     */
    DwdAddResponse addModel(DwdAddRequest request);


    /**
     * 添加建模的存储信息
     *
     * @param connectionStoragePointsList 连接存储点列表
     * @param dataModel                   数据建模
     * <AUTHOR>
     * @since 2024/12/2 14:36
     */
    void addStorageInfo(List<ConnectionStoragePoints> connectionStoragePointsList, DataModel dataModel);

    /**
     * 创建数据来源
     *
     * @param executeMode    执行模式
     * @param dataModelId    数据建模id
     * @param sourceModelIds 源模型 ID
     * <AUTHOR>
     * @since 2025/05/19 16:52:30
     */
    void createDataSources(ExecuteModeEnum executeMode, Integer dataModelId, List<Integer> sourceModelIds);

    /**
     * 从 元数据标准 创建建模字段
     *
     * @param type               连接数据库类型
     * @param dataModelId        数据建模id
     * @param metaDataStandardId 元数据标准id
     * <AUTHOR>
     * @since 2024/12/20 15:11:22
     */
    void createModelFieldsFromMetaDataStandard(Integer dataModelId, Integer metaDataStandardId,
        SourceStructureType type);

    /**
     * 启动要素库任务
     *
     * @param id              贴源库id
     * @param dwdStartRequest spark配置
     */
    void startTask(Integer id, DwdStartRequest dwdStartRequest);

    /**
     * 停止要素库任务
     *
     * @param id 贴源库id
     */
    void stopTask(Integer id);

    /**
     * 暂停要素库任务
     *
     * @param id 贴源库id
     */
    void pauseTask(Integer id);


    /**
     * 获取批处理处理数据列表
     *
     * @param request 前端请求
     * @return {@link BatchTaskRecord}
     * <AUTHOR>
     * @since 2024/10/17 14:23
     */
    PageResponse<BatchTaskRecordResponse> getBatchProcessDataList(ProcessDataListRequest request);


    /**
     * 获取流处理数据列表
     *
     * @param request 前端请求
     * @return {@link StreamProcessDataResponse}
     * <AUTHOR>
     * @since 2024/10/17 16:54
     */
    PageResponse<StreamProcessDataResponse> getStreamProcessDataList(StreamProcessDataListRequest request);

    /**
     * 获取批任务监控记录链路列表
     *
     * @param executeId 执行id
     * @return 链路列表
     */
    List<BatchTaskTracer> getBatchTaskRecordTracerList(String executeId);

    /**
     * 查询数据监控流程信息
     *
     * @param recordId  数据id
     * @param processId 流程数据唯一id
     * @return [processId]
     * @throws BizException 业务异常
     * <AUTHOR>
     * @since 2021/5/16 20:38
     */
    ProcessInfoVO getProcessInfo(String recordId, String processId) throws BizException;


    /**
     * 获取每一条数据的处理流程
     *
     * @param dataModelId 数据建模id
     * @param recordId 数据id
     * @return {@link ProcessFlowResponse }
     * <AUTHOR>
     * @since 2024/10/25 17:40
     */
    List<ProcessFlowResponse> getProcessFlow(Integer dataModelId, String recordId);


    /**
     * 根据唯一id查询数据处理详情
     *
     * @param recordId  数据id
     * @param processId 唯一id
     * @return {@link ProcessMonitorDetailsResponse}
     * <AUTHOR>
     * @since 2024/10/25 17:46
     */
    ProcessMonitorDetailsResponse getMonitorDetails(String recordId, String processId) throws BizException;

    /**
     * 异常算子重新验证
     *
     * @param request 前端请求
     * @return {@link ProcessRetryResponse}
     * <AUTHOR>
     * @since 2024/10/25 17:46
     */
    ProcessRetryResponse retryProcess(ProcessRetryRequest request) throws BizException;

    /**
     * 获取批处理日志列表
     *
     * @param request 前端请求
     * @return {@link BatchTaskLogResponse}
     * <AUTHOR>
     * @since 2024/10/29 10:59
     */
    List<BatchTaskLogResponse> getBatchLogs(BatchTaskLogRequest request);


    /**
     * 通过文件路径获取日志文件
     *
     * @param path 文件路径
     * @return {@link String}
     * <AUTHOR>
     * @since 2024/10/29 11:11
     */
    String getLogFileByLogPath(String path);

    /**
     * 更新数据来源
     *
     * @param dataModelId   数据建模id
     * @param dataSourceIds 数据来源id
     */
    void updateDataSource(Integer dataModelId, List<Integer> dataSourceIds);

    /**
     * 检查已存在表字段
     *
     * @param request 请求
     * @return {@link CheckExistedTableFieldsResponse }
     * <AUTHOR>
     * @since 2024/12/11 17:21:56
     */
    CheckExistedTableFieldsResponse checkExistedTableFields(CheckExistedTableFieldsRequest request);

    /**
     * 获取要素库执行计划信息
     *
     * @param dataModelId 数据型id
     * @return {@link DwdExecuteScheduleResponse }
     * <AUTHOR>
     * @since 2025/02/21 17:51:44
     */
    DwdExecuteScheduleResponse getModelExecuteScheduleInfo(Integer dataModelId);
}
