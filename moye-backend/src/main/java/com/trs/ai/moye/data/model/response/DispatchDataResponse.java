package com.trs.ai.moye.data.model.response;

import com.trs.ai.moye.monitor.entity.DataAccessTrace;
import com.trs.moye.base.common.utils.DateTimeUtils;
import lombok.Data;

/**
 * AccessErrorDataResponse
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025/1/23 14:32
 **/
@Data
public class DispatchDataResponse {


    private String recordId;

    private String msgContent;

    private Boolean isError;

    private String errorMsg;

    private String accessTime;

    private String storageName;


    /**
     * 构造响应实体
     *
     * @param data 接入错误数据
     * @return {@link DispatchDataResponse}
     * <AUTHOR>
     * @since 2025/1/23 15:08
     */
    public static DispatchDataResponse from(DataAccessTrace data) {
        DispatchDataResponse response = new DispatchDataResponse();
        response.setRecordId(String.valueOf(data.getRecordId()));
        response.setMsgContent(data.getData());
        response.setErrorMsg(data.getErrorMessage());
        response.setAccessTime(DateTimeUtils.formatStr(data.getAccessTime()));
        response.setStorageName(data.getStorageName());
        response.setIsError(data.getIsError());
        return response;


    }
}
