package com.trs.ai.moye.data.model.request.ods;

import com.trs.moye.base.common.entity.field.DefaultFieldAdvanceConfig;
import com.trs.moye.base.common.entity.field.FieldAdvanceConfig;
import com.trs.moye.base.common.enums.FieldType;
import com.trs.moye.base.data.model.entity.DataModelField;
import javax.validation.constraints.NotBlank;
import lombok.Data;

/**
 * 贴源表请求
 *
 * <AUTHOR>
 * @since 2024-09-27 16:44
 */
@Data
public class OdsFieldRequest {

    @NotBlank(message = "中文名不允许为空")
    private String zhName;

    @NotBlank(message = "英文名不允许为空")
    private String enName;

    @NotBlank(message = "类型不允许为空")
    private FieldType type;

    @NotBlank(message = "类型名称不允许为空")
    private String typeName;

    /**
     * 是增量字段
     */
    private boolean isIncrement;

    /**
     * 是否是主键
     */
    private boolean isPrimaryKey;

    /**
     * 高级配置
     * <p>
     * 默认值为{@link DefaultFieldAdvanceConfig}
     */
    private FieldAdvanceConfig advanceConfig;


    /**
     * 创建DataModelField
     *
     * @return {@link DataModelField}
     */
    public DataModelField toDataModelField() {
        DataModelField field = new DataModelField();
        field.setZhName(zhName);
        field.setEnName(enName);
        field.setType(type);
        field.setTypeName(typeName);
        field.setIncrement(isIncrement);
        field.setPrimaryKey(isPrimaryKey);
        field.setAdvanceConfig(advanceConfig);
        return field;
    }
}
