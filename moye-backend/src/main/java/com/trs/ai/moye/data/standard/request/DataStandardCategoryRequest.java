package com.trs.ai.moye.data.standard.request;

import com.trs.moye.base.common.request.BaseNameRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @version 1.0
 * @description 数据标准分类请求
 * @since 2024/9/19 15:20
 **/
@EqualsAndHashCode(callSuper = true)
@Data
public class DataStandardCategoryRequest extends BaseNameRequest {


    /**
     * 父ID
     */
    private Integer pid;


}
