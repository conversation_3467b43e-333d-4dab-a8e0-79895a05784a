package com.trs.ai.moye.data.standard.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.trs.moye.base.common.entity.AuditBaseEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 结构化元数据目录实体类
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025/5/12 14:40
 **/
@EqualsAndHashCode(callSuper = true)
@Data
@TableName(value = "struct_meta_data_catalog")
@AllArgsConstructor
@NoArgsConstructor
public class StructMetaDataCatalog extends AuditBaseEntity {

    /**
     * 分类名称
     */
    private String name;


    /**
     * 父级id
     */
    private Integer pid;

    /**
     * 描述
     */
    private String description;
}
