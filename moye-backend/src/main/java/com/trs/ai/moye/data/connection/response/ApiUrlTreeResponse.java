package com.trs.ai.moye.data.connection.response;


import com.trs.moye.base.data.connection.entity.JsonFieldStruct;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Http 获取树的结构响应
 *
 * <AUTHOR>
 * @since 2024/8/7
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ApiUrlTreeResponse {

    /**
     * 表结构
     */
    private TableInfoResponse tableInfoResponse;

    /**
     * 树结构
     */
    private List<JsonFieldStruct> tree;
}
