package com.trs.ai.moye.data.model.response;

import com.trs.moye.base.common.entity.mq.MqConsumeInfoResponse;
import java.time.LocalDateTime;
import lombok.AllArgsConstructor;
import lombok.Data;

/**
 * 实时监控DWD层实体（对应表 t_monitor_dwd_realtime）
 *
 * <AUTHOR>
 * @since 2025/02/17 16:28:50
 */
@Data
@AllArgsConstructor
public class DwdRealtimeConsumeInfoResponse {

    // ==================== 监控指标 ====================

    private LocalDateTime monitorTime;

    /**
     * 当前消费的Offset
     */
    private Long currentOffset;

    /**
     * 当前消费的结束Offset
     */
    private Long endOffset;

    /**
     * 积压量
     */
    private Long lag;

    /**
     * 接入TPS
     */
    private Long avgTps;


    public DwdRealtimeConsumeInfoResponse(MqConsumeInfoResponse mqConsumeInfo, Long tps) {
        setCurrentOffset(mqConsumeInfo.getCurrentOffset());
        setEndOffset(mqConsumeInfo.getEndOffset());
        setLag(mqConsumeInfo.getLag());
        setAvgTps(tps);
        setMonitorTime(LocalDateTime.now());
    }

    public DwdRealtimeConsumeInfoResponse() {
        setMonitorTime(LocalDateTime.now());
    }
}
