package com.trs.ai.moye.data.service.entity.query;

import com.trs.ai.moye.data.service.entity.DataServiceField;
import com.trs.ai.moye.data.service.enums.DataServiceConditionType;
import com.trs.ai.moye.data.service.enums.LogicLinkOperator;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * condition实体类
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class Condition {

    /**
     * 条件类型
     */
    private DataServiceConditionType type;
    /**
     * 字段
     */
    private DataServiceField key;
    /**
     * 字段的值
     */
    private List<ValueObject> values;
    /**
     * 操作符
     */
    private String operator;

    public Condition(DataServiceConditionType type, String operator) {
        this.type = type;
        this.operator = operator;
        this.values = new ArrayList<>();
        this.key = null;
    }

    /**
     * 专门用于构造逻辑连接符的构造函数
     */
    public Condition(LogicLinkOperator logicOperator) {
        this.type = DataServiceConditionType.LOGIC;
        this.operator = logicOperator.getValue(); // 从枚举获取值
        this.values = Collections.emptyList();
        this.key = null;
    }
}
