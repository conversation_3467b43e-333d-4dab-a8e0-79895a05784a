package com.trs.ai.moye.bi.service.impl;

import com.trs.ai.moye.bi.dao.VisualAnalysisChartMapper;
import com.trs.ai.moye.bi.dao.VisualAnalysisPublishedSubjectMapper;
import com.trs.ai.moye.bi.dao.VisualAnalysisSubjectMapper;
import com.trs.ai.moye.bi.domain.entity.ChartPosition;
import com.trs.ai.moye.bi.domain.entity.VisualAnalysisChart;
import com.trs.ai.moye.bi.domain.entity.VisualAnalysisPublishedSubject;
import com.trs.ai.moye.bi.domain.entity.VisualAnalysisSubject;
import com.trs.ai.moye.bi.domain.response.DashboardChartResponse;
import com.trs.ai.moye.bi.domain.response.SubjectPublishStatusResponse;
import com.trs.ai.moye.bi.enums.SubjectPublishLocation;
import com.trs.moye.base.common.exception.BizException;
import com.trs.moye.base.data.model.dao.DataModelFieldMapper;
import com.trs.moye.base.data.model.entity.DataModelField;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.dao.DeadlockLoserDataAccessException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.TransactionDefinition;
import org.springframework.transaction.support.TransactionTemplate;

/**
 * <AUTHOR>
 * @since 2025/4/25
 **/

@Slf4j
@Service
public class SubjectPublishService {

    @Resource
    private VisualAnalysisSubjectMapper visualAnalysisSubjectMapper;

    @Resource
    private VisualAnalysisChartMapper visualAnalysisChartMapper;

    @Resource
    private VisualAnalysisPublishedSubjectMapper publishedSubjectMapper;

    @Resource
    private DataModelFieldMapper dataModelFieldMapper;

    private final TransactionTemplate transactionTemplate;

    // 构造方法中注入TransactionTemplate
    public SubjectPublishService(PlatformTransactionManager transactionManager) {
        this.transactionTemplate = new TransactionTemplate(transactionManager);
        // 设置事务传播行为（根据需要调整）
        this.transactionTemplate.setPropagationBehavior(TransactionDefinition.PROPAGATION_REQUIRED);
    }


    /**
     * 发布主题到到指定页面
     *
     * @param id            主题id
     * @param location      发布位置
     * @param publishStatus 发布状态
     */
    public void publish2VisualAnalysis(Integer id, SubjectPublishLocation location, boolean publishStatus) {
        int maxRetries = 3;
        for (int attempt = 0; attempt < maxRetries; attempt++) {
            try {
                executeInSynchronizedTransaction(id, location, publishStatus);
                break; // 成功则退出循环
            } catch (DeadlockLoserDataAccessException e) {
                handleDeadlockRetry(attempt, maxRetries, e);
            }
        }
    }

    // 核心事务操作 (同步块+事务模板)
    private synchronized void executeInSynchronizedTransaction(Integer id, SubjectPublishLocation location,
        boolean publishStatus) {
        transactionTemplate.execute(status -> {
            performDatabaseOperations(id, location, publishStatus);
            return null;
        });
    }

    // 数据库操作原子方法
    private void performDatabaseOperations(Integer id, SubjectPublishLocation location, boolean publishStatus) {
        publishedSubjectMapper.deleteBySubjectIdAndLocation(id, location);
        if (publishStatus) {
            VisualAnalysisPublishedSubject publishedSubject = createPublishedSubject(id, location);
            publishedSubjectMapper.insert(publishedSubject);
        }
    }

    // 领域对象构建
    private VisualAnalysisPublishedSubject createPublishedSubject(Integer id, SubjectPublishLocation location) {
        VisualAnalysisSubject subject = visualAnalysisSubjectMapper.selectById(id);
        List<VisualAnalysisChart> charts = visualAnalysisChartMapper.selectBySubjectId(id);

        VisualAnalysisPublishedSubject publishedSubject = new VisualAnalysisPublishedSubject();
        publishedSubject.setSubjectId(id);
        publishedSubject.setPublishLocation(location);
        publishedSubject.setCharts(buildChartResponses(charts, subject.getPositionConfig()));

        return publishedSubject;
    }

    // DTO转换
    private DashboardChartResponse[] buildChartResponses(List<VisualAnalysisChart> charts,
        ChartPosition[] positionConfig) {
        List<DashboardChartResponse> responses = toChartResponse(charts, positionConfig);
        return responses.toArray(new DashboardChartResponse[0]);
    }

    // 死锁重试处理
    private void handleDeadlockRetry(int currentAttempt, int maxAttempts, DeadlockLoserDataAccessException ex) {
        if (currentAttempt == maxAttempts - 1) {
            throw ex; // 最后一次尝试直接抛出
        }
        performRetryWait(currentAttempt + 1); // 指数退避等待
    }

    // 重试等待逻辑
    private void performRetryWait(int attemptNumber) {
        try {
            Thread.sleep(attemptNumber * 100L);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            throw new BizException("Thread interrupted during retry", e);
        }
    }

    /**
     * 将图表转换为看板图表响应
     *
     * @param charts         图表列表
     * @param positionConfig 看板图表位置配置
     * @return 看板图表响应列表
     */
    public List<DashboardChartResponse> toChartResponse(List<VisualAnalysisChart> charts,
        ChartPosition[] positionConfig) {
        return charts.stream().map(chart -> {
            DashboardChartResponse chartResponse = new DashboardChartResponse(chart);
            List<DataModelField> fields = dataModelFieldMapper.listByDataModelId(chart.getDataModelId());
            chartResponse.getStatisticGroups().forEach(group -> group.getDataSchema().updateFieldZhName(fields));
            Arrays.stream(positionConfig).filter(c -> c.getId().equals(chart.getId())).findFirst()
                .ifPresent(config -> chartResponse.setPosition(config.getPosition()));
            return chartResponse;
        }).toList();
    }


    /**
     * 过滤未发布的主题
     *
     * @param subjects 所有主题列表，未过滤
     * @param location 发布位置
     */
    public void filterPublishedSubjectByLocation(List<VisualAnalysisSubject> subjects,
        SubjectPublishLocation location) {
        List<VisualAnalysisPublishedSubject> subjectByLocationList = publishedSubjectMapper.selectByLocation(location);
        if (Objects.nonNull(subjectByLocationList)) {
            // 如果subjects中元素的id再subjectByLocationList中不存在，则将该元素去除
            subjects.removeIf(subject -> subjectByLocationList.stream()
                .noneMatch(publishedSubject -> publishedSubject.getSubjectId().equals(subject.getId())));
        }

    }

    /**
     * 获取主题发布状态
     *
     * @param id 主题id
     * @return 主题发布状态
     */
    public SubjectPublishStatusResponse getSubjectPublishStatus(Integer id) {
        VisualAnalysisPublishedSubject visualAnalysisPublishedSubject = publishedSubjectMapper.selectBySubjectIdAndLocation(
            id,
            SubjectPublishLocation.VISUAL_ANALYSIS);
        VisualAnalysisPublishedSubject homePagePublishedSubject = publishedSubjectMapper.selectBySubjectIdAndLocation(
            id,
            SubjectPublishLocation.HOME_PAGE);

        SubjectPublishStatusResponse subjectPublishStatusResponse = new SubjectPublishStatusResponse();
        if (Objects.nonNull(visualAnalysisPublishedSubject)) {
            subjectPublishStatusResponse.setVisualAnalysisPublishStatus(true);
        }
        if (Objects.nonNull(homePagePublishedSubject)) {
            subjectPublishStatusResponse.setHomePagePublishStatus(true);
        }
        return subjectPublishStatusResponse;
    }
}
