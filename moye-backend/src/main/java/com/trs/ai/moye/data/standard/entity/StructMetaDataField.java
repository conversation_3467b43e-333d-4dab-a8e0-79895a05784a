package com.trs.ai.moye.data.standard.entity;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.trs.moye.base.common.entity.AuditBaseEntity;
import com.trs.moye.base.common.entity.field.FieldAdvanceConfig;
import com.trs.moye.base.common.enums.FieldType;
import com.trs.moye.base.common.typehandler.PolymorphismTypeHandler;
import com.trs.moye.base.data.model.entity.DataModelField;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 结构化元数据字段实体类
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025/5/12 14:58
 **/
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName(value = "struct_meta_data_field", autoResultMap = true)
public class StructMetaDataField extends AuditBaseEntity {

    /**
     * 中文名：系统属性中文名
     */
    protected String zhName;

    /**
     * 系统属性英文名：系统属性英文名
     */
    protected String enName;

    /**
     * 系统属性类型：系统属性类型
     */
    protected FieldType type;

    /**
     * 类型中文名称
     */
    protected String typeName;

    /**
     * 允许多值。1：是否允许多值。1：是；0：否；默认否
     */
    protected boolean isMultiValue;

    /**
     * 允许空
     */
    protected boolean isNullable = true;

    /**
     * 系统属性描述：系统属性描述
     */
    protected String description;

    /**
     * 元数据ID：元数据ID
     */
    private Integer structMetaDataId;


    /**
     * 是否为统计字段。1：是否为统计字段。1：是；0：否；默认否
     */
    private boolean isStatistic;

    /**
     * 内置类型：内置类型：1普通类型，2系统内置
     */
    private boolean isBuiltIn;
    /**
     * 高级属性：高级属性
     */
    @TableField(typeHandler = PolymorphismTypeHandler.class, updateStrategy = FieldStrategy.ALWAYS)
    protected FieldAdvanceConfig advanceConfig;

    /**
     * 默认值
     */
    protected String defaultValue;


    /**
     * 转化为数据建模字段
     *
     * @return {@link DataModelField}
     */
    public DataModelField fromDataModelField() {
        DataModelField field = new DataModelField();
        field.setZhName(zhName);
        field.setEnName(enName);
        field.setType(type);
        field.setTypeName(typeName);
        field.setNullable(isNullable);
        field.setMultiValue(isMultiValue);
        field.setDescription(description);
        field.setAdvanceConfig(advanceConfig);
        field.setDefaultValue(defaultValue);
        field.setStatistic(isStatistic);
        field.setBuiltIn(isBuiltIn);
        field.setUseStandard(false);
        return field;
    }
}
