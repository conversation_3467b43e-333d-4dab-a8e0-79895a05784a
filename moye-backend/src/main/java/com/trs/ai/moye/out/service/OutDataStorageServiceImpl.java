package com.trs.ai.moye.out.service;

import com.trs.ai.moye.data.connection.request.NebulaConnectionRequest;
import com.trs.moye.base.common.exception.BizException;
import com.trs.moye.base.data.connection.entity.params.NebulaConnectionParams;
import com.vesoft.nebula.client.graph.NebulaPoolConfig;
import com.vesoft.nebula.client.graph.data.HostAddress;
import com.vesoft.nebula.client.graph.data.ResultSet;
import com.vesoft.nebula.client.graph.data.ValueWrapper;
import com.vesoft.nebula.client.graph.exception.AuthFailedException;
import com.vesoft.nebula.client.graph.exception.ClientServerIncompatibleException;
import com.vesoft.nebula.client.graph.exception.IOErrorException;
import com.vesoft.nebula.client.graph.exception.NotValidConnectionException;
import com.vesoft.nebula.client.graph.net.NebulaPool;
import com.vesoft.nebula.client.graph.net.Session;
import java.net.UnknownHostException;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @since 2024/12/30
 **/
@Slf4j
@Service
public class OutDataStorageServiceImpl implements OutDataStorageService {

    public static final String COLUMN_NAME = "Name";
    public static final String SHOW_SPACES = "SHOW SPACES";


    @Override
    public void createDatabaseIfNotExist(NebulaConnectionRequest nebulaConnectionRequest)
        throws UnknownHostException {
        if (nebulaConnectionRequest.getConnectionParams() instanceof NebulaConnectionParams dbConnectionParams
            && Boolean.FALSE.equals(isDatabaseExist(dbConnectionParams))) {
            createDatabase(nebulaConnectionRequest);
        }
    }

    private boolean isDatabaseExist(NebulaConnectionParams dbConnectionParams) throws UnknownHostException {
        Session nebulaSession = createNebulaSession(dbConnectionParams);
        try {
            ResultSet showSpaces = nebulaSession.execute(SHOW_SPACES);
            // 获取name这一列的数据, 去掉字符串首位的双引号
            List<String> databases = showSpaces.colValues(COLUMN_NAME).stream().map(ValueWrapper::toString).map(
                s -> s.substring(1, s.length() - 1)
            ).collect(Collectors.toCollection(ArrayList::new));
            return databases.contains(dbConnectionParams.getDatabase());
        } catch (Exception e) {
            throw new BizException(e);
        } finally {
            nebulaSession.release();
            nebulaSession.close();
        }
    }

    private void createDatabase(NebulaConnectionRequest nebulaConnectionRequest)
        throws UnknownHostException {
        if (nebulaConnectionRequest.getConnectionParams() instanceof NebulaConnectionParams dbConnectionParams) {
            Session session = createNebulaSession(dbConnectionParams);
            try {
                ResultSet execute = session.execute(
                    buildCreateSpaceStatement(dbConnectionParams, nebulaConnectionRequest.getVidType()));
                if (execute.isSucceeded()) {
                    // 强制刷新空间
                    session.execute(SHOW_SPACES);
                    log.info("Create database {} successfully", dbConnectionParams.getDatabase());
                } else {
                    log.error("Create database {} failed", dbConnectionParams.getDatabase());
                    throw new BizException("建表语句错误: " + execute.getErrorMessage());
                }
            } catch (IOErrorException e) {
                throw new BizException(e);
            } finally {
                session.release();
                session.close();
            }
        }
    }

    private static String buildCreateSpaceStatement(NebulaConnectionParams dbConnectionParams, String vidType) {
        String template = "CREATE SPACE IF NOT EXISTS %s(vid_type=%s)";
        return String.format(template, dbConnectionParams.getDatabase(), vidType);
    }

    private Session createNebulaSession(NebulaConnectionParams dbConnectionParams) throws UnknownHostException {
        NebulaPoolConfig nebulaPoolConfig = new NebulaPoolConfig();
        nebulaPoolConfig.setMaxConnSize(10);
        List<HostAddress> addresses = List.of(new HostAddress(dbConnectionParams.getHost(),
            dbConnectionParams.getPort()));
        NebulaPool pool = new NebulaPool();
        pool.init(addresses, nebulaPoolConfig);
        Session session = null;
        try {
            session = pool.getSession(dbConnectionParams.getUsername(),
                dbConnectionParams.getDecryptedPassword(), true);
        } catch (NotValidConnectionException | ClientServerIncompatibleException | IOErrorException
                 | AuthFailedException e) {
            throw new BizException(e);
        }
        return session;
    }

}
