package com.trs.ai.moye.bi.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.trs.ai.moye.bi.domain.response.DashboardChartResponse;
import com.trs.ai.moye.bi.enums.SubjectPublishLocation;
import com.trs.moye.base.common.typehandler.CustomJacksonTypeHandler;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 已发布看板
 *
 * <AUTHOR>
 * @since 2025/1/14 11:19
 */
@Data
@NoArgsConstructor
@TableName(value = "visual_analysis_published_subject", autoResultMap = true)
public class VisualAnalysisPublishedSubject {

    @TableId(type = IdType.AUTO)
    private Integer id;

    /**
     * 看板id
     */
    private Integer subjectId;

    /**
     * 发布到的位置
     */
    private SubjectPublishLocation publishLocation;

    /**
     * 图表信息
     */
    @TableField(typeHandler = CustomJacksonTypeHandler.class)
    private DashboardChartResponse[] charts;
}
