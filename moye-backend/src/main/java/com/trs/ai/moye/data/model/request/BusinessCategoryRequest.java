package com.trs.ai.moye.data.model.request;

import com.trs.moye.base.data.model.entity.BusinessCategory;
import com.trs.ai.moye.out.request.OutBusinessCategoryUpdateRequest;
import java.util.Objects;
import javax.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.validation.annotation.Validated;

/**
 * 业务分类请求实体
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024/9/26 14:35
 **/
@Data
@Validated
@AllArgsConstructor
@NoArgsConstructor
public class BusinessCategoryRequest {


    /**
     * 业务名称
     */
    @NotBlank(message = "业务名称不能为空")
    private String businessName;

    /**
     * 英文缩写
     */
    @NotBlank(message = "英文缩写不能为空")
    private String businessEnName;

    /**
     * 描述
     */
    private String description;

    public BusinessCategoryRequest(OutBusinessCategoryUpdateRequest request) {
        this.businessName = request.getBusinessName();
        this.description = request.getDescription();
    }

    /**
     * 转换添加实体
     *
     * @param request 前端请求
     * @return {@link BusinessCategory}
     * <AUTHOR>
     * @since 2024/9/26 15:20
     */
    public static BusinessCategory fromAdd(BusinessCategoryRequest request) {
        BusinessCategory businessCategory = new BusinessCategory();
        businessCategory.setZhName(request.getBusinessName());
        businessCategory.setEnName(request.getBusinessEnName());
        businessCategory.setDescription(request.getDescription());
        return businessCategory;

    }

    /**
     * 转换更新实体
     *
     * @param id      业务分类id
     * @param request 前端请求
     * @return {@link BusinessCategory}
     * <AUTHOR>
     * @since 2024/9/26 15:20
     */
    public static BusinessCategory fromUpdate(BusinessCategoryRequest request, Integer id) {
        BusinessCategory businessCategory = new BusinessCategory();
        businessCategory.setZhName(request.getBusinessName());
        businessCategory.setEnName(request.getBusinessEnName());
        businessCategory.setDescription(Objects.nonNull(request.getDescription()) ? request.getDescription() : "");
        businessCategory.setId(id);
        return businessCategory;
    }
}
