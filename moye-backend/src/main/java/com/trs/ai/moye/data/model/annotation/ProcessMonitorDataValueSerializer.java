package com.trs.ai.moye.data.model.annotation;

import com.trs.ai.moye.data.model.response.ProcessMonitorDataValue;
import com.trs.moye.base.common.utils.DateTimeUtils.Formatter;
import java.lang.reflect.Field;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import lombok.extern.slf4j.Slf4j;

/**
 * ProcessMonitorDataValueSerializer
 *
 * <AUTHOR>
 * @since 2025/2/12 9:59
 */
@Slf4j
public class ProcessMonitorDataValueSerializer {

    /**
     * 序列化对象
     *
     * @param obj 对象
     * @return 序列化结果
     */
    public static List<ProcessMonitorDataValue> serialize(Object obj) {
        List<ProcessMonitorDataValue> processMonitorDataValues = new ArrayList<>();

        Field[] fields = obj.getClass().getDeclaredFields();
        for (Field field : fields) {
            // 检查是否需要序列化
            if (field.isAnnotationPresent(ProcessMonitorDataValueSerializable.class)) {
                field.setAccessible(true);
                ProcessMonitorDataValueSerializable annotation = field.getAnnotation(
                    ProcessMonitorDataValueSerializable.class);
                String zhName = annotation.value();
                String enName = field.getName();
                Object originalValue = null;
                try {
                    originalValue = field.get(obj);
                } catch (IllegalAccessException e) {
                    log.warn("", e);
                }
                String value = formatValue(originalValue);
                processMonitorDataValues.add(new ProcessMonitorDataValue(zhName, enName, value));
            }
        }

        return processMonitorDataValues;
    }

    private static String formatValue(Object value) {
        if (value == null) {
            return null;
        }
        if (value instanceof LocalDateTime) {
            return ((LocalDateTime) value).format(Formatter.YYYY_MM_DD_HH_MM_SS.getDateTimeFormatter());
        }
        return value.toString();
    }

}
