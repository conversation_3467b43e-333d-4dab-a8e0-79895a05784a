package com.trs.ai.moye.data.service.entity.params;

import com.trs.ai.moye.data.service.entity.DataServiceField;
import com.trs.ai.moye.data.service.entity.DataServiceSortField;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 服务查询参数
 *
 * <AUTHOR>
 * @since 2024/09/26 10:30:41
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ServiceQueryParams extends GuideModeParams {

    /**
     * 返回字段
     */
    private List<DataServiceField> returnFields;
    /**
     * 排序字段
     */
    private List<DataServiceSortField> sortFields;

    @Override
    public <T extends DataServiceConfigParams> DataServiceConfigParams merge(T dataServiceConfigParams){
        super.merge(dataServiceConfigParams);
        if (dataServiceConfigParams instanceof ServiceQueryParams params){
            updateFieldsIfNotEmpty(params.getReturnFields(), this::setReturnFields);
            updateFieldsIfNotEmpty(params.getSortFields(), this::setSortFields);
        }
        return this;
    }
}
