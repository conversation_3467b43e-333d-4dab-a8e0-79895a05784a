package com.xxl.job.admin.config;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import javax.sql.DataSource;
import org.flywaydb.core.Flyway;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

/**
 * flyway配置
 *
 * <AUTHOR>
 * @since 2024/10/25 17:26:43
 */
@Configuration
public class FlywayConfiguration {

    private static final Logger log = LoggerFactory.getLogger(FlywayConfiguration.class);

    @Resource
    private DataSource dataSource;

    /**
     * mysql flyway
     *
     * @return {@link Flyway }
     * <AUTHOR>
     * @since 2024/10/25 20:02:55
     */
    @Primary
    @Bean("mysqlFlyway")
    public Flyway mysqlFlyway() {
        DataSource mysql = dataSource;
        return Flyway.configure()
            .dataSource(mysql)
            .locations("classpath:migration")
            .load();
    }


    /**
     * flyway runner
     *
     * <AUTHOR>
     * @since 2024/10/25 20:06:01
     */
    @Order(1)
    @Component
    public static class FlywayRunner {

        /**
         * MySQL Flyway
         */
        @Resource(name = "mysqlFlyway")
        private Flyway mysqlFlyway;

        @Value("${spring.flyway.enabled}")
        private Boolean flywayEnable;

        /**
         * run
         *
         * <AUTHOR>
         * @since 2024/10/25 20:05:58
         */
        @PostConstruct
        public void run() {
            if (flywayEnable) {
                log.info("开始执行Mysql Flyway迁移");
                mysqlFlyway.migrate();
                log.info("Mysql Flyway迁移结束");
            }
        }
    }
}
