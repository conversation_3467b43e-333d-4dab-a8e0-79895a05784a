package spark;

import com.trs.moye.ability.entity.operator.BatchOperatorRuntimeEntity;
import com.trs.moye.base.common.utils.JsonUtils;
import java.util.Base64;
import java.util.List;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.apache.spark.sql.SparkSession;
import spark.entity.TaskInfo;
import spark.redis.JedisConnection;
import spark.service.ArrangeService;
import spark.service.impl.ArrangeServiceImpl;

/**
 * 程序入口
 *
 * <AUTHOR>
 */
@Slf4j
public class Main {


    public static void main(String[] args) {
        String taskId = args[0];
        String batchId = args[1];
        String redisHost = args[2];
        String redisPort = args[3];
        String redisPassword = args[4];
        String redisDb = args[5];
        String hiveMetastoreUris = args[6];
        String codeParametersString = new String(Base64.getDecoder().decode(args[7]));
        Map<String, String> codeParameters = JsonUtils.toMap(codeParametersString, String.class, String.class);
        log.info("spark app custom variables: taskId:{}, batchId:{}, redisHost:{}, redisPort:{}, redisPwd:{}, redisDB:{}, hiveMetastoreUris:{}, codeParametersString:{}",
                taskId, batchId, redisHost, redisPort, redisPassword, redisDb, hiveMetastoreUris, codeParametersString);

        JedisConnection.init(redisHost, Integer.parseInt(redisPort), redisPassword, Integer.parseInt(redisDb));

        ArrangeService arrangeService = new ArrangeServiceImpl();
        SparkSession sparkSession = SparkSession.builder()
//            .config("hive.metastore.uris", hiveMetastoreUris)
                .enableHiveSupport()
                .config("spark.redis.host", redisHost)
                .config("spark.redis.port", redisPort)
                .config("spark.redis.auth", redisPassword)
                .config("spark.redis.db", redisDb)
                .getOrCreate();

        // 获取任务信息，初步校验任务信息
        List<BatchOperatorRuntimeEntity> executeJobs = arrangeService.getArrangementsFromRedis(sparkSession, taskId);
        // 执行任务
        arrangeService.executeArrangement(sparkSession, executeJobs, new TaskInfo(batchId, taskId, codeParameters));

        sparkSession.stop();
        JedisConnection.closePool();
    }

}
