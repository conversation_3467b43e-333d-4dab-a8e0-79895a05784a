package spark.ability.batch;

import com.trs.moye.ability.entity.operator.BatchOperatorRuntimeEntity;
import java.util.List;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.spark.sql.Dataset;
import org.apache.spark.sql.Row;
import org.apache.spark.sql.SparkSession;
import spark.ability.base.Ability;
import spark.ability.base.AbilityInterface;
import spark.ability.base.InputBindingParser;
import spark.entity.TaskInfo;
import spark.exception.ParamValidationException;

/**
 * 排重
 *
 * <AUTHOR>
 */
public class Distinct extends Ability implements AbilityInterface {

    /**
     * 排重列
     * zhName: 排重列
     * enName: distinctColumn
     * type: ARRAY
     * required: true
     * bindType: PROPERTY
     * 传属性/数组拼接字段
     */
    public static final String DISTINCT_COLUMN = "distinctColumn";

    @Override
    public Dataset<Row> process(SparkSession sparkSession, BatchOperatorRuntimeEntity operator, TaskInfo taskInfo) {
        // 获取输入表
        Dataset<Row> resourceDf = sparkSession.table(operator.getInputTables().values().iterator().next());

        // 获取排重字段列表
        List<String> distinctColumns = InputBindingParser.getBindingColumnNameList(operator.getInputBind().getBinding(DISTINCT_COLUMN));

        if (CollectionUtils.isEmpty(distinctColumns)) {
            throw new ParamValidationException("Distinct column binding is required");
        }

        // 对指定字段进行排重
        return resourceDf.dropDuplicates(distinctColumns.toArray(new String[0]));
    }

    @Override
    public void validate(BatchOperatorRuntimeEntity operator) throws ParamValidationException {
        // 验证输入表数量
        validateInputTables(operator.getInputTables(), 1, 1);

        // 验证排重字段绑定
        List<String> distinctColumns = InputBindingParser.getBindingColumnNameList(operator.getInputBind().getBinding(DISTINCT_COLUMN));
        if (CollectionUtils.isEmpty(distinctColumns)) {
            throw new ParamValidationException("Distinct column binding is required");
        }
    }
}
