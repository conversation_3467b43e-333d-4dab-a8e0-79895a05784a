package spark.ability.batch;

import com.trs.moye.ability.entity.operator.BatchOperatorRuntimeEntity;
import com.trs.moye.base.common.utils.JsonUtils;
import java.util.List;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.spark.sql.Dataset;
import org.apache.spark.sql.Row;
import org.apache.spark.sql.SparkSession;
import spark.ability.base.Ability;
import spark.ability.base.AbilityInterface;
import spark.entity.TaskInfo;
import spark.exception.ParamValidationException;

/**
 * MultiJoin
 * TODO
 *
 * <AUTHOR>
 * @since 2025/1/7 18:09
 */
@Slf4j
public class MultiJoin extends Ability implements AbilityInterface {

    @Override
    public Dataset<Row> process(SparkSession sparkSession, BatchOperatorRuntimeEntity operator, TaskInfo taskInfo) {
//        Param param = Param.from(operator.getParameters());
//        String joinColumn = param.getTableColumn();
//        String joinType = param.getJoinType();
//        List<String> inputTables = operator.getInputTables();
//
//        Dataset<Row> leftTable = sparkSession.table(inputTables.get(0));
//        for (int i = 1; i < inputTables.size(); i++) {
//            Dataset<Row> rightTable = sparkSession.table(inputTables.get(i));
//            leftTable = leftTable.join(rightTable, joinColumn, joinType);
//        }
//        return leftTable;
        return null;
    }

    @Override
    public void validate(BatchOperatorRuntimeEntity operator) throws ParamValidationException {
//        if (Objects.isNull(inputTableNames) || inputTableNames.isEmpty()) {
//            throw new ParamValidationException("ability [MultiJoin] input table count error! can not be null or empty");
//        }
//        Param param = Param.from(batchOperatorRuntimeEntity);
//        if (StringUtils.isBlank(param.getTableColumn()) || Objects.isNull(param.getJoinType())) {
//            throw new ParamValidationException(String.format("ability [MultiJoin] parse params error! actual: %s",
//                batchOperatorRuntimeEntity));
//        }
    }

    @Data
    private static class Param {

        private String tableColumn;

        private String joinType;

        private static Param from(String parameters) {
            List<Param> p = JsonUtils.toList(parameters, Param.class);
            if (p == null || p.isEmpty()) {
                throw new ParamValidationException(String.format("ability [MultiJoin] parse params error! actual: %s", parameters));
            }
            return p.get(0);
        }
    }

}
