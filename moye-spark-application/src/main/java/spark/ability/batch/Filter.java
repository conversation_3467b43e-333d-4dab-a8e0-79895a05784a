package spark.ability.batch;

import com.trs.moye.ability.entity.operator.BatchOperatorRuntimeEntity;
import com.trs.moye.base.common.utils.conditions.SqlUtils;
import com.trs.moye.base.data.connection.enums.ConnectionType;
import lombok.extern.slf4j.Slf4j;
import org.apache.spark.sql.Dataset;
import org.apache.spark.sql.Row;
import org.apache.spark.sql.SparkSession;
import spark.ability.base.Ability;
import spark.ability.base.AbilityInterface;
import spark.entity.TaskInfo;
import spark.exception.ParamValidationException;

/**
 * 过滤
 *
 * <AUTHOR>
 */
@Slf4j
public class Filter extends Ability implements AbilityInterface {

    @Override
    public Dataset<Row> process(SparkSession sparkSession, BatchOperatorRuntimeEntity operator, TaskInfo taskInfo) {
        String sql = SqlUtils.getQuerySql(operator.getConditions(), ConnectionType.HIVE, operator.getInputTables().values().iterator().next());
        log.info("Filter sql:{}", sql);
        return sparkSession.sql(sql);
    }

    @Override
    public void validate(BatchOperatorRuntimeEntity operator) throws ParamValidationException {
        validateInputTables(operator.getInputTables(), 1, 1);
    }

}
