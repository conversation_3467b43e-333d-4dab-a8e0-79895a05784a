package spark.ability.base;

import com.trs.moye.ability.entity.operator.BatchOperatorRuntimeEntity;
import org.apache.spark.sql.Dataset;
import org.apache.spark.sql.Row;
import org.apache.spark.sql.SparkSession;
import spark.entity.TaskInfo;
import spark.exception.ParamValidationException;

/**
 * <AUTHOR>
 */
public interface AbilityInterface {

    /**
     * 执行能力
     *
     * @param sparkSession spark session
     * @param operator     算子信息
     * @param taskInfo     任务信息
     * @return data frame
     */
    Dataset<Row> process(SparkSession sparkSession, BatchOperatorRuntimeEntity operator, TaskInfo taskInfo);

    /**
     * 校验参数
     *
     * @param operator 参数
     */
    void validate(BatchOperatorRuntimeEntity operator) throws ParamValidationException;
}
