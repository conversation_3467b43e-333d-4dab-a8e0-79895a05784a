package spark.ability.base;

import com.trs.moye.ability.entity.InputBind;
import com.trs.moye.ability.entity.InputBind.ArrayBinding;
import com.trs.moye.ability.entity.InputBind.Binding;
import com.trs.moye.ability.entity.InputBind.FixedValueBinding;
import com.trs.moye.ability.entity.InputBind.ObjectArrayBinding;
import com.trs.moye.ability.entity.InputBind.PropertyBinding;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Optional;
import java.util.stream.Collectors;
import javax.validation.constraints.NotNull;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import spark.entity.OperatorColumn;
import spark.exception.ParamValidationException;

/**
 * input bind 解析
 */
public class InputBindingParser {

    /**
     * 获取绑定值, 取单值
     *
     * @param binding 固定值绑定
     * @return 绑定值
     */
    public static String getBindingValue(Binding binding) {
        Object value = getFixedValueBindingValue(binding);
        return Optional.ofNullable(value).map(Object::toString).orElse(null);
    }

    /**
     * 获取绑定值, 取多值
     *
     * @param binding 绑定
     * @return 绑定值, list是描述多值, map是属性与对应的值
     */
    public static List<Map<String, Object>> getBindingValues(Binding binding) {
//        Object value = getFixedValueBindingValue(binding);
//        return Optional.ofNullable(value)
//            .map(list -> {
//                if (!(list instanceof List<?>)) {
//                    throw new ParamValidationException("Binding value must be a list");
//                }
//                // value 必然是个 list, 每个 item 必然是 map
//                return ((List<?>) list).stream()
//                    .map(item -> {
//                        if (!(item instanceof Map<?, ?>)) {
//                            throw new ParamValidationException("Binding value must be a list of objects");
//                        }
//                        Map<?, ?> map = (Map<?, ?>) item;
//                        return map.entrySet().stream().collect(
//                            Collectors.toMap(entry -> entry.getKey().toString(), entry -> (Object) entry.getValue()));
//                    })
//                    .collect(Collectors.toList());
//            }).orElse(null);
        return getObjectArrayBinding(binding).stream().map(item -> item.entrySet().stream()
                .collect(Collectors.toMap(Entry::getKey, entry -> getFixedValueBindingValue(entry.getValue()))))
            .collect(Collectors.toList());
    }

    private static Object getFixedValueBindingValue(Binding binding) {
        if (binding == null) {
            return null;
        }
        if (binding instanceof FixedValueBinding) {
            FixedValueBinding fixedValueBinding = (FixedValueBinding) binding;
            return fixedValueBinding.getFixedValue();
        } else {
            throw new ParamValidationException("Binding must be a fixed value binding");
        }
    }

    @NotNull
    private static List<Map<String, Binding>> getObjectArrayBinding(Binding binding) {
        if (binding == null) {
            return Collections.emptyList();
        }
        if (binding instanceof ObjectArrayBinding) {
            List<InputBind> items = ((ObjectArrayBinding) binding).getItems();
            return Optional.ofNullable(items)
                .map(list -> list.stream().map(InputBind::getBindings).collect(Collectors.toList()))
                .orElse(Collections.emptyList());
        } else {
            throw new ParamValidationException("Binding must be an object array binding");
        }
    }

    /**
     * 获取绑定值, 取多属性, 只要字段名
     *
     * @param binding 绑定
     * @return 绑定字段名列表
     */
    public static List<String> getBindingColumnNameList(Binding binding) {
        if (binding == null) {
            return Collections.emptyList();
        } else if (binding instanceof ArrayBinding) {
            return getArrayBindingColumnNameList((ArrayBinding) binding);
        } else if (binding instanceof PropertyBinding) {
            String propertyBindingValue = getPropertyBindingColumnName(binding);
            return propertyBindingValue == null ? Collections.emptyList(): Collections.singletonList(propertyBindingValue);
        } else {
            throw new ParamValidationException("Binding must be a property binding or array binding");
        }
    }

    /**
     * 获取绑定值, 取单属性, 只要字段名
     *
     * @param binding 属性绑定
     * @return 绑定字段名
     */
    public static String getPropertyBindingColumnName(Binding binding) {
        if (binding == null) {
            return null;
        }
        if (binding instanceof PropertyBinding) {
            String jsonPath = ((PropertyBinding) binding).getJsonPath();
            if (StringUtils.isBlank(jsonPath)) {
                return null;
            }
            return getFieldNameFromJsonPath(jsonPath);
        } else {
            throw new ParamValidationException("Binding must be a property binding");
        }
    }

    private static List<String> getArrayBindingColumnNameList(ArrayBinding arrayBinding) {
        List<String> jsonPaths = arrayBinding.getJsonPaths();
        if (CollectionUtils.isEmpty(jsonPaths)) {
            return Collections.emptyList();
        }
        return jsonPaths.stream().map(InputBindingParser::getFieldNameFromJsonPath).collect(Collectors.toList());
    }

    public static String getFieldNameFromJsonPath(String jsonPath) {
        if (jsonPath == null) {
            return null;
        }
        return jsonPath.substring(jsonPath.lastIndexOf("/") + 1);
    }


    /**
     * 获取绑定值, 取单属性
     *
     * @param binding 属性绑定
     * @param inputTables 输入表列表
     * @return 绑定字段名
     */
    public static OperatorColumn getPropertyBindingColumn(Binding binding, Map<Long, String> inputTables) {
        if (binding == null) {
            return null;
        }
        if (binding instanceof PropertyBinding) {
            String jsonPath = ((PropertyBinding) binding).getJsonPath();
            if (StringUtils.isBlank(jsonPath)) {
                return null;
            }
            return getFieldFromJsonPath(jsonPath, inputTables);
        } else {
            throw new ParamValidationException("Binding must be a property binding");
        }
    }

    /**
     * 获取绑定值, 取多属性
     *
     * @param binding 绑定
     * @return 绑定字段名列表
     */
    public static List<OperatorColumn> getBindingColumnList(Binding binding, Map<Long, String> inputTables) {
        if (binding == null) {
            return Collections.emptyList();
        } else if (binding instanceof ArrayBinding) {
            return getArrayBindingColumnList((ArrayBinding) binding, inputTables);
        } else if (binding instanceof PropertyBinding) {
            OperatorColumn propertyBindingValue = getPropertyBindingColumn(binding, inputTables);
            return propertyBindingValue == null ? Collections.emptyList(): Collections.singletonList(propertyBindingValue);
        } else {
            throw new ParamValidationException("Binding must be a property binding or array binding");
        }
    }

    private static List<OperatorColumn> getArrayBindingColumnList(ArrayBinding arrayBinding, Map<Long, String> inputTables) {
        List<String> jsonPaths = arrayBinding.getJsonPaths();
        if (CollectionUtils.isEmpty(jsonPaths)) {
            return Collections.emptyList();
        }
        return jsonPaths.stream().map(jsonPath -> getFieldFromJsonPath(jsonPath, inputTables)).collect(Collectors.toList());
    }

    /**
     * 处理jsonPath，获取字段详细信息
     *
     * @param jsonPath e.g. 1749714033936:/id
     * @param inputTables 输入表列表
     * @return 表和字段名
     */
    public static OperatorColumn getFieldFromJsonPath(String jsonPath, Map<Long, String> inputTables) {
        if (jsonPath == null) {
            return null;
        }

        // 获取displayId，即 : 前到 / 为止的数字部分
        Long displayId = null;
        if (jsonPath.contains(":")) {
            String substringWithDisplayId = jsonPath.substring(0, jsonPath.indexOf(":"));
            displayId = Long.valueOf(substringWithDisplayId.substring(substringWithDisplayId.lastIndexOf("/")+1));
        }

        String tableName = null;
        if (displayId != null) {
            tableName = inputTables.get(displayId);
            if (tableName == null) {
                throw new ParamValidationException("Table not found for displayId: " + displayId);
            }
        }

        return new OperatorColumn(displayId, tableName, jsonPath.substring(jsonPath.lastIndexOf("/") + 1));
    }
}
