package spark.ability.connection.connector.elasticsearch.eight;

import co.elastic.clients.elasticsearch.ElasticsearchClient;
import co.elastic.clients.elasticsearch._types.mapping.Property;
import co.elastic.clients.elasticsearch.core.bulk.BulkOperation;
import co.elastic.clients.elasticsearch.indices.GetMappingResponse;
import co.elastic.clients.elasticsearch.indices.get_mapping.IndexMappingRecord;
import com.trs.moye.base.common.utils.JsonUtils;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import lombok.extern.slf4j.Slf4j;
import org.apache.spark.api.java.function.ForeachPartitionFunction;
import org.elasticsearch.client.RestClient;
import org.jetbrains.annotations.NotNull;
import spark.ability.connection.connector.elasticsearch.FieldMetadata;
import spark.ability.connection.connector.elasticsearch.common.ElasticsearchClientCreator;
import spark.ability.connection.connector.elasticsearch.common.HandleOperation;
import spark.ability.connection.params.EsConnectionParams;

/**
 * OperationHandler
 *
 * <AUTHOR>
 * @since 2025/6/20 18:03
 */
@Slf4j
public class OperationHandler implements HandleOperation {

    @NotNull
    public ForeachPartitionFunction<String> forEachPartition(EsConnectionParams params,
        String index, String idField, int bulkMaxNum, int bulkMaxSize, int flushTime, int concurrentRequestsNum,
        int sleepTime) {
        return rowIterator -> {
            try (RestClient restClient = ElasticsearchClientCreator.initRestClient(params)) {
                ElasticsearchClient elasticsearchClient = ElasticsearchClientCreator.initElasticsearchClient(restClient);

                // 创建BulkIngester
                BulkIngesterWrapper bulkIngester = BulkIngesterWrapper.createBulkIngester(elasticsearchClient, bulkMaxNum,
                    bulkMaxSize,
                    flushTime, concurrentRequestsNum, sleepTime);

                // 添加请求
                rowIterator.forEachRemaining(row -> {
                    Map<String, Object> dfInfoMap = JsonUtils.toMap(row, String.class, Object.class);
                    String id = HandleOperation.getId(idField, dfInfoMap);
                    bulkIngester.add(BulkOperation.of(b -> b.index(i -> i.index(index).id(id).document(dfInfoMap))));
                });

                // 数据操作完毕，手动触发一次请求
                bulkIngester.close();
                // 抛出es请求异常，包括部分数据异常
                bulkIngester.checkForFailures();
            } catch (RuntimeException e) {
                throw e;
            } catch (Throwable e) {
                throw new RuntimeException(e);
            }
        };
    }

    public Map<String, FieldMetadata> getFieldsForConverter(String index, EsConnectionParams params) {
        Map<String, FieldMetadata> fieldsDataType = new HashMap<>();
        try (RestClient restClient = ElasticsearchClientCreator.initRestClient(params)) {
            ElasticsearchClient elasticsearchClient = ElasticsearchClientCreator.initElasticsearchClient(restClient);
            GetMappingResponse response = elasticsearchClient.indices().getMapping(b -> b.index(index));
            IndexMappingRecord mappingRecord = Objects.requireNonNull(response.get(index));
            Map<String, Property> properties = mappingRecord.mappings().properties();
            for (Map.Entry<String, Property> entry : properties.entrySet()) {
                String field = entry.getKey();
                Property property = entry.getValue();
                if (property.isDate()) {
                    fieldsDataType.put(field, new FieldMetadata(field, "date", property.date().format()));
                }
            }
        } catch (Exception e) {
            log.warn("获取/{}/_mapping失败", index, e);
        }
        return fieldsDataType;
    }

}
