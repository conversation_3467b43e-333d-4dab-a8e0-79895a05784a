package spark.ability.connection.connector.hybase;

import com.trs.hybase.client.TRSException;
import com.trs.hybase.client.TRSRecord;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.StringTokenizer;
import org.apache.spark.sql.types.DataType;
import org.apache.spark.sql.types.DataTypes;
import spark.version.SparkVersionUtil;


/**
 * field type
 * <br>
 * 字段类型，hybase可获取的数据类型与spark数据类型的映射
 *
 * <AUTHOR>
 * @since 2024/11/18 15:47
 */
public enum FieldType {

    STRING(DataTypes.StringType),

    DATETIME(SparkVersionUtil.getCompatibleTimestampType()) {
        @Override
        public LocalDateTime get(TRSRecord trsRecord, String colName) throws TRSException {
            return trsRecord.getDate(colName) == null ? null:
                    trsRecord.getDate(colName).toInstant()
                            .atZone(ZoneId.systemDefault())
                            .toLocalDateTime();
        }
    },

    BOOL(DataTypes.BooleanType) {
        @Override
        public Boolean get(TRSRecord trsRecord, String colName) throws TRSException {
            return Boolean.valueOf(trsRecord.getString(colName));
        }
    },

    LONG(DataTypes.LongType) {
        /**
         * {@link TRSRecord#getLong(String)}
         */
        @Override
        public Long get(TRSRecord trsRecord, String colName) throws TRSException {
            String[] values = getValues(trsRecord, colName);
            if (values != null && values.length != 0) {
                try {
                    return Long.parseLong(values[0]);
                } catch (NumberFormatException var4) {
                    throw new TRSException(500001, var4.getMessage());
                }
            } else {
                return null;
            }
        }
    },

    BYTE(DataTypes.ByteType),

    DOUBLE(DataTypes.DoubleType) {
        /**
         * {@link TRSRecord#getDouble(String)}
         */
        @Override
        public Double get(TRSRecord trsRecord, String colName) throws TRSException {
            String[] values = getValues(trsRecord, colName);
            if (values != null && values.length != 0) {
                try {
                    return Double.parseDouble(values[0]);
                } catch (NumberFormatException var4) {
                    throw new TRSException(500001, var4.getMessage());
                }
            } else {
                return null;
            }
        }
    };

    private final DataType dataType;

    /**
     * 从TRSRecord中获取字段值
     *
     * @param trsRecord hybase数据记录
     * @param colName   字段名
     * @return 字段值
     */
    public Object get(TRSRecord trsRecord, String colName) throws TRSException {
        return trsRecord.getString(colName);
    }

    FieldType(DataType dataType) {
        this.dataType = dataType;
    }

    public DataType getDataType() {
        return dataType;
    }


    /**
     * 同 {@code TRSRecord#getValues(String)}
     * <br>
     * 提取出来供访问，用于修改getLong等逻辑
     */
    String[] getValues(TRSRecord trsRecord, String colName) throws TRSException {
        String value = trsRecord.getString(colName);
        if (value == null) {
            return null;
        } else {
            StringTokenizer token = new StringTokenizer(value, ";");
            String[] valStrings = new String[token.countTokens()];

            for(int i = 0; token.hasMoreTokens(); valStrings[i++] = token.nextToken()) {
            }

            return valStrings;
        }
    }

}
