package spark.ability.connection.connector.hybase;

import com.trs.hybase.client.TRSConnection;
import com.trs.hybase.client.TRSDatabase;
import com.trs.hybase.client.TRSDatabaseColumn;
import com.trs.hybase.client.TRSException;
import com.trs.hybase.client.TRSInputRecord;
import com.trs.hybase.client.TRSResultSet;
import com.trs.hybase.client.params.ConnectParams;
import com.trs.hybase.client.params.OperationParams;
import com.trs.hybase.client.params.SearchParams;
import com.trs.moye.base.data.connection.entity.params.HybaseConnectionParams;
import java.io.Closeable;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import spark.exception.HybaseException;

/**
 * HybaseConnection
 * <br>
 * 包装海贝的api
 *
 * <AUTHOR>
 * @since 2024/11/19 11:12
 */
public class HybaseConnection implements Closeable {

    private final TRSConnection trsConnection;

    private HybaseConnection(TRSConnection trsConnection) {
        this.trsConnection = trsConnection;
    }

    public static HybaseConnection createHybaseConnection(HybaseConnectionParams params) {
        TRSConnection trsConnection = new TRSConnection(String.join(",", params.getHosts()), params.getUsername(),
            params.getDecryptedPassword(), new ConnectParams());
        return new HybaseConnection(trsConnection);
    }

    /**
     * 获取字段信息
     *
     * @param tableName 表名
     * @return {@link LinkedHashMap} 字段信息
     */
    public List<HybaseField> getFieldsInfo(String tableName) {
        List<HybaseField> fields = new ArrayList<>();
        try {
            TRSDatabase database = getDatabases(tableName);
            TRSDatabaseColumn[] allColumns = database.getAllColumns();
            for (TRSDatabaseColumn column : allColumns) {
                String colName = column.getName();
                int colType = column.getColType();
                Boolean isFloat = column.isFloat();
                fields.add(new HybaseField(colName, colType, isFloat));
            }
        } catch (TRSException e) {
            throw new HybaseException("get table[" + tableName + "] fields info error.", e);
        }
        return fields;
    }

    private TRSDatabase getDatabases(String tableName) {
        Map<String, String> options = new HashMap<>(1);
        options.put("alias.orig.db", "true");
        try {
            TRSDatabase[] databases = trsConnection.getDatabases(tableName, options);
            if (Objects.isNull(databases) || databases.length == 0) {
                throw new HybaseException("table:" + tableName + " not found.");
            }
            return databases[0];
        } catch (TRSException e) {
            String errMsg = String.format("get database error. table: %s", tableName);
            throw new HybaseException(errMsg, e);
        }
    }

    /**
     * 读取海贝数据
     *
     * @param tableName 表
     * @param sql       sql
     * @param start     分页: 开始位置
     * @param num       分页: 数据数量
     * @return 数据
     */
    public TRSResultSet executeSelect(String tableName, String sql, long start, long num) {
        try {
            return trsConnection.executeSelectNoSort(tableName, sql, start, num, new SearchParams());
        } catch (TRSException e) {
            String errMsg = String.format("search table: %s data error.", tableName);
            throw new HybaseException(errMsg, e);
        }
    }

    /**
     * 获取数据总量
     *
     * @param tableName 表
     * @return 表数据总量
     */
    public long getTotal(String tableName) {
        TRSResultSet countResult;
        try {
            countResult = trsConnection.executeSelectNoSort(tableName, null, 0, 0, new SearchParams());
        } catch (TRSException e) {
            String errMsg = String.format("table:%s get total number error.", tableName);
            throw new HybaseException(errMsg, e);
        }
        return countResult.getNumFound();
    }

    /**
     * 插入数据
     *
     * @param tableName 表名
     * @param records   数据
     */
    public void executeInsert(String tableName, List<TRSInputRecord> records) {
        OperationParams params = new OperationParams();
        params.setBoolProperty("insert.duplicate.override", true);
        try {
            trsConnection.executeInsert(tableName, records, params, null);
        } catch (TRSException e) {
            String errMsg = String.format("table:%s execute insert error.", tableName);
            throw new HybaseException(errMsg, e);
        }
    }

    @Override
    public void close() {
        trsConnection.close();
    }

}
