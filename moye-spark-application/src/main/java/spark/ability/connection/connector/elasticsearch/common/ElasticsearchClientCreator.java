package spark.ability.connection.connector.elasticsearch.common;

import co.elastic.clients.elasticsearch.ElasticsearchClient;
import co.elastic.clients.json.jackson.JacksonJsonpMapper;
import co.elastic.clients.transport.rest_client.RestClientTransport;
import com.trs.moye.base.data.connection.entity.KerberosCertificate;
import java.util.Base64;
import java.util.stream.Stream;
import org.apache.http.Header;
import org.apache.http.HttpHost;
import org.apache.http.auth.AuthScope;
import org.apache.http.auth.UsernamePasswordCredentials;
import org.apache.http.client.CredentialsProvider;
import org.apache.http.impl.client.BasicCredentialsProvider;
import org.apache.http.message.BasicHeader;
import org.elasticsearch.client.RestClient;
import org.elasticsearch.client.RestClientBuilder;
import org.elasticsearch.client.RestHighLevelClient;
import spark.ability.connection.params.EsConnectionParams;

/**
 * esClientCreator
 *
 * <AUTHOR>
 * @since 2024/11/27 14:45
 */
public class ElasticsearchClientCreator {
    private ElasticsearchClientCreator() {}


    private static RestClientBuilder initRestClientBuilder(EsConnectionParams params) {
        HttpHost[] esHosts = Stream.of(params.getHost().split(","))
            .map(host -> new HttpHost(host.trim(), params.getPort(), params.getProtocol()))
            .toArray(HttpHost[]::new);
        RestClientBuilder builder = RestClient.builder(esHosts);
        KerberosCertificate kerberosCertificate = params.getKerberosCertificate();
        //如果开启kerberos认证
        if (kerberosCertificate != null) {
            String keytabPath = kerberosCertificate.getKeytabPath();
            String principal = kerberosCertificate.getPrincipal();
            String krb5Path = kerberosCertificate.getKrb5Path();
            System.setProperty("java.security.krb5.conf", krb5Path);
            builder.setHttpClientConfigCallback(
                    new SpnegoHttpClientConfigCallbackHandler(principal, keytabPath, false));
        }

        //如果开启用户名密码认证
        else if (params.getUsername() != null && params.getPassword() != null) {
            final CredentialsProvider credentialsProvider = new BasicCredentialsProvider();
            credentialsProvider.setCredentials(AuthScope.ANY,
                    new UsernamePasswordCredentials(params.getUsername(), params.getPassword()));
            builder.setHttpClientConfigCallback(httpClientBuilder ->
                    httpClientBuilder.setDefaultCredentialsProvider(credentialsProvider));
            //华为云es需要使用basic auth
            String auth = Base64.getEncoder()
                    .encodeToString((params.getUsername() + ":" + params.getPassword()).getBytes());
            builder.setDefaultHeaders(new Header[]{new BasicHeader("Authorization", "Basic " + auth)});
        }

        return builder;
    }

    public static RestClient initRestClient(EsConnectionParams params) {
        return initRestClientBuilder(params).build();
    }

    public static RestHighLevelClient initRestHighLevelClient(EsConnectionParams params) {
        return new RestHighLevelClient(initRestClientBuilder(params));
    }

    public static ElasticsearchClient initElasticsearchClient(RestClient restClient) {
        return new ElasticsearchClient(new RestClientTransport(restClient, new JacksonJsonpMapper()));
    }
}
