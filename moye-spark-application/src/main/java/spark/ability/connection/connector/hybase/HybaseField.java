package spark.ability.connection.connector.hybase;

import com.trs.hybase.client.TRSDatabaseColumn;
import org.apache.spark.sql.types.Metadata;
import org.apache.spark.sql.types.StructField;

/**
 * HybaseField
 * <br>
 * 字段信息
 *
 * <AUTHOR>
 * @since 2024/11/19 15:02
 */
public class HybaseField {

    private String name;
    private FieldType type;

    public HybaseField(String colName, int colType, Boolean isFloat) {
        this.name = colName;
        switch (colType) {
            case TRSDatabaseColumn.TYPE_DATE:
                this.type = FieldType.DATETIME;
                break;
            case TRSDatabaseColumn.TYPE_NUMBER:
                if (Boolean.TRUE.equals(isFloat)) {
                    this.type = FieldType.DOUBLE;
                } else {
                    this.type = FieldType.LONG;
                }
                break;
            case TRSDatabaseColumn.TYPE_BOOL:
                this.type = FieldType.BOOL;
                break;
            case TRSDatabaseColumn.TYPE_BIT:
                this.type = FieldType.BYTE;
                break;
            case TRSDatabaseColumn.TYPE_CHAR:
            case TRSDatabaseColumn.TYPE_DOCUMENT:
            case TRSDatabaseColumn.TYPE_PHRASE:
            case TRSDatabaseColumn.TYPE_OBJECT:
            case TRSDatabaseColumn.TYPE_GEO:
            case TRSDatabaseColumn.TYPE_VECTOR:
            default:
                this.type = FieldType.STRING;
        }
    }

    public StructField getStructField() {
        return new StructField(name, type.getDataType(), true, Metadata.empty());
    }

    public String getName() {
        return name;
    }

    public FieldType getType() {
        return type;
    }
}
