package spark.ability.connection.connector;

import com.trs.moye.ability.entity.operator.BatchOperatorRuntimeEntity.TableInfo;
import com.trs.moye.base.data.service.entity.Condition;
import java.util.List;
import org.apache.spark.sql.Dataset;
import org.apache.spark.sql.Row;
import org.apache.spark.sql.SparkSession;

/**
 * <AUTHOR>
 * @since 2024/10/31 10:16
 */
public interface Connector {

    /**
     * 根据连接信息生成dataFrame
     *
     * @param sparkSession  spark会话
     * @param storageParams 存储信息
     * @param conditions    查询条件
     * @return dataFrame
     */
    Dataset<Row> getInputDf(SparkSession sparkSession, TableInfo storageParams, List<Condition> conditions, String sql);

    /**
     * 存储数据
     *
     * @param sparkSession  spark会话
     * @param df            dataFrame
     * @param storageParams 存储信息
     */
    void save(SparkSession sparkSession, Dataset<Row> df, TableInfo storageParams);
}
