package spark.ability.connection.connector.elasticsearch.eight;

import co.elastic.clients.elasticsearch.ElasticsearchClient;
import co.elastic.clients.elasticsearch._helpers.bulk.BulkIngester;
import co.elastic.clients.elasticsearch.core.bulk.BulkOperation;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * BulkProcessorWithCheck
 *
 * <AUTHOR>
 * @since 2025/6/20 15:55
 */
public class BulkIngesterWrapper {
    private final BulkIngester<Map<String,Object>> processor;
    private final BulkListener listener;

    public BulkIngesterWrapper(BulkIngester<Map<String,Object>> processor, BulkListener listener) {
        this.processor = processor;
        this.listener = listener;
    }

    /**
     * 检查并抛出异常
     *
     * @throws Throwable throwable
     */
    public void checkForFailures() throws Throwable {
        if (listener.getFailure() != null) {
            throw listener.getFailure();
        }
    }

    public void add(BulkOperation request) {
        processor.add(request);
    }

    public void close() {
        processor.close();
    }

    /**
     * 批量请求管理器
     *
     * @param elasticsearchClient   es client
     * @param bulkMaxNum            最大请求数
     * @param bulkMaxSize           最大请求量，单位 MB
     * @param flushTime             刷新间隔，单位 秒
     * @param concurrentRequestsNum 并发请求数
     * @param sleepTime             批次请求 间隔 睡眠时间
     * @return BulkIngesterWrapper
     */
    public static BulkIngesterWrapper createBulkIngester(ElasticsearchClient elasticsearchClient, int bulkMaxNum,
        int bulkMaxSize, int flushTime, int concurrentRequestsNum, int sleepTime) {
        BulkListener bulkListener = new BulkListener(sleepTime);
        BulkIngester<Map<String, Object>> bulkIngester = BulkIngester.of(bi -> bi
            .listener(bulkListener)
            .client(elasticsearchClient)
            .maxOperations(bulkMaxNum)
            .maxSize((long) bulkMaxSize * 1024 * 1024)
            .flushInterval(flushTime, TimeUnit.SECONDS)
            .maxConcurrentRequests(concurrentRequestsNum));
        return new BulkIngesterWrapper(bulkIngester, bulkListener);
    }
}
