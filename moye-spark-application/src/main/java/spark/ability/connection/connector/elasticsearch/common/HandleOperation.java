package spark.ability.connection.connector.elasticsearch.common;

import java.util.Map;
import org.apache.commons.lang3.StringUtils;
import org.apache.spark.api.java.function.ForeachPartitionFunction;
import spark.ability.connection.connector.elasticsearch.FieldMetadata;
import spark.ability.connection.params.EsConnectionParams;

/**
 * HandleOperation
 *
 * <AUTHOR>
 * @since 2025/6/20 18:06
 */
public interface HandleOperation {

    static String getId(String id, Map<String, Object> dfInfoMap) {
        if (StringUtils.isBlank(id)) {
            return null;
        }
        return dfInfoMap.get(id) == null ? null : dfInfoMap.get(id).toString();
    }

    ForeachPartitionFunction<String> forEachPartition(EsConnectionParams params,
        String index, String idField, int bulkMaxNum, int bulkMaxSize, int flushTime, int concurrentRequestsNum,
        int sleepTime);

    Map<String, FieldMetadata> getFieldsForConverter(String index, EsConnectionParams params);
}
