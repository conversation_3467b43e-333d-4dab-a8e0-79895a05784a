package com.trs.ai.moye.monitor.notice.event;

import static org.junit.jupiter.api.Assertions.assertAll;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;

import java.time.LocalDateTime;
import java.util.LinkedHashMap;
import org.junit.jupiter.api.Test;

class NoticeContentBuilderTest {

    // 测试基础键值对生成
    @Test
    void testBuildBasicContent() {
        LinkedHashMap<String, Object> params = new LinkedHashMap<>();
        params.put("数据库", "order_db");
        params.put("记录数", 1500);
        params.put("状态", "正常");

        String result = NoticeContentBuilder.build("基础测试告警", params);

        String expected = """
            ### 基础测试告警  \s
            - **数据库**: `order_db`  \s
            - **记录数**: `1500`  \s
            - **状态**: `正常`
            """.replaceAll("\\s+", " ").trim();

        assertEquals(expected, result.replaceAll("\\s+", " ").trim());
    }

    // 测试时间格式化
    @Test
    void testDateTimeFormatting() {
        LocalDateTime time = LocalDateTime.of(2023, 10, 5, 14, 30);
        LinkedHashMap<String, Object> params = new LinkedHashMap<>();
        params.put("时间", time);

        String result = NoticeContentBuilder.build("时间测试", params);

        assertTrue(result.contains("`2023-10-05 14:30:00`"));
    }

    // 测试多行文本自动添加代码块
    @Test
    void testMultilineTextBlock() {
        String errorMsg = "第一行错误\n第二行详情";
        LinkedHashMap<String, Object> params = new LinkedHashMap<>();
        params.put("错误日志", errorMsg);

        String result = NoticeContentBuilder.build("多行文本测试", params);

        String expected = """
            ```text
            第一行错误
            第二行详情
            ```
            """.trim();

        assertTrue(result.contains(expected));
    }

    // 测试空值处理
    @Test
    void testNullValueHandling() {
        LinkedHashMap<String, Object> params = new LinkedHashMap<>();
        params.put("空字段", null);
        params.put("空字符串", "");

        String result = NoticeContentBuilder.build("空值测试", params);

        assertAll(
            () -> assertTrue(result.contains("`null`")),
            () -> assertTrue(result.contains("``")) // 空字符串显示为 ``
        );
    }

    // 测试数字类型处理
    @Test
    void testNumberFormatting() {
        LinkedHashMap<String, Object> params = new LinkedHashMap<>();
        params.put("整型", 100);
        params.put("浮点型", 3.14);

        String result = NoticeContentBuilder.build("数字测试", params);

        assertAll(
            () -> assertTrue(result.contains("`100`")),
            () -> assertTrue(result.contains("`3.14`"))
        );
    }

    // 测试字段顺序保持
    @Test
    void testFieldOrderPreservation() {
        LinkedHashMap<String, Object> params = new LinkedHashMap<>();
        params.put("第一字段", "1");
        params.put("第二字段", "2");
        params.put("第三字段", "3");

        String result = NoticeContentBuilder.build("顺序测试", params);

        assertTrue(result.indexOf("第一字段") < result.indexOf("第二字段"));
        assertTrue(result.indexOf("第二字段") < result.indexOf("第三字段"));
    }

    // 测试标题生成
    @Test
    void testTitleGeneration() {
        LinkedHashMap<String, Object> params = new LinkedHashMap<>();
        params.put("测试键", "值");

        String result = NoticeContentBuilder.build("标题测试", params);

        assertTrue(result.startsWith("### 标题测试  \n- **测试键**:"));
    }
}