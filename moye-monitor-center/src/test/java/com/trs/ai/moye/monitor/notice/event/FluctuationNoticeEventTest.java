// FluctuationNoticeEventTest.java
package com.trs.ai.moye.monitor.notice.event;

import static org.junit.jupiter.api.Assertions.*;

import com.trs.moye.base.monitor.entity.MonitorOdsFluctuation;
import java.time.LocalDateTime;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;

@Slf4j
class FluctuationNoticeEventTest {

    @Test
    void testFluctuationNoticeEventContent() {
        MonitorOdsFluctuation fluctuation = new MonitorOdsFluctuation();
        fluctuation.setDataModelName("数据模型C");
        fluctuation.setMonitorTime(LocalDateTime.of(2023, 10, 1, 12, 30, 45));
        fluctuation.setFluctuation(90);
        fluctuation.setFluctuationType(1);

        FluctuationNoticeEvent event = new FluctuationNoticeEvent(123, fluctuation);

        String content = event.getContent();
        log.info("FluctuationNoticeEvent content: {}", content);

        assertTrue(content.contains("数据模型C"));
        assertTrue(content.contains("2023-10-01 12:30:45"));
        assertTrue(content.contains("90"));
    }
}