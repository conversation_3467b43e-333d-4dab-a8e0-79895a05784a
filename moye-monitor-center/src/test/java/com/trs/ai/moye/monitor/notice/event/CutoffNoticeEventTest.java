// CutoffNoticeEventTest.java
package com.trs.ai.moye.monitor.notice.event;

import static org.junit.jupiter.api.Assertions.*;

import com.trs.moye.base.monitor.entity.MonitorOdsCutoff;
import java.time.LocalDateTime;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;

@Slf4j
class CutoffNoticeEventTest {

    @Test
    void testCutoffNoticeEventContent() {
        MonitorOdsCutoff cutoff = new MonitorOdsCutoff();
        cutoff.setDataModelName("数据模型A");
        cutoff.setMonitorTime(LocalDateTime.of(2023, 10, 1, 12, 30, 45));

        CutoffNoticeEvent event = new CutoffNoticeEvent(123, cutoff);

        String content = event.getContent();
        log.info("CutoffNoticeEvent content: {}", content);

        assertTrue(content.contains("数据模型A"));
        assertTrue(content.contains("2023-10-01 12:30:45"));
    }
}