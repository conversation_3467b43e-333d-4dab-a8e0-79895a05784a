package com.trs.ai.moye.monitor.websocket;

import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 用于描述连接channel
 *
 * <AUTHOR>
 * @date 2022/04/01
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode
public class Channel implements Serializable {

    private static final long serialVersionUID = 2651123191983330393L;

    /**
     * 频道名称
     */
    private String channelName;

    public static Channel getNoticeChannel() {
        return new Channel("notice-center");
    }
}
