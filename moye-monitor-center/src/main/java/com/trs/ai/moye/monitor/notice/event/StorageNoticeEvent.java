package com.trs.ai.moye.monitor.notice.event;

import com.trs.ai.moye.monitor.utils.BeanUtil;
import com.trs.ai.moye.monitor.websocket.NoticeVO;
import com.trs.moye.base.common.enums.ModelLayer;
import com.trs.moye.base.common.utils.JsonUtils;
import com.trs.moye.base.data.model.dao.DataModelMapper;
import com.trs.moye.base.data.model.entity.DataModel;
import com.trs.moye.base.monitor.enums.NoticeTypeEnum;
import java.util.LinkedHashMap;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 存储引擎报错消息
 *
 * <AUTHOR>
 */
@Slf4j
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
public class StorageNoticeEvent extends AbstractNoticeEvent {

    /**
     * 接入批次号
     */
    private String batchNo;
    /**
     * 报错信息
     */
    private String errMessage;


    @Override
    public NoticeVO createNoticeVO() {
        log.info("StorageNotice-- StorageNoticeEvent {}", JsonUtils.toJsonString(this));
        NoticeVO vo = new NoticeVO(this);
        vo.setNoticeType(NoticeTypeEnum.TASK_EXCEPTION.getCode());
        DataModelMapper dataModelMapper = BeanUtil.getBean(DataModelMapper.class);
        DataModel dataModel = dataModelMapper.selectById(getMetadataId());
        ModelLayer layer = dataModel.getLayer();
        vo.setSubType(layer.getLabel() + "任务");

        log.info("StorageNotice-- metadataId: {}", getMetadataId());
        String title = String.format(layer.getLabel() + "【%s】数据存储任务失败", dataModel.getZhName());
        vo.setTitle(title);

        LinkedHashMap<String, Object> contentParams = new LinkedHashMap<>();
        contentParams.put(layer.getLabel() + "名称", dataModel.getZhName());
        contentParams.put("批次号", batchNo);
        contentParams.putAll(ErrorMessageParser.parseErrorMessage(errMessage));
        vo.setContent(NoticeContentBuilder.build("数据存储失败告警", contentParams));

        return vo;
    }
}
