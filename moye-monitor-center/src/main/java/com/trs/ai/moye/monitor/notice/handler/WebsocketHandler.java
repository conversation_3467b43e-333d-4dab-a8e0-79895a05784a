package com.trs.ai.moye.monitor.notice.handler;

import com.trs.ai.moye.monitor.entity.InsideNoticeConfig;
import com.trs.ai.moye.monitor.mapper.MessageSendConfInsideMapper;
import com.trs.ai.moye.monitor.mapper.NoticeRelationMapper;
import com.trs.ai.moye.monitor.mapper.UserMapper;
import com.trs.ai.moye.monitor.service.NoticeService;
import com.trs.ai.moye.monitor.utils.BeanUtil;
import com.trs.ai.moye.monitor.websocket.NoticeVO;
import com.trs.ai.moye.monitor.websocket.WsSessionManager;
import com.trs.moye.base.common.constants.RoleConstants;
import com.trs.moye.base.monitor.enums.NoticeTypeEnum;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import lombok.extern.slf4j.Slf4j;

/**
 * 消息推送到websocket
 *
 * <AUTHOR>
 */
@Slf4j
public class WebsocketHandler implements NoticeHandler {

    @Override
    public void handleNotice(NoticeVO notice) {
        List<InsideNoticeConfig> configs;
        // 服务异常, 默认推送超级管理员
        if (NoticeTypeEnum.SERVER_EXCEPTION.getCode().equals(notice.getNoticeType())) {
            InsideNoticeConfig configForSuperAdmin = getNoticeConfigForSuperAdmin();
            configs = List.of(configForSuperAdmin);
        } else {
            MessageSendConfInsideMapper insideMapper = BeanUtil.getBean(MessageSendConfInsideMapper.class);
            configs = insideMapper.selectConfigList(notice.getMetadataId(), notice.getNoticeType());
        }

        for (InsideNoticeConfig config : configs) {
            Exception exception = null;
            try {
                List<Integer> userIds = getUserIdsByConfig(config);
                if (!userIds.isEmpty()) {
                    WsSessionManager.getInstance().sendMessageToUsers(notice, userIds);
                    NoticeRelationMapper relationMapper = BeanUtil.getBean(NoticeRelationMapper.class);
                    relationMapper.insertAll(notice.getId(), userIds);
                }

                log.info("处理消息：ws config id:{} notice:{}", config.getId(), notice);
            } catch (Exception e) {
                exception = e;
                log.error("站内推送消息异常 config id: {}", config.getId(), e);
            } finally {
                NoticeService noticeService = BeanUtil.getBean(NoticeService.class);
                noticeService.record(config.toMessage(notice, exception));
            }
        }
    }

    private InsideNoticeConfig getNoticeConfigForSuperAdmin() {
        InsideNoticeConfig config = new InsideNoticeConfig();
        config.setId(-1);
        config.setName("服务异常默认推送超级管理员");
        config.setSendRoleIds(List.of(RoleConstants.SUPER_ADMIN_ROLE_ID));
        return config;
    }

    private List<Integer> getUserIdsByConfig(InsideNoticeConfig config) {
        Set<Integer> userIds = new HashSet<>();
        //用户id
        if (config.getSendUserIds() != null) {
            userIds.addAll(config.getSendUserIds());
        }
        //角色id
        if (config.getSendRoleIds() != null) {
            UserMapper userMapper = BeanUtil.getBean(UserMapper.class);
            List<Integer> roleUserIds = config.getSendRoleIds().stream()
                .flatMap(roleId -> userMapper.getUserIdsByRoleId(roleId).stream())
                .toList();
            userIds.addAll(roleUserIds);
        }

        return new ArrayList<>(userIds);
    }
}
