package com.trs.ai.moye.monitor.mapper;

import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * 用户mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface UserMapper {

    /**
     * 查询所有用户id
     *
     * @return id列表
     */
    @Select("select id from users")
    List<Integer> getAllUserIds();

    /**
     * 根据租户查询用户id
     *
     * @param tenantId 租户id
     * @return 用户id
     */
    @Select("select id from users where tenant_id = #{tenantId} ")
    List<Integer> getUserIdsByTenantId(@Param("tenantId") Integer tenantId);

    /**
     * 根据角色查询用户id
     *
     * @param roleId 角色id
     * @return 用户id
     */
    @Select("select id from users where #{roleId} member of (role_ids) ")
    List<Integer> getUserIdsByRoleId(@Param("roleId") Integer roleId);
}
