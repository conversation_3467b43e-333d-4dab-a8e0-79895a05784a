package com.trs.ai.moye.monitor.entity;

import static lombok.Lombok.checkNotNull;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;
import java.util.function.Consumer;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpStatus;

/**
 * RestfulResults
 *
 * @param <T> 类型参数
 * <AUTHOR>
 * @since 2024/5/23 15:23
 */
@Data
public class RestfulResults<T> implements Serializable {

    private static final long serialVersionUID = 1024770724393982045L;

    private String code;

    private String msg;

    private T data;

    private SummaryInfo summary = new SummaryInfo();

    private String requestId;

    private RestfulResults() {
        super();
    }


    /**
     * 构建一个成功的RestfulResults。code为200
     *
     * @param datas 结果集
     * @param <T>   类型参数
     * @return 初始化的RestfulResults
     */
    public static <T> RestfulResults<T> ok(T datas) {
        RestfulResults<T> result = new RestfulResults<>();
        result.code = "200";
        result.data = datas;
        return result;
    }

    /**
     * 构建一个失败的RestfulResults。
     *
     * @param status http状态码
     * @param msg   消息
     * @param <T> 类型参数
     * @return RestfulResults
     */
    public static <T> RestfulResults<T> error(HttpStatus status, String msg) {
        RestfulResults<T> result = new RestfulResults<>();
        result.code = String.valueOf((status == null) ? HttpStatus.INTERNAL_SERVER_ERROR.value() : status.value());
        result.msg = msg;
        return result;
    }

    /**
     * 构建一个失败的RestfulResults。code为500
     *
     * @param msg 消息
     * @param <T> 类型参数
     * @return RestfulResults
     */
    public static <T> RestfulResults<T> error(String msg) {
        RestfulResults<T> result = new RestfulResults<>();
        result.code = String.valueOf(HttpStatus.INTERNAL_SERVER_ERROR.value());
        result.msg = msg;
        return result;
    }

    /**
     * 是否成功
     *
     * @return 是否成功
     */
    public boolean isSuccess() {
        return StringUtils.isNotBlank(code) && "200".equals(code);
    }

    /**
     * 是否失败
     *
     * @return 是否失败
     */
    public boolean isFailure() {
        return !isSuccess();
    }

    /**
     * 成功时执行
     *
     * @param action 执行的动作
     * @return RestfulResults
     */
    public RestfulResults<T> onSuccess(Consumer<? super T> action) {
        checkNotNull(action, "action is null");
        if (isSuccess()) {
            action.accept(this.data);
        }
        return this;
    }

    /**
     * 失败时执行
     *
     * @param action 执行的动作
     * @return RestfulResults
     */
    public RestfulResults<T> onFailure(Consumer<RestfulResults<T>> action) {
        checkNotNull(action, "action is null");
        if (isFailure()) {
            action.accept(this);
        }
        return this;
    }


    /**
     * 设置异常信息
     *
     * @param msg 异常信息
     * @return RestfulResults
     */
    public RestfulResults<T> addMsg(String msg) {
        this.msg = msg;
        return this;
    }

    /**
     * 对summary进行结果集总数的赋值
     *
     * @param total 总数
     * @return RestfulResults
     */
    public RestfulResults<T> addTotalCount(Long total) {
        checkNotNull(total, "total不能为空");
        this.summary.setTotal(total);
        return this;
    }

    /**
     * 对summary进行结果集pageSize的赋值
     *
     * @param pagesize pageSize
     * @return RestfulResults
     */
    public RestfulResults<T> addPageSize(Integer pagesize) {
        checkNotNull(pagesize, "pagesize不能为空");
        this.summary.setPageSize(pagesize);
        return this;
    }

    /**
     * 对summary进行结果集页码进行赋值
     *
     * @param pageNum 页吗
     * @return RestfulResults
     */
    public RestfulResults<T> addPageNum(Integer pageNum) {
        checkNotNull(pageNum, "pageNum不能为空");
        this.summary.setPageNum(pageNum);
        return this;
    }

    /**
     * 对summary进行结果集indexName的赋值
     *
     * @param indexName 索引名称
     * @return RestfulResults
     */
    public RestfulResults<T> addIndexName(String indexName) {
        this.summary.setIndexName(indexName);
        return this;
    }

    /**
     * 对summary进行结果集检索条件的赋值
     *
     * @param condition 检索条件
     * @return RestfulResults
     */
    public RestfulResults<T> addCondition(String condition) {
        this.summary.setCondition(condition);
        return this;
    }

    /**
     * 添加额外的信息
     *
     * @param key   key
     * @param value value
     * @return RestfulResults
     */
    public RestfulResults<T> addExtraInfo(String key, Object value) {
        this.summary.getExtraInfo().put(key, value);
        return this;
    }

    /**
     * SummaryInfo
     */
    @Data
    public static class SummaryInfo implements Serializable {

        private static final long serialVersionUID = 1016045351737202333L;

        private Map<String, Object> extraInfo = new HashMap<>();

        private Integer pageNum = 0;

        private Integer pageSize = 0;

        private Long total = 0L;

        private String indexName;

        private String condition;
    }
}
