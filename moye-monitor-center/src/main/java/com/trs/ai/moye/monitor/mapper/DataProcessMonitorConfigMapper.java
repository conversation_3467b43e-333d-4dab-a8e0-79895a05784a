package com.trs.ai.moye.monitor.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.trs.moye.base.monitor.entity.DataProcessMonitorConfig;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * DataProcessMonitorConfig数据访问层
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025/03/13 10:11
 */
@Mapper
public interface DataProcessMonitorConfigMapper extends BaseMapper<DataProcessMonitorConfig> {

    /**
     * 列表查询
     *
     * @param dataModelId 元数据id
     * @return List<DataProcessMonitorConfig>
     */
    List<DataProcessMonitorConfig> listQuery(@Param("dataModelId") Integer dataModelId);
}