package com.trs.ai.moye.monitor.mapper.handler;

import java.sql.Array;
import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Types;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.MappedJdbcTypes;
import org.apache.ibatis.type.MappedTypes;

/**
 * ck array() 处理器
 */
@MappedTypes({List.class})
@MappedJdbcTypes(JdbcType.ARRAY)
public class ClickHouseArrayHandler extends BaseTypeHandler<List<?>> {

    @Override
    public void setNonNullParameter(PreparedStatement ps, int i, List<?> parameter, JdbcType jdbcType)
        throws SQLException {
        if (Objects.isNull(parameter)) {
            ps.setNull(i, JdbcType.ARRAY.TYPE_CODE);
        } else {
            ps.setArray(i, ps.getConnection().createArrayOf(jdbcType.name(), parameter.toArray()));
        }
    }

    @Override
    public List<?> getNullableResult(ResultSet rs, String columnName) throws SQLException {
        Array array = rs.getArray(columnName);
        return parseClickHouseArray(array);
    }

    @Override
    public List<?> getNullableResult(ResultSet rs, int columnIndex) throws SQLException {
        Array array = rs.getArray(columnIndex);
        return parseClickHouseArray(array);
    }

    @Override
    public List<?> getNullableResult(CallableStatement cs, int columnIndex) throws SQLException {
        Array array = cs.getArray(columnIndex);
        return parseClickHouseArray(array);
    }

    private List<?> parseClickHouseArray(Array array) throws SQLException {
        int baseType = array.getBaseType();
        return switch (baseType) {
            case Types.INTEGER -> Arrays.stream((int[]) array.getArray()).boxed().toList();
            case Types.BIGINT -> Arrays.stream((long[]) array.getArray()).boxed().toList();
            case Types.VARCHAR -> new ArrayList<>(Arrays.asList((String[]) array.getArray()));
            case Types.DOUBLE, Types.FLOAT -> Arrays.stream((double[]) array.getArray()).boxed().toList();
            default -> Collections.singletonList(array.getArray());
        };
    }
}
