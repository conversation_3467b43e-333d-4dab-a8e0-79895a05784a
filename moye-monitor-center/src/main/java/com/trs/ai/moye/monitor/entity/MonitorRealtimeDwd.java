package com.trs.ai.moye.monitor.entity;

import com.trs.moye.base.common.utils.SnowflakeIdUtil;
import com.trs.moye.base.data.connection.enums.ConnectionType;
import com.trs.moye.base.data.connection.enums.DataSourceCategory;
import com.trs.moye.base.data.model.entity.DataModel;
import com.trs.moye.base.data.model.entity.DataSourceConfig;
import com.trs.moye.base.monitor.enums.RealtimeDwdRecordQuality;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 实时监控DWD层实体（对应表 t_monitor_dwd_realtime）
 *
 * <AUTHOR>
 * @since 2025/02/17 16:28:50
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class MonitorRealtimeDwd {

    /**
     * id
     */
    private Long id;

    /**
     * 数据建模id
     */
    @NotNull(message = "元数据id不允许空")
    private Integer dataModelId;

    /**
     * 数据建模名称
     */
    @NotEmpty(message = "元数据名称不允许空")
    private String dataModelName;

    /**
     * 消息主题
     */
    private String topic;

    // ==================== 监控指标 ====================
    /**
     * 监控时间
     */
    @NotNull(message = "监控时间不允许空")
    private LocalDateTime monitorTime;

    /**
     * 当前Offset
     */
    private Long currentOffset;

    /**
     * 结束Offset
     */
    private Long endOffset;

    /**
     * 积压量
     */
    private Long lag;

    /**
     * 平均TPS
     */
    private BigDecimal avgTps;

    // ==================== 数据来源 ====================
    /**
     * 来源大类,MQ
     */
    @NotEmpty(message = "表来源分类不允许空")
    private DataSourceCategory sourceType;

    /**
     * 来源子类型表；mysql、kafka、sftp
     */
    @NotEmpty(message = "表来源实际类型不允许空")
    private ConnectionType sourceSubType;

    // ==================== 系统字段 ====================
    /**
     * 入库时间
     */
    @NotNull(message = "实际入库时间不允许空")
    private LocalDateTime storageTime;

    /**
     * 数据质量标记
     */
    private RealtimeDwdRecordQuality dataQuality;

    /**
     * 异常备注
     */
    private String remark;

    public MonitorRealtimeDwd(DataModel metaData,
        MonitorRealtimeDwdMetrics metrics,
        RealtimeDwdRecordQuality dataQuality, DataSourceConfig dataSource) {
        setDataModelId(metaData.getId());
        setDataModelName(metaData.getZhName());
        setDataQuality(dataQuality);
        setRemark(dataQuality.getDesc());
        // 基础指标
        setCurrentOffset(metrics.getCurrentOffset());
        setEndOffset(metrics.getEndOffset());
        setLag(metrics.getLag());
        // TPS（可能为null）
        setAvgTps(metrics.getTps());
        // 数据建模信息
        setTopic(dataSource.getEnName());
        setSourceType(dataSource.getConnection().getConnectionType().getCategory());
        setSourceSubType(dataSource.getConnection().getConnectionType());

        setMonitorTime(LocalDateTime.now());
        setStorageTime(LocalDateTime.now());
        setId(SnowflakeIdUtil.newId());
    }
}
