package com.trs.ai.moye.monitor.mapper.clickhouse;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.trs.moye.base.monitor.entity.MonitorOdsFluctuation;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 波动监控 数据访问层
 */
@DS("clickhouse")
@Mapper
public interface MonitorOdsFluctuationMapper {

    /**
     * 单条插入
     *
     * @param data 数据对象
     * @return int
     */
    int insert(@Param("data") MonitorOdsFluctuation data);


    /**
     * 查询所有波动监控，按存储时间降序排序，最新的记录在前<br>
     * 查询近 N 条，时间段在 hour 到 hour+1 小时内的数据
     *
     * @param configId 配置id
     * @param num      近几条数据
     * @param hour     时间段 [hour, hour+1)
     * @return 存储时间降序排序的波动监控
     */
    List<MonitorOdsFluctuation> selectLatestNByConfigIdAndHourOrderByStorageTimeDesc(@Param("configId") Integer configId,
                                                                                     @Param("hour") Integer hour,
                                                                                     @Param("num") Integer num);

    /**
     * 查询最新一条监控
     *
     * @param configId 配置id
     * @return 最新一条监控
     */
    MonitorOdsFluctuation selectLatestByConfigId(@Param("configId") Integer configId);
}
