package com.trs.ai.moye.monitor.websocket;

import com.trs.ai.moye.monitor.constants.TimeConstants;
import com.trs.moye.base.common.enums.ModelLayer;
import com.trs.moye.base.monitor.enums.NoticeTypeEnum;
import lombok.Data;

/**ws消息vo
 *
 * <AUTHOR>
 */
@Data
public class MessageVO {

    private Integer id;
    /**
     * 标题
     */
    private String title;
    /**
     * 内容
     */
    private String content;
    /**
     * 消息类型
     */
    private String noticeType;
    /**
     * 监控时间
     */
    private String publishTime;
    /**
     * 建模id
     */
    private Integer dataModelId;
    /**
     * 分层
     */
    private ModelLayer layer;


    public MessageVO(NoticeVO vo) {
        this.id = vo.getId();
        this.title = vo.getTitle();
        this.content = vo.getContent();
        this.noticeType = NoticeTypeEnum.getName(vo.getNoticeType());
        this.publishTime = vo.getPublishTime().format(TimeConstants.DEFAULT_FORMATTER);
        this.dataModelId = vo.getMetadataId();
        this.layer = vo.getModelLayer();
    }
}
