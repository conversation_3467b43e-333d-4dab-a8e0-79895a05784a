package com.trs.ai.moye.monitor;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.transaction.annotation.EnableTransactionManagement;

//@SpringBootApplication(scanBasePackages = {"com.trs"})
@SpringBootApplication(scanBasePackages = {"com.trs.ai.moye.monitor", "com.trs.moye.base"})
@EnableTransactionManagement
@EnableScheduling
@EnableFeignClients
@MapperScan(basePackages = {"com.trs.**.dao", "com.trs.**.mapper"})
public class MoyeMonitorCenterApplication {

    public static void main(String[] args) {
        SpringApplication.run(MoyeMonitorCenterApplication.class, args);
    }

}
