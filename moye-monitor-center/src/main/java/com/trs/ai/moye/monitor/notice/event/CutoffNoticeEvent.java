package com.trs.ai.moye.monitor.notice.event;

import com.trs.ai.moye.monitor.constants.TimeConstants;
import com.trs.ai.moye.monitor.utils.BeanUtil;
import com.trs.moye.base.common.enums.ModelLayer;
import com.trs.moye.base.data.model.dao.DataModelMapper;
import com.trs.moye.base.data.model.entity.DataModel;
import com.trs.moye.base.monitor.entity.MonitorOdsCutoff;
import com.trs.moye.base.monitor.enums.NoticeTypeEnum;
import com.trs.ai.moye.monitor.websocket.NoticeVO;
import java.util.LinkedHashMap;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 断流消息
 *
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class CutoffNoticeEvent extends AbstractNoticeEvent {

    private String title;

    private String content;

    public CutoffNoticeEvent(Integer metadataId, MonitorOdsCutoff cutoff) {
        DataModelMapper dataModelMapper = BeanUtil.getBean(DataModelMapper.class);
        DataModel dataModel = dataModelMapper.selectById(metadataId);
        ModelLayer layer = dataModel.getLayer();
        String layerLabel = layer.getLabel();

        this.setType(NoticeTypeEnum.DATA_EXCEPTION.getCode());
        this.setSubType("断流");
        // 动态标题
        this.setTitle(String.format("%s【%s】出现数据断流", layerLabel, cutoff.getDataModelName()));
        LinkedHashMap<String, Object> contentParams = new LinkedHashMap<>();
        contentParams.put(layerLabel + "名称", cutoff.getDataModelName());  // 动态键名（如 "要素库名称"）
        contentParams.put("监控时间", cutoff.getMonitorTime().format(TimeConstants.DEFAULT_FORMATTER));
        this.setContent(NoticeContentBuilder.build("数据断流告警", contentParams));
        this.setPublishTime(cutoff.getMonitorTime());
        this.setMetadataId(metadataId);
    }

    @Override
    public NoticeVO createNoticeVO() {
        NoticeVO vo = new NoticeVO(this);
        vo.setTitle(title);
        vo.setContent(content);
        return vo;
    }
}
