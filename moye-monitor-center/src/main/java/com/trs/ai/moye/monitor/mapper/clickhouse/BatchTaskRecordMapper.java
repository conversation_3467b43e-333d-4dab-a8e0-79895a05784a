package com.trs.ai.moye.monitor.mapper.clickhouse;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.trs.ai.moye.monitor.entity.BatchTaskRecord;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 * @since 2025/3/3
 **/
@DS("clickhouse")
@Mapper
public interface BatchTaskRecordMapper extends BaseMapper<BatchTaskRecord> {

    /**
     * 根据时间范围查询
     *
     * @param taskId 任务id
     * @return List<BatchTaskRecord>
     */
    BatchTaskRecord selectLatestByTaskId(@Param("taskId") Integer taskId);
}
