package com.trs.ai.moye.monitor.notice.event;

import com.trs.ai.moye.monitor.constants.TimeConstants;
import com.trs.ai.moye.monitor.utils.BeanUtil;
import com.trs.moye.base.common.enums.ModelLayer;
import com.trs.moye.base.data.model.dao.DataModelMapper;
import com.trs.moye.base.data.model.entity.DataModel;
import com.trs.moye.base.monitor.enums.NoticeTypeEnum;
import com.trs.moye.base.monitor.entity.MonitorOdsLag;
import com.trs.ai.moye.monitor.websocket.NoticeVO;
import java.time.LocalDateTime;
import java.util.LinkedHashMap;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 积压消息
 *
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class LagNoticeEvent extends AbstractNoticeEvent {

    private String title;

    private String content;

    public LagNoticeEvent(Integer metadataId, MonitorOdsLag lag) {
        DataModelMapper dataModelMapper = BeanUtil.getBean(DataModelMapper.class);
        DataModel dataModel = dataModelMapper.selectById(metadataId);
        ModelLayer layer = dataModel.getLayer();

        this.setMetadataId(metadataId);
        this.setType(NoticeTypeEnum.DATA_EXCEPTION.getCode());
        this.setSubType("积压");
        this.setTitle(String.format("%s【%s】出现数据积压", layer.getLabel(), lag.getDataModelName()));
        // 格式化内容为Markdown
        LinkedHashMap<String, Object> contentParams = new LinkedHashMap<>();
        contentParams.put(layer.getLabel() + "名称", lag.getDataModelName());
        contentParams.put("监控时间", formatLocalDateTime(lag.getMonitorTime()));  // 传递 LocalDateTime 对象
        contentParams.put("当前积压量", lag.getLag() + " 条");

        this.setContent(NoticeContentBuilder.build("数据积压告警", contentParams));
        this.setPublishTime(lag.getMonitorTime());
    }

    /**
     * 批任务执行时间超市
     *
     * @param metadataId    元数据id
     * @param taskName      任务名称
     * @param executionTime 执行时间
     */
    public LagNoticeEvent(Integer metadataId, String taskName, String executionTime) {
        this.setMetadataId(metadataId);
        this.setType(NoticeTypeEnum.DATA_EXCEPTION.getCode());
        this.setSubType("要素库任务超时");
        this.setTitle(String.format("任务【%s】执行超时", taskName));
        // 格式化内容为Markdown
        LinkedHashMap<String, Object> contentParams = new LinkedHashMap<>();
        contentParams.put("任务名称", taskName);
        contentParams.put("执行时间", executionTime);        // 已格式化的字符串
        contentParams.put("触发时间", formatLocalDateTime(LocalDateTime.now()));  // 传递 LocalDateTime 对象

        this.setContent(NoticeContentBuilder.build("任务执行超时告警", contentParams));
        this.setPublishTime(LocalDateTime.now());
    }

    private String formatLocalDateTime(LocalDateTime time) {
        return time.format(TimeConstants.DEFAULT_FORMATTER);

    }


    @Override
    public NoticeVO createNoticeVO() {
        NoticeVO vo = new NoticeVO(this);
        vo.setTitle(title);
        vo.setContent(content);
        return vo;
    }
}
