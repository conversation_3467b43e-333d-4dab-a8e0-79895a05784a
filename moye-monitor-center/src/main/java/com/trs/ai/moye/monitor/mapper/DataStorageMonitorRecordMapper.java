package com.trs.ai.moye.monitor.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.trs.moye.base.monitor.entity.DataStorageMonitorRecord;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * DataStorageMonitorRecord数据访问层
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025/03/13 10:16
 */
@DS("clickhouse")
@Mapper
public interface DataStorageMonitorRecordMapper extends BaseMapper<DataStorageMonitorRecord> {

    /**
     * 多条查询：通过dataModelId查询
     *
     * @param dataModelId 要素库ID
     * @return List<DataStorageMonitorRecord>
     */
    DataStorageMonitorRecord selectLatestByDataModelId(@Param("dataModelId") Integer dataModelId,
        @Param("dataStorageConnectionId") Integer dataStorageConnectionId);

}