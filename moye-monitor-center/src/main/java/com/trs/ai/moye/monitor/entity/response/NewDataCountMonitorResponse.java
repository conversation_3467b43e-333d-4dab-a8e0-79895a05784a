package com.trs.ai.moye.monitor.entity.response;

import com.trs.moye.base.data.storage.IncrementInfo;
import javax.validation.constraints.NotNull;
import lombok.Data;

/**
 * 数据源数据增加量监控传输类
 */
@Data
public class NewDataCountMonitorResponse {

    /**
     * 数据增加量
     */
    @NotNull
    private Long newDataCount;

    /*
      以下参数 只有一个有值，数据要不以全量或者以增量信息增加
     */
    /**
     * 数据源总量信息
     */
    private Long amount;
    /**
     * 数据源增量信息
     */
    private IncrementInfo increment;

}
