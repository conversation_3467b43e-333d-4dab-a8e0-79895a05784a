package com.trs.ai.moye.monitor.utils;

import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Calendar;
import java.util.Date;
import lombok.extern.slf4j.Slf4j;

/**
 * 日期工具类
 *
 * <AUTHOR>
 * @date 2021/07/14
 */
@Slf4j
public class DateUtil {

    private DateUtil() {

    }

    /**
     * 时间戳转时间
     *
     * @param utc 时间戳
     * @return 时间对象
     */
    public static LocalDateTime utcToLocalDateTime(long utc) {
        return LocalDateTime.ofInstant(Instant.ofEpochMilli(utc), ZoneId.systemDefault());
    }

    /**
     * 时间戳转日期
     *
     * @param utc 时间戳
     * @return 日期对象
     */
    public static LocalDate utcToLocalDate(long utc) {
        return utcToLocalDateTime(utc).toLocalDate();
    }

    /**
     * 时间转时间戳
     *
     * @param dateTime 时间
     * @return 时间戳
     */
    public static Long dateTimeToUtc(LocalDateTime dateTime) {
        return dateTime.atZone(ZoneId.systemDefault()).toInstant().toEpochMilli();
    }

    /**
     * 日期转时间戳
     *
     * @param date 日期
     * @return 时间戳
     */
    public static Long dateToUtc(LocalDate date) {
        return date.atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli();
    }

    /**
     *
     * 根据出生年月日计算年龄
     *
     * @param birthday 生日
     * @return 年龄
     */
    public static int getAgeByBirth(Date birthday){

        //获取当前时间
        Calendar cal = Calendar.getInstance();

        //获取出生日期的Calendar对象
        Calendar bir = Calendar.getInstance();
        bir.setTime(birthday);
        //如果出生日期大于当前日期，则返回0
        if(cal.before(birthday)){
            return 0;
        }
        //取出当前年月日
        int nowYear = cal.get(Calendar.YEAR);
        int nowMonth = cal.get(Calendar.MONTH);
        int nowDay = cal.get(Calendar.DAY_OF_MONTH);

        //取出出生日期的年月日
        int birthYear = bir.get(Calendar.YEAR);
        int birthMonth = bir.get(Calendar.MONTH);
        int birthDay = bir.get(Calendar.DAY_OF_MONTH);

        //计算年份
        int age = nowYear - birthYear;

        //计算月份和日，看看是否大于当前月日，如果小于则减去一岁
        if(nowMonth < birthMonth || (nowMonth == birthMonth && nowDay < birthDay)){
            age--;
        }
        return age;
    }

}
