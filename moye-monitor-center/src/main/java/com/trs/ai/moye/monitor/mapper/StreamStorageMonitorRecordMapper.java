package com.trs.ai.moye.monitor.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.trs.moye.base.monitor.entity.StorageStatisticsRecord;
import com.trs.moye.base.monitor.entity.StreamStorageMonitorRecord;
import com.trs.moye.base.monitor.entity.ProcessStatisticsRecord;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 * @since 2025-07-08 11:29
 */
@DS("clickhouse")
@Mapper
public interface StreamStorageMonitorRecordMapper extends BaseMapper<StreamStorageMonitorRecord> {

    /**
     * 根据id查询数据
     *
     * @param id id
     * @return 存储监控记录
     */
    List<StreamStorageMonitorRecord> selectListById(Long id);

    /**
     * 根据id查询数据
     *
     * @param id id
     * @param storagePointId 存储点id
     * @return 存储监控记录
     */
    StreamStorageMonitorRecord selectByIdStorageId(@Param("id") Long id, @Param("storagePointId") Integer storagePointId);

    /**
     * 查询趋势图数据
     *
     * @param dataModelId 数据建模id
     * @param startTime   开始时间
     * @param endTime     结束时间
     * @return 流处理监控数据
     */
    List<StreamStorageMonitorRecord> selectTrendChart(@Param("dataModelId") Integer dataModelId,
        @Param("startTime") String startTime, @Param("endTime") String endTime);

    /**
     * 统计趋势图数据-按时
     *
     * @param dataModelId 数据建模id
     * @param startTime   开始时间
     * @param endTime     结束时间
     * @return 流处理监控数据
     */
    List<StorageStatisticsRecord> statisticsTrendChartHour(@Param("dataModelId") Integer dataModelId,
        @Param("startTime") String startTime, @Param("endTime") String endTime);

    /**
     * 统计趋势图数据-按日
     *
     * @param dataModelId 数据建模id
     * @param startTime   开始时间
     * @param endTime     结束时间
     * @return 流处理监控数据
     */
    List<StorageStatisticsRecord> statisticsTrendChartDay(@Param("dataModelId") Integer dataModelId,
        @Param("startTime") String startTime, @Param("endTime") String endTime);

}
