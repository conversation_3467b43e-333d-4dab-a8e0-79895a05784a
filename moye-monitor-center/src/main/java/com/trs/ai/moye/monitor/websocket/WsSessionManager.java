package com.trs.ai.moye.monitor.websocket;

import com.alibaba.fastjson.JSON;
import com.trs.ai.moye.monitor.mapper.UserMapper;
import com.trs.ai.moye.monitor.utils.BeanUtil;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;
import javax.websocket.Session;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 */
@Slf4j
public class WsSessionManager extends ConcurrentHashMap<Integer, UserSession> {

    private WsSessionManager() {
    }

    private static final WsSessionManager INSTANCE = new WsSessionManager();

    /**
     * 获取sessionManager
     *
     * @return SessionManager
     */
    public static WsSessionManager getInstance() {
        return INSTANCE;
    }

    /**
     * 获取当前用户在该系统中的session
     *
     * @param userId  用户
     * @param channel 频道
     * @return ws 的session
     */
    public List<Session> getUserSession(Integer userId, Channel channel) {
        UserSession userSession = this.get(userId);
        return Objects.isNull(userSession) ? new ArrayList<>() : userSession.entrySet().stream()
            .filter(item -> Objects.nonNull(item) && item.getKey().equals(channel))
            .findAny().map(item -> item.getValue().getSessions())
            .orElse(new ArrayList<>());
    }

    /**
     * 从内存中删除当前用户session
     *
     * @param channel 当前用户链接分组
     * @param userId  用户id
     */
    public void closeCurrentUserSession(Integer userId, Channel channel) {
        ChannelSession channelSession = this.get(userId).get(channel);
        if (Objects.nonNull(channelSession)) {
            log.info("{}用户根据channel:{}找到了可以关闭的连接", userId, channel);
            channelSession.getSessions().removeIf(next -> !next.isOpen());
        } else {
            log.warn("{}用户根据channel:{}没能找到可以关闭的连接", userId, channel);
        }
    }

    /**
     * 向用户发送日志
     *
     * @param userId 用户id
     * @param message  消息
     */
    public synchronized void sendMessage(Integer userId, String message) {
        List<Session> userSessions = this.getUserSession(userId, Channel.getNoticeChannel()).stream()
            .filter(Session::isOpen)
            .toList();
        if (userSessions.isEmpty()) {
            log.debug("未找到对应session，无法推送消息！用户id：{} 待发送的消息：{}", userId, message);
            return;
        }
        userSessions.forEach(session -> {
            try {
                session.getAsyncRemote().sendText(message);
            } catch (Exception e) {
                log.error("发送websocket消息失败！用户id：{} 待发送的信息：{}", userId, message, e);
            }
        });
    }

    public void sendMessageToAllUser(NoticeVO noticeVO) {
        UserMapper userMapper = BeanUtil.getBean(UserMapper.class);
        List<Integer> userIds = userMapper.getAllUserIds();
        String msg = JSON.toJSONString(new MessageVO(noticeVO));
        log.info("发送告警信息：{}", msg);
        userIds.forEach(userId -> sendMessage(userId, msg));
    }

    public void sendMessageToUsers(NoticeVO noticeVO, List<Integer> userIds) {
        String msg = JSON.toJSONString(new MessageVO(noticeVO));
        log.info("发送告警信息到ws: userIds: {} msg: {}",userIds, msg);
        userIds.forEach(userId -> sendMessage(userId, msg));
    }

    /**
     * 将当前长连接信息更新到sessionManger中
     *
     * @param userId  当前用户
     * @param channel 长连接channel
     * @param session 长连接session
     */
    public void updateSessionManger(Integer userId, Channel channel, Session session) {
        //判断当前sessionManager是否存在当前user
        boolean isExistUser = false;
        for (Entry<Integer, UserSession> sessionEntry : this.entrySet()) {
            if (sessionEntry.getKey().equals(userId)) {
                //当前通道存在则 add  不存在则新增
                if (sessionEntry.getValue().containsKey(channel)) {
                    sessionEntry.getValue().get(channel).getSessions().add(session);
                } else {
                    sessionEntry.getValue().put(channel, new ChannelSession(new ArrayList<>(List.of(session))));
                }

                log.info("{}用户增加socket连接，sessionId:{},channel:{}", userId, session.getId(), channel);
                isExistUser = true;
                break;
            }
        }
        //如果不存在则新增
        if (!isExistUser) {
            log.info("{}用户创建socket连接，sessionId:{},channel:{}", userId, session.getId(), channel);
            UserSession userSession = new UserSession();
            userSession.put(channel,
                new ChannelSession(new ArrayList<>(List.of(session))));
            this.put(userId, userSession);
        }

    }
}
