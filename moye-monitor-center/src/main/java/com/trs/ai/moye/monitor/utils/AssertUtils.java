package com.trs.ai.moye.monitor.utils;

import com.trs.ai.moye.monitor.exception.AssertException;
import org.apache.commons.lang3.ObjectUtils;

/**
 * 断言工具类
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-09-26 22:18
 */
public class AssertUtils {

    private AssertUtils() {
    }

    /**
     * 断言对象空
     *
     * @param obj     对象
     * @param message 提示消息
     */
    public static void empty(Object obj, String message) {
        if (ObjectUtils.isNotEmpty(obj)) {
            throw new AssertException(message);
        }
    }

    /**
     * 断言对象空
     *
     * @param obj    对象
     * @param format 提示消息格式
     * @param args   提示消息参数
     */
    public static void empty(Object obj, String format, Object... args) {
        if (ObjectUtils.isNotEmpty(obj)) {
            throw new AssertException(format, args);
        }
    }

    /**
     * 断言对象不空
     *
     * @param obj     对象
     * @param message 提示消息
     */
    public static void notEmpty(Object obj, String message) {
        if (ObjectUtils.isEmpty(obj)) {
            throw new AssertException(message);
        }
    }

    /**
     * 断言对象不空
     *
     * @param obj    对象
     * @param format 提示消息格式
     * @param args   提示消息参数
     */
    public static void notEmpty(Object obj, String format, Object... args) {
        if (ObjectUtils.isEmpty(obj)) {
            throw new AssertException(format, args);
        }
    }
}
