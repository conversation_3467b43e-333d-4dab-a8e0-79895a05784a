package com.trs.ai.moye.monitor.mapper;

import com.trs.ai.moye.monitor.entity.ApiNoticeConfig;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * api消息配置mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface MessageSendConfHttpMapper {

    /**
     * 根据消息元数据id和消息类型查询对应的站内消息配置
     *
     * @param dataModelId 元数据id
     * @param noticeType 消息类型
     * @return 配置列表
     */
    List<ApiNoticeConfig> selectConfigList(@Param("dataModelId") Integer dataModelId,
        @Param("noticeType") Integer noticeType);
}
