//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by FernFlower decompiler)
//

package com.trs.ai.moye.monitor.exception;

/**
 * 业务异常
 */
public class BizException extends RuntimeException {

    public BizException() {
    }

    public BizException(String message) {
        super(message);
    }

    public BizException(String message, Throwable cause) {
        super(message, cause);
    }

    public BizException(Throwable cause) {
        super(cause);
    }

    public BizException(String format, Object... objects) {
        super(String.format(format, objects));
    }

    public BizException(Throwable cause, String message) {
        super(message, cause);
    }

    public BizException(Throwable cause, String format, Object... objects) {
        super(String.format(format, objects), cause);
    }
}
