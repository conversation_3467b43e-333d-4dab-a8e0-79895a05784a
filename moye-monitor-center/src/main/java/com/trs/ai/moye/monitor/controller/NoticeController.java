package com.trs.ai.moye.monitor.controller;

import com.trs.ai.moye.monitor.notice.EventPublish;
import com.trs.ai.moye.monitor.notice.event.BatchNoticeEvent;
import com.trs.ai.moye.monitor.notice.event.DataConnectionNoticeEvent;
import com.trs.ai.moye.monitor.notice.event.ServerNoticeEvent;
import com.trs.ai.moye.monitor.notice.event.StorageNoticeEvent;
import com.trs.moye.base.common.utils.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

/**
 * 消息中心
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
public class NoticeController {

    /**
     * 推送storage engine消息
     *
     * @param noticeVO 消息内容
     */
    @PostMapping("/notice-center/storage/send")
    public void sendStorageMessage(@RequestBody StorageNoticeEvent noticeVO) {
        log.info("StorageNotice-- 接收到的消息内容: {}", JsonUtils.toJsonString(noticeVO));
        EventPublish.saveAndPost(noticeVO);
    }

    /**
     * 推送batch engine消息
     *
     * @param noticeVO 消息内容
     */
    @PostMapping("/notice-center/batch/send")
    public void sendBatchMessage(@RequestBody BatchNoticeEvent noticeVO) {
        EventPublish.saveAndPost(noticeVO);
    }

    /**
     * 推送moye服务异常消息
     *
     * @param noticeVO 消息内容
     */
    @PostMapping("/notice-center/server/send")
    public void sendServerMessage(@RequestBody ServerNoticeEvent noticeVO) {
       EventPublish.saveAndPost(noticeVO);
    }

    /**
     * 推送数据连接异常消息
     *
     * @param noticeVO 消息内容
     */
    @PostMapping("/notice-center/data-connection/send")
    public void sendDataConnectionMessage(@RequestBody DataConnectionNoticeEvent noticeVO) {
        EventPublish.saveAndPost(noticeVO);
    }
}
