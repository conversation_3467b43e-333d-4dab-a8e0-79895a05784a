package com.trs.ai.moye.monitor.montior.impl;

import com.trs.ai.moye.monitor.feign.StorageEngineFeign;
import com.trs.ai.moye.monitor.mapper.DataProcessMonitorRecordMapper;
import com.trs.ai.moye.monitor.mapper.OperatorPipelineMapper;
import com.trs.ai.moye.monitor.mapper.StreamProcessMetricsMapper;
import com.trs.ai.moye.monitor.montior.DataProcessMonitorRecordService;
import com.trs.ai.moye.monitor.montior.StreamProcessHelper;
import com.trs.moye.ability.entity.operator.OperatorPipeline;
import com.trs.moye.base.common.entity.mq.MqConsumeInfoResponse;
import com.trs.moye.base.common.utils.AssertUtils;
import com.trs.moye.base.common.utils.DataProcessUtils;
import com.trs.moye.base.common.utils.SnowflakeIdUtil;
import com.trs.moye.base.data.connection.enums.DataSourceCategory;
import com.trs.moye.base.data.model.dao.DataModelMapper;
import com.trs.moye.base.data.model.entity.DataModel;
import com.trs.moye.base.data.model.entity.DataSourceConfig;
import com.trs.moye.base.data.source.dao.DataSourceConfigMapper;
import com.trs.moye.base.data.storage.DataStorage;
import com.trs.moye.base.monitor.entity.DataProcessMonitorRecord;
import com.trs.moye.base.monitor.utils.MonitorUtils;
import java.time.LocalDateTime;
import java.util.Comparator;
import java.util.List;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class DataProcessMonitorRecordServiceImpl implements DataProcessMonitorRecordService {

    @Resource
    private DataModelMapper dataModelMapper;
    @Resource
    private StorageEngineFeign storageEngineFeign;
    @Resource
    private DataProcessMonitorRecordMapper mapper;
    @Resource
    private DataSourceConfigMapper dataSourceConfigMapper;
    @Resource
    private OperatorPipelineMapper operatorPipelineMapper;
    @Resource
    private StreamProcessHelper streamProcessHelper;
    @Resource
    private StreamProcessMetricsMapper streamProcessMetricsMapper;

    @Override
    public void collectConsumeMonitor(Integer dataModelId) {
        // 初始化监控记录对象
        DataProcessMonitorRecord monitorRecord = createInitMonitorRecord();
        // 查询并设置数据建模信息
        DataModel dataModel = dataModelMapper.selectById(dataModelId);
        AssertUtils.notEmpty(dataModel, "主键为【%s】的元数据不存在", dataModelId);
        monitorRecord.setDataModelId(dataModel.getId());
        monitorRecord.setDataModelName(dataModel.getZhName());
        try {
            DataStorage storage = streamProcessHelper.getStreamProcessSourceModelStoragePoint(dataModel);
            monitorRecord.setTopic(storage.getEnName());
            monitorRecord.setGroup(DataProcessUtils.buildDataProcessGroup(dataModelId));
            MqConsumeInfoResponse consumeInfo = storageEngineFeign.getDataProcessKafkaConsumeInfo(
                storage.getConnectionId(),
                monitorRecord.getTopic(),
                monitorRecord.getGroup());
            log.debug("开始执行实时要素库监控任务，元数据【主键：{}，名称：{}】", dataModel.getId(), dataModel.getZhName());
            DataProcessMonitorRecord lastRecord = mapper.selectLatestByDataModelId(dataModelId);
            // 设置监控指标信息
            MonitorUtils.setConsumeMonitorMetrics(monitorRecord, consumeInfo, lastRecord);
            // 插入监控记录
            monitorRecord.setStorageTime(LocalDateTime.now());
            mapper.insert(monitorRecord);
        } catch (Exception e) {
            log.error("实时要素库监控任务执行失败，元数据【主键：{}，名称：{}】", dataModel.getId(), dataModel.getZhName(), e);
        }
    }


    private DataProcessMonitorRecord createInitMonitorRecord() {
        DataProcessMonitorRecord monitorRecord = new DataProcessMonitorRecord();
        monitorRecord.setScheduleTime(LocalDateTime.now());
        monitorRecord.setId(SnowflakeIdUtil.newId());
        return monitorRecord;
    }

}
