package com.trs.ai.moye.monitor.properties;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * 能力中心配置
 *
 * <AUTHOR>
 */
@Configuration
@ConfigurationProperties(prefix = "ability.center")
@Setter
@Getter
public class AbilityProperties {

    private String abilityServiceUrl;
    private String abilityClientKey;
    private String abilityClientName;
    private String abilityPublicKey;
}
