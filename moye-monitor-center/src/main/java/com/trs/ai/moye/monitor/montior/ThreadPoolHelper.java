package com.trs.ai.moye.monitor.montior;

import java.util.concurrent.SynchronousQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.ThreadPoolExecutor.CallerRunsPolicy;
import java.util.concurrent.TimeUnit;
import javax.annotation.PostConstruct;
import lombok.Setter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @since 2025-07-08 15:41
 */
@Setter
@Component
public class ThreadPoolHelper {

    @Value("${executor.pool-size:20}")
    private int poolSize = 20;

    @Value("${executor.keep-alive-time:20}")
    private int keepAliveTime = 10;

    public ThreadPoolExecutor threadPool;

    @PostConstruct
    private void init() {
        threadPool = new ThreadPoolExecutor(
            0, poolSize,
            keepAliveTime, TimeUnit.MINUTES,
            new SynchronousQueue<>(), new CallerRunsPolicy());
    }

}
