package com.trs.ai.moye.monitor.websocket;

import com.trs.ai.moye.monitor.entity.Notice;
import com.trs.ai.moye.monitor.notice.event.AbstractNoticeEvent;
import com.trs.moye.base.common.enums.ModelLayer;
import java.time.LocalDateTime;
import java.util.Objects;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 站内消息vo
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
public class NoticeVO {

    private Integer id;

    /**
     * 关联元数据id
     */
    private Integer metadataId;
    /**
     * 消息大类
     */
    private Integer noticeType;
    /**
     * 消息小类
     */
    private String subType;
    /**
     * 标题
     */
    private String title;
    /**
     * 内容
     */
    private String content;
    /**
     * 推送时间
     */
    private LocalDateTime publishTime;
    /**
     * 分层
     */
    private ModelLayer modelLayer;


    public Notice toEntity() {
        Notice notice = new Notice();
        notice.setTitle(title);
        notice.setContent(content);
        notice.setNoticeType(noticeType);
        notice.setPublishTime(Objects.isNull(publishTime) ? LocalDateTime.now() : publishTime);
        notice.setDataModelId(metadataId);
        notice.setNoticeSubType(subType);
        return notice;
    }

    public NoticeVO(AbstractNoticeEvent notice) {
        this.metadataId = notice.getMetadataId();
        this.noticeType = notice.getType();
        this.subType = notice.getSubType();
        this.publishTime = notice.getPublishTime();
    }
}
