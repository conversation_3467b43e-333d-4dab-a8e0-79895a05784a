<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.trs.ai.moye.monitor.mapper.MessageSendConfInsideMapper">

    <resultMap id="insideConfigMap" type="com.trs.ai.moye.monitor.entity.InsideNoticeConfig">
        <result column="id" property="id"/>
        <result column="name" property="name"/>
        <result column="send_role_ids" property="sendRoleIds"
            typeHandler="com.trs.moye.base.common.typehandler.CustomJacksonTypeHandler"/>
        <result column="send_user_ids" property="sendUserIds"
            typeHandler="com.trs.moye.base.common.typehandler.CustomJacksonTypeHandler"/>
        <result column="message_type_ids" property="messageTypeIds"
            typeHandler="com.trs.moye.base.common.typehandler.CustomJacksonTypeHandler"/>
        <result column="business_ids" property="businessIds"
            typeHandler="com.trs.moye.base.common.typehandler.CustomJacksonTypeHandler"/>
    </resultMap>

    <select id="selectConfigList" resultMap="insideConfigMap">
        select *
        from notice_send_conf_inside
        where state = 1
          and json_contains(message_type_ids, JSON_ARRAY(#{noticeType}))
          and (
                json_contains(business_ids, JSON_ARRAY(
                    (select business_category_id
                     from data_model
                     where id = #{dataModelId})))
                or data_model_id = #{dataModelId}
            )
    </select>
</mapper>