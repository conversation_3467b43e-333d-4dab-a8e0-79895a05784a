<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.trs.ai.moye.monitor.mapper.NoticeRelationMapper">

    <insert id="insertAll">
        INSERT IGNORE INTO `notice_read_relation`(
        `notice_id`,
        `user_id`,
        `is_read`)
        <foreach collection="userIds" item="userId" index="index" open="VALUES" separator=",">
        (
            #{noticeId},
            #{userId},
            0)
        </foreach>
    </insert>
</mapper>