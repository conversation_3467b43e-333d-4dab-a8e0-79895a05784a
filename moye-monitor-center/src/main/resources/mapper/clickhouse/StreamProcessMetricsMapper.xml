<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.trs.ai.moye.monitor.mapper.StreamProcessMetricsMapper">

    <resultMap id="BaseResultMap" type="com.trs.moye.base.monitor.entity.StreamProcessMonitorRecord">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="data_model_id" property="dataModelId" jdbcType="INTEGER"/>
        <result column="data_model_name" property="dataModelName" jdbcType="VARCHAR"/>
        <result column="topic" property="topic" jdbcType="VARCHAR"/>
        <result column="group" property="group" jdbcType="VARCHAR"/>
        <result column="monitor_time" property="monitorTime" jdbcType="TIMESTAMP"/>
        <result column="last_monitor_time" property="lastMonitorTime" jdbcType="TIMESTAMP"/>
        <result column="storage_time" property="storageTime" jdbcType="TIMESTAMP"/>
        <result column="access_total_count" property="accessTotalCount" jdbcType="BIGINT"/>
        <result column="process_total_count" property="processTotalCount" jdbcType="BIGINT"/>
        <result column="lag_count" property="lagCount" jdbcType="BIGINT"/>
        <result column="access_count" property="accessCount" jdbcType="BIGINT"/>
        <result column="process_count" property="processCount" jdbcType="BIGINT"/>
        <result column="lag_change_count" property="lagChangeCount" jdbcType="BIGINT"/>
        <result column="access_tps" property="accessTps" jdbcType="DECIMAL"/>
        <result column="process_tps" property="processTps" jdbcType="DECIMAL"/>
        <result column="remark" property="remark" jdbcType="VARCHAR"/>
    </resultMap>

    <select id="sumExpirationDateProcessTotalCount" resultType="long">
        select
        SUM(total_count)
        from home_page_dwd_statistics
        where
        data_model_id = #{dataModelId,jdbcType=INTEGER}
        and type = 'STREAM'
        and time &lt; #{expirationDate,jdbcType=VARCHAR}
    </select>
    <select id="sumExpirationDateStorageTotalCount" resultType="long">
        select
        SUM(total_count)
        from home_page_dwd_statistics
        where
        storage_id = #{storageId,jdbcType=INTEGER}
        and type = 'STORAGE'
        and time &lt; #{expirationDate,jdbcType=VARCHAR}
    </select>
    <select id="countAssignDateProcessCount" resultType="long">
        select
        COUNT(*)
        from data_process_record
        where
        data_model_id = #{dataModelId,jdbcType=INTEGER}
        AND storage_time &gt;= concat(#{assignDate,jdbcType=VARCHAR}, ' 00:00:00')
        AND storage_time &lt;= concat(#{assignDate,jdbcType=VARCHAR}, ' 23:59:59')
    </select>
    <select id="countTimePointAfterDataCount" resultType="long">
        select
        COUNT(*)
        from data_process_record
        where data_model_id = #{dataModelId,jdbcType=INTEGER} AND storage_time &gt;= #{time,jdbcType=VARCHAR}
    </select>
    <select id="countAssignDateProcessErrorCount" resultType="long">
        select
        COUNT(*)
        from data_process_record
        where
        data_model_id = #{dataModelId,jdbcType=INTEGER}
        AND storage_time &gt;= concat(#{assignDate,jdbcType=VARCHAR}, ' 00:00:00')
        AND storage_time &lt;= concat(#{assignDate,jdbcType=VARCHAR}, ' 23:59:59')
        AND is_error = 1
    </select>
    <select id="countAssignDateStorageErrorCount" resultType="long">
        select
        COUNT(*)
        from data_process_trace
        where
        data_model_id = #{dataModelId,jdbcType=INTEGER}
        AND storage_time &gt;= concat(#{assignDate,jdbcType=VARCHAR}, ' 00:00:00')
        AND storage_time &lt;= concat(#{assignDate,jdbcType=VARCHAR}, ' 23:59:59')
        AND is_error = 1
        AND storage_id = #{storageId,jdbcType=INTEGER}
    </select>
    <select id="sumWriteFailCount" resultType="long">
        SELECT
        sum(JSONExtractInt(item, 'failCount'))
        FROM storage_task
        ARRAY JOIN JSONExtractArrayRaw(write_count_info) AS item
        WHERE
        start_time >= #{time}
        AND JSONExtractInt(item, 'storageId') = #{storageId}
    </select>
    <select id="sumWriteTotalCount" resultType="long">
        SELECT
        sum(JSONExtractInt(item, 'failCount') + JSONExtractInt(item, 'successCount'))
        FROM storage_task
        ARRAY JOIN JSONExtractArrayRaw(write_count_info) AS item
        WHERE
        start_time >= #{time}
        AND JSONExtractInt(item, 'storageId') = #{storageId}
    </select>
</mapper>