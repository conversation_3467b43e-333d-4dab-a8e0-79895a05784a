<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.trs.ai.moye.monitor.mapper.clickhouse.MonitorOdsFluctuationMapper">
    <!-- 结果集映射 -->
    <resultMap id="BaseResultMap" type="com.trs.moye.base.monitor.entity.MonitorOdsFluctuation">
        <result column="id" jdbcType="BIGINT" property="id"/>
        <result column="data_model_id" jdbcType="INTEGER" property="dataModelId"/>
        <result column="data_model_name" jdbcType="VARCHAR" property="dataModelName"/>
        <result column="source_type" jdbcType="VARCHAR" property="sourceType"/>
        <result column="source_sub_type" jdbcType="VARCHAR" property="sourceSubType"/>
        <result column="config_id" jdbcType="INTEGER" property="configId"/>
        <result column="config_version" jdbcType="VARCHAR" property="configVersion"/>
        <result column="monitor_time" jdbcType="TIMESTAMP" property="monitorTime"/>
        <result column="storage_time" jdbcType="TIMESTAMP" property="storageTime"/>
        <result column="total" jdbcType="BIGINT" property="total"/>
        <result column="increment_column" jdbcType="VARCHAR" property="incrementColumn"/>
        <result column="increment_value" jdbcType="VARCHAR" property="incrementValue"/>
        <result column="increment" jdbcType="BIGINT" property="increment"/>
        <result column="average" jdbcType="BIGINT" property="average"/>
        <result column="fluctuation_type" jdbcType="INTEGER" property="fluctuationType"/>
        <result column="fluctuation" jdbcType="INTEGER" property="fluctuation"/>
        <result column="is_threshold_exceeded" jdbcType="TINYINT" property="thresholdExceeded"/>
    </resultMap>
    <!-- 属性列表 -->
    <sql id="Base_Column_List">
        <trim suffixOverrides=",">
            id,
            data_model_id,
            data_model_name,
            source_type,
            source_sub_type,
            config_id,
            config_version,
            monitor_time,
            storage_time,
            total,
            increment_column,
            increment_value,
            `increment`,
            average,
            fluctuation_type,
            fluctuation,
            is_threshold_exceeded,
            layer
        </trim>
    </sql>
    <!-- 普通插入属性列表，注意与Base_Column_List属性列表一一对应 -->
    <sql id="Insert_Property_List">
        <trim suffixOverrides=",">
            #{data.id,jdbcType=BIGINT},
            #{data.dataModelId,jdbcType=INTEGER},
            #{data.dataModelName,jdbcType=VARCHAR},
            #{data.sourceType,jdbcType=VARCHAR},
            #{data.sourceSubType,jdbcType=VARCHAR},
            #{data.configId,jdbcType=INTEGER},
            #{data.configVersion,jdbcType=VARCHAR},
            #{data.monitorTime,jdbcType=TIMESTAMP},
            #{data.storageTime,jdbcType=TIMESTAMP},
            #{data.total,jdbcType=BIGINT},
            #{data.incrementColumn,jdbcType=VARCHAR},
            #{data.incrementValue,jdbcType=VARCHAR},
            #{data.increment,jdbcType=BIGINT},
            #{data.average,jdbcType=BIGINT},
            #{data.fluctuationType,jdbcType=INTEGER},
            #{data.fluctuation,jdbcType=INTEGER},
            #{data.thresholdExceeded,jdbcType=TINYINT},
            #{data.layer,jdbcType=VARCHAR},
        </trim>
    </sql>

    <insert id="insert">
        insert into t_monitor_ods_fluctuation
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <include refid="Base_Column_List"/>
        </trim>
        values
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <include refid="Insert_Property_List"/>
        </trim>
    </insert>

    <select id="selectLatestNByConfigIdAndHourOrderByStorageTimeDesc" resultMap="BaseResultMap">
        select * from t_monitor_ods_fluctuation
        where config_id = #{configId}
          and toHour(monitor_time) >= #{hour}
          and toHour(monitor_time) &lt; #{hour} + 1
        order by monitor_time desc
        limit #{num}
    </select>

    <select id="selectLatestByConfigId" resultMap="BaseResultMap">
        select * from t_monitor_ods_fluctuation
        where config_id = #{configId}
        order by monitor_time desc
        limit 1
    </select>
</mapper>
